   @  <  8  {"files":{".vite":{"files":{"build":{"files":{"fonts":{"files":{}},"main.js":{"size":290552,"offset":"0","integrity":{"algorithm":"SHA256","hash":"2ab7a874c35c389d43253d0faea6258a78852a23204392e12a546f46ad84ab5c","blockSize":4194304,"blocks":["2ab7a874c35c389d43253d0faea6258a78852a23204392e12a546f46ad84ab5c"]}},"preload.js":{"size":1402,"offset":"290552","integrity":{"algorithm":"SHA256","hash":"bce043a36f7bc4a924705f1d171c8c709120723a7ea30fde2c496a54d6b9d30d","blockSize":4194304,"blocks":["bce043a36f7bc4a924705f1d171c8c709120723a7ea30fde2c496a54d6b9d30d"]}}}}}},"package.json":{"size":3892,"offset":"291954","integrity":{"algorithm":"SHA256","hash":"cf547243f1bfc2fde90e38bd39fdc9f69eba0ba5d4b94f2158dc6adde174fa83","blockSize":4194304,"blocks":["cf547243f1bfc2fde90e38bd39fdc9f69eba0ba5d4b94f2158dc6adde174fa83"]}}}}"use strict";const ae=require("electron"),Ir=require("node:path"),Qt=require("fs"),Nt=require("path"),Hu=require("child_process"),Bu=require("tty"),xs=require("util"),Wu=require("net"),Ju=require("crypto"),Xu=require("assert"),Yu=require("events"),Qu=require("os");var nn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ic(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ts={exports:{}},sn={exports:{}},an={exports:{}},hs,ri;function Zu(){if(ri)return hs;ri=1;var e=1e3,t=e*60,r=t*60,n=r*24,s=n*365.25;hs=function(u,c){c=c||{};var h=typeof u;if(h==="string"&&u.length>0)return a(u);if(h==="number"&&isNaN(u)===!1)return c.long?d(u):i(u);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(u))};function a(u){if(u=String(u),!(u.length>100)){var c=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(u);if(c){var h=parseFloat(c[1]),S=(c[2]||"ms").toLowerCase();switch(S){case"years":case"year":case"yrs":case"yr":case"y":return h*s;case"days":case"day":case"d":return h*n;case"hours":case"hour":case"hrs":case"hr":case"h":return h*r;case"minutes":case"minute":case"mins":case"min":case"m":return h*t;case"seconds":case"second":case"secs":case"sec":case"s":return h*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return h;default:return}}}}function i(u){return u>=n?Math.round(u/n)+"d":u>=r?Math.round(u/r)+"h":u>=t?Math.round(u/t)+"m":u>=e?Math.round(u/e)+"s":u+"ms"}function d(u){return l(u,n,"day")||l(u,r,"hour")||l(u,t,"minute")||l(u,e,"second")||u+" ms"}function l(u,c,h){if(!(u<c))return u<c*1.5?Math.floor(u/c)+" "+h:Math.ceil(u/c)+" "+h+"s"}return hs}var ni;function Tc(){return ni||(ni=1,function(e,t){t=e.exports=s.debug=s.default=s,t.coerce=l,t.disable=i,t.enable=a,t.enabled=d,t.humanize=Zu(),t.names=[],t.skips=[],t.formatters={};var r;function n(u){var c=0,h;for(h in u)c=(c<<5)-c+u.charCodeAt(h),c|=0;return t.colors[Math.abs(c)%t.colors.length]}function s(u){function c(){if(c.enabled){var h=c,S=+new Date,y=S-(r||S);h.diff=y,h.prev=r,h.curr=S,r=S;for(var v=new Array(arguments.length),g=0;g<v.length;g++)v[g]=arguments[g];v[0]=t.coerce(v[0]),typeof v[0]!="string"&&v.unshift("%O");var $=0;v[0]=v[0].replace(/%([a-zA-Z%])/g,function(w,N){if(w==="%%")return w;$++;var O=t.formatters[N];if(typeof O=="function"){var T=v[$];w=O.call(h,T),v.splice($,1),$--}return w}),t.formatArgs.call(h,v);var p=c.log||t.log||console.log.bind(console);p.apply(h,v)}}return c.namespace=u,c.enabled=t.enabled(u),c.useColors=t.useColors(),c.color=n(u),typeof t.init=="function"&&t.init(c),c}function a(u){t.save(u),t.names=[],t.skips=[];for(var c=(typeof u=="string"?u:"").split(/[\s,]+/),h=c.length,S=0;S<h;S++)c[S]&&(u=c[S].replace(/\*/g,".*?"),u[0]==="-"?t.skips.push(new RegExp("^"+u.substr(1)+"$")):t.names.push(new RegExp("^"+u+"$")))}function i(){t.enable("")}function d(u){var c,h;for(c=0,h=t.skips.length;c<h;c++)if(t.skips[c].test(u))return!1;for(c=0,h=t.names.length;c<h;c++)if(t.names[c].test(u))return!0;return!1}function l(u){return u instanceof Error?u.stack||u.message:u}}(an,an.exports)),an.exports}var si;function xu(){return si||(si=1,function(e,t){t=e.exports=Tc(),t.log=s,t.formatArgs=n,t.save=a,t.load=i,t.useColors=r,t.storage=typeof chrome<"u"&&typeof chrome.storage<"u"?chrome.storage.local:d(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"];function r(){return typeof window<"u"&&window.process&&window.process.type==="renderer"?!0:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}t.formatters.j=function(l){try{return JSON.stringify(l)}catch(u){return"[UnexpectedJSONParseError]: "+u.message}};function n(l){var u=this.useColors;if(l[0]=(u?"%c":"")+this.namespace+(u?" %c":" ")+l[0]+(u?"%c ":" ")+"+"+t.humanize(this.diff),!!u){var c="color: "+this.color;l.splice(1,0,c,"color: inherit");var h=0,S=0;l[0].replace(/%[a-zA-Z%]/g,function(y){y!=="%%"&&(h++,y==="%c"&&(S=h))}),l.splice(S,0,c)}}function s(){return typeof console=="object"&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function a(l){try{l==null?t.storage.removeItem("debug"):t.storage.debug=l}catch{}}function i(){var l;try{l=t.storage.debug}catch{}return!l&&typeof process<"u"&&"env"in process&&(l=process.env.DEBUG),l}t.enable(i());function d(){try{return window.localStorage}catch{}}}(sn,sn.exports)),sn.exports}var on={exports:{}},ai;function ed(){return ai||(ai=1,function(e,t){var r=Bu,n=xs;t=e.exports=Tc(),t.init=S,t.log=l,t.formatArgs=d,t.save=u,t.load=c,t.useColors=i,t.colors=[6,2,3,4,5,1],t.inspectOpts=Object.keys(process.env).filter(function(y){return/^debug_/i.test(y)}).reduce(function(y,v){var g=v.substring(6).toLowerCase().replace(/_([a-z])/g,function(p,w){return w.toUpperCase()}),$=process.env[v];return/^(yes|on|true|enabled)$/i.test($)?$=!0:/^(no|off|false|disabled)$/i.test($)?$=!1:$==="null"?$=null:$=Number($),y[g]=$,y},{});var s=parseInt(process.env.DEBUG_FD,10)||2;s!==1&&s!==2&&n.deprecate(function(){},"except for stderr(2) and stdout(1), any other usage of DEBUG_FD is deprecated. Override debug.log if you want to use a different log function (https://git.io/debug_fd)")();var a=s===1?process.stdout:s===2?process.stderr:h(s);function i(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(s)}t.formatters.o=function(y){return this.inspectOpts.colors=this.useColors,n.inspect(y,this.inspectOpts).split(`
`).map(function(v){return v.trim()}).join(" ")},t.formatters.O=function(y){return this.inspectOpts.colors=this.useColors,n.inspect(y,this.inspectOpts)};function d(y){var v=this.namespace,g=this.useColors;if(g){var $=this.color,p="  \x1B[3"+$+";1m"+v+" \x1B[0m";y[0]=p+y[0].split(`
`).join(`
`+p),y.push("\x1B[3"+$+"m+"+t.humanize(this.diff)+"\x1B[0m")}else y[0]=new Date().toUTCString()+" "+v+" "+y[0]}function l(){return a.write(n.format.apply(n,arguments)+`
`)}function u(y){y==null?delete process.env.DEBUG:process.env.DEBUG=y}function c(){return process.env.DEBUG}function h(y){var v,g=process.binding("tty_wrap");switch(g.guessHandleType(y)){case"TTY":v=new r.WriteStream(y),v._type="tty",v._handle&&v._handle.unref&&v._handle.unref();break;case"FILE":var $=Qt;v=new $.SyncWriteStream(y,{autoClose:!1}),v._type="fs";break;case"PIPE":case"TCP":var p=Wu;v=new p.Socket({fd:y,readable:!1,writable:!0}),v.readable=!1,v.read=null,v._type="pipe",v._handle&&v._handle.unref&&v._handle.unref();break;default:throw new Error("Implement me. Unknown stream file type!")}return v.fd=y,v._isStdio=!0,v}function S(y){y.inspectOpts={};for(var v=Object.keys(t.inspectOpts),g=0;g<v.length;g++)y.inspectOpts[v[g]]=t.inspectOpts[v[g]]}t.enable(c())}(on,on.exports)),on.exports}typeof process<"u"&&process.type==="renderer"?Ts.exports=xu():Ts.exports=ed();var td=Ts.exports,js=Nt,rd=Hu.spawn,jc=td("electron-squirrel-startup"),ps=ae.app,oi=function(e,t){var r=js.resolve(js.dirname(process.execPath),"..","Update.exe");jc("Spawning `%s` with args `%s`",r,e),rd(r,e,{detached:!0}).on("close",t)},nd=function(){if(process.platform==="win32"){var e=process.argv[1];jc("processing squirrel command `%s`",e);var t=js.basename(process.execPath);if(e==="--squirrel-install"||e==="--squirrel-updated")return oi(["--createShortcut="+t],ps.quit),!0;if(e==="--squirrel-uninstall")return oi(["--removeShortcut="+t],ps.quit),!0;if(e==="--squirrel-obsolete")return ps.quit(),!0}return!1},sd=nd();const ad=Ic(sd);var As={exports:{}},od=e=>{const t=typeof e;return e!==null&&(t==="object"||t==="function")};const zt=od,id=new Set(["__proto__","prototype","constructor"]),cd=e=>!e.some(t=>id.has(t));function cn(e){const t=e.split("."),r=[];for(let n=0;n<t.length;n++){let s=t[n];for(;s[s.length-1]==="\\"&&t[n+1]!==void 0;)s=s.slice(0,-1)+".",s+=t[++n];r.push(s)}return cd(r)?r:[]}var ld={get(e,t,r){if(!zt(e)||typeof t!="string")return r===void 0?e:r;const n=cn(t);if(n.length!==0){for(let s=0;s<n.length;s++)if(e=e[n[s]],e==null){if(s!==n.length-1)return r;break}return e===void 0?r:e}},set(e,t,r){if(!zt(e)||typeof t!="string")return e;const n=e,s=cn(t);for(let a=0;a<s.length;a++){const i=s[a];zt(e[i])||(e[i]={}),a===s.length-1&&(e[i]=r),e=e[i]}return n},delete(e,t){if(!zt(e)||typeof t!="string")return!1;const r=cn(t);for(let n=0;n<r.length;n++){const s=r[n];if(n===r.length-1)return delete e[s],!0;if(e=e[s],!zt(e))return!1}},has(e,t){if(!zt(e)||typeof t!="string")return!1;const r=cn(t);if(r.length===0)return!1;for(let n=0;n<r.length;n++)if(zt(e)){if(!(r[n]in e))return!1;e=e[r[n]]}else return!1;return!0}},ea={exports:{}},ta={exports:{}},ra={exports:{}},na={exports:{}};const Ac=Qt;na.exports=e=>new Promise(t=>{Ac.access(e,r=>{t(!r)})});na.exports.sync=e=>{try{return Ac.accessSync(e),!0}catch{return!1}};var ud=na.exports,sa={exports:{}},aa={exports:{}};const kc=(e,...t)=>new Promise(r=>{r(e(...t))});aa.exports=kc;aa.exports.default=kc;var dd=aa.exports;const fd=dd,Cc=e=>{if(!((Number.isInteger(e)||e===1/0)&&e>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));const t=[];let r=0;const n=()=>{r--,t.length>0&&t.shift()()},s=(d,l,...u)=>{r++;const c=fd(d,...u);l(c),c.then(n,n)},a=(d,l,...u)=>{r<e?s(d,l,...u):t.push(s.bind(null,d,l,...u))},i=(d,...l)=>new Promise(u=>a(d,u,...l));return Object.defineProperties(i,{activeCount:{get:()=>r},pendingCount:{get:()=>t.length},clearQueue:{value:()=>{t.length=0}}}),i};sa.exports=Cc;sa.exports.default=Cc;var hd=sa.exports;const ii=hd;class Dc extends Error{constructor(t){super(),this.value=t}}const pd=(e,t)=>Promise.resolve(e).then(t),md=e=>Promise.all(e).then(t=>t[1]===!0&&Promise.reject(new Dc(t[0])));var $d=(e,t,r)=>{r=Object.assign({concurrency:1/0,preserveOrder:!0},r);const n=ii(r.concurrency),s=[...e].map(i=>[i,n(pd,i,t)]),a=ii(r.preserveOrder?1:1/0);return Promise.all(s.map(i=>a(md,i))).then(()=>{}).catch(i=>i instanceof Dc?i.value:Promise.reject(i))};const Mc=Nt,Lc=ud,yd=$d;ra.exports=(e,t)=>(t=Object.assign({cwd:process.cwd()},t),yd(e,r=>Lc(Mc.resolve(t.cwd,r)),t));ra.exports.sync=(e,t)=>{t=Object.assign({cwd:process.cwd()},t);for(const r of e)if(Lc.sync(Mc.resolve(t.cwd,r)))return r};var _d=ra.exports;const vt=Nt,Fc=_d;ta.exports=(e,t={})=>{const r=vt.resolve(t.cwd||""),{root:n}=vt.parse(r),s=[].concat(e);return new Promise(a=>{(function i(d){Fc(s,{cwd:d}).then(l=>{l?a(vt.join(d,l)):d===n?a(null):i(vt.dirname(d))})})(r)})};ta.exports.sync=(e,t={})=>{let r=vt.resolve(t.cwd||"");const{root:n}=vt.parse(r),s=[].concat(e);for(;;){const a=Fc.sync(s,{cwd:r});if(a)return vt.join(r,a);if(r===n)return null;r=vt.dirname(r)}};var gd=ta.exports;const Vc=gd;ea.exports=async({cwd:e}={})=>Vc("package.json",{cwd:e});ea.exports.sync=({cwd:e}={})=>Vc.sync("package.json",{cwd:e});var vd=ea.exports,oa={exports:{}};const me=Nt,Uc=Qu,gt=Uc.homedir(),ia=Uc.tmpdir(),{env:lr}=process,wd=e=>{const t=me.join(gt,"Library");return{data:me.join(t,"Application Support",e),config:me.join(t,"Preferences",e),cache:me.join(t,"Caches",e),log:me.join(t,"Logs",e),temp:me.join(ia,e)}},Ed=e=>{const t=lr.APPDATA||me.join(gt,"AppData","Roaming"),r=lr.LOCALAPPDATA||me.join(gt,"AppData","Local");return{data:me.join(r,e,"Data"),config:me.join(t,e,"Config"),cache:me.join(r,e,"Cache"),log:me.join(r,e,"Log"),temp:me.join(ia,e)}},Sd=e=>{const t=me.basename(gt);return{data:me.join(lr.XDG_DATA_HOME||me.join(gt,".local","share"),e),config:me.join(lr.XDG_CONFIG_HOME||me.join(gt,".config"),e),cache:me.join(lr.XDG_CACHE_HOME||me.join(gt,".cache"),e),log:me.join(lr.XDG_STATE_HOME||me.join(gt,".local","state"),e),temp:me.join(ia,t,e)}},zc=(e,t)=>{if(typeof e!="string")throw new TypeError(`Expected string, got ${typeof e}`);return t=Object.assign({suffix:"nodejs"},t),t.suffix&&(e+=`-${t.suffix}`),process.platform==="darwin"?wd(e):process.platform==="win32"?Ed(e):Sd(e)};oa.exports=zc;oa.exports.default=zc;var bd=oa.exports,st={},oe={};Object.defineProperty(oe,"__esModule",{value:!0});oe.NOOP=oe.LIMIT_FILES_DESCRIPTORS=oe.LIMIT_BASENAME_LENGTH=oe.IS_USER_ROOT=oe.IS_POSIX=oe.DEFAULT_TIMEOUT_SYNC=oe.DEFAULT_TIMEOUT_ASYNC=oe.DEFAULT_WRITE_OPTIONS=oe.DEFAULT_READ_OPTIONS=oe.DEFAULT_FOLDER_MODE=oe.DEFAULT_FILE_MODE=oe.DEFAULT_ENCODING=void 0;const Pd="utf8";oe.DEFAULT_ENCODING=Pd;const Nd=438;oe.DEFAULT_FILE_MODE=Nd;const Od=511;oe.DEFAULT_FOLDER_MODE=Od;const Rd={};oe.DEFAULT_READ_OPTIONS=Rd;const Id={};oe.DEFAULT_WRITE_OPTIONS=Id;const Td=5e3;oe.DEFAULT_TIMEOUT_ASYNC=Td;const jd=100;oe.DEFAULT_TIMEOUT_SYNC=jd;const Ad=!!process.getuid;oe.IS_POSIX=Ad;const kd=process.getuid?!process.getuid():!1;oe.IS_USER_ROOT=kd;const Cd=128;oe.LIMIT_BASENAME_LENGTH=Cd;const Dd=1e4;oe.LIMIT_FILES_DESCRIPTORS=Dd;const Md=()=>{};oe.NOOP=Md;var Wn={},pr={};Object.defineProperty(pr,"__esModule",{value:!0});pr.attemptifySync=pr.attemptifyAsync=void 0;const qc=oe,Ld=(e,t=qc.NOOP)=>function(){return e.apply(void 0,arguments).catch(t)};pr.attemptifyAsync=Ld;const Fd=(e,t=qc.NOOP)=>function(){try{return e.apply(void 0,arguments)}catch(r){return t(r)}};pr.attemptifySync=Fd;var ca={};Object.defineProperty(ca,"__esModule",{value:!0});const Vd=oe,Kc={isChangeErrorOk:e=>{const{code:t}=e;return t==="ENOSYS"||!Vd.IS_USER_ROOT&&(t==="EINVAL"||t==="EPERM")},isRetriableError:e=>{const{code:t}=e;return t==="EMFILE"||t==="ENFILE"||t==="EAGAIN"||t==="EBUSY"||t==="EACCESS"||t==="EACCS"||t==="EPERM"},onChangeError:e=>{if(!Kc.isChangeErrorOk(e))throw e}};ca.default=Kc;var mr={},la={};Object.defineProperty(la,"__esModule",{value:!0});const Ud=oe,ue={interval:25,intervalId:void 0,limit:Ud.LIMIT_FILES_DESCRIPTORS,queueActive:new Set,queueWaiting:new Set,init:()=>{ue.intervalId||(ue.intervalId=setInterval(ue.tick,ue.interval))},reset:()=>{ue.intervalId&&(clearInterval(ue.intervalId),delete ue.intervalId)},add:e=>{ue.queueWaiting.add(e),ue.queueActive.size<ue.limit/2?ue.tick():ue.init()},remove:e=>{ue.queueWaiting.delete(e),ue.queueActive.delete(e)},schedule:()=>new Promise(e=>{const t=()=>ue.remove(r),r=()=>e(t);ue.add(r)}),tick:()=>{if(!(ue.queueActive.size>=ue.limit)){if(!ue.queueWaiting.size)return ue.reset();for(const e of ue.queueWaiting){if(ue.queueActive.size>=ue.limit)break;ue.queueWaiting.delete(e),ue.queueActive.add(e),e()}}}};la.default=ue;Object.defineProperty(mr,"__esModule",{value:!0});mr.retryifySync=mr.retryifyAsync=void 0;const zd=la,qd=(e,t)=>function(r){return function n(){return zd.default.schedule().then(s=>e.apply(void 0,arguments).then(a=>(s(),a),a=>{if(s(),Date.now()>=r)throw a;if(t(a)){const i=Math.round(100+400*Math.random());return new Promise(l=>setTimeout(l,i)).then(()=>n.apply(void 0,arguments))}throw a}))}};mr.retryifyAsync=qd;const Kd=(e,t)=>function(r){return function n(){try{return e.apply(void 0,arguments)}catch(s){if(Date.now()>r)throw s;if(t(s))return n.apply(void 0,arguments);throw s}}};mr.retryifySync=Kd;Object.defineProperty(Wn,"__esModule",{value:!0});const ie=Qt,Re=xs,Ie=pr,ve=ca,Ce=mr,Gd={chmodAttempt:Ie.attemptifyAsync(Re.promisify(ie.chmod),ve.default.onChangeError),chownAttempt:Ie.attemptifyAsync(Re.promisify(ie.chown),ve.default.onChangeError),closeAttempt:Ie.attemptifyAsync(Re.promisify(ie.close)),fsyncAttempt:Ie.attemptifyAsync(Re.promisify(ie.fsync)),mkdirAttempt:Ie.attemptifyAsync(Re.promisify(ie.mkdir)),realpathAttempt:Ie.attemptifyAsync(Re.promisify(ie.realpath)),statAttempt:Ie.attemptifyAsync(Re.promisify(ie.stat)),unlinkAttempt:Ie.attemptifyAsync(Re.promisify(ie.unlink)),closeRetry:Ce.retryifyAsync(Re.promisify(ie.close),ve.default.isRetriableError),fsyncRetry:Ce.retryifyAsync(Re.promisify(ie.fsync),ve.default.isRetriableError),openRetry:Ce.retryifyAsync(Re.promisify(ie.open),ve.default.isRetriableError),readFileRetry:Ce.retryifyAsync(Re.promisify(ie.readFile),ve.default.isRetriableError),renameRetry:Ce.retryifyAsync(Re.promisify(ie.rename),ve.default.isRetriableError),statRetry:Ce.retryifyAsync(Re.promisify(ie.stat),ve.default.isRetriableError),writeRetry:Ce.retryifyAsync(Re.promisify(ie.write),ve.default.isRetriableError),chmodSyncAttempt:Ie.attemptifySync(ie.chmodSync,ve.default.onChangeError),chownSyncAttempt:Ie.attemptifySync(ie.chownSync,ve.default.onChangeError),closeSyncAttempt:Ie.attemptifySync(ie.closeSync),mkdirSyncAttempt:Ie.attemptifySync(ie.mkdirSync),realpathSyncAttempt:Ie.attemptifySync(ie.realpathSync),statSyncAttempt:Ie.attemptifySync(ie.statSync),unlinkSyncAttempt:Ie.attemptifySync(ie.unlinkSync),closeSyncRetry:Ce.retryifySync(ie.closeSync,ve.default.isRetriableError),fsyncSyncRetry:Ce.retryifySync(ie.fsyncSync,ve.default.isRetriableError),openSyncRetry:Ce.retryifySync(ie.openSync,ve.default.isRetriableError),readFileSyncRetry:Ce.retryifySync(ie.readFileSync,ve.default.isRetriableError),renameSyncRetry:Ce.retryifySync(ie.renameSync,ve.default.isRetriableError),statSyncRetry:Ce.retryifySync(ie.statSync,ve.default.isRetriableError),writeSyncRetry:Ce.retryifySync(ie.writeSync,ve.default.isRetriableError)};Wn.default=Gd;var ua={};Object.defineProperty(ua,"__esModule",{value:!0});const Hd={isFunction:e=>typeof e=="function",isString:e=>typeof e=="string",isUndefined:e=>typeof e>"u"};ua.default=Hd;var da={};Object.defineProperty(da,"__esModule",{value:!0});const ln={},ks={next:e=>{const t=ln[e];if(!t)return;t.shift();const r=t[0];r?r(()=>ks.next(e)):delete ln[e]},schedule:e=>new Promise(t=>{let r=ln[e];r||(r=ln[e]=[]),r.push(t),!(r.length>1)&&t(()=>ks.next(e))})};da.default=ks;var fa={};Object.defineProperty(fa,"__esModule",{value:!0});const Bd=Nt,ci=oe,li=Wn,Ue={store:{},create:e=>{const t=`000000${Math.floor(Math.random()*16777215).toString(16)}`.slice(-6),r=Date.now().toString().slice(-10),n="tmp-",s=`.${n}${r}${t}`;return`${e}${s}`},get:(e,t,r=!0)=>{const n=Ue.truncate(t(e));return n in Ue.store?Ue.get(e,t,r):(Ue.store[n]=r,[n,()=>delete Ue.store[n]])},purge:e=>{Ue.store[e]&&(delete Ue.store[e],li.default.unlinkAttempt(e))},purgeSync:e=>{Ue.store[e]&&(delete Ue.store[e],li.default.unlinkSyncAttempt(e))},purgeSyncAll:()=>{for(const e in Ue.store)Ue.purgeSync(e)},truncate:e=>{const t=Bd.basename(e);if(t.length<=ci.LIMIT_BASENAME_LENGTH)return e;const r=/^(\.?)(.*?)((?:\.[^.]+)?(?:\.tmp-\d{10}[a-f0-9]{6})?)$/.exec(t);if(!r)return e;const n=t.length-ci.LIMIT_BASENAME_LENGTH;return`${e.slice(0,-t.length)}${r[1]}${r[2].slice(0,-n)}${r[3]}`}};process.on("exit",Ue.purgeSyncAll);fa.default=Ue;Object.defineProperty(st,"__esModule",{value:!0});st.writeFileSync=st.writeFile=st.readFileSync=st.readFile=void 0;const Gc=Nt,Se=oe,se=Wn,ze=ua,Wd=da,wt=fa;function Hc(e,t=Se.DEFAULT_READ_OPTIONS){var r;if(ze.default.isString(t))return Hc(e,{encoding:t});const n=Date.now()+((r=t.timeout)!==null&&r!==void 0?r:Se.DEFAULT_TIMEOUT_ASYNC);return se.default.readFileRetry(n)(e,t)}st.readFile=Hc;function Bc(e,t=Se.DEFAULT_READ_OPTIONS){var r;if(ze.default.isString(t))return Bc(e,{encoding:t});const n=Date.now()+((r=t.timeout)!==null&&r!==void 0?r:Se.DEFAULT_TIMEOUT_SYNC);return se.default.readFileSyncRetry(n)(e,t)}st.readFileSync=Bc;const Wc=(e,t,r,n)=>{if(ze.default.isFunction(r))return Wc(e,t,Se.DEFAULT_WRITE_OPTIONS,r);const s=Jc(e,t,r);return n&&s.then(n,n),s};st.writeFile=Wc;const Jc=async(e,t,r=Se.DEFAULT_WRITE_OPTIONS)=>{var n;if(ze.default.isString(r))return Jc(e,t,{encoding:r});const s=Date.now()+((n=r.timeout)!==null&&n!==void 0?n:Se.DEFAULT_TIMEOUT_ASYNC);let a=null,i=null,d=null,l=null,u=null;try{r.schedule&&(a=await r.schedule(e)),i=await Wd.default.schedule(e),e=await se.default.realpathAttempt(e)||e,[l,d]=wt.default.get(e,r.tmpCreate||wt.default.create,r.tmpPurge!==!1);const c=Se.IS_POSIX&&ze.default.isUndefined(r.chown),h=ze.default.isUndefined(r.mode);if(c||h){const y=await se.default.statAttempt(e);y&&(r={...r},c&&(r.chown={uid:y.uid,gid:y.gid}),h&&(r.mode=y.mode))}const S=Gc.dirname(e);await se.default.mkdirAttempt(S,{mode:Se.DEFAULT_FOLDER_MODE,recursive:!0}),u=await se.default.openRetry(s)(l,"w",r.mode||Se.DEFAULT_FILE_MODE),r.tmpCreated&&r.tmpCreated(l),ze.default.isString(t)?await se.default.writeRetry(s)(u,t,0,r.encoding||Se.DEFAULT_ENCODING):ze.default.isUndefined(t)||await se.default.writeRetry(s)(u,t,0,t.length,0),r.fsync!==!1&&(r.fsyncWait!==!1?await se.default.fsyncRetry(s)(u):se.default.fsyncAttempt(u)),await se.default.closeRetry(s)(u),u=null,r.chown&&await se.default.chownAttempt(l,r.chown.uid,r.chown.gid),r.mode&&await se.default.chmodAttempt(l,r.mode);try{await se.default.renameRetry(s)(l,e)}catch(y){if(y.code!=="ENAMETOOLONG")throw y;await se.default.renameRetry(s)(l,wt.default.truncate(e))}d(),l=null}finally{u&&await se.default.closeAttempt(u),l&&wt.default.purge(l),a&&a(),i&&i()}},Xc=(e,t,r=Se.DEFAULT_WRITE_OPTIONS)=>{var n;if(ze.default.isString(r))return Xc(e,t,{encoding:r});const s=Date.now()+((n=r.timeout)!==null&&n!==void 0?n:Se.DEFAULT_TIMEOUT_SYNC);let a=null,i=null,d=null;try{e=se.default.realpathSyncAttempt(e)||e,[i,a]=wt.default.get(e,r.tmpCreate||wt.default.create,r.tmpPurge!==!1);const l=Se.IS_POSIX&&ze.default.isUndefined(r.chown),u=ze.default.isUndefined(r.mode);if(l||u){const h=se.default.statSyncAttempt(e);h&&(r={...r},l&&(r.chown={uid:h.uid,gid:h.gid}),u&&(r.mode=h.mode))}const c=Gc.dirname(e);se.default.mkdirSyncAttempt(c,{mode:Se.DEFAULT_FOLDER_MODE,recursive:!0}),d=se.default.openSyncRetry(s)(i,"w",r.mode||Se.DEFAULT_FILE_MODE),r.tmpCreated&&r.tmpCreated(i),ze.default.isString(t)?se.default.writeSyncRetry(s)(d,t,0,r.encoding||Se.DEFAULT_ENCODING):ze.default.isUndefined(t)||se.default.writeSyncRetry(s)(d,t,0,t.length,0),r.fsync!==!1&&(r.fsyncWait!==!1?se.default.fsyncSyncRetry(s)(d):se.default.fsyncAttempt(d)),se.default.closeSyncRetry(s)(d),d=null,r.chown&&se.default.chownSyncAttempt(i,r.chown.uid,r.chown.gid),r.mode&&se.default.chmodSyncAttempt(i,r.mode);try{se.default.renameSyncRetry(s)(i,e)}catch(h){if(h.code!=="ENAMETOOLONG")throw h;se.default.renameSyncRetry(s)(i,wt.default.truncate(e))}a(),i=null}finally{d&&se.default.closeSyncAttempt(d),i&&wt.default.purge(i)}};st.writeFileSync=Xc;var Cs={exports:{}},Yc={},Qe={},$r={},Yr={},te={},Jr={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.regexpCode=e.getEsmExportName=e.getProperty=e.safeStringify=e.stringify=e.strConcat=e.addCodeArg=e.str=e._=e.nil=e._Code=e.Name=e.IDENTIFIER=e._CodeOrName=void 0;class t{}e._CodeOrName=t,e.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class r extends t{constructor(w){if(super(),!e.IDENTIFIER.test(w))throw new Error("CodeGen: name must be a valid identifier");this.str=w}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}e.Name=r;class n extends t{constructor(w){super(),this._items=typeof w=="string"?[w]:w}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const w=this._items[0];return w===""||w==='""'}get str(){var w;return(w=this._str)!==null&&w!==void 0?w:this._str=this._items.reduce((N,O)=>`${N}${O}`,"")}get names(){var w;return(w=this._names)!==null&&w!==void 0?w:this._names=this._items.reduce((N,O)=>(O instanceof r&&(N[O.str]=(N[O.str]||0)+1),N),{})}}e._Code=n,e.nil=new n("");function s(p,...w){const N=[p[0]];let O=0;for(;O<w.length;)d(N,w[O]),N.push(p[++O]);return new n(N)}e._=s;const a=new n("+");function i(p,...w){const N=[y(p[0])];let O=0;for(;O<w.length;)N.push(a),d(N,w[O]),N.push(a,y(p[++O]));return l(N),new n(N)}e.str=i;function d(p,w){w instanceof n?p.push(...w._items):w instanceof r?p.push(w):p.push(h(w))}e.addCodeArg=d;function l(p){let w=1;for(;w<p.length-1;){if(p[w]===a){const N=u(p[w-1],p[w+1]);if(N!==void 0){p.splice(w-1,3,N);continue}p[w++]="+"}w++}}function u(p,w){if(w==='""')return p;if(p==='""')return w;if(typeof p=="string")return w instanceof r||p[p.length-1]!=='"'?void 0:typeof w!="string"?`${p.slice(0,-1)}${w}"`:w[0]==='"'?p.slice(0,-1)+w.slice(1):void 0;if(typeof w=="string"&&w[0]==='"'&&!(p instanceof r))return`"${p}${w.slice(1)}`}function c(p,w){return w.emptyStr()?p:p.emptyStr()?w:i`${p}${w}`}e.strConcat=c;function h(p){return typeof p=="number"||typeof p=="boolean"||p===null?p:y(Array.isArray(p)?p.join(","):p)}function S(p){return new n(y(p))}e.stringify=S;function y(p){return JSON.stringify(p).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}e.safeStringify=y;function v(p){return typeof p=="string"&&e.IDENTIFIER.test(p)?new n(`.${p}`):s`[${p}]`}e.getProperty=v;function g(p){if(typeof p=="string"&&e.IDENTIFIER.test(p))return new n(`${p}`);throw new Error(`CodeGen: invalid export name: ${p}, use explicit $id name mapping`)}e.getEsmExportName=g;function $(p){return new n(p.toString())}e.regexpCode=$})(Jr);var Ds={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ValueScope=e.ValueScopeName=e.Scope=e.varKinds=e.UsedValueState=void 0;const t=Jr;class r extends Error{constructor(u){super(`CodeGen: "code" for ${u} not defined`),this.value=u.value}}var n;(function(l){l[l.Started=0]="Started",l[l.Completed=1]="Completed"})(n||(e.UsedValueState=n={})),e.varKinds={const:new t.Name("const"),let:new t.Name("let"),var:new t.Name("var")};class s{constructor({prefixes:u,parent:c}={}){this._names={},this._prefixes=u,this._parent=c}toName(u){return u instanceof t.Name?u:this.name(u)}name(u){return new t.Name(this._newName(u))}_newName(u){const c=this._names[u]||this._nameGroup(u);return`${u}${c.index++}`}_nameGroup(u){var c,h;if(!((h=(c=this._parent)===null||c===void 0?void 0:c._prefixes)===null||h===void 0)&&h.has(u)||this._prefixes&&!this._prefixes.has(u))throw new Error(`CodeGen: prefix "${u}" is not allowed in this scope`);return this._names[u]={prefix:u,index:0}}}e.Scope=s;class a extends t.Name{constructor(u,c){super(c),this.prefix=u}setValue(u,{property:c,itemIndex:h}){this.value=u,this.scopePath=(0,t._)`.${new t.Name(c)}[${h}]`}}e.ValueScopeName=a;const i=(0,t._)`\n`;class d extends s{constructor(u){super(u),this._values={},this._scope=u.scope,this.opts={...u,_n:u.lines?i:t.nil}}get(){return this._scope}name(u){return new a(u,this._newName(u))}value(u,c){var h;if(c.ref===void 0)throw new Error("CodeGen: ref must be passed in value");const S=this.toName(u),{prefix:y}=S,v=(h=c.key)!==null&&h!==void 0?h:c.ref;let g=this._values[y];if(g){const w=g.get(v);if(w)return w}else g=this._values[y]=new Map;g.set(v,S);const $=this._scope[y]||(this._scope[y]=[]),p=$.length;return $[p]=c.ref,S.setValue(c,{property:y,itemIndex:p}),S}getValue(u,c){const h=this._values[u];if(h)return h.get(c)}scopeRefs(u,c=this._values){return this._reduceValues(c,h=>{if(h.scopePath===void 0)throw new Error(`CodeGen: name "${h}" has no value`);return(0,t._)`${u}${h.scopePath}`})}scopeCode(u=this._values,c,h){return this._reduceValues(u,S=>{if(S.value===void 0)throw new Error(`CodeGen: name "${S}" has no value`);return S.value.code},c,h)}_reduceValues(u,c,h={},S){let y=t.nil;for(const v in u){const g=u[v];if(!g)continue;const $=h[v]=h[v]||new Map;g.forEach(p=>{if($.has(p))return;$.set(p,n.Started);let w=c(p);if(w){const N=this.opts.es5?e.varKinds.var:e.varKinds.const;y=(0,t._)`${y}${N} ${p} = ${w};${this.opts._n}`}else if(w=S==null?void 0:S(p))y=(0,t._)`${y}${w}${this.opts._n}`;else throw new r(p);$.set(p,n.Completed)})}return y}}e.ValueScope=d})(Ds);(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.or=e.and=e.not=e.CodeGen=e.operators=e.varKinds=e.ValueScopeName=e.ValueScope=e.Scope=e.Name=e.regexpCode=e.stringify=e.getProperty=e.nil=e.strConcat=e.str=e._=void 0;const t=Jr,r=Ds;var n=Jr;Object.defineProperty(e,"_",{enumerable:!0,get:function(){return n._}}),Object.defineProperty(e,"str",{enumerable:!0,get:function(){return n.str}}),Object.defineProperty(e,"strConcat",{enumerable:!0,get:function(){return n.strConcat}}),Object.defineProperty(e,"nil",{enumerable:!0,get:function(){return n.nil}}),Object.defineProperty(e,"getProperty",{enumerable:!0,get:function(){return n.getProperty}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return n.stringify}}),Object.defineProperty(e,"regexpCode",{enumerable:!0,get:function(){return n.regexpCode}}),Object.defineProperty(e,"Name",{enumerable:!0,get:function(){return n.Name}});var s=Ds;Object.defineProperty(e,"Scope",{enumerable:!0,get:function(){return s.Scope}}),Object.defineProperty(e,"ValueScope",{enumerable:!0,get:function(){return s.ValueScope}}),Object.defineProperty(e,"ValueScopeName",{enumerable:!0,get:function(){return s.ValueScopeName}}),Object.defineProperty(e,"varKinds",{enumerable:!0,get:function(){return s.varKinds}}),e.operators={GT:new t._Code(">"),GTE:new t._Code(">="),LT:new t._Code("<"),LTE:new t._Code("<="),EQ:new t._Code("==="),NEQ:new t._Code("!=="),NOT:new t._Code("!"),OR:new t._Code("||"),AND:new t._Code("&&"),ADD:new t._Code("+")};class a{optimizeNodes(){return this}optimizeNames(o,f){return this}}class i extends a{constructor(o,f,P){super(),this.varKind=o,this.name=f,this.rhs=P}render({es5:o,_n:f}){const P=o?r.varKinds.var:this.varKind,j=this.rhs===void 0?"":` = ${this.rhs}`;return`${P} ${this.name}${j};`+f}optimizeNames(o,f){if(o[this.name.str])return this.rhs&&(this.rhs=C(this.rhs,o,f)),this}get names(){return this.rhs instanceof t._CodeOrName?this.rhs.names:{}}}class d extends a{constructor(o,f,P){super(),this.lhs=o,this.rhs=f,this.sideEffects=P}render({_n:o}){return`${this.lhs} = ${this.rhs};`+o}optimizeNames(o,f){if(!(this.lhs instanceof t.Name&&!o[this.lhs.str]&&!this.sideEffects))return this.rhs=C(this.rhs,o,f),this}get names(){const o=this.lhs instanceof t.Name?{}:{...this.lhs.names};return fe(o,this.rhs)}}class l extends d{constructor(o,f,P,j){super(o,P,j),this.op=f}render({_n:o}){return`${this.lhs} ${this.op}= ${this.rhs};`+o}}class u extends a{constructor(o){super(),this.label=o,this.names={}}render({_n:o}){return`${this.label}:`+o}}class c extends a{constructor(o){super(),this.label=o,this.names={}}render({_n:o}){return`break${this.label?` ${this.label}`:""};`+o}}class h extends a{constructor(o){super(),this.error=o}render({_n:o}){return`throw ${this.error};`+o}get names(){return this.error.names}}class S extends a{constructor(o){super(),this.code=o}render({_n:o}){return`${this.code};`+o}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(o,f){return this.code=C(this.code,o,f),this}get names(){return this.code instanceof t._CodeOrName?this.code.names:{}}}class y extends a{constructor(o=[]){super(),this.nodes=o}render(o){return this.nodes.reduce((f,P)=>f+P.render(o),"")}optimizeNodes(){const{nodes:o}=this;let f=o.length;for(;f--;){const P=o[f].optimizeNodes();Array.isArray(P)?o.splice(f,1,...P):P?o[f]=P:o.splice(f,1)}return o.length>0?this:void 0}optimizeNames(o,f){const{nodes:P}=this;let j=P.length;for(;j--;){const A=P[j];A.optimizeNames(o,f)||(k(o,A.names),P.splice(j,1))}return P.length>0?this:void 0}get names(){return this.nodes.reduce((o,f)=>Q(o,f.names),{})}}class v extends y{render(o){return"{"+o._n+super.render(o)+"}"+o._n}}class g extends y{}class $ extends v{}$.kind="else";class p extends v{constructor(o,f){super(f),this.condition=o}render(o){let f=`if(${this.condition})`+super.render(o);return this.else&&(f+="else "+this.else.render(o)),f}optimizeNodes(){super.optimizeNodes();const o=this.condition;if(o===!0)return this.nodes;let f=this.else;if(f){const P=f.optimizeNodes();f=this.else=Array.isArray(P)?new $(P):P}if(f)return o===!1?f instanceof p?f:f.nodes:this.nodes.length?this:new p(U(o),f instanceof p?[f]:f.nodes);if(!(o===!1||!this.nodes.length))return this}optimizeNames(o,f){var P;if(this.else=(P=this.else)===null||P===void 0?void 0:P.optimizeNames(o,f),!!(super.optimizeNames(o,f)||this.else))return this.condition=C(this.condition,o,f),this}get names(){const o=super.names;return fe(o,this.condition),this.else&&Q(o,this.else.names),o}}p.kind="if";class w extends v{}w.kind="for";class N extends w{constructor(o){super(),this.iteration=o}render(o){return`for(${this.iteration})`+super.render(o)}optimizeNames(o,f){if(super.optimizeNames(o,f))return this.iteration=C(this.iteration,o,f),this}get names(){return Q(super.names,this.iteration.names)}}class O extends w{constructor(o,f,P,j){super(),this.varKind=o,this.name=f,this.from=P,this.to=j}render(o){const f=o.es5?r.varKinds.var:this.varKind,{name:P,from:j,to:A}=this;return`for(${f} ${P}=${j}; ${P}<${A}; ${P}++)`+super.render(o)}get names(){const o=fe(super.names,this.from);return fe(o,this.to)}}class T extends w{constructor(o,f,P,j){super(),this.loop=o,this.varKind=f,this.name=P,this.iterable=j}render(o){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(o)}optimizeNames(o,f){if(super.optimizeNames(o,f))return this.iterable=C(this.iterable,o,f),this}get names(){return Q(super.names,this.iterable.names)}}class z extends v{constructor(o,f,P){super(),this.name=o,this.args=f,this.async=P}render(o){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(o)}}z.kind="func";class B extends y{render(o){return"return "+super.render(o)}}B.kind="return";class de extends v{render(o){let f="try"+super.render(o);return this.catch&&(f+=this.catch.render(o)),this.finally&&(f+=this.finally.render(o)),f}optimizeNodes(){var o,f;return super.optimizeNodes(),(o=this.catch)===null||o===void 0||o.optimizeNodes(),(f=this.finally)===null||f===void 0||f.optimizeNodes(),this}optimizeNames(o,f){var P,j;return super.optimizeNames(o,f),(P=this.catch)===null||P===void 0||P.optimizeNames(o,f),(j=this.finally)===null||j===void 0||j.optimizeNames(o,f),this}get names(){const o=super.names;return this.catch&&Q(o,this.catch.names),this.finally&&Q(o,this.finally.names),o}}class V extends v{constructor(o){super(),this.error=o}render(o){return`catch(${this.error})`+super.render(o)}}V.kind="catch";class H extends v{render(o){return"finally"+super.render(o)}}H.kind="finally";class ne{constructor(o,f={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...f,_n:f.lines?`
`:""},this._extScope=o,this._scope=new r.Scope({parent:o}),this._nodes=[new g]}toString(){return this._root.render(this.opts)}name(o){return this._scope.name(o)}scopeName(o){return this._extScope.name(o)}scopeValue(o,f){const P=this._extScope.value(o,f);return(this._values[P.prefix]||(this._values[P.prefix]=new Set)).add(P),P}getScopeValue(o,f){return this._extScope.getValue(o,f)}scopeRefs(o){return this._extScope.scopeRefs(o,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(o,f,P,j){const A=this._scope.toName(f);return P!==void 0&&j&&(this._constants[A.str]=P),this._leafNode(new i(o,A,P)),A}const(o,f,P){return this._def(r.varKinds.const,o,f,P)}let(o,f,P){return this._def(r.varKinds.let,o,f,P)}var(o,f,P){return this._def(r.varKinds.var,o,f,P)}assign(o,f,P){return this._leafNode(new d(o,f,P))}add(o,f){return this._leafNode(new l(o,e.operators.ADD,f))}code(o){return typeof o=="function"?o():o!==t.nil&&this._leafNode(new S(o)),this}object(...o){const f=["{"];for(const[P,j]of o)f.length>1&&f.push(","),f.push(P),(P!==j||this.opts.es5)&&(f.push(":"),(0,t.addCodeArg)(f,j));return f.push("}"),new t._Code(f)}if(o,f,P){if(this._blockNode(new p(o)),f&&P)this.code(f).else().code(P).endIf();else if(f)this.code(f).endIf();else if(P)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(o){return this._elseNode(new p(o))}else(){return this._elseNode(new $)}endIf(){return this._endBlockNode(p,$)}_for(o,f){return this._blockNode(o),f&&this.code(f).endFor(),this}for(o,f){return this._for(new N(o),f)}forRange(o,f,P,j,A=this.opts.es5?r.varKinds.var:r.varKinds.let){const q=this._scope.toName(o);return this._for(new O(A,q,f,P),()=>j(q))}forOf(o,f,P,j=r.varKinds.const){const A=this._scope.toName(o);if(this.opts.es5){const q=f instanceof t.Name?f:this.var("_arr",f);return this.forRange("_i",0,(0,t._)`${q}.length`,F=>{this.var(A,(0,t._)`${q}[${F}]`),P(A)})}return this._for(new T("of",j,A,f),()=>P(A))}forIn(o,f,P,j=this.opts.es5?r.varKinds.var:r.varKinds.const){if(this.opts.ownProperties)return this.forOf(o,(0,t._)`Object.keys(${f})`,P);const A=this._scope.toName(o);return this._for(new T("in",j,A,f),()=>P(A))}endFor(){return this._endBlockNode(w)}label(o){return this._leafNode(new u(o))}break(o){return this._leafNode(new c(o))}return(o){const f=new B;if(this._blockNode(f),this.code(o),f.nodes.length!==1)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(B)}try(o,f,P){if(!f&&!P)throw new Error('CodeGen: "try" without "catch" and "finally"');const j=new de;if(this._blockNode(j),this.code(o),f){const A=this.name("e");this._currNode=j.catch=new V(A),f(A)}return P&&(this._currNode=j.finally=new H,this.code(P)),this._endBlockNode(V,H)}throw(o){return this._leafNode(new h(o))}block(o,f){return this._blockStarts.push(this._nodes.length),o&&this.code(o).endBlock(f),this}endBlock(o){const f=this._blockStarts.pop();if(f===void 0)throw new Error("CodeGen: not in self-balancing block");const P=this._nodes.length-f;if(P<0||o!==void 0&&P!==o)throw new Error(`CodeGen: wrong number of nodes: ${P} vs ${o} expected`);return this._nodes.length=f,this}func(o,f=t.nil,P,j){return this._blockNode(new z(o,f,P)),j&&this.code(j).endFunc(),this}endFunc(){return this._endBlockNode(z)}optimize(o=1){for(;o-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(o){return this._currNode.nodes.push(o),this}_blockNode(o){this._currNode.nodes.push(o),this._nodes.push(o)}_endBlockNode(o,f){const P=this._currNode;if(P instanceof o||f&&P instanceof f)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${f?`${o.kind}/${f.kind}`:o.kind}"`)}_elseNode(o){const f=this._currNode;if(!(f instanceof p))throw new Error('CodeGen: "else" without "if"');return this._currNode=f.else=o,this}get _root(){return this._nodes[0]}get _currNode(){const o=this._nodes;return o[o.length-1]}set _currNode(o){const f=this._nodes;f[f.length-1]=o}}e.CodeGen=ne;function Q(_,o){for(const f in o)_[f]=(_[f]||0)+(o[f]||0);return _}function fe(_,o){return o instanceof t._CodeOrName?Q(_,o.names):_}function C(_,o,f){if(_ instanceof t.Name)return P(_);if(!j(_))return _;return new t._Code(_._items.reduce((A,q)=>(q instanceof t.Name&&(q=P(q)),q instanceof t._Code?A.push(...q._items):A.push(q),A),[]));function P(A){const q=f[A.str];return q===void 0||o[A.str]!==1?A:(delete o[A.str],q)}function j(A){return A instanceof t._Code&&A._items.some(q=>q instanceof t.Name&&o[q.str]===1&&f[q.str]!==void 0)}}function k(_,o){for(const f in o)_[f]=(_[f]||0)-(o[f]||0)}function U(_){return typeof _=="boolean"||typeof _=="number"||_===null?!_:(0,t._)`!${b(_)}`}e.not=U;const D=m(e.operators.AND);function R(..._){return _.reduce(D)}e.and=R;const I=m(e.operators.OR);function E(..._){return _.reduce(I)}e.or=E;function m(_){return(o,f)=>o===t.nil?f:f===t.nil?o:(0,t._)`${b(o)} ${_} ${b(f)}`}function b(_){return _ instanceof t.Name?_:(0,t._)`(${_})`}})(te);var M={};Object.defineProperty(M,"__esModule",{value:!0});M.checkStrictMode=M.getErrorPath=M.Type=M.useFunc=M.setEvaluated=M.evaluatedPropsToName=M.mergeEvaluated=M.eachItem=M.unescapeJsonPointer=M.escapeJsonPointer=M.escapeFragment=M.unescapeFragment=M.schemaRefOrVal=M.schemaHasRulesButRef=M.schemaHasRules=M.checkUnknownRules=M.alwaysValidSchema=M.toHash=void 0;const ce=te,Jd=Jr;function Xd(e){const t={};for(const r of e)t[r]=!0;return t}M.toHash=Xd;function Yd(e,t){return typeof t=="boolean"?t:Object.keys(t).length===0?!0:(Qc(e,t),!Zc(t,e.self.RULES.all))}M.alwaysValidSchema=Yd;function Qc(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema||typeof t=="boolean")return;const s=n.RULES.keywords;for(const a in t)s[a]||tl(e,`unknown keyword: "${a}"`)}M.checkUnknownRules=Qc;function Zc(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(t[r])return!0;return!1}M.schemaHasRules=Zc;function Qd(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(r!=="$ref"&&t.all[r])return!0;return!1}M.schemaHasRulesButRef=Qd;function Zd({topSchemaRef:e,schemaPath:t},r,n,s){if(!s){if(typeof r=="number"||typeof r=="boolean")return r;if(typeof r=="string")return(0,ce._)`${r}`}return(0,ce._)`${e}${t}${(0,ce.getProperty)(n)}`}M.schemaRefOrVal=Zd;function xd(e){return xc(decodeURIComponent(e))}M.unescapeFragment=xd;function ef(e){return encodeURIComponent(ha(e))}M.escapeFragment=ef;function ha(e){return typeof e=="number"?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}M.escapeJsonPointer=ha;function xc(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}M.unescapeJsonPointer=xc;function tf(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)}M.eachItem=tf;function ui({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(s,a,i,d)=>{const l=i===void 0?a:i instanceof ce.Name?(a instanceof ce.Name?e(s,a,i):t(s,a,i),i):a instanceof ce.Name?(t(s,i,a),a):r(a,i);return d===ce.Name&&!(l instanceof ce.Name)?n(s,l):l}}M.mergeEvaluated={props:ui({mergeNames:(e,t,r)=>e.if((0,ce._)`${r} !== true && ${t} !== undefined`,()=>{e.if((0,ce._)`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,(0,ce._)`${r} || {}`).code((0,ce._)`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if((0,ce._)`${r} !== true`,()=>{t===!0?e.assign(r,!0):(e.assign(r,(0,ce._)`${r} || {}`),pa(e,r,t))}),mergeValues:(e,t)=>e===!0?!0:{...e,...t},resultToName:el}),items:ui({mergeNames:(e,t,r)=>e.if((0,ce._)`${r} !== true && ${t} !== undefined`,()=>e.assign(r,(0,ce._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if((0,ce._)`${r} !== true`,()=>e.assign(r,t===!0?!0:(0,ce._)`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>e===!0?!0:Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})};function el(e,t){if(t===!0)return e.var("props",!0);const r=e.var("props",(0,ce._)`{}`);return t!==void 0&&pa(e,r,t),r}M.evaluatedPropsToName=el;function pa(e,t,r){Object.keys(r).forEach(n=>e.assign((0,ce._)`${t}${(0,ce.getProperty)(n)}`,!0))}M.setEvaluated=pa;const di={};function rf(e,t){return e.scopeValue("func",{ref:t,code:di[t.code]||(di[t.code]=new Jd._Code(t.code))})}M.useFunc=rf;var Ms;(function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"})(Ms||(M.Type=Ms={}));function nf(e,t,r){if(e instanceof ce.Name){const n=t===Ms.Num;return r?n?(0,ce._)`"[" + ${e} + "]"`:(0,ce._)`"['" + ${e} + "']"`:n?(0,ce._)`"/" + ${e}`:(0,ce._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,ce.getProperty)(e).toString():"/"+ha(e)}M.getErrorPath=nf;function tl(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,r===!0)throw new Error(t);e.self.logger.warn(t)}}M.checkStrictMode=tl;var ct={};Object.defineProperty(ct,"__esModule",{value:!0});const Ne=te,sf={data:new Ne.Name("data"),valCxt:new Ne.Name("valCxt"),instancePath:new Ne.Name("instancePath"),parentData:new Ne.Name("parentData"),parentDataProperty:new Ne.Name("parentDataProperty"),rootData:new Ne.Name("rootData"),dynamicAnchors:new Ne.Name("dynamicAnchors"),vErrors:new Ne.Name("vErrors"),errors:new Ne.Name("errors"),this:new Ne.Name("this"),self:new Ne.Name("self"),scope:new Ne.Name("scope"),json:new Ne.Name("json"),jsonPos:new Ne.Name("jsonPos"),jsonLen:new Ne.Name("jsonLen"),jsonPart:new Ne.Name("jsonPart")};ct.default=sf;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.extendErrors=e.resetErrorsCount=e.reportExtraError=e.reportError=e.keyword$DataError=e.keywordError=void 0;const t=te,r=M,n=ct;e.keywordError={message:({keyword:$})=>(0,t.str)`must pass "${$}" keyword validation`},e.keyword$DataError={message:({keyword:$,schemaType:p})=>p?(0,t.str)`"${$}" keyword must be ${p} ($data)`:(0,t.str)`"${$}" keyword is invalid ($data)`};function s($,p=e.keywordError,w,N){const{it:O}=$,{gen:T,compositeRule:z,allErrors:B}=O,de=h($,p,w);N??(z||B)?l(T,de):u(O,(0,t._)`[${de}]`)}e.reportError=s;function a($,p=e.keywordError,w){const{it:N}=$,{gen:O,compositeRule:T,allErrors:z}=N,B=h($,p,w);l(O,B),T||z||u(N,n.default.vErrors)}e.reportExtraError=a;function i($,p){$.assign(n.default.errors,p),$.if((0,t._)`${n.default.vErrors} !== null`,()=>$.if(p,()=>$.assign((0,t._)`${n.default.vErrors}.length`,p),()=>$.assign(n.default.vErrors,null)))}e.resetErrorsCount=i;function d({gen:$,keyword:p,schemaValue:w,data:N,errsCount:O,it:T}){if(O===void 0)throw new Error("ajv implementation error");const z=$.name("err");$.forRange("i",O,n.default.errors,B=>{$.const(z,(0,t._)`${n.default.vErrors}[${B}]`),$.if((0,t._)`${z}.instancePath === undefined`,()=>$.assign((0,t._)`${z}.instancePath`,(0,t.strConcat)(n.default.instancePath,T.errorPath))),$.assign((0,t._)`${z}.schemaPath`,(0,t.str)`${T.errSchemaPath}/${p}`),T.opts.verbose&&($.assign((0,t._)`${z}.schema`,w),$.assign((0,t._)`${z}.data`,N))})}e.extendErrors=d;function l($,p){const w=$.const("err",p);$.if((0,t._)`${n.default.vErrors} === null`,()=>$.assign(n.default.vErrors,(0,t._)`[${w}]`),(0,t._)`${n.default.vErrors}.push(${w})`),$.code((0,t._)`${n.default.errors}++`)}function u($,p){const{gen:w,validateName:N,schemaEnv:O}=$;O.$async?w.throw((0,t._)`new ${$.ValidationError}(${p})`):(w.assign((0,t._)`${N}.errors`,p),w.return(!1))}const c={keyword:new t.Name("keyword"),schemaPath:new t.Name("schemaPath"),params:new t.Name("params"),propertyName:new t.Name("propertyName"),message:new t.Name("message"),schema:new t.Name("schema"),parentSchema:new t.Name("parentSchema")};function h($,p,w){const{createErrors:N}=$.it;return N===!1?(0,t._)`{}`:S($,p,w)}function S($,p,w={}){const{gen:N,it:O}=$,T=[y(O,w),v($,w)];return g($,p,T),N.object(...T)}function y({errorPath:$},{instancePath:p}){const w=p?(0,t.str)`${$}${(0,r.getErrorPath)(p,r.Type.Str)}`:$;return[n.default.instancePath,(0,t.strConcat)(n.default.instancePath,w)]}function v({keyword:$,it:{errSchemaPath:p}},{schemaPath:w,parentSchema:N}){let O=N?p:(0,t.str)`${p}/${$}`;return w&&(O=(0,t.str)`${O}${(0,r.getErrorPath)(w,r.Type.Str)}`),[c.schemaPath,O]}function g($,{params:p,message:w},N){const{keyword:O,data:T,schemaValue:z,it:B}=$,{opts:de,propertyName:V,topSchemaRef:H,schemaPath:ne}=B;N.push([c.keyword,O],[c.params,typeof p=="function"?p($):p||(0,t._)`{}`]),de.messages&&N.push([c.message,typeof w=="function"?w($):w]),de.verbose&&N.push([c.schema,z],[c.parentSchema,(0,t._)`${H}${ne}`],[n.default.data,T]),V&&N.push([c.propertyName,V])}})(Yr);Object.defineProperty($r,"__esModule",{value:!0});$r.boolOrEmptySchema=$r.topBoolOrEmptySchema=void 0;const af=Yr,of=te,cf=ct,lf={message:"boolean schema is false"};function uf(e){const{gen:t,schema:r,validateName:n}=e;r===!1?rl(e,!1):typeof r=="object"&&r.$async===!0?t.return(cf.default.data):(t.assign((0,of._)`${n}.errors`,null),t.return(!0))}$r.topBoolOrEmptySchema=uf;function df(e,t){const{gen:r,schema:n}=e;n===!1?(r.var(t,!1),rl(e)):r.var(t,!0)}$r.boolOrEmptySchema=df;function rl(e,t){const{gen:r,data:n}=e,s={gen:r,keyword:"false schema",data:n,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,af.reportError)(s,lf,void 0,t)}var _e={},Zt={};Object.defineProperty(Zt,"__esModule",{value:!0});Zt.getRules=Zt.isJSONType=void 0;const ff=["string","number","integer","boolean","null","object","array"],hf=new Set(ff);function pf(e){return typeof e=="string"&&hf.has(e)}Zt.isJSONType=pf;function mf(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}Zt.getRules=mf;var ut={};Object.defineProperty(ut,"__esModule",{value:!0});ut.shouldUseRule=ut.shouldUseGroup=ut.schemaHasRulesForType=void 0;function $f({schema:e,self:t},r){const n=t.RULES.types[r];return n&&n!==!0&&nl(e,n)}ut.schemaHasRulesForType=$f;function nl(e,t){return t.rules.some(r=>sl(e,r))}ut.shouldUseGroup=nl;function sl(e,t){var r;return e[t.keyword]!==void 0||((r=t.definition.implements)===null||r===void 0?void 0:r.some(n=>e[n]!==void 0))}ut.shouldUseRule=sl;Object.defineProperty(_e,"__esModule",{value:!0});_e.reportTypeError=_e.checkDataTypes=_e.checkDataType=_e.coerceAndCheckDataType=_e.getJSONTypes=_e.getSchemaTypes=_e.DataType=void 0;const yf=Zt,_f=ut,gf=Yr,X=te,al=M;var ur;(function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"})(ur||(_e.DataType=ur={}));function vf(e){const t=ol(e.type);if(t.includes("null")){if(e.nullable===!1)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&e.nullable!==void 0)throw new Error('"nullable" cannot be used without "type"');e.nullable===!0&&t.push("null")}return t}_e.getSchemaTypes=vf;function ol(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(yf.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}_e.getJSONTypes=ol;function wf(e,t){const{gen:r,data:n,opts:s}=e,a=Ef(t,s.coerceTypes),i=t.length>0&&!(a.length===0&&t.length===1&&(0,_f.schemaHasRulesForType)(e,t[0]));if(i){const d=ma(t,n,s.strictNumbers,ur.Wrong);r.if(d,()=>{a.length?Sf(e,t,a):$a(e)})}return i}_e.coerceAndCheckDataType=wf;const il=new Set(["string","number","integer","boolean","null"]);function Ef(e,t){return t?e.filter(r=>il.has(r)||t==="array"&&r==="array"):[]}function Sf(e,t,r){const{gen:n,data:s,opts:a}=e,i=n.let("dataType",(0,X._)`typeof ${s}`),d=n.let("coerced",(0,X._)`undefined`);a.coerceTypes==="array"&&n.if((0,X._)`${i} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,()=>n.assign(s,(0,X._)`${s}[0]`).assign(i,(0,X._)`typeof ${s}`).if(ma(t,s,a.strictNumbers),()=>n.assign(d,s))),n.if((0,X._)`${d} !== undefined`);for(const u of r)(il.has(u)||u==="array"&&a.coerceTypes==="array")&&l(u);n.else(),$a(e),n.endIf(),n.if((0,X._)`${d} !== undefined`,()=>{n.assign(s,d),bf(e,d)});function l(u){switch(u){case"string":n.elseIf((0,X._)`${i} == "number" || ${i} == "boolean"`).assign(d,(0,X._)`"" + ${s}`).elseIf((0,X._)`${s} === null`).assign(d,(0,X._)`""`);return;case"number":n.elseIf((0,X._)`${i} == "boolean" || ${s} === null
              || (${i} == "string" && ${s} && ${s} == +${s})`).assign(d,(0,X._)`+${s}`);return;case"integer":n.elseIf((0,X._)`${i} === "boolean" || ${s} === null
              || (${i} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(d,(0,X._)`+${s}`);return;case"boolean":n.elseIf((0,X._)`${s} === "false" || ${s} === 0 || ${s} === null`).assign(d,!1).elseIf((0,X._)`${s} === "true" || ${s} === 1`).assign(d,!0);return;case"null":n.elseIf((0,X._)`${s} === "" || ${s} === 0 || ${s} === false`),n.assign(d,null);return;case"array":n.elseIf((0,X._)`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${s} === null`).assign(d,(0,X._)`[${s}]`)}}}function bf({gen:e,parentData:t,parentDataProperty:r},n){e.if((0,X._)`${t} !== undefined`,()=>e.assign((0,X._)`${t}[${r}]`,n))}function Ls(e,t,r,n=ur.Correct){const s=n===ur.Correct?X.operators.EQ:X.operators.NEQ;let a;switch(e){case"null":return(0,X._)`${t} ${s} null`;case"array":a=(0,X._)`Array.isArray(${t})`;break;case"object":a=(0,X._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":a=i((0,X._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":a=i();break;default:return(0,X._)`typeof ${t} ${s} ${e}`}return n===ur.Correct?a:(0,X.not)(a);function i(d=X.nil){return(0,X.and)((0,X._)`typeof ${t} == "number"`,d,r?(0,X._)`isFinite(${t})`:X.nil)}}_e.checkDataType=Ls;function ma(e,t,r,n){if(e.length===1)return Ls(e[0],t,r,n);let s;const a=(0,al.toHash)(e);if(a.array&&a.object){const i=(0,X._)`typeof ${t} != "object"`;s=a.null?i:(0,X._)`!${t} || ${i}`,delete a.null,delete a.array,delete a.object}else s=X.nil;a.number&&delete a.integer;for(const i in a)s=(0,X.and)(s,Ls(i,t,r,n));return s}_e.checkDataTypes=ma;const Pf={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>typeof e=="string"?(0,X._)`{type: ${e}}`:(0,X._)`{type: ${t}}`};function $a(e){const t=Nf(e);(0,gf.reportError)(t,Pf)}_e.reportTypeError=$a;function Nf(e){const{gen:t,data:r,schema:n}=e,s=(0,al.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:s,schemaValue:s,parentSchema:n,params:{},it:e}}var Jn={};Object.defineProperty(Jn,"__esModule",{value:!0});Jn.assignDefaults=void 0;const rr=te,Of=M;function Rf(e,t){const{properties:r,items:n}=e.schema;if(t==="object"&&r)for(const s in r)fi(e,s,r[s].default);else t==="array"&&Array.isArray(n)&&n.forEach((s,a)=>fi(e,a,s.default))}Jn.assignDefaults=Rf;function fi(e,t,r){const{gen:n,compositeRule:s,data:a,opts:i}=e;if(r===void 0)return;const d=(0,rr._)`${a}${(0,rr.getProperty)(t)}`;if(s){(0,Of.checkStrictMode)(e,`default is ignored for: ${d}`);return}let l=(0,rr._)`${d} === undefined`;i.useDefaults==="empty"&&(l=(0,rr._)`${l} || ${d} === null || ${d} === ""`),n.if(l,(0,rr._)`${d} = ${(0,rr.stringify)(r)}`)}var at={},x={};Object.defineProperty(x,"__esModule",{value:!0});x.validateUnion=x.validateArray=x.usePattern=x.callValidateCode=x.schemaProperties=x.allSchemaProperties=x.noPropertyInData=x.propertyInData=x.isOwnProperty=x.hasPropFunc=x.reportMissingProp=x.checkMissingProp=x.checkReportMissingProp=void 0;const he=te,ya=M,mt=ct,If=M;function Tf(e,t){const{gen:r,data:n,it:s}=e;r.if(ga(r,n,t,s.opts.ownProperties),()=>{e.setParams({missingProperty:(0,he._)`${t}`},!0),e.error()})}x.checkReportMissingProp=Tf;function jf({gen:e,data:t,it:{opts:r}},n,s){return(0,he.or)(...n.map(a=>(0,he.and)(ga(e,t,a,r.ownProperties),(0,he._)`${s} = ${a}`)))}x.checkMissingProp=jf;function Af(e,t){e.setParams({missingProperty:t},!0),e.error()}x.reportMissingProp=Af;function cl(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,he._)`Object.prototype.hasOwnProperty`})}x.hasPropFunc=cl;function _a(e,t,r){return(0,he._)`${cl(e)}.call(${t}, ${r})`}x.isOwnProperty=_a;function kf(e,t,r,n){const s=(0,he._)`${t}${(0,he.getProperty)(r)} !== undefined`;return n?(0,he._)`${s} && ${_a(e,t,r)}`:s}x.propertyInData=kf;function ga(e,t,r,n){const s=(0,he._)`${t}${(0,he.getProperty)(r)} === undefined`;return n?(0,he.or)(s,(0,he.not)(_a(e,t,r))):s}x.noPropertyInData=ga;function ll(e){return e?Object.keys(e).filter(t=>t!=="__proto__"):[]}x.allSchemaProperties=ll;function Cf(e,t){return ll(t).filter(r=>!(0,ya.alwaysValidSchema)(e,t[r]))}x.schemaProperties=Cf;function Df({schemaCode:e,data:t,it:{gen:r,topSchemaRef:n,schemaPath:s,errorPath:a},it:i},d,l,u){const c=u?(0,he._)`${e}, ${t}, ${n}${s}`:t,h=[[mt.default.instancePath,(0,he.strConcat)(mt.default.instancePath,a)],[mt.default.parentData,i.parentData],[mt.default.parentDataProperty,i.parentDataProperty],[mt.default.rootData,mt.default.rootData]];i.opts.dynamicRef&&h.push([mt.default.dynamicAnchors,mt.default.dynamicAnchors]);const S=(0,he._)`${c}, ${r.object(...h)}`;return l!==he.nil?(0,he._)`${d}.call(${l}, ${S})`:(0,he._)`${d}(${S})`}x.callValidateCode=Df;const Mf=(0,he._)`new RegExp`;function Lf({gen:e,it:{opts:t}},r){const n=t.unicodeRegExp?"u":"",{regExp:s}=t.code,a=s(r,n);return e.scopeValue("pattern",{key:a.toString(),ref:a,code:(0,he._)`${s.code==="new RegExp"?Mf:(0,If.useFunc)(e,s)}(${r}, ${n})`})}x.usePattern=Lf;function Ff(e){const{gen:t,data:r,keyword:n,it:s}=e,a=t.name("valid");if(s.allErrors){const d=t.let("valid",!0);return i(()=>t.assign(d,!1)),d}return t.var(a,!0),i(()=>t.break()),a;function i(d){const l=t.const("len",(0,he._)`${r}.length`);t.forRange("i",0,l,u=>{e.subschema({keyword:n,dataProp:u,dataPropType:ya.Type.Num},a),t.if((0,he.not)(a),d)})}}x.validateArray=Ff;function Vf(e){const{gen:t,schema:r,keyword:n,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(l=>(0,ya.alwaysValidSchema)(s,l))&&!s.opts.unevaluated)return;const i=t.let("valid",!1),d=t.name("_valid");t.block(()=>r.forEach((l,u)=>{const c=e.subschema({keyword:n,schemaProp:u,compositeRule:!0},d);t.assign(i,(0,he._)`${i} || ${d}`),e.mergeValidEvaluated(c,d)||t.if((0,he.not)(i))})),e.result(i,()=>e.reset(),()=>e.error(!0))}x.validateUnion=Vf;Object.defineProperty(at,"__esModule",{value:!0});at.validateKeywordUsage=at.validSchemaType=at.funcKeywordCode=at.macroKeywordCode=void 0;const Te=te,Ht=ct,Uf=x,zf=Yr;function qf(e,t){const{gen:r,keyword:n,schema:s,parentSchema:a,it:i}=e,d=t.macro.call(i.self,s,a,i),l=ul(r,n,d);i.opts.validateSchema!==!1&&i.self.validateSchema(d,!0);const u=r.name("valid");e.subschema({schema:d,schemaPath:Te.nil,errSchemaPath:`${i.errSchemaPath}/${n}`,topSchemaRef:l,compositeRule:!0},u),e.pass(u,()=>e.error(!0))}at.macroKeywordCode=qf;function Kf(e,t){var r;const{gen:n,keyword:s,schema:a,parentSchema:i,$data:d,it:l}=e;Hf(l,t);const u=!d&&t.compile?t.compile.call(l.self,a,i,l):t.validate,c=ul(n,s,u),h=n.let("valid");e.block$data(h,S),e.ok((r=t.valid)!==null&&r!==void 0?r:h);function S(){if(t.errors===!1)g(),t.modifying&&hi(e),$(()=>e.error());else{const p=t.async?y():v();t.modifying&&hi(e),$(()=>Gf(e,p))}}function y(){const p=n.let("ruleErrs",null);return n.try(()=>g((0,Te._)`await `),w=>n.assign(h,!1).if((0,Te._)`${w} instanceof ${l.ValidationError}`,()=>n.assign(p,(0,Te._)`${w}.errors`),()=>n.throw(w))),p}function v(){const p=(0,Te._)`${c}.errors`;return n.assign(p,null),g(Te.nil),p}function g(p=t.async?(0,Te._)`await `:Te.nil){const w=l.opts.passContext?Ht.default.this:Ht.default.self,N=!("compile"in t&&!d||t.schema===!1);n.assign(h,(0,Te._)`${p}${(0,Uf.callValidateCode)(e,c,w,N)}`,t.modifying)}function $(p){var w;n.if((0,Te.not)((w=t.valid)!==null&&w!==void 0?w:h),p)}}at.funcKeywordCode=Kf;function hi(e){const{gen:t,data:r,it:n}=e;t.if(n.parentData,()=>t.assign(r,(0,Te._)`${n.parentData}[${n.parentDataProperty}]`))}function Gf(e,t){const{gen:r}=e;r.if((0,Te._)`Array.isArray(${t})`,()=>{r.assign(Ht.default.vErrors,(0,Te._)`${Ht.default.vErrors} === null ? ${t} : ${Ht.default.vErrors}.concat(${t})`).assign(Ht.default.errors,(0,Te._)`${Ht.default.vErrors}.length`),(0,zf.extendErrors)(e)},()=>e.error())}function Hf({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}function ul(e,t,r){if(r===void 0)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword",typeof r=="function"?{ref:r}:{ref:r,code:(0,Te.stringify)(r)})}function Bf(e,t,r=!1){return!t.length||t.some(n=>n==="array"?Array.isArray(e):n==="object"?e&&typeof e=="object"&&!Array.isArray(e):typeof e==n||r&&typeof e>"u")}at.validSchemaType=Bf;function Wf({schema:e,opts:t,self:r,errSchemaPath:n},s,a){if(Array.isArray(s.keyword)?!s.keyword.includes(a):s.keyword!==a)throw new Error("ajv implementation error");const i=s.dependencies;if(i!=null&&i.some(d=>!Object.prototype.hasOwnProperty.call(e,d)))throw new Error(`parent schema must have dependencies of ${a}: ${i.join(",")}`);if(s.validateSchema&&!s.validateSchema(e[a])){const l=`keyword "${a}" value is invalid at path "${n}": `+r.errorsText(s.validateSchema.errors);if(t.validateSchema==="log")r.logger.error(l);else throw new Error(l)}}at.validateKeywordUsage=Wf;var bt={};Object.defineProperty(bt,"__esModule",{value:!0});bt.extendSubschemaMode=bt.extendSubschemaData=bt.getSubschema=void 0;const rt=te,dl=M;function Jf(e,{keyword:t,schemaProp:r,schema:n,schemaPath:s,errSchemaPath:a,topSchemaRef:i}){if(t!==void 0&&n!==void 0)throw new Error('both "keyword" and "schema" passed, only one allowed');if(t!==void 0){const d=e.schema[t];return r===void 0?{schema:d,schemaPath:(0,rt._)`${e.schemaPath}${(0,rt.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:d[r],schemaPath:(0,rt._)`${e.schemaPath}${(0,rt.getProperty)(t)}${(0,rt.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,dl.escapeFragment)(r)}`}}if(n!==void 0){if(s===void 0||a===void 0||i===void 0)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:n,schemaPath:s,topSchemaRef:i,errSchemaPath:a}}throw new Error('either "keyword" or "schema" must be passed')}bt.getSubschema=Jf;function Xf(e,t,{dataProp:r,dataPropType:n,data:s,dataTypes:a,propertyName:i}){if(s!==void 0&&r!==void 0)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:d}=t;if(r!==void 0){const{errorPath:u,dataPathArr:c,opts:h}=t,S=d.let("data",(0,rt._)`${t.data}${(0,rt.getProperty)(r)}`,!0);l(S),e.errorPath=(0,rt.str)`${u}${(0,dl.getErrorPath)(r,n,h.jsPropertySyntax)}`,e.parentDataProperty=(0,rt._)`${r}`,e.dataPathArr=[...c,e.parentDataProperty]}if(s!==void 0){const u=s instanceof rt.Name?s:d.let("data",s,!0);l(u),i!==void 0&&(e.propertyName=i)}a&&(e.dataTypes=a);function l(u){e.data=u,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,u]}}bt.extendSubschemaData=Xf;function Yf(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:s,allErrors:a}){n!==void 0&&(e.compositeRule=n),s!==void 0&&(e.createErrors=s),a!==void 0&&(e.allErrors=a),e.jtdDiscriminator=t,e.jtdMetadata=r}bt.extendSubschemaMode=Yf;var be={},Xn=function e(t,r){if(t===r)return!0;if(t&&r&&typeof t=="object"&&typeof r=="object"){if(t.constructor!==r.constructor)return!1;var n,s,a;if(Array.isArray(t)){if(n=t.length,n!=r.length)return!1;for(s=n;s--!==0;)if(!e(t[s],r[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if(a=Object.keys(t),n=a.length,n!==Object.keys(r).length)return!1;for(s=n;s--!==0;)if(!Object.prototype.hasOwnProperty.call(r,a[s]))return!1;for(s=n;s--!==0;){var i=a[s];if(!e(t[i],r[i]))return!1}return!0}return t!==t&&r!==r},fl={exports:{}},Et=fl.exports=function(e,t,r){typeof t=="function"&&(r=t,t={}),r=t.cb||r;var n=typeof r=="function"?r:r.pre||function(){},s=r.post||function(){};Nn(t,n,s,e,"",e)};Et.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0};Et.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0};Et.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0};Et.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function Nn(e,t,r,n,s,a,i,d,l,u){if(n&&typeof n=="object"&&!Array.isArray(n)){t(n,s,a,i,d,l,u);for(var c in n){var h=n[c];if(Array.isArray(h)){if(c in Et.arrayKeywords)for(var S=0;S<h.length;S++)Nn(e,t,r,h[S],s+"/"+c+"/"+S,a,s,c,n,S)}else if(c in Et.propsKeywords){if(h&&typeof h=="object")for(var y in h)Nn(e,t,r,h[y],s+"/"+c+"/"+Qf(y),a,s,c,n,y)}else(c in Et.keywords||e.allKeys&&!(c in Et.skipKeywords))&&Nn(e,t,r,h,s+"/"+c,a,s,c,n)}r(n,s,a,i,d,l,u)}}function Qf(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}var Zf=fl.exports;Object.defineProperty(be,"__esModule",{value:!0});be.getSchemaRefs=be.resolveUrl=be.normalizeId=be._getFullPath=be.getFullPath=be.inlineRef=void 0;const xf=M,eh=Xn,th=Zf,rh=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);function nh(e,t=!0){return typeof e=="boolean"?!0:t===!0?!Fs(e):t?hl(e)<=t:!1}be.inlineRef=nh;const sh=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function Fs(e){for(const t in e){if(sh.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(Fs)||typeof r=="object"&&Fs(r))return!0}return!1}function hl(e){let t=0;for(const r in e){if(r==="$ref")return 1/0;if(t++,!rh.has(r)&&(typeof e[r]=="object"&&(0,xf.eachItem)(e[r],n=>t+=hl(n)),t===1/0))return 1/0}return t}function pl(e,t="",r){r!==!1&&(t=dr(t));const n=e.parse(t);return ml(e,n)}be.getFullPath=pl;function ml(e,t){return e.serialize(t).split("#")[0]+"#"}be._getFullPath=ml;const ah=/#\/?$/;function dr(e){return e?e.replace(ah,""):""}be.normalizeId=dr;function oh(e,t,r){return r=dr(r),e.resolve(t,r)}be.resolveUrl=oh;const ih=/^[a-z_][-a-z0-9._]*$/i;function ch(e,t){if(typeof e=="boolean")return{};const{schemaId:r,uriResolver:n}=this.opts,s=dr(e[r]||t),a={"":s},i=pl(n,s,!1),d={},l=new Set;return th(e,{allKeys:!0},(h,S,y,v)=>{if(v===void 0)return;const g=i+S;let $=a[v];typeof h[r]=="string"&&($=p.call(this,h[r])),w.call(this,h.$anchor),w.call(this,h.$dynamicAnchor),a[S]=$;function p(N){const O=this.opts.uriResolver.resolve;if(N=dr($?O($,N):N),l.has(N))throw c(N);l.add(N);let T=this.refs[N];return typeof T=="string"&&(T=this.refs[T]),typeof T=="object"?u(h,T.schema,N):N!==dr(g)&&(N[0]==="#"?(u(h,d[N],N),d[N]=h):this.refs[N]=g),N}function w(N){if(typeof N=="string"){if(!ih.test(N))throw new Error(`invalid anchor "${N}"`);p.call(this,`#${N}`)}}}),d;function u(h,S,y){if(S!==void 0&&!eh(h,S))throw c(y)}function c(h){return new Error(`reference "${h}" resolves to more than one schema`)}}be.getSchemaRefs=ch;Object.defineProperty(Qe,"__esModule",{value:!0});Qe.getData=Qe.KeywordCxt=Qe.validateFunctionCode=void 0;const $l=$r,pi=_e,va=ut,Mn=_e,lh=Jn,Fr=at,ms=bt,K=te,W=ct,uh=be,dt=M,Tr=Yr;function dh(e){if(gl(e)&&(vl(e),_l(e))){ph(e);return}yl(e,()=>(0,$l.topBoolOrEmptySchema)(e))}Qe.validateFunctionCode=dh;function yl({gen:e,validateName:t,schema:r,schemaEnv:n,opts:s},a){s.code.es5?e.func(t,(0,K._)`${W.default.data}, ${W.default.valCxt}`,n.$async,()=>{e.code((0,K._)`"use strict"; ${mi(r,s)}`),hh(e,s),e.code(a)}):e.func(t,(0,K._)`${W.default.data}, ${fh(s)}`,n.$async,()=>e.code(mi(r,s)).code(a))}function fh(e){return(0,K._)`{${W.default.instancePath}="", ${W.default.parentData}, ${W.default.parentDataProperty}, ${W.default.rootData}=${W.default.data}${e.dynamicRef?(0,K._)`, ${W.default.dynamicAnchors}={}`:K.nil}}={}`}function hh(e,t){e.if(W.default.valCxt,()=>{e.var(W.default.instancePath,(0,K._)`${W.default.valCxt}.${W.default.instancePath}`),e.var(W.default.parentData,(0,K._)`${W.default.valCxt}.${W.default.parentData}`),e.var(W.default.parentDataProperty,(0,K._)`${W.default.valCxt}.${W.default.parentDataProperty}`),e.var(W.default.rootData,(0,K._)`${W.default.valCxt}.${W.default.rootData}`),t.dynamicRef&&e.var(W.default.dynamicAnchors,(0,K._)`${W.default.valCxt}.${W.default.dynamicAnchors}`)},()=>{e.var(W.default.instancePath,(0,K._)`""`),e.var(W.default.parentData,(0,K._)`undefined`),e.var(W.default.parentDataProperty,(0,K._)`undefined`),e.var(W.default.rootData,W.default.data),t.dynamicRef&&e.var(W.default.dynamicAnchors,(0,K._)`{}`)})}function ph(e){const{schema:t,opts:r,gen:n}=e;yl(e,()=>{r.$comment&&t.$comment&&El(e),gh(e),n.let(W.default.vErrors,null),n.let(W.default.errors,0),r.unevaluated&&mh(e),wl(e),Eh(e)})}function mh(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,K._)`${r}.evaluated`),t.if((0,K._)`${e.evaluated}.dynamicProps`,()=>t.assign((0,K._)`${e.evaluated}.props`,(0,K._)`undefined`)),t.if((0,K._)`${e.evaluated}.dynamicItems`,()=>t.assign((0,K._)`${e.evaluated}.items`,(0,K._)`undefined`))}function mi(e,t){const r=typeof e=="object"&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,K._)`/*# sourceURL=${r} */`:K.nil}function $h(e,t){if(gl(e)&&(vl(e),_l(e))){yh(e,t);return}(0,$l.boolOrEmptySchema)(e,t)}function _l({schema:e,self:t}){if(typeof e=="boolean")return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function gl(e){return typeof e.schema!="boolean"}function yh(e,t){const{schema:r,gen:n,opts:s}=e;s.$comment&&r.$comment&&El(e),vh(e),wh(e);const a=n.const("_errs",W.default.errors);wl(e,a),n.var(t,(0,K._)`${a} === ${W.default.errors}`)}function vl(e){(0,dt.checkUnknownRules)(e),_h(e)}function wl(e,t){if(e.opts.jtd)return $i(e,[],!1,t);const r=(0,pi.getSchemaTypes)(e.schema),n=(0,pi.coerceAndCheckDataType)(e,r);$i(e,r,!n,t)}function _h(e){const{schema:t,errSchemaPath:r,opts:n,self:s}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,dt.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}function gh(e){const{schema:t,opts:r}=e;t.default!==void 0&&r.useDefaults&&r.strictSchema&&(0,dt.checkStrictMode)(e,"default is ignored in the schema root")}function vh(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,uh.resolveUrl)(e.opts.uriResolver,e.baseId,t))}function wh(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}function El({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:s}){const a=r.$comment;if(s.$comment===!0)e.code((0,K._)`${W.default.self}.logger.log(${a})`);else if(typeof s.$comment=="function"){const i=(0,K.str)`${n}/$comment`,d=e.scopeValue("root",{ref:t.root});e.code((0,K._)`${W.default.self}.opts.$comment(${a}, ${i}, ${d}.schema)`)}}function Eh(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:s,opts:a}=e;r.$async?t.if((0,K._)`${W.default.errors} === 0`,()=>t.return(W.default.data),()=>t.throw((0,K._)`new ${s}(${W.default.vErrors})`)):(t.assign((0,K._)`${n}.errors`,W.default.vErrors),a.unevaluated&&Sh(e),t.return((0,K._)`${W.default.errors} === 0`))}function Sh({gen:e,evaluated:t,props:r,items:n}){r instanceof K.Name&&e.assign((0,K._)`${t}.props`,r),n instanceof K.Name&&e.assign((0,K._)`${t}.items`,n)}function $i(e,t,r,n){const{gen:s,schema:a,data:i,allErrors:d,opts:l,self:u}=e,{RULES:c}=u;if(a.$ref&&(l.ignoreKeywordsWithRef||!(0,dt.schemaHasRulesButRef)(a,c))){s.block(()=>Pl(e,"$ref",c.all.$ref.definition));return}l.jtd||bh(e,t),s.block(()=>{for(const S of c.rules)h(S);h(c.post)});function h(S){(0,va.shouldUseGroup)(a,S)&&(S.type?(s.if((0,Mn.checkDataType)(S.type,i,l.strictNumbers)),yi(e,S),t.length===1&&t[0]===S.type&&r&&(s.else(),(0,Mn.reportTypeError)(e)),s.endIf()):yi(e,S),d||s.if((0,K._)`${W.default.errors} === ${n||0}`))}}function yi(e,t){const{gen:r,schema:n,opts:{useDefaults:s}}=e;s&&(0,lh.assignDefaults)(e,t.type),r.block(()=>{for(const a of t.rules)(0,va.shouldUseRule)(n,a)&&Pl(e,a.keyword,a.definition,t.type)})}function bh(e,t){e.schemaEnv.meta||!e.opts.strictTypes||(Ph(e,t),e.opts.allowUnionTypes||Nh(e,t),Oh(e,e.dataTypes))}function Ph(e,t){if(t.length){if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach(r=>{Sl(e.dataTypes,r)||wa(e,`type "${r}" not allowed by context "${e.dataTypes.join(",")}"`)}),Ih(e,t)}}function Nh(e,t){t.length>1&&!(t.length===2&&t.includes("null"))&&wa(e,"use allowUnionTypes to allow union type keyword")}function Oh(e,t){const r=e.self.RULES.all;for(const n in r){const s=r[n];if(typeof s=="object"&&(0,va.shouldUseRule)(e.schema,s)){const{type:a}=s.definition;a.length&&!a.some(i=>Rh(t,i))&&wa(e,`missing type "${a.join(",")}" for keyword "${n}"`)}}}function Rh(e,t){return e.includes(t)||t==="number"&&e.includes("integer")}function Sl(e,t){return e.includes(t)||t==="integer"&&e.includes("number")}function Ih(e,t){const r=[];for(const n of e.dataTypes)Sl(t,n)?r.push(n):t.includes("integer")&&n==="number"&&r.push("integer");e.dataTypes=r}function wa(e,t){const r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`,(0,dt.checkStrictMode)(e,t,e.opts.strictTypes)}let bl=class{constructor(t,r,n){if((0,Fr.validateKeywordUsage)(t,r,n),this.gen=t.gen,this.allErrors=t.allErrors,this.keyword=n,this.data=t.data,this.schema=t.schema[n],this.$data=r.$data&&t.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,dt.schemaRefOrVal)(t,this.schema,n,this.$data),this.schemaType=r.schemaType,this.parentSchema=t.schema,this.params={},this.it=t,this.def=r,this.$data)this.schemaCode=t.gen.const("vSchema",Nl(this.$data,t));else if(this.schemaCode=this.schemaValue,!(0,Fr.validSchemaType)(this.schema,r.schemaType,r.allowUndefined))throw new Error(`${n} value must be ${JSON.stringify(r.schemaType)}`);("code"in r?r.trackErrors:r.errors!==!1)&&(this.errsCount=t.gen.const("_errs",W.default.errors))}result(t,r,n){this.failResult((0,K.not)(t),r,n)}failResult(t,r,n){this.gen.if(t),n?n():this.error(),r?(this.gen.else(),r(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(t,r){this.failResult((0,K.not)(t),void 0,r)}fail(t){if(t===void 0){this.error(),this.allErrors||this.gen.if(!1);return}this.gen.if(t),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(t){if(!this.$data)return this.fail(t);const{schemaCode:r}=this;this.fail((0,K._)`${r} !== undefined && (${(0,K.or)(this.invalid$data(),t)})`)}error(t,r,n){if(r){this.setParams(r),this._error(t,n),this.setParams({});return}this._error(t,n)}_error(t,r){(t?Tr.reportExtraError:Tr.reportError)(this,this.def.error,r)}$dataError(){(0,Tr.reportError)(this,this.def.$dataError||Tr.keyword$DataError)}reset(){if(this.errsCount===void 0)throw new Error('add "trackErrors" to keyword definition');(0,Tr.resetErrorsCount)(this.gen,this.errsCount)}ok(t){this.allErrors||this.gen.if(t)}setParams(t,r){r?Object.assign(this.params,t):this.params=t}block$data(t,r,n=K.nil){this.gen.block(()=>{this.check$data(t,n),r()})}check$data(t=K.nil,r=K.nil){if(!this.$data)return;const{gen:n,schemaCode:s,schemaType:a,def:i}=this;n.if((0,K.or)((0,K._)`${s} === undefined`,r)),t!==K.nil&&n.assign(t,!0),(a.length||i.validateSchema)&&(n.elseIf(this.invalid$data()),this.$dataError(),t!==K.nil&&n.assign(t,!1)),n.else()}invalid$data(){const{gen:t,schemaCode:r,schemaType:n,def:s,it:a}=this;return(0,K.or)(i(),d());function i(){if(n.length){if(!(r instanceof K.Name))throw new Error("ajv implementation error");const l=Array.isArray(n)?n:[n];return(0,K._)`${(0,Mn.checkDataTypes)(l,r,a.opts.strictNumbers,Mn.DataType.Wrong)}`}return K.nil}function d(){if(s.validateSchema){const l=t.scopeValue("validate$data",{ref:s.validateSchema});return(0,K._)`!${l}(${r})`}return K.nil}}subschema(t,r){const n=(0,ms.getSubschema)(this.it,t);(0,ms.extendSubschemaData)(n,this.it,t),(0,ms.extendSubschemaMode)(n,t);const s={...this.it,...n,items:void 0,props:void 0};return $h(s,r),s}mergeEvaluated(t,r){const{it:n,gen:s}=this;n.opts.unevaluated&&(n.props!==!0&&t.props!==void 0&&(n.props=dt.mergeEvaluated.props(s,t.props,n.props,r)),n.items!==!0&&t.items!==void 0&&(n.items=dt.mergeEvaluated.items(s,t.items,n.items,r)))}mergeValidEvaluated(t,r){const{it:n,gen:s}=this;if(n.opts.unevaluated&&(n.props!==!0||n.items!==!0))return s.if(r,()=>this.mergeEvaluated(t,K.Name)),!0}};Qe.KeywordCxt=bl;function Pl(e,t,r,n){const s=new bl(e,r,t);"code"in r?r.code(s,n):s.$data&&r.validate?(0,Fr.funcKeywordCode)(s,r):"macro"in r?(0,Fr.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,Fr.funcKeywordCode)(s,r)}const Th=/^\/(?:[^~]|~0|~1)*$/,jh=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function Nl(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let s,a;if(e==="")return W.default.rootData;if(e[0]==="/"){if(!Th.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,a=W.default.rootData}else{const u=jh.exec(e);if(!u)throw new Error(`Invalid JSON-pointer: ${e}`);const c=+u[1];if(s=u[2],s==="#"){if(c>=t)throw new Error(l("property/index",c));return n[t-c]}if(c>t)throw new Error(l("data",c));if(a=r[t-c],!s)return a}let i=a;const d=s.split("/");for(const u of d)u&&(a=(0,K._)`${a}${(0,K.getProperty)((0,dt.unescapeJsonPointer)(u))}`,i=(0,K._)`${i} && ${a}`);return i;function l(u,c){return`Cannot access ${u} ${c} levels up, current level is ${t}`}}Qe.getData=Nl;var Qr={};Object.defineProperty(Qr,"__esModule",{value:!0});let Ah=class extends Error{constructor(t){super("validation failed"),this.errors=t,this.ajv=this.validation=!0}};Qr.default=Ah;var vr={};Object.defineProperty(vr,"__esModule",{value:!0});const $s=be;let kh=class extends Error{constructor(t,r,n,s){super(s||`can't resolve reference ${n} from id ${r}`),this.missingRef=(0,$s.resolveUrl)(t,r,n),this.missingSchema=(0,$s.normalizeId)((0,$s.getFullPath)(t,this.missingRef))}};vr.default=kh;var Le={};Object.defineProperty(Le,"__esModule",{value:!0});Le.resolveSchema=Le.getCompilingSchema=Le.resolveRef=Le.compileSchema=Le.SchemaEnv=void 0;const He=te,Ch=Qr,qt=ct,Xe=be,_i=M,Dh=Qe;let Yn=class{constructor(t){var r;this.refs={},this.dynamicAnchors={};let n;typeof t.schema=="object"&&(n=t.schema),this.schema=t.schema,this.schemaId=t.schemaId,this.root=t.root||this,this.baseId=(r=t.baseId)!==null&&r!==void 0?r:(0,Xe.normalizeId)(n==null?void 0:n[t.schemaId||"$id"]),this.schemaPath=t.schemaPath,this.localRefs=t.localRefs,this.meta=t.meta,this.$async=n==null?void 0:n.$async,this.refs={}}};Le.SchemaEnv=Yn;function Ea(e){const t=Ol.call(this,e);if(t)return t;const r=(0,Xe.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:n,lines:s}=this.opts.code,{ownProperties:a}=this.opts,i=new He.CodeGen(this.scope,{es5:n,lines:s,ownProperties:a});let d;e.$async&&(d=i.scopeValue("Error",{ref:Ch.default,code:(0,He._)`require("ajv/dist/runtime/validation_error").default`}));const l=i.scopeName("validate");e.validateName=l;const u={gen:i,allErrors:this.opts.allErrors,data:qt.default.data,parentData:qt.default.parentData,parentDataProperty:qt.default.parentDataProperty,dataNames:[qt.default.data],dataPathArr:[He.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:i.scopeValue("schema",this.opts.code.source===!0?{ref:e.schema,code:(0,He.stringify)(e.schema)}:{ref:e.schema}),validateName:l,ValidationError:d,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:He.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,He._)`""`,opts:this.opts,self:this};let c;try{this._compilations.add(e),(0,Dh.validateFunctionCode)(u),i.optimize(this.opts.code.optimize);const h=i.toString();c=`${i.scopeRefs(qt.default.scope)}return ${h}`,this.opts.code.process&&(c=this.opts.code.process(c,e));const y=new Function(`${qt.default.self}`,`${qt.default.scope}`,c)(this,this.scope.get());if(this.scope.value(l,{ref:y}),y.errors=null,y.schema=e.schema,y.schemaEnv=e,e.$async&&(y.$async=!0),this.opts.code.source===!0&&(y.source={validateName:l,validateCode:h,scopeValues:i._values}),this.opts.unevaluated){const{props:v,items:g}=u;y.evaluated={props:v instanceof He.Name?void 0:v,items:g instanceof He.Name?void 0:g,dynamicProps:v instanceof He.Name,dynamicItems:g instanceof He.Name},y.source&&(y.source.evaluated=(0,He.stringify)(y.evaluated))}return e.validate=y,e}catch(h){throw delete e.validate,delete e.validateName,c&&this.logger.error("Error compiling schema, function code:",c),h}finally{this._compilations.delete(e)}}Le.compileSchema=Ea;function Mh(e,t,r){var n;r=(0,Xe.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let a=Vh.call(this,e,r);if(a===void 0){const i=(n=e.localRefs)===null||n===void 0?void 0:n[r],{schemaId:d}=this.opts;i&&(a=new Yn({schema:i,schemaId:d,root:e,baseId:t}))}if(a!==void 0)return e.refs[r]=Lh.call(this,a)}Le.resolveRef=Mh;function Lh(e){return(0,Xe.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:Ea.call(this,e)}function Ol(e){for(const t of this._compilations)if(Fh(t,e))return t}Le.getCompilingSchema=Ol;function Fh(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function Vh(e,t){let r;for(;typeof(r=this.refs[t])=="string";)t=r;return r||this.schemas[t]||Qn.call(this,e,t)}function Qn(e,t){const r=this.opts.uriResolver.parse(t),n=(0,Xe._getFullPath)(this.opts.uriResolver,r);let s=(0,Xe.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===s)return ys.call(this,r,e);const a=(0,Xe.normalizeId)(n),i=this.refs[a]||this.schemas[a];if(typeof i=="string"){const d=Qn.call(this,e,i);return typeof(d==null?void 0:d.schema)!="object"?void 0:ys.call(this,r,d)}if(typeof(i==null?void 0:i.schema)=="object"){if(i.validate||Ea.call(this,i),a===(0,Xe.normalizeId)(t)){const{schema:d}=i,{schemaId:l}=this.opts,u=d[l];return u&&(s=(0,Xe.resolveUrl)(this.opts.uriResolver,s,u)),new Yn({schema:d,schemaId:l,root:e,baseId:s})}return ys.call(this,r,i)}}Le.resolveSchema=Qn;const Uh=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function ys(e,{baseId:t,schema:r,root:n}){var s;if(((s=e.fragment)===null||s===void 0?void 0:s[0])!=="/")return;for(const d of e.fragment.slice(1).split("/")){if(typeof r=="boolean")return;const l=r[(0,_i.unescapeFragment)(d)];if(l===void 0)return;r=l;const u=typeof r=="object"&&r[this.opts.schemaId];!Uh.has(d)&&u&&(t=(0,Xe.resolveUrl)(this.opts.uriResolver,t,u))}let a;if(typeof r!="boolean"&&r.$ref&&!(0,_i.schemaHasRulesButRef)(r,this.RULES)){const d=(0,Xe.resolveUrl)(this.opts.uriResolver,t,r.$ref);a=Qn.call(this,n,d)}const{schemaId:i}=this.opts;if(a=a||new Yn({schema:r,schemaId:i,root:n,baseId:t}),a.schema!==a.root.schema)return a}const zh="https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",qh="Meta-schema for $data reference (JSON AnySchema extension proposal)",Kh="object",Gh=["$data"],Hh={$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},Bh=!1,Wh={$id:zh,description:qh,type:Kh,required:Gh,properties:Hh,additionalProperties:Bh};var Sa={},Zn={exports:{}};const Jh={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};var Xh={HEX:Jh};const{HEX:Yh}=Xh,Qh=/^(?:(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)\.){3}(?:25[0-5]|2[0-4]\d|1\d{2}|[1-9]\d|\d)$/u;function Rl(e){if(Tl(e,".")<3)return{host:e,isIPV4:!1};const t=e.match(Qh)||[],[r]=t;return r?{host:xh(r,"."),isIPV4:!0}:{host:e,isIPV4:!1}}function gi(e,t=!1){let r="",n=!0;for(const s of e){if(Yh[s]===void 0)return;s!=="0"&&n===!0&&(n=!1),n||(r+=s)}return t&&r.length===0&&(r="0"),r}function Zh(e){let t=0;const r={error:!1,address:"",zone:""},n=[],s=[];let a=!1,i=!1,d=!1;function l(){if(s.length){if(a===!1){const u=gi(s);if(u!==void 0)n.push(u);else return r.error=!0,!1}s.length=0}return!0}for(let u=0;u<e.length;u++){const c=e[u];if(!(c==="["||c==="]"))if(c===":"){if(i===!0&&(d=!0),!l())break;if(t++,n.push(":"),t>7){r.error=!0;break}u-1>=0&&e[u-1]===":"&&(i=!0);continue}else if(c==="%"){if(!l())break;a=!0}else{s.push(c);continue}}return s.length&&(a?r.zone=s.join(""):d?n.push(s.join("")):n.push(gi(s))),r.address=n.join(""),r}function Il(e){if(Tl(e,":")<2)return{host:e,isIPV6:!1};const t=Zh(e);if(t.error)return{host:e,isIPV6:!1};{let r=t.address,n=t.address;return t.zone&&(r+="%"+t.zone,n+="%25"+t.zone),{host:r,escapedHost:n,isIPV6:!0}}}function xh(e,t){let r="",n=!0;const s=e.length;for(let a=0;a<s;a++){const i=e[a];i==="0"&&n?(a+1<=s&&e[a+1]===t||a+1===s)&&(r+=i,n=!1):(i===t?n=!0:n=!1,r+=i)}return r}function Tl(e,t){let r=0;for(let n=0;n<e.length;n++)e[n]===t&&r++;return r}const vi=/^\.\.?\//u,wi=/^\/\.(?:\/|$)/u,Ei=/^\/\.\.(?:\/|$)/u,ep=/^\/?(?:.|\n)*?(?=\/|$)/u;function tp(e){const t=[];for(;e.length;)if(e.match(vi))e=e.replace(vi,"");else if(e.match(wi))e=e.replace(wi,"/");else if(e.match(Ei))e=e.replace(Ei,"/"),t.pop();else if(e==="."||e==="..")e="";else{const r=e.match(ep);if(r){const n=r[0];e=e.slice(n.length),t.push(n)}else throw new Error("Unexpected dot segment condition")}return t.join("")}function rp(e,t){const r=t!==!0?escape:unescape;return e.scheme!==void 0&&(e.scheme=r(e.scheme)),e.userinfo!==void 0&&(e.userinfo=r(e.userinfo)),e.host!==void 0&&(e.host=r(e.host)),e.path!==void 0&&(e.path=r(e.path)),e.query!==void 0&&(e.query=r(e.query)),e.fragment!==void 0&&(e.fragment=r(e.fragment)),e}function np(e){const t=[];if(e.userinfo!==void 0&&(t.push(e.userinfo),t.push("@")),e.host!==void 0){let r=unescape(e.host);const n=Rl(r);if(n.isIPV4)r=n.host;else{const s=Il(n.host);s.isIPV6===!0?r=`[${s.escapedHost}]`:r=e.host}t.push(r)}return(typeof e.port=="number"||typeof e.port=="string")&&(t.push(":"),t.push(String(e.port))),t.length?t.join(""):void 0}var sp={recomposeAuthority:np,normalizeComponentEncoding:rp,removeDotSegments:tp,normalizeIPv4:Rl,normalizeIPv6:Il};const ap=/^[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}$/iu,op=/([\da-z][\d\-a-z]{0,31}):((?:[\w!$'()*+,\-.:;=@]|%[\da-f]{2})+)/iu;function jl(e){return typeof e.secure=="boolean"?e.secure:String(e.scheme).toLowerCase()==="wss"}function Al(e){return e.host||(e.error=e.error||"HTTP URIs must have a host."),e}function kl(e){const t=String(e.scheme).toLowerCase()==="https";return(e.port===(t?443:80)||e.port==="")&&(e.port=void 0),e.path||(e.path="/"),e}function ip(e){return e.secure=jl(e),e.resourceName=(e.path||"/")+(e.query?"?"+e.query:""),e.path=void 0,e.query=void 0,e}function cp(e){if((e.port===(jl(e)?443:80)||e.port==="")&&(e.port=void 0),typeof e.secure=="boolean"&&(e.scheme=e.secure?"wss":"ws",e.secure=void 0),e.resourceName){const[t,r]=e.resourceName.split("?");e.path=t&&t!=="/"?t:void 0,e.query=r,e.resourceName=void 0}return e.fragment=void 0,e}function lp(e,t){if(!e.path)return e.error="URN can not be parsed",e;const r=e.path.match(op);if(r){const n=t.scheme||e.scheme||"urn";e.nid=r[1].toLowerCase(),e.nss=r[2];const s=`${n}:${t.nid||e.nid}`,a=ba[s];e.path=void 0,a&&(e=a.parse(e,t))}else e.error=e.error||"URN can not be parsed.";return e}function up(e,t){const r=t.scheme||e.scheme||"urn",n=e.nid.toLowerCase(),s=`${r}:${t.nid||n}`,a=ba[s];a&&(e=a.serialize(e,t));const i=e,d=e.nss;return i.path=`${n||t.nid}:${d}`,t.skipEscape=!0,i}function dp(e,t){const r=e;return r.uuid=r.nss,r.nss=void 0,!t.tolerant&&(!r.uuid||!ap.test(r.uuid))&&(r.error=r.error||"UUID is not valid."),r}function fp(e){const t=e;return t.nss=(e.uuid||"").toLowerCase(),t}const Cl={scheme:"http",domainHost:!0,parse:Al,serialize:kl},hp={scheme:"https",domainHost:Cl.domainHost,parse:Al,serialize:kl},On={scheme:"ws",domainHost:!0,parse:ip,serialize:cp},pp={scheme:"wss",domainHost:On.domainHost,parse:On.parse,serialize:On.serialize},mp={scheme:"urn",parse:lp,serialize:up,skipNormalize:!0},$p={scheme:"urn:uuid",parse:dp,serialize:fp,skipNormalize:!0},ba={http:Cl,https:hp,ws:On,wss:pp,urn:mp,"urn:uuid":$p};var yp=ba;const{normalizeIPv6:_p,normalizeIPv4:gp,removeDotSegments:Dr,recomposeAuthority:vp,normalizeComponentEncoding:un}=sp,Pa=yp;function wp(e,t){return typeof e=="string"?e=ot(pt(e,t),t):typeof e=="object"&&(e=pt(ot(e,t),t)),e}function Ep(e,t,r){const n=Object.assign({scheme:"null"},r),s=Dl(pt(e,n),pt(t,n),n,!0);return ot(s,{...n,skipEscape:!0})}function Dl(e,t,r,n){const s={};return n||(e=pt(ot(e,r),r),t=pt(ot(t,r),r)),r=r||{},!r.tolerant&&t.scheme?(s.scheme=t.scheme,s.userinfo=t.userinfo,s.host=t.host,s.port=t.port,s.path=Dr(t.path||""),s.query=t.query):(t.userinfo!==void 0||t.host!==void 0||t.port!==void 0?(s.userinfo=t.userinfo,s.host=t.host,s.port=t.port,s.path=Dr(t.path||""),s.query=t.query):(t.path?(t.path.charAt(0)==="/"?s.path=Dr(t.path):((e.userinfo!==void 0||e.host!==void 0||e.port!==void 0)&&!e.path?s.path="/"+t.path:e.path?s.path=e.path.slice(0,e.path.lastIndexOf("/")+1)+t.path:s.path=t.path,s.path=Dr(s.path)),s.query=t.query):(s.path=e.path,t.query!==void 0?s.query=t.query:s.query=e.query),s.userinfo=e.userinfo,s.host=e.host,s.port=e.port),s.scheme=e.scheme),s.fragment=t.fragment,s}function Sp(e,t,r){return typeof e=="string"?(e=unescape(e),e=ot(un(pt(e,r),!0),{...r,skipEscape:!0})):typeof e=="object"&&(e=ot(un(e,!0),{...r,skipEscape:!0})),typeof t=="string"?(t=unescape(t),t=ot(un(pt(t,r),!0),{...r,skipEscape:!0})):typeof t=="object"&&(t=ot(un(t,!0),{...r,skipEscape:!0})),e.toLowerCase()===t.toLowerCase()}function ot(e,t){const r={host:e.host,scheme:e.scheme,userinfo:e.userinfo,port:e.port,path:e.path,query:e.query,nid:e.nid,nss:e.nss,uuid:e.uuid,fragment:e.fragment,reference:e.reference,resourceName:e.resourceName,secure:e.secure,error:""},n=Object.assign({},t),s=[],a=Pa[(n.scheme||r.scheme||"").toLowerCase()];a&&a.serialize&&a.serialize(r,n),r.path!==void 0&&(n.skipEscape?r.path=unescape(r.path):(r.path=escape(r.path),r.scheme!==void 0&&(r.path=r.path.split("%3A").join(":")))),n.reference!=="suffix"&&r.scheme&&s.push(r.scheme,":");const i=vp(r);if(i!==void 0&&(n.reference!=="suffix"&&s.push("//"),s.push(i),r.path&&r.path.charAt(0)!=="/"&&s.push("/")),r.path!==void 0){let d=r.path;!n.absolutePath&&(!a||!a.absolutePath)&&(d=Dr(d)),i===void 0&&(d=d.replace(/^\/\//u,"/%2F")),s.push(d)}return r.query!==void 0&&s.push("?",r.query),r.fragment!==void 0&&s.push("#",r.fragment),s.join("")}const bp=Array.from({length:127},(e,t)=>/[^!"$&'()*+,\-.;=_`a-z{}~]/u.test(String.fromCharCode(t)));function Pp(e){let t=0;for(let r=0,n=e.length;r<n;++r)if(t=e.charCodeAt(r),t>126||bp[t])return!0;return!1}const Np=/^(?:([^#/:?]+):)?(?:\/\/((?:([^#/?@]*)@)?(\[[^#/?\]]+\]|[^#/:?]*)(?::(\d*))?))?([^#?]*)(?:\?([^#]*))?(?:#((?:.|[\n\r])*))?/u;function pt(e,t){const r=Object.assign({},t),n={scheme:void 0,userinfo:void 0,host:"",port:void 0,path:"",query:void 0,fragment:void 0},s=e.indexOf("%")!==-1;let a=!1;r.reference==="suffix"&&(e=(r.scheme?r.scheme+":":"")+"//"+e);const i=e.match(Np);if(i){if(n.scheme=i[1],n.userinfo=i[3],n.host=i[4],n.port=parseInt(i[5],10),n.path=i[6]||"",n.query=i[7],n.fragment=i[8],isNaN(n.port)&&(n.port=i[5]),n.host){const l=gp(n.host);if(l.isIPV4===!1){const u=_p(l.host);n.host=u.host.toLowerCase(),a=u.isIPV6}else n.host=l.host,a=!0}n.scheme===void 0&&n.userinfo===void 0&&n.host===void 0&&n.port===void 0&&n.query===void 0&&!n.path?n.reference="same-document":n.scheme===void 0?n.reference="relative":n.fragment===void 0?n.reference="absolute":n.reference="uri",r.reference&&r.reference!=="suffix"&&r.reference!==n.reference&&(n.error=n.error||"URI is not a "+r.reference+" reference.");const d=Pa[(r.scheme||n.scheme||"").toLowerCase()];if(!r.unicodeSupport&&(!d||!d.unicodeSupport)&&n.host&&(r.domainHost||d&&d.domainHost)&&a===!1&&Pp(n.host))try{n.host=URL.domainToASCII(n.host.toLowerCase())}catch(l){n.error=n.error||"Host's domain name can not be converted to ASCII: "+l}(!d||d&&!d.skipNormalize)&&(s&&n.scheme!==void 0&&(n.scheme=unescape(n.scheme)),s&&n.host!==void 0&&(n.host=unescape(n.host)),n.path&&(n.path=escape(unescape(n.path))),n.fragment&&(n.fragment=encodeURI(decodeURIComponent(n.fragment)))),d&&d.parse&&d.parse(n,r)}else n.error=n.error||"URI can not be parsed.";return n}const Na={SCHEMES:Pa,normalize:wp,resolve:Ep,resolveComponents:Dl,equal:Sp,serialize:ot,parse:pt};Zn.exports=Na;Zn.exports.default=Na;Zn.exports.fastUri=Na;var Ml=Zn.exports;Object.defineProperty(Sa,"__esModule",{value:!0});const Ll=Ml;Ll.code='require("ajv/dist/runtime/uri").default';Sa.default=Ll;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.CodeGen=e.Name=e.nil=e.stringify=e.str=e._=e.KeywordCxt=void 0;var t=Qe;Object.defineProperty(e,"KeywordCxt",{enumerable:!0,get:function(){return t.KeywordCxt}});var r=te;Object.defineProperty(e,"_",{enumerable:!0,get:function(){return r._}}),Object.defineProperty(e,"str",{enumerable:!0,get:function(){return r.str}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return r.stringify}}),Object.defineProperty(e,"nil",{enumerable:!0,get:function(){return r.nil}}),Object.defineProperty(e,"Name",{enumerable:!0,get:function(){return r.Name}}),Object.defineProperty(e,"CodeGen",{enumerable:!0,get:function(){return r.CodeGen}});const n=Qr,s=vr,a=Zt,i=Le,d=te,l=be,u=_e,c=M,h=Wh,S=Sa,y=(E,m)=>new RegExp(E,m);y.code="new RegExp";const v=["removeAdditional","useDefaults","coerceTypes"],g=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),$={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},p={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'},w=200;function N(E){var m,b,_,o,f,P,j,A,q,F,re,Ve,Ot,Rt,It,Tt,jt,At,kt,Ct,Dt,Mt,Lt,Ft,Vt;const Ge=E.strict,Ut=(m=E.code)===null||m===void 0?void 0:m.optimize,Or=Ut===!0||Ut===void 0?1:Ut||0,Rr=(_=(b=E.code)===null||b===void 0?void 0:b.regExp)!==null&&_!==void 0?_:y,fs=(o=E.uriResolver)!==null&&o!==void 0?o:S.default;return{strictSchema:(P=(f=E.strictSchema)!==null&&f!==void 0?f:Ge)!==null&&P!==void 0?P:!0,strictNumbers:(A=(j=E.strictNumbers)!==null&&j!==void 0?j:Ge)!==null&&A!==void 0?A:!0,strictTypes:(F=(q=E.strictTypes)!==null&&q!==void 0?q:Ge)!==null&&F!==void 0?F:"log",strictTuples:(Ve=(re=E.strictTuples)!==null&&re!==void 0?re:Ge)!==null&&Ve!==void 0?Ve:"log",strictRequired:(Rt=(Ot=E.strictRequired)!==null&&Ot!==void 0?Ot:Ge)!==null&&Rt!==void 0?Rt:!1,code:E.code?{...E.code,optimize:Or,regExp:Rr}:{optimize:Or,regExp:Rr},loopRequired:(It=E.loopRequired)!==null&&It!==void 0?It:w,loopEnum:(Tt=E.loopEnum)!==null&&Tt!==void 0?Tt:w,meta:(jt=E.meta)!==null&&jt!==void 0?jt:!0,messages:(At=E.messages)!==null&&At!==void 0?At:!0,inlineRefs:(kt=E.inlineRefs)!==null&&kt!==void 0?kt:!0,schemaId:(Ct=E.schemaId)!==null&&Ct!==void 0?Ct:"$id",addUsedSchema:(Dt=E.addUsedSchema)!==null&&Dt!==void 0?Dt:!0,validateSchema:(Mt=E.validateSchema)!==null&&Mt!==void 0?Mt:!0,validateFormats:(Lt=E.validateFormats)!==null&&Lt!==void 0?Lt:!0,unicodeRegExp:(Ft=E.unicodeRegExp)!==null&&Ft!==void 0?Ft:!0,int32range:(Vt=E.int32range)!==null&&Vt!==void 0?Vt:!0,uriResolver:fs}}class O{constructor(m={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,m=this.opts={...m,...N(m)};const{es5:b,lines:_}=this.opts.code;this.scope=new d.ValueScope({scope:{},prefixes:g,es5:b,lines:_}),this.logger=Q(m.logger);const o=m.validateFormats;m.validateFormats=!1,this.RULES=(0,a.getRules)(),T.call(this,$,m,"NOT SUPPORTED"),T.call(this,p,m,"DEPRECATED","warn"),this._metaOpts=H.call(this),m.formats&&de.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),m.keywords&&V.call(this,m.keywords),typeof m.meta=="object"&&this.addMetaSchema(m.meta),B.call(this),m.validateFormats=o}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:m,meta:b,schemaId:_}=this.opts;let o=h;_==="id"&&(o={...h},o.id=o.$id,delete o.$id),b&&m&&this.addMetaSchema(o,o[_],!1)}defaultMeta(){const{meta:m,schemaId:b}=this.opts;return this.opts.defaultMeta=typeof m=="object"?m[b]||m:void 0}validate(m,b){let _;if(typeof m=="string"){if(_=this.getSchema(m),!_)throw new Error(`no schema with key or ref "${m}"`)}else _=this.compile(m);const o=_(b);return"$async"in _||(this.errors=_.errors),o}compile(m,b){const _=this._addSchema(m,b);return _.validate||this._compileSchemaEnv(_)}compileAsync(m,b){if(typeof this.opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");const{loadSchema:_}=this.opts;return o.call(this,m,b);async function o(F,re){await f.call(this,F.$schema);const Ve=this._addSchema(F,re);return Ve.validate||P.call(this,Ve)}async function f(F){F&&!this.getSchema(F)&&await o.call(this,{$ref:F},!0)}async function P(F){try{return this._compileSchemaEnv(F)}catch(re){if(!(re instanceof s.default))throw re;return j.call(this,re),await A.call(this,re.missingSchema),P.call(this,F)}}function j({missingSchema:F,missingRef:re}){if(this.refs[F])throw new Error(`AnySchema ${F} is loaded but ${re} cannot be resolved`)}async function A(F){const re=await q.call(this,F);this.refs[F]||await f.call(this,re.$schema),this.refs[F]||this.addSchema(re,F,b)}async function q(F){const re=this._loading[F];if(re)return re;try{return await(this._loading[F]=_(F))}finally{delete this._loading[F]}}}addSchema(m,b,_,o=this.opts.validateSchema){if(Array.isArray(m)){for(const P of m)this.addSchema(P,void 0,_,o);return this}let f;if(typeof m=="object"){const{schemaId:P}=this.opts;if(f=m[P],f!==void 0&&typeof f!="string")throw new Error(`schema ${P} must be string`)}return b=(0,l.normalizeId)(b||f),this._checkUnique(b),this.schemas[b]=this._addSchema(m,_,b,o,!0),this}addMetaSchema(m,b,_=this.opts.validateSchema){return this.addSchema(m,b,!0,_),this}validateSchema(m,b){if(typeof m=="boolean")return!0;let _;if(_=m.$schema,_!==void 0&&typeof _!="string")throw new Error("$schema must be a string");if(_=_||this.opts.defaultMeta||this.defaultMeta(),!_)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const o=this.validate(_,m);if(!o&&b){const f="schema is invalid: "+this.errorsText();if(this.opts.validateSchema==="log")this.logger.error(f);else throw new Error(f)}return o}getSchema(m){let b;for(;typeof(b=z.call(this,m))=="string";)m=b;if(b===void 0){const{schemaId:_}=this.opts,o=new i.SchemaEnv({schema:{},schemaId:_});if(b=i.resolveSchema.call(this,o,m),!b)return;this.refs[m]=b}return b.validate||this._compileSchemaEnv(b)}removeSchema(m){if(m instanceof RegExp)return this._removeAllSchemas(this.schemas,m),this._removeAllSchemas(this.refs,m),this;switch(typeof m){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const b=z.call(this,m);return typeof b=="object"&&this._cache.delete(b.schema),delete this.schemas[m],delete this.refs[m],this}case"object":{const b=m;this._cache.delete(b);let _=m[this.opts.schemaId];return _&&(_=(0,l.normalizeId)(_),delete this.schemas[_],delete this.refs[_]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(m){for(const b of m)this.addKeyword(b);return this}addKeyword(m,b){let _;if(typeof m=="string")_=m,typeof b=="object"&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),b.keyword=_);else if(typeof m=="object"&&b===void 0){if(b=m,_=b.keyword,Array.isArray(_)&&!_.length)throw new Error("addKeywords: keyword must be string or non-empty array")}else throw new Error("invalid addKeywords parameters");if(C.call(this,_,b),!b)return(0,c.eachItem)(_,f=>k.call(this,f)),this;D.call(this,b);const o={...b,type:(0,u.getJSONTypes)(b.type),schemaType:(0,u.getJSONTypes)(b.schemaType)};return(0,c.eachItem)(_,o.type.length===0?f=>k.call(this,f,o):f=>o.type.forEach(P=>k.call(this,f,o,P))),this}getKeyword(m){const b=this.RULES.all[m];return typeof b=="object"?b.definition:!!b}removeKeyword(m){const{RULES:b}=this;delete b.keywords[m],delete b.all[m];for(const _ of b.rules){const o=_.rules.findIndex(f=>f.keyword===m);o>=0&&_.rules.splice(o,1)}return this}addFormat(m,b){return typeof b=="string"&&(b=new RegExp(b)),this.formats[m]=b,this}errorsText(m=this.errors,{separator:b=", ",dataVar:_="data"}={}){return!m||m.length===0?"No errors":m.map(o=>`${_}${o.instancePath} ${o.message}`).reduce((o,f)=>o+b+f)}$dataMetaSchema(m,b){const _=this.RULES.all;m=JSON.parse(JSON.stringify(m));for(const o of b){const f=o.split("/").slice(1);let P=m;for(const j of f)P=P[j];for(const j in _){const A=_[j];if(typeof A!="object")continue;const{$data:q}=A.definition,F=P[j];q&&F&&(P[j]=I(F))}}return m}_removeAllSchemas(m,b){for(const _ in m){const o=m[_];(!b||b.test(_))&&(typeof o=="string"?delete m[_]:o&&!o.meta&&(this._cache.delete(o.schema),delete m[_]))}}_addSchema(m,b,_,o=this.opts.validateSchema,f=this.opts.addUsedSchema){let P;const{schemaId:j}=this.opts;if(typeof m=="object")P=m[j];else{if(this.opts.jtd)throw new Error("schema must be object");if(typeof m!="boolean")throw new Error("schema must be object or boolean")}let A=this._cache.get(m);if(A!==void 0)return A;_=(0,l.normalizeId)(P||_);const q=l.getSchemaRefs.call(this,m,_);return A=new i.SchemaEnv({schema:m,schemaId:j,meta:b,baseId:_,localRefs:q}),this._cache.set(A.schema,A),f&&!_.startsWith("#")&&(_&&this._checkUnique(_),this.refs[_]=A),o&&this.validateSchema(m,!0),A}_checkUnique(m){if(this.schemas[m]||this.refs[m])throw new Error(`schema with key or id "${m}" already exists`)}_compileSchemaEnv(m){if(m.meta?this._compileMetaSchema(m):i.compileSchema.call(this,m),!m.validate)throw new Error("ajv implementation error");return m.validate}_compileMetaSchema(m){const b=this.opts;this.opts=this._metaOpts;try{i.compileSchema.call(this,m)}finally{this.opts=b}}}O.ValidationError=n.default,O.MissingRefError=s.default,e.default=O;function T(E,m,b,_="error"){for(const o in E){const f=o;f in m&&this.logger[_](`${b}: option ${o}. ${E[f]}`)}}function z(E){return E=(0,l.normalizeId)(E),this.schemas[E]||this.refs[E]}function B(){const E=this.opts.schemas;if(E)if(Array.isArray(E))this.addSchema(E);else for(const m in E)this.addSchema(E[m],m)}function de(){for(const E in this.opts.formats){const m=this.opts.formats[E];m&&this.addFormat(E,m)}}function V(E){if(Array.isArray(E)){this.addVocabulary(E);return}this.logger.warn("keywords option as map is deprecated, pass array");for(const m in E){const b=E[m];b.keyword||(b.keyword=m),this.addKeyword(b)}}function H(){const E={...this.opts};for(const m of v)delete E[m];return E}const ne={log(){},warn(){},error(){}};function Q(E){if(E===!1)return ne;if(E===void 0)return console;if(E.log&&E.warn&&E.error)return E;throw new Error("logger must implement log, warn and error methods")}const fe=/^[a-z_$][a-z0-9_$:-]*$/i;function C(E,m){const{RULES:b}=this;if((0,c.eachItem)(E,_=>{if(b.keywords[_])throw new Error(`Keyword ${_} is already defined`);if(!fe.test(_))throw new Error(`Keyword ${_} has invalid name`)}),!!m&&m.$data&&!("code"in m||"validate"in m))throw new Error('$data keyword must have "code" or "validate" function')}function k(E,m,b){var _;const o=m==null?void 0:m.post;if(b&&o)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:f}=this;let P=o?f.post:f.rules.find(({type:A})=>A===b);if(P||(P={type:b,rules:[]},f.rules.push(P)),f.keywords[E]=!0,!m)return;const j={keyword:E,definition:{...m,type:(0,u.getJSONTypes)(m.type),schemaType:(0,u.getJSONTypes)(m.schemaType)}};m.before?U.call(this,P,j,m.before):P.rules.push(j),f.all[E]=j,(_=m.implements)===null||_===void 0||_.forEach(A=>this.addKeyword(A))}function U(E,m,b){const _=E.rules.findIndex(o=>o.keyword===b);_>=0?E.rules.splice(_,0,m):(E.rules.push(m),this.logger.warn(`rule ${b} is not defined`))}function D(E){let{metaSchema:m}=E;m!==void 0&&(E.$data&&this.opts.$data&&(m=I(m)),E.validateSchema=this.compile(m,!0))}const R={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function I(E){return{anyOf:[E,R]}}})(Yc);var Oa={},Ra={},Ia={};Object.defineProperty(Ia,"__esModule",{value:!0});const Op={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};Ia.default=Op;var xt={};Object.defineProperty(xt,"__esModule",{value:!0});xt.callRef=xt.getValidate=void 0;const Rp=vr,Si=x,De=te,nr=ct,bi=Le,dn=M,Ip={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:n}=e,{baseId:s,schemaEnv:a,validateName:i,opts:d,self:l}=n,{root:u}=a;if((r==="#"||r==="#/")&&s===u.baseId)return h();const c=bi.resolveRef.call(l,u,s,r);if(c===void 0)throw new Rp.default(n.opts.uriResolver,s,r);if(c instanceof bi.SchemaEnv)return S(c);return y(c);function h(){if(a===u)return Rn(e,i,a,a.$async);const v=t.scopeValue("root",{ref:u});return Rn(e,(0,De._)`${v}.validate`,u,u.$async)}function S(v){const g=Fl(e,v);Rn(e,g,v,v.$async)}function y(v){const g=t.scopeValue("schema",d.code.source===!0?{ref:v,code:(0,De.stringify)(v)}:{ref:v}),$=t.name("valid"),p=e.subschema({schema:v,dataTypes:[],schemaPath:De.nil,topSchemaRef:g,errSchemaPath:r},$);e.mergeEvaluated(p),e.ok($)}}};function Fl(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,De._)`${r.scopeValue("wrapper",{ref:t})}.validate`}xt.getValidate=Fl;function Rn(e,t,r,n){const{gen:s,it:a}=e,{allErrors:i,schemaEnv:d,opts:l}=a,u=l.passContext?nr.default.this:De.nil;n?c():h();function c(){if(!d.$async)throw new Error("async schema referenced by sync schema");const v=s.let("valid");s.try(()=>{s.code((0,De._)`await ${(0,Si.callValidateCode)(e,t,u)}`),y(t),i||s.assign(v,!0)},g=>{s.if((0,De._)`!(${g} instanceof ${a.ValidationError})`,()=>s.throw(g)),S(g),i||s.assign(v,!1)}),e.ok(v)}function h(){e.result((0,Si.callValidateCode)(e,t,u),()=>y(t),()=>S(t))}function S(v){const g=(0,De._)`${v}.errors`;s.assign(nr.default.vErrors,(0,De._)`${nr.default.vErrors} === null ? ${g} : ${nr.default.vErrors}.concat(${g})`),s.assign(nr.default.errors,(0,De._)`${nr.default.vErrors}.length`)}function y(v){var g;if(!a.opts.unevaluated)return;const $=(g=r==null?void 0:r.validate)===null||g===void 0?void 0:g.evaluated;if(a.props!==!0)if($&&!$.dynamicProps)$.props!==void 0&&(a.props=dn.mergeEvaluated.props(s,$.props,a.props));else{const p=s.var("props",(0,De._)`${v}.evaluated.props`);a.props=dn.mergeEvaluated.props(s,p,a.props,De.Name)}if(a.items!==!0)if($&&!$.dynamicItems)$.items!==void 0&&(a.items=dn.mergeEvaluated.items(s,$.items,a.items));else{const p=s.var("items",(0,De._)`${v}.evaluated.items`);a.items=dn.mergeEvaluated.items(s,p,a.items,De.Name)}}}xt.callRef=Rn;xt.default=Ip;Object.defineProperty(Ra,"__esModule",{value:!0});const Tp=Ia,jp=xt,Ap=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",Tp.default,jp.default];Ra.default=Ap;var Ta={},ja={};Object.defineProperty(ja,"__esModule",{value:!0});const Ln=te,$t=Ln.operators,Fn={maximum:{okStr:"<=",ok:$t.LTE,fail:$t.GT},minimum:{okStr:">=",ok:$t.GTE,fail:$t.LT},exclusiveMaximum:{okStr:"<",ok:$t.LT,fail:$t.GTE},exclusiveMinimum:{okStr:">",ok:$t.GT,fail:$t.LTE}},kp={message:({keyword:e,schemaCode:t})=>(0,Ln.str)`must be ${Fn[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,Ln._)`{comparison: ${Fn[e].okStr}, limit: ${t}}`},Cp={keyword:Object.keys(Fn),type:"number",schemaType:"number",$data:!0,error:kp,code(e){const{keyword:t,data:r,schemaCode:n}=e;e.fail$data((0,Ln._)`${r} ${Fn[t].fail} ${n} || isNaN(${r})`)}};ja.default=Cp;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0});const Vr=te,Dp={message:({schemaCode:e})=>(0,Vr.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,Vr._)`{multipleOf: ${e}}`},Mp={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:Dp,code(e){const{gen:t,data:r,schemaCode:n,it:s}=e,a=s.opts.multipleOfPrecision,i=t.let("res"),d=a?(0,Vr._)`Math.abs(Math.round(${i}) - ${i}) > 1e-${a}`:(0,Vr._)`${i} !== parseInt(${i})`;e.fail$data((0,Vr._)`(${n} === 0 || (${i} = ${r}/${n}, ${d}))`)}};Aa.default=Mp;var ka={},Ca={};Object.defineProperty(Ca,"__esModule",{value:!0});function Vl(e){const t=e.length;let r=0,n=0,s;for(;n<t;)r++,s=e.charCodeAt(n++),s>=55296&&s<=56319&&n<t&&(s=e.charCodeAt(n),(s&64512)===56320&&n++);return r}Ca.default=Vl;Vl.code='require("ajv/dist/runtime/ucs2length").default';Object.defineProperty(ka,"__esModule",{value:!0});const Bt=te,Lp=M,Fp=Ca,Vp={message({keyword:e,schemaCode:t}){const r=e==="maxLength"?"more":"fewer";return(0,Bt.str)`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>(0,Bt._)`{limit: ${e}}`},Up={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:Vp,code(e){const{keyword:t,data:r,schemaCode:n,it:s}=e,a=t==="maxLength"?Bt.operators.GT:Bt.operators.LT,i=s.opts.unicode===!1?(0,Bt._)`${r}.length`:(0,Bt._)`${(0,Lp.useFunc)(e.gen,Fp.default)}(${r})`;e.fail$data((0,Bt._)`${i} ${a} ${n}`)}};ka.default=Up;var Da={};Object.defineProperty(Da,"__esModule",{value:!0});const zp=x,Vn=te,qp={message:({schemaCode:e})=>(0,Vn.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,Vn._)`{pattern: ${e}}`},Kp={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:qp,code(e){const{data:t,$data:r,schema:n,schemaCode:s,it:a}=e,i=a.opts.unicodeRegExp?"u":"",d=r?(0,Vn._)`(new RegExp(${s}, ${i}))`:(0,zp.usePattern)(e,n);e.fail$data((0,Vn._)`!${d}.test(${t})`)}};Da.default=Kp;var Ma={};Object.defineProperty(Ma,"__esModule",{value:!0});const Ur=te,Gp={message({keyword:e,schemaCode:t}){const r=e==="maxProperties"?"more":"fewer";return(0,Ur.str)`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>(0,Ur._)`{limit: ${e}}`},Hp={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:Gp,code(e){const{keyword:t,data:r,schemaCode:n}=e,s=t==="maxProperties"?Ur.operators.GT:Ur.operators.LT;e.fail$data((0,Ur._)`Object.keys(${r}).length ${s} ${n}`)}};Ma.default=Hp;var La={};Object.defineProperty(La,"__esModule",{value:!0});const jr=x,zr=te,Bp=M,Wp={message:({params:{missingProperty:e}})=>(0,zr.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,zr._)`{missingProperty: ${e}}`},Jp={keyword:"required",type:"object",schemaType:"array",$data:!0,error:Wp,code(e){const{gen:t,schema:r,schemaCode:n,data:s,$data:a,it:i}=e,{opts:d}=i;if(!a&&r.length===0)return;const l=r.length>=d.loopRequired;if(i.allErrors?u():c(),d.strictRequired){const y=e.parentSchema.properties,{definedProperties:v}=e.it;for(const g of r)if((y==null?void 0:y[g])===void 0&&!v.has(g)){const $=i.schemaEnv.baseId+i.errSchemaPath,p=`required property "${g}" is not defined at "${$}" (strictRequired)`;(0,Bp.checkStrictMode)(i,p,i.opts.strictRequired)}}function u(){if(l||a)e.block$data(zr.nil,h);else for(const y of r)(0,jr.checkReportMissingProp)(e,y)}function c(){const y=t.let("missing");if(l||a){const v=t.let("valid",!0);e.block$data(v,()=>S(y,v)),e.ok(v)}else t.if((0,jr.checkMissingProp)(e,r,y)),(0,jr.reportMissingProp)(e,y),t.else()}function h(){t.forOf("prop",n,y=>{e.setParams({missingProperty:y}),t.if((0,jr.noPropertyInData)(t,s,y,d.ownProperties),()=>e.error())})}function S(y,v){e.setParams({missingProperty:y}),t.forOf(y,n,()=>{t.assign(v,(0,jr.propertyInData)(t,s,y,d.ownProperties)),t.if((0,zr.not)(v),()=>{e.error(),t.break()})},zr.nil)}}};La.default=Jp;var Fa={};Object.defineProperty(Fa,"__esModule",{value:!0});const qr=te,Xp={message({keyword:e,schemaCode:t}){const r=e==="maxItems"?"more":"fewer";return(0,qr.str)`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>(0,qr._)`{limit: ${e}}`},Yp={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:Xp,code(e){const{keyword:t,data:r,schemaCode:n}=e,s=t==="maxItems"?qr.operators.GT:qr.operators.LT;e.fail$data((0,qr._)`${r}.length ${s} ${n}`)}};Fa.default=Yp;var Va={},Zr={};Object.defineProperty(Zr,"__esModule",{value:!0});const Ul=Xn;Ul.code='require("ajv/dist/runtime/equal").default';Zr.default=Ul;Object.defineProperty(Va,"__esModule",{value:!0});const _s=_e,we=te,Qp=M,Zp=Zr,xp={message:({params:{i:e,j:t}})=>(0,we.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,we._)`{i: ${e}, j: ${t}}`},em={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:xp,code(e){const{gen:t,data:r,$data:n,schema:s,parentSchema:a,schemaCode:i,it:d}=e;if(!n&&!s)return;const l=t.let("valid"),u=a.items?(0,_s.getSchemaTypes)(a.items):[];e.block$data(l,c,(0,we._)`${i} === false`),e.ok(l);function c(){const v=t.let("i",(0,we._)`${r}.length`),g=t.let("j");e.setParams({i:v,j:g}),t.assign(l,!0),t.if((0,we._)`${v} > 1`,()=>(h()?S:y)(v,g))}function h(){return u.length>0&&!u.some(v=>v==="object"||v==="array")}function S(v,g){const $=t.name("item"),p=(0,_s.checkDataTypes)(u,$,d.opts.strictNumbers,_s.DataType.Wrong),w=t.const("indices",(0,we._)`{}`);t.for((0,we._)`;${v}--;`,()=>{t.let($,(0,we._)`${r}[${v}]`),t.if(p,(0,we._)`continue`),u.length>1&&t.if((0,we._)`typeof ${$} == "string"`,(0,we._)`${$} += "_"`),t.if((0,we._)`typeof ${w}[${$}] == "number"`,()=>{t.assign(g,(0,we._)`${w}[${$}]`),e.error(),t.assign(l,!1).break()}).code((0,we._)`${w}[${$}] = ${v}`)})}function y(v,g){const $=(0,Qp.useFunc)(t,Zp.default),p=t.name("outer");t.label(p).for((0,we._)`;${v}--;`,()=>t.for((0,we._)`${g} = ${v}; ${g}--;`,()=>t.if((0,we._)`${$}(${r}[${v}], ${r}[${g}])`,()=>{e.error(),t.assign(l,!1).break(p)})))}}};Va.default=em;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0});const Vs=te,tm=M,rm=Zr,nm={message:"must be equal to constant",params:({schemaCode:e})=>(0,Vs._)`{allowedValue: ${e}}`},sm={keyword:"const",$data:!0,error:nm,code(e){const{gen:t,data:r,$data:n,schemaCode:s,schema:a}=e;n||a&&typeof a=="object"?e.fail$data((0,Vs._)`!${(0,tm.useFunc)(t,rm.default)}(${r}, ${s})`):e.fail((0,Vs._)`${a} !== ${r}`)}};Ua.default=sm;var za={};Object.defineProperty(za,"__esModule",{value:!0});const Mr=te,am=M,om=Zr,im={message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,Mr._)`{allowedValues: ${e}}`},cm={keyword:"enum",schemaType:"array",$data:!0,error:im,code(e){const{gen:t,data:r,$data:n,schema:s,schemaCode:a,it:i}=e;if(!n&&s.length===0)throw new Error("enum must have non-empty array");const d=s.length>=i.opts.loopEnum;let l;const u=()=>l??(l=(0,am.useFunc)(t,om.default));let c;if(d||n)c=t.let("valid"),e.block$data(c,h);else{if(!Array.isArray(s))throw new Error("ajv implementation error");const y=t.const("vSchema",a);c=(0,Mr.or)(...s.map((v,g)=>S(y,g)))}e.pass(c);function h(){t.assign(c,!1),t.forOf("v",a,y=>t.if((0,Mr._)`${u()}(${r}, ${y})`,()=>t.assign(c,!0).break()))}function S(y,v){const g=s[v];return typeof g=="object"&&g!==null?(0,Mr._)`${u()}(${r}, ${y}[${v}])`:(0,Mr._)`${r} === ${g}`}}};za.default=cm;Object.defineProperty(Ta,"__esModule",{value:!0});const lm=ja,um=Aa,dm=ka,fm=Da,hm=Ma,pm=La,mm=Fa,$m=Va,ym=Ua,_m=za,gm=[lm.default,um.default,dm.default,fm.default,hm.default,pm.default,mm.default,$m.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},ym.default,_m.default];Ta.default=gm;var qa={},wr={};Object.defineProperty(wr,"__esModule",{value:!0});wr.validateAdditionalItems=void 0;const Wt=te,Us=M,vm={message:({params:{len:e}})=>(0,Wt.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,Wt._)`{limit: ${e}}`},wm={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:vm,code(e){const{parentSchema:t,it:r}=e,{items:n}=t;if(!Array.isArray(n)){(0,Us.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');return}zl(e,n)}};function zl(e,t){const{gen:r,schema:n,data:s,keyword:a,it:i}=e;i.items=!0;const d=r.const("len",(0,Wt._)`${s}.length`);if(n===!1)e.setParams({len:t.length}),e.pass((0,Wt._)`${d} <= ${t.length}`);else if(typeof n=="object"&&!(0,Us.alwaysValidSchema)(i,n)){const u=r.var("valid",(0,Wt._)`${d} <= ${t.length}`);r.if((0,Wt.not)(u),()=>l(u)),e.ok(u)}function l(u){r.forRange("i",t.length,d,c=>{e.subschema({keyword:a,dataProp:c,dataPropType:Us.Type.Num},u),i.allErrors||r.if((0,Wt.not)(u),()=>r.break())})}}wr.validateAdditionalItems=zl;wr.default=wm;var Ka={},Er={};Object.defineProperty(Er,"__esModule",{value:!0});Er.validateTuple=void 0;const Pi=te,In=M,Em=x,Sm={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return ql(e,"additionalItems",t);r.items=!0,!(0,In.alwaysValidSchema)(r,t)&&e.ok((0,Em.validateArray)(e))}};function ql(e,t,r=e.schema){const{gen:n,parentSchema:s,data:a,keyword:i,it:d}=e;c(s),d.opts.unevaluated&&r.length&&d.items!==!0&&(d.items=In.mergeEvaluated.items(n,r.length,d.items));const l=n.name("valid"),u=n.const("len",(0,Pi._)`${a}.length`);r.forEach((h,S)=>{(0,In.alwaysValidSchema)(d,h)||(n.if((0,Pi._)`${u} > ${S}`,()=>e.subschema({keyword:i,schemaProp:S,dataProp:S},l)),e.ok(l))});function c(h){const{opts:S,errSchemaPath:y}=d,v=r.length,g=v===h.minItems&&(v===h.maxItems||h[t]===!1);if(S.strictTuples&&!g){const $=`"${i}" is ${v}-tuple, but minItems or maxItems/${t} are not specified or different at path "${y}"`;(0,In.checkStrictMode)(d,$,S.strictTuples)}}}Er.validateTuple=ql;Er.default=Sm;Object.defineProperty(Ka,"__esModule",{value:!0});const bm=Er,Pm={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,bm.validateTuple)(e,"items")};Ka.default=Pm;var Ga={};Object.defineProperty(Ga,"__esModule",{value:!0});const Ni=te,Nm=M,Om=x,Rm=wr,Im={message:({params:{len:e}})=>(0,Ni.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,Ni._)`{limit: ${e}}`},Tm={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:Im,code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:s}=r;n.items=!0,!(0,Nm.alwaysValidSchema)(n,t)&&(s?(0,Rm.validateAdditionalItems)(e,s):e.ok((0,Om.validateArray)(e)))}};Ga.default=Tm;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0});const qe=te,fn=M,jm={message:({params:{min:e,max:t}})=>t===void 0?(0,qe.str)`must contain at least ${e} valid item(s)`:(0,qe.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>t===void 0?(0,qe._)`{minContains: ${e}}`:(0,qe._)`{minContains: ${e}, maxContains: ${t}}`},Am={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:jm,code(e){const{gen:t,schema:r,parentSchema:n,data:s,it:a}=e;let i,d;const{minContains:l,maxContains:u}=n;a.opts.next?(i=l===void 0?1:l,d=u):i=1;const c=t.const("len",(0,qe._)`${s}.length`);if(e.setParams({min:i,max:d}),d===void 0&&i===0){(0,fn.checkStrictMode)(a,'"minContains" == 0 without "maxContains": "contains" keyword ignored');return}if(d!==void 0&&i>d){(0,fn.checkStrictMode)(a,'"minContains" > "maxContains" is always invalid'),e.fail();return}if((0,fn.alwaysValidSchema)(a,r)){let g=(0,qe._)`${c} >= ${i}`;d!==void 0&&(g=(0,qe._)`${g} && ${c} <= ${d}`),e.pass(g);return}a.items=!0;const h=t.name("valid");d===void 0&&i===1?y(h,()=>t.if(h,()=>t.break())):i===0?(t.let(h,!0),d!==void 0&&t.if((0,qe._)`${s}.length > 0`,S)):(t.let(h,!1),S()),e.result(h,()=>e.reset());function S(){const g=t.name("_valid"),$=t.let("count",0);y(g,()=>t.if(g,()=>v($)))}function y(g,$){t.forRange("i",0,c,p=>{e.subschema({keyword:"contains",dataProp:p,dataPropType:fn.Type.Num,compositeRule:!0},g),$()})}function v(g){t.code((0,qe._)`${g}++`),d===void 0?t.if((0,qe._)`${g} >= ${i}`,()=>t.assign(h,!0).break()):(t.if((0,qe._)`${g} > ${d}`,()=>t.assign(h,!1).break()),i===1?t.assign(h,!0):t.if((0,qe._)`${g} >= ${i}`,()=>t.assign(h,!0)))}}};Ha.default=Am;var Kl={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.validateSchemaDeps=e.validatePropertyDeps=e.error=void 0;const t=te,r=M,n=x;e.error={message:({params:{property:l,depsCount:u,deps:c}})=>{const h=u===1?"property":"properties";return(0,t.str)`must have ${h} ${c} when property ${l} is present`},params:({params:{property:l,depsCount:u,deps:c,missingProperty:h}})=>(0,t._)`{property: ${l},
    missingProperty: ${h},
    depsCount: ${u},
    deps: ${c}}`};const s={keyword:"dependencies",type:"object",schemaType:"object",error:e.error,code(l){const[u,c]=a(l);i(l,u),d(l,c)}};function a({schema:l}){const u={},c={};for(const h in l){if(h==="__proto__")continue;const S=Array.isArray(l[h])?u:c;S[h]=l[h]}return[u,c]}function i(l,u=l.schema){const{gen:c,data:h,it:S}=l;if(Object.keys(u).length===0)return;const y=c.let("missing");for(const v in u){const g=u[v];if(g.length===0)continue;const $=(0,n.propertyInData)(c,h,v,S.opts.ownProperties);l.setParams({property:v,depsCount:g.length,deps:g.join(", ")}),S.allErrors?c.if($,()=>{for(const p of g)(0,n.checkReportMissingProp)(l,p)}):(c.if((0,t._)`${$} && (${(0,n.checkMissingProp)(l,g,y)})`),(0,n.reportMissingProp)(l,y),c.else())}}e.validatePropertyDeps=i;function d(l,u=l.schema){const{gen:c,data:h,keyword:S,it:y}=l,v=c.name("valid");for(const g in u)(0,r.alwaysValidSchema)(y,u[g])||(c.if((0,n.propertyInData)(c,h,g,y.opts.ownProperties),()=>{const $=l.subschema({keyword:S,schemaProp:g},v);l.mergeValidEvaluated($,v)},()=>c.var(v,!0)),l.ok(v))}e.validateSchemaDeps=d,e.default=s})(Kl);var Ba={};Object.defineProperty(Ba,"__esModule",{value:!0});const Gl=te,km=M,Cm={message:"property name must be valid",params:({params:e})=>(0,Gl._)`{propertyName: ${e.propertyName}}`},Dm={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:Cm,code(e){const{gen:t,schema:r,data:n,it:s}=e;if((0,km.alwaysValidSchema)(s,r))return;const a=t.name("valid");t.forIn("key",n,i=>{e.setParams({propertyName:i}),e.subschema({keyword:"propertyNames",data:i,dataTypes:["string"],propertyName:i,compositeRule:!0},a),t.if((0,Gl.not)(a),()=>{e.error(!0),s.allErrors||t.break()})}),e.ok(a)}};Ba.default=Dm;var xn={};Object.defineProperty(xn,"__esModule",{value:!0});const hn=x,We=te,Mm=ct,pn=M,Lm={message:"must NOT have additional properties",params:({params:e})=>(0,We._)`{additionalProperty: ${e.additionalProperty}}`},Fm={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:Lm,code(e){const{gen:t,schema:r,parentSchema:n,data:s,errsCount:a,it:i}=e;if(!a)throw new Error("ajv implementation error");const{allErrors:d,opts:l}=i;if(i.props=!0,l.removeAdditional!=="all"&&(0,pn.alwaysValidSchema)(i,r))return;const u=(0,hn.allSchemaProperties)(n.properties),c=(0,hn.allSchemaProperties)(n.patternProperties);h(),e.ok((0,We._)`${a} === ${Mm.default.errors}`);function h(){t.forIn("key",s,$=>{!u.length&&!c.length?v($):t.if(S($),()=>v($))})}function S($){let p;if(u.length>8){const w=(0,pn.schemaRefOrVal)(i,n.properties,"properties");p=(0,hn.isOwnProperty)(t,w,$)}else u.length?p=(0,We.or)(...u.map(w=>(0,We._)`${$} === ${w}`)):p=We.nil;return c.length&&(p=(0,We.or)(p,...c.map(w=>(0,We._)`${(0,hn.usePattern)(e,w)}.test(${$})`))),(0,We.not)(p)}function y($){t.code((0,We._)`delete ${s}[${$}]`)}function v($){if(l.removeAdditional==="all"||l.removeAdditional&&r===!1){y($);return}if(r===!1){e.setParams({additionalProperty:$}),e.error(),d||t.break();return}if(typeof r=="object"&&!(0,pn.alwaysValidSchema)(i,r)){const p=t.name("valid");l.removeAdditional==="failing"?(g($,p,!1),t.if((0,We.not)(p),()=>{e.reset(),y($)})):(g($,p),d||t.if((0,We.not)(p),()=>t.break()))}}function g($,p,w){const N={keyword:"additionalProperties",dataProp:$,dataPropType:pn.Type.Str};w===!1&&Object.assign(N,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(N,p)}}};xn.default=Fm;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0});const Vm=Qe,Oi=x,gs=M,Ri=xn,Um={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:n,data:s,it:a}=e;a.opts.removeAdditional==="all"&&n.additionalProperties===void 0&&Ri.default.code(new Vm.KeywordCxt(a,Ri.default,"additionalProperties"));const i=(0,Oi.allSchemaProperties)(r);for(const h of i)a.definedProperties.add(h);a.opts.unevaluated&&i.length&&a.props!==!0&&(a.props=gs.mergeEvaluated.props(t,(0,gs.toHash)(i),a.props));const d=i.filter(h=>!(0,gs.alwaysValidSchema)(a,r[h]));if(d.length===0)return;const l=t.name("valid");for(const h of d)u(h)?c(h):(t.if((0,Oi.propertyInData)(t,s,h,a.opts.ownProperties)),c(h),a.allErrors||t.else().var(l,!0),t.endIf()),e.it.definedProperties.add(h),e.ok(l);function u(h){return a.opts.useDefaults&&!a.compositeRule&&r[h].default!==void 0}function c(h){e.subschema({keyword:"properties",schemaProp:h,dataProp:h},l)}}};Wa.default=Um;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0});const Ii=x,mn=te,Ti=M,ji=M,zm={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:n,parentSchema:s,it:a}=e,{opts:i}=a,d=(0,Ii.allSchemaProperties)(r),l=d.filter(g=>(0,Ti.alwaysValidSchema)(a,r[g]));if(d.length===0||l.length===d.length&&(!a.opts.unevaluated||a.props===!0))return;const u=i.strictSchema&&!i.allowMatchingProperties&&s.properties,c=t.name("valid");a.props!==!0&&!(a.props instanceof mn.Name)&&(a.props=(0,ji.evaluatedPropsToName)(t,a.props));const{props:h}=a;S();function S(){for(const g of d)u&&y(g),a.allErrors?v(g):(t.var(c,!0),v(g),t.if(c))}function y(g){for(const $ in u)new RegExp(g).test($)&&(0,Ti.checkStrictMode)(a,`property ${$} matches pattern ${g} (use allowMatchingProperties)`)}function v(g){t.forIn("key",n,$=>{t.if((0,mn._)`${(0,Ii.usePattern)(e,g)}.test(${$})`,()=>{const p=l.includes(g);p||e.subschema({keyword:"patternProperties",schemaProp:g,dataProp:$,dataPropType:ji.Type.Str},c),a.opts.unevaluated&&h!==!0?t.assign((0,mn._)`${h}[${$}]`,!0):!p&&!a.allErrors&&t.if((0,mn.not)(c),()=>t.break())})})}}};Ja.default=zm;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0});const qm=M,Km={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:n}=e;if((0,qm.alwaysValidSchema)(n,r)){e.fail();return}const s=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},s),e.failResult(s,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};Xa.default=Km;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0});const Gm=x,Hm={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:Gm.validateUnion,error:{message:"must match a schema in anyOf"}};Ya.default=Hm;var Qa={};Object.defineProperty(Qa,"__esModule",{value:!0});const Tn=te,Bm=M,Wm={message:"must match exactly one schema in oneOf",params:({params:e})=>(0,Tn._)`{passingSchemas: ${e.passing}}`},Jm={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:Wm,code(e){const{gen:t,schema:r,parentSchema:n,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(s.opts.discriminator&&n.discriminator)return;const a=r,i=t.let("valid",!1),d=t.let("passing",null),l=t.name("_valid");e.setParams({passing:d}),t.block(u),e.result(i,()=>e.reset(),()=>e.error(!0));function u(){a.forEach((c,h)=>{let S;(0,Bm.alwaysValidSchema)(s,c)?t.var(l,!0):S=e.subschema({keyword:"oneOf",schemaProp:h,compositeRule:!0},l),h>0&&t.if((0,Tn._)`${l} && ${i}`).assign(i,!1).assign(d,(0,Tn._)`[${d}, ${h}]`).else(),t.if(l,()=>{t.assign(i,!0),t.assign(d,h),S&&e.mergeEvaluated(S,Tn.Name)})})}}};Qa.default=Jm;var Za={};Object.defineProperty(Za,"__esModule",{value:!0});const Xm=M,Ym={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const s=t.name("valid");r.forEach((a,i)=>{if((0,Xm.alwaysValidSchema)(n,a))return;const d=e.subschema({keyword:"allOf",schemaProp:i},s);e.ok(s),e.mergeEvaluated(d)})}};Za.default=Ym;var xa={};Object.defineProperty(xa,"__esModule",{value:!0});const Un=te,Hl=M,Qm={message:({params:e})=>(0,Un.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,Un._)`{failingKeyword: ${e.ifClause}}`},Zm={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:Qm,code(e){const{gen:t,parentSchema:r,it:n}=e;r.then===void 0&&r.else===void 0&&(0,Hl.checkStrictMode)(n,'"if" without "then" and "else" is ignored');const s=Ai(n,"then"),a=Ai(n,"else");if(!s&&!a)return;const i=t.let("valid",!0),d=t.name("_valid");if(l(),e.reset(),s&&a){const c=t.let("ifClause");e.setParams({ifClause:c}),t.if(d,u("then",c),u("else",c))}else s?t.if(d,u("then")):t.if((0,Un.not)(d),u("else"));e.pass(i,()=>e.error(!0));function l(){const c=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},d);e.mergeEvaluated(c)}function u(c,h){return()=>{const S=e.subschema({keyword:c},d);t.assign(i,d),e.mergeValidEvaluated(S,i),h?t.assign(h,(0,Un._)`${c}`):e.setParams({ifClause:c})}}}};function Ai(e,t){const r=e.schema[t];return r!==void 0&&!(0,Hl.alwaysValidSchema)(e,r)}xa.default=Zm;var eo={};Object.defineProperty(eo,"__esModule",{value:!0});const xm=M,e$={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){t.if===void 0&&(0,xm.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};eo.default=e$;Object.defineProperty(qa,"__esModule",{value:!0});const t$=wr,r$=Ka,n$=Er,s$=Ga,a$=Ha,o$=Kl,i$=Ba,c$=xn,l$=Wa,u$=Ja,d$=Xa,f$=Ya,h$=Qa,p$=Za,m$=xa,$$=eo;function y$(e=!1){const t=[d$.default,f$.default,h$.default,p$.default,m$.default,$$.default,i$.default,c$.default,o$.default,l$.default,u$.default];return e?t.push(r$.default,s$.default):t.push(t$.default,n$.default),t.push(a$.default),t}qa.default=y$;var to={},ro={};Object.defineProperty(ro,"__esModule",{value:!0});const $e=te,_$={message:({schemaCode:e})=>(0,$e.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,$e._)`{format: ${e}}`},g$={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:_$,code(e,t){const{gen:r,data:n,$data:s,schema:a,schemaCode:i,it:d}=e,{opts:l,errSchemaPath:u,schemaEnv:c,self:h}=d;if(!l.validateFormats)return;s?S():y();function S(){const v=r.scopeValue("formats",{ref:h.formats,code:l.code.formats}),g=r.const("fDef",(0,$e._)`${v}[${i}]`),$=r.let("fType"),p=r.let("format");r.if((0,$e._)`typeof ${g} == "object" && !(${g} instanceof RegExp)`,()=>r.assign($,(0,$e._)`${g}.type || "string"`).assign(p,(0,$e._)`${g}.validate`),()=>r.assign($,(0,$e._)`"string"`).assign(p,g)),e.fail$data((0,$e.or)(w(),N()));function w(){return l.strictSchema===!1?$e.nil:(0,$e._)`${i} && !${p}`}function N(){const O=c.$async?(0,$e._)`(${g}.async ? await ${p}(${n}) : ${p}(${n}))`:(0,$e._)`${p}(${n})`,T=(0,$e._)`(typeof ${p} == "function" ? ${O} : ${p}.test(${n}))`;return(0,$e._)`${p} && ${p} !== true && ${$} === ${t} && !${T}`}}function y(){const v=h.formats[a];if(!v){w();return}if(v===!0)return;const[g,$,p]=N(v);g===t&&e.pass(O());function w(){if(l.strictSchema===!1){h.logger.warn(T());return}throw new Error(T());function T(){return`unknown format "${a}" ignored in schema at path "${u}"`}}function N(T){const z=T instanceof RegExp?(0,$e.regexpCode)(T):l.code.formats?(0,$e._)`${l.code.formats}${(0,$e.getProperty)(a)}`:void 0,B=r.scopeValue("formats",{key:a,ref:T,code:z});return typeof T=="object"&&!(T instanceof RegExp)?[T.type||"string",T.validate,(0,$e._)`${B}.validate`]:["string",T,B]}function O(){if(typeof v=="object"&&!(v instanceof RegExp)&&v.async){if(!c.$async)throw new Error("async format in sync schema");return(0,$e._)`await ${p}(${n})`}return typeof $=="function"?(0,$e._)`${p}(${n})`:(0,$e._)`${p}.test(${n})`}}}};ro.default=g$;Object.defineProperty(to,"__esModule",{value:!0});const v$=ro,w$=[v$.default];to.default=w$;var yr={};Object.defineProperty(yr,"__esModule",{value:!0});yr.contentVocabulary=yr.metadataVocabulary=void 0;yr.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"];yr.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"];Object.defineProperty(Oa,"__esModule",{value:!0});const E$=Ra,S$=Ta,b$=qa,P$=to,ki=yr,N$=[E$.default,S$.default,(0,b$.default)(),P$.default,ki.metadataVocabulary,ki.contentVocabulary];Oa.default=N$;var no={},es={};Object.defineProperty(es,"__esModule",{value:!0});es.DiscrError=void 0;var Ci;(function(e){e.Tag="tag",e.Mapping="mapping"})(Ci||(es.DiscrError=Ci={}));Object.defineProperty(no,"__esModule",{value:!0});const ir=te,zs=es,Di=Le,O$=vr,R$=M,I$={message:({params:{discrError:e,tagName:t}})=>e===zs.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,ir._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`},T$={keyword:"discriminator",type:"object",schemaType:"object",error:I$,code(e){const{gen:t,data:r,schema:n,parentSchema:s,it:a}=e,{oneOf:i}=s;if(!a.opts.discriminator)throw new Error("discriminator: requires discriminator option");const d=n.propertyName;if(typeof d!="string")throw new Error("discriminator: requires propertyName");if(n.mapping)throw new Error("discriminator: mapping is not supported");if(!i)throw new Error("discriminator: requires oneOf keyword");const l=t.let("valid",!1),u=t.const("tag",(0,ir._)`${r}${(0,ir.getProperty)(d)}`);t.if((0,ir._)`typeof ${u} == "string"`,()=>c(),()=>e.error(!1,{discrError:zs.DiscrError.Tag,tag:u,tagName:d})),e.ok(l);function c(){const y=S();t.if(!1);for(const v in y)t.elseIf((0,ir._)`${u} === ${v}`),t.assign(l,h(y[v]));t.else(),e.error(!1,{discrError:zs.DiscrError.Mapping,tag:u,tagName:d}),t.endIf()}function h(y){const v=t.name("valid"),g=e.subschema({keyword:"oneOf",schemaProp:y},v);return e.mergeEvaluated(g,ir.Name),v}function S(){var y;const v={},g=p(s);let $=!0;for(let O=0;O<i.length;O++){let T=i[O];if(T!=null&&T.$ref&&!(0,R$.schemaHasRulesButRef)(T,a.self.RULES)){const B=T.$ref;if(T=Di.resolveRef.call(a.self,a.schemaEnv.root,a.baseId,B),T instanceof Di.SchemaEnv&&(T=T.schema),T===void 0)throw new O$.default(a.opts.uriResolver,a.baseId,B)}const z=(y=T==null?void 0:T.properties)===null||y===void 0?void 0:y[d];if(typeof z!="object")throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${d}"`);$=$&&(g||p(T)),w(z,O)}if(!$)throw new Error(`discriminator: "${d}" must be required`);return v;function p({required:O}){return Array.isArray(O)&&O.includes(d)}function w(O,T){if(O.const)N(O.const,T);else if(O.enum)for(const z of O.enum)N(z,T);else throw new Error(`discriminator: "properties/${d}" must have "const" or "enum"`)}function N(O,T){if(typeof O!="string"||O in v)throw new Error(`discriminator: "${d}" values must be unique strings`);v[O]=T}}}};no.default=T$;const j$="http://json-schema.org/draft-07/schema#",A$="http://json-schema.org/draft-07/schema#",k$="Core schema meta-schema",C$={schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},D$=["object","boolean"],M$={$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},L$={$schema:j$,$id:A$,title:k$,definitions:C$,type:D$,properties:M$,default:!0};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;const r=Yc,n=Oa,s=no,a=L$,i=["/properties"],d="http://json-schema.org/draft-07/schema";class l extends r.default{_addVocabularies(){super._addVocabularies(),n.default.forEach(v=>this.addVocabulary(v)),this.opts.discriminator&&this.addKeyword(s.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const v=this.opts.$data?this.$dataMetaSchema(a,i):a;this.addMetaSchema(v,d,!1),this.refs["http://json-schema.org/schema"]=d}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(d)?d:void 0)}}t.Ajv=l,e.exports=t=l,e.exports.Ajv=l,Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var u=Qe;Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return u.KeywordCxt}});var c=te;Object.defineProperty(t,"_",{enumerable:!0,get:function(){return c._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return c.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return c.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return c.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return c.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return c.CodeGen}});var h=Qr;Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return h.default}});var S=vr;Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return S.default}})})(Cs,Cs.exports);var F$=Cs.exports,qs={exports:{}},Bl={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.formatNames=e.fastFormats=e.fullFormats=void 0;function t(V,H){return{validate:V,compare:H}}e.fullFormats={date:t(a,i),time:t(l,u),"date-time":t(h,S),duration:/^P(?!$)((\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+S)?)?|(\d+W)?)$/,uri:g,"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,url:/^(?:https?|ftp):\/\/(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)(?:\.(?:[a-z0-9\u{00a1}-\u{ffff}]+-)*[a-z0-9\u{00a1}-\u{ffff}]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i,regex:de,uuid:/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,"json-pointer":/^(?:\/(?:[^~/]|~0|~1)*)*$/,"json-pointer-uri-fragment":/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,"relative-json-pointer":/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/,byte:p,int32:{type:"number",validate:O},int64:{type:"number",validate:T},float:{type:"number",validate:z},double:{type:"number",validate:z},password:!0,binary:!0},e.fastFormats={...e.fullFormats,date:t(/^\d\d\d\d-[0-1]\d-[0-3]\d$/,i),time:t(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i,u),"date-time":t(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i,S),uri:/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/)?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+\-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i},e.formatNames=Object.keys(e.fullFormats);function r(V){return V%4===0&&(V%100!==0||V%400===0)}const n=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,s=[0,31,28,31,30,31,30,31,31,30,31,30,31];function a(V){const H=n.exec(V);if(!H)return!1;const ne=+H[1],Q=+H[2],fe=+H[3];return Q>=1&&Q<=12&&fe>=1&&fe<=(Q===2&&r(ne)?29:s[Q])}function i(V,H){if(V&&H)return V>H?1:V<H?-1:0}const d=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function l(V,H){const ne=d.exec(V);if(!ne)return!1;const Q=+ne[1],fe=+ne[2],C=+ne[3],k=ne[5];return(Q<=23&&fe<=59&&C<=59||Q===23&&fe===59&&C===60)&&(!H||k!=="")}function u(V,H){if(!(V&&H))return;const ne=d.exec(V),Q=d.exec(H);if(ne&&Q)return V=ne[1]+ne[2]+ne[3]+(ne[4]||""),H=Q[1]+Q[2]+Q[3]+(Q[4]||""),V>H?1:V<H?-1:0}const c=/t|\s/i;function h(V){const H=V.split(c);return H.length===2&&a(H[0])&&l(H[1],!0)}function S(V,H){if(!(V&&H))return;const[ne,Q]=V.split(c),[fe,C]=H.split(c),k=i(ne,fe);if(k!==void 0)return k||u(Q,C)}const y=/\/|:/,v=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;function g(V){return y.test(V)&&v.test(V)}const $=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/gm;function p(V){return $.lastIndex=0,$.test(V)}const w=-2147483648,N=2**31-1;function O(V){return Number.isInteger(V)&&V<=N&&V>=w}function T(V){return Number.isInteger(V)}function z(){return!0}const B=/[^\\]\\Z/;function de(V){if(B.test(V))return!1;try{return new RegExp(V),!0}catch{return!1}}})(Bl);var Wl={},Ks={exports:{}},Jl={},Ze={},_r={},xr={},Z={},Xr={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.regexpCode=e.getEsmExportName=e.getProperty=e.safeStringify=e.stringify=e.strConcat=e.addCodeArg=e.str=e._=e.nil=e._Code=e.Name=e.IDENTIFIER=e._CodeOrName=void 0;class t{}e._CodeOrName=t,e.IDENTIFIER=/^[a-z$_][a-z$_0-9]*$/i;class r extends t{constructor(w){if(super(),!e.IDENTIFIER.test(w))throw new Error("CodeGen: name must be a valid identifier");this.str=w}toString(){return this.str}emptyStr(){return!1}get names(){return{[this.str]:1}}}e.Name=r;class n extends t{constructor(w){super(),this._items=typeof w=="string"?[w]:w}toString(){return this.str}emptyStr(){if(this._items.length>1)return!1;const w=this._items[0];return w===""||w==='""'}get str(){var w;return(w=this._str)!==null&&w!==void 0?w:this._str=this._items.reduce((N,O)=>`${N}${O}`,"")}get names(){var w;return(w=this._names)!==null&&w!==void 0?w:this._names=this._items.reduce((N,O)=>(O instanceof r&&(N[O.str]=(N[O.str]||0)+1),N),{})}}e._Code=n,e.nil=new n("");function s(p,...w){const N=[p[0]];let O=0;for(;O<w.length;)d(N,w[O]),N.push(p[++O]);return new n(N)}e._=s;const a=new n("+");function i(p,...w){const N=[y(p[0])];let O=0;for(;O<w.length;)N.push(a),d(N,w[O]),N.push(a,y(p[++O]));return l(N),new n(N)}e.str=i;function d(p,w){w instanceof n?p.push(...w._items):w instanceof r?p.push(w):p.push(h(w))}e.addCodeArg=d;function l(p){let w=1;for(;w<p.length-1;){if(p[w]===a){const N=u(p[w-1],p[w+1]);if(N!==void 0){p.splice(w-1,3,N);continue}p[w++]="+"}w++}}function u(p,w){if(w==='""')return p;if(p==='""')return w;if(typeof p=="string")return w instanceof r||p[p.length-1]!=='"'?void 0:typeof w!="string"?`${p.slice(0,-1)}${w}"`:w[0]==='"'?p.slice(0,-1)+w.slice(1):void 0;if(typeof w=="string"&&w[0]==='"'&&!(p instanceof r))return`"${p}${w.slice(1)}`}function c(p,w){return w.emptyStr()?p:p.emptyStr()?w:i`${p}${w}`}e.strConcat=c;function h(p){return typeof p=="number"||typeof p=="boolean"||p===null?p:y(Array.isArray(p)?p.join(","):p)}function S(p){return new n(y(p))}e.stringify=S;function y(p){return JSON.stringify(p).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")}e.safeStringify=y;function v(p){return typeof p=="string"&&e.IDENTIFIER.test(p)?new n(`.${p}`):s`[${p}]`}e.getProperty=v;function g(p){if(typeof p=="string"&&e.IDENTIFIER.test(p))return new n(`${p}`);throw new Error(`CodeGen: invalid export name: ${p}, use explicit $id name mapping`)}e.getEsmExportName=g;function $(p){return new n(p.toString())}e.regexpCode=$})(Xr);var Gs={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.ValueScope=e.ValueScopeName=e.Scope=e.varKinds=e.UsedValueState=void 0;const t=Xr;class r extends Error{constructor(u){super(`CodeGen: "code" for ${u} not defined`),this.value=u.value}}var n;(function(l){l[l.Started=0]="Started",l[l.Completed=1]="Completed"})(n||(e.UsedValueState=n={})),e.varKinds={const:new t.Name("const"),let:new t.Name("let"),var:new t.Name("var")};class s{constructor({prefixes:u,parent:c}={}){this._names={},this._prefixes=u,this._parent=c}toName(u){return u instanceof t.Name?u:this.name(u)}name(u){return new t.Name(this._newName(u))}_newName(u){const c=this._names[u]||this._nameGroup(u);return`${u}${c.index++}`}_nameGroup(u){var c,h;if(!((h=(c=this._parent)===null||c===void 0?void 0:c._prefixes)===null||h===void 0)&&h.has(u)||this._prefixes&&!this._prefixes.has(u))throw new Error(`CodeGen: prefix "${u}" is not allowed in this scope`);return this._names[u]={prefix:u,index:0}}}e.Scope=s;class a extends t.Name{constructor(u,c){super(c),this.prefix=u}setValue(u,{property:c,itemIndex:h}){this.value=u,this.scopePath=(0,t._)`.${new t.Name(c)}[${h}]`}}e.ValueScopeName=a;const i=(0,t._)`\n`;class d extends s{constructor(u){super(u),this._values={},this._scope=u.scope,this.opts={...u,_n:u.lines?i:t.nil}}get(){return this._scope}name(u){return new a(u,this._newName(u))}value(u,c){var h;if(c.ref===void 0)throw new Error("CodeGen: ref must be passed in value");const S=this.toName(u),{prefix:y}=S,v=(h=c.key)!==null&&h!==void 0?h:c.ref;let g=this._values[y];if(g){const w=g.get(v);if(w)return w}else g=this._values[y]=new Map;g.set(v,S);const $=this._scope[y]||(this._scope[y]=[]),p=$.length;return $[p]=c.ref,S.setValue(c,{property:y,itemIndex:p}),S}getValue(u,c){const h=this._values[u];if(h)return h.get(c)}scopeRefs(u,c=this._values){return this._reduceValues(c,h=>{if(h.scopePath===void 0)throw new Error(`CodeGen: name "${h}" has no value`);return(0,t._)`${u}${h.scopePath}`})}scopeCode(u=this._values,c,h){return this._reduceValues(u,S=>{if(S.value===void 0)throw new Error(`CodeGen: name "${S}" has no value`);return S.value.code},c,h)}_reduceValues(u,c,h={},S){let y=t.nil;for(const v in u){const g=u[v];if(!g)continue;const $=h[v]=h[v]||new Map;g.forEach(p=>{if($.has(p))return;$.set(p,n.Started);let w=c(p);if(w){const N=this.opts.es5?e.varKinds.var:e.varKinds.const;y=(0,t._)`${y}${N} ${p} = ${w};${this.opts._n}`}else if(w=S==null?void 0:S(p))y=(0,t._)`${y}${w}${this.opts._n}`;else throw new r(p);$.set(p,n.Completed)})}return y}}e.ValueScope=d})(Gs);(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.or=e.and=e.not=e.CodeGen=e.operators=e.varKinds=e.ValueScopeName=e.ValueScope=e.Scope=e.Name=e.regexpCode=e.stringify=e.getProperty=e.nil=e.strConcat=e.str=e._=void 0;const t=Xr,r=Gs;var n=Xr;Object.defineProperty(e,"_",{enumerable:!0,get:function(){return n._}}),Object.defineProperty(e,"str",{enumerable:!0,get:function(){return n.str}}),Object.defineProperty(e,"strConcat",{enumerable:!0,get:function(){return n.strConcat}}),Object.defineProperty(e,"nil",{enumerable:!0,get:function(){return n.nil}}),Object.defineProperty(e,"getProperty",{enumerable:!0,get:function(){return n.getProperty}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return n.stringify}}),Object.defineProperty(e,"regexpCode",{enumerable:!0,get:function(){return n.regexpCode}}),Object.defineProperty(e,"Name",{enumerable:!0,get:function(){return n.Name}});var s=Gs;Object.defineProperty(e,"Scope",{enumerable:!0,get:function(){return s.Scope}}),Object.defineProperty(e,"ValueScope",{enumerable:!0,get:function(){return s.ValueScope}}),Object.defineProperty(e,"ValueScopeName",{enumerable:!0,get:function(){return s.ValueScopeName}}),Object.defineProperty(e,"varKinds",{enumerable:!0,get:function(){return s.varKinds}}),e.operators={GT:new t._Code(">"),GTE:new t._Code(">="),LT:new t._Code("<"),LTE:new t._Code("<="),EQ:new t._Code("==="),NEQ:new t._Code("!=="),NOT:new t._Code("!"),OR:new t._Code("||"),AND:new t._Code("&&"),ADD:new t._Code("+")};class a{optimizeNodes(){return this}optimizeNames(o,f){return this}}class i extends a{constructor(o,f,P){super(),this.varKind=o,this.name=f,this.rhs=P}render({es5:o,_n:f}){const P=o?r.varKinds.var:this.varKind,j=this.rhs===void 0?"":` = ${this.rhs}`;return`${P} ${this.name}${j};`+f}optimizeNames(o,f){if(o[this.name.str])return this.rhs&&(this.rhs=C(this.rhs,o,f)),this}get names(){return this.rhs instanceof t._CodeOrName?this.rhs.names:{}}}class d extends a{constructor(o,f,P){super(),this.lhs=o,this.rhs=f,this.sideEffects=P}render({_n:o}){return`${this.lhs} = ${this.rhs};`+o}optimizeNames(o,f){if(!(this.lhs instanceof t.Name&&!o[this.lhs.str]&&!this.sideEffects))return this.rhs=C(this.rhs,o,f),this}get names(){const o=this.lhs instanceof t.Name?{}:{...this.lhs.names};return fe(o,this.rhs)}}class l extends d{constructor(o,f,P,j){super(o,P,j),this.op=f}render({_n:o}){return`${this.lhs} ${this.op}= ${this.rhs};`+o}}class u extends a{constructor(o){super(),this.label=o,this.names={}}render({_n:o}){return`${this.label}:`+o}}class c extends a{constructor(o){super(),this.label=o,this.names={}}render({_n:o}){return`break${this.label?` ${this.label}`:""};`+o}}class h extends a{constructor(o){super(),this.error=o}render({_n:o}){return`throw ${this.error};`+o}get names(){return this.error.names}}class S extends a{constructor(o){super(),this.code=o}render({_n:o}){return`${this.code};`+o}optimizeNodes(){return`${this.code}`?this:void 0}optimizeNames(o,f){return this.code=C(this.code,o,f),this}get names(){return this.code instanceof t._CodeOrName?this.code.names:{}}}class y extends a{constructor(o=[]){super(),this.nodes=o}render(o){return this.nodes.reduce((f,P)=>f+P.render(o),"")}optimizeNodes(){const{nodes:o}=this;let f=o.length;for(;f--;){const P=o[f].optimizeNodes();Array.isArray(P)?o.splice(f,1,...P):P?o[f]=P:o.splice(f,1)}return o.length>0?this:void 0}optimizeNames(o,f){const{nodes:P}=this;let j=P.length;for(;j--;){const A=P[j];A.optimizeNames(o,f)||(k(o,A.names),P.splice(j,1))}return P.length>0?this:void 0}get names(){return this.nodes.reduce((o,f)=>Q(o,f.names),{})}}class v extends y{render(o){return"{"+o._n+super.render(o)+"}"+o._n}}class g extends y{}class $ extends v{}$.kind="else";class p extends v{constructor(o,f){super(f),this.condition=o}render(o){let f=`if(${this.condition})`+super.render(o);return this.else&&(f+="else "+this.else.render(o)),f}optimizeNodes(){super.optimizeNodes();const o=this.condition;if(o===!0)return this.nodes;let f=this.else;if(f){const P=f.optimizeNodes();f=this.else=Array.isArray(P)?new $(P):P}if(f)return o===!1?f instanceof p?f:f.nodes:this.nodes.length?this:new p(U(o),f instanceof p?[f]:f.nodes);if(!(o===!1||!this.nodes.length))return this}optimizeNames(o,f){var P;if(this.else=(P=this.else)===null||P===void 0?void 0:P.optimizeNames(o,f),!!(super.optimizeNames(o,f)||this.else))return this.condition=C(this.condition,o,f),this}get names(){const o=super.names;return fe(o,this.condition),this.else&&Q(o,this.else.names),o}}p.kind="if";class w extends v{}w.kind="for";class N extends w{constructor(o){super(),this.iteration=o}render(o){return`for(${this.iteration})`+super.render(o)}optimizeNames(o,f){if(super.optimizeNames(o,f))return this.iteration=C(this.iteration,o,f),this}get names(){return Q(super.names,this.iteration.names)}}class O extends w{constructor(o,f,P,j){super(),this.varKind=o,this.name=f,this.from=P,this.to=j}render(o){const f=o.es5?r.varKinds.var:this.varKind,{name:P,from:j,to:A}=this;return`for(${f} ${P}=${j}; ${P}<${A}; ${P}++)`+super.render(o)}get names(){const o=fe(super.names,this.from);return fe(o,this.to)}}class T extends w{constructor(o,f,P,j){super(),this.loop=o,this.varKind=f,this.name=P,this.iterable=j}render(o){return`for(${this.varKind} ${this.name} ${this.loop} ${this.iterable})`+super.render(o)}optimizeNames(o,f){if(super.optimizeNames(o,f))return this.iterable=C(this.iterable,o,f),this}get names(){return Q(super.names,this.iterable.names)}}class z extends v{constructor(o,f,P){super(),this.name=o,this.args=f,this.async=P}render(o){return`${this.async?"async ":""}function ${this.name}(${this.args})`+super.render(o)}}z.kind="func";class B extends y{render(o){return"return "+super.render(o)}}B.kind="return";class de extends v{render(o){let f="try"+super.render(o);return this.catch&&(f+=this.catch.render(o)),this.finally&&(f+=this.finally.render(o)),f}optimizeNodes(){var o,f;return super.optimizeNodes(),(o=this.catch)===null||o===void 0||o.optimizeNodes(),(f=this.finally)===null||f===void 0||f.optimizeNodes(),this}optimizeNames(o,f){var P,j;return super.optimizeNames(o,f),(P=this.catch)===null||P===void 0||P.optimizeNames(o,f),(j=this.finally)===null||j===void 0||j.optimizeNames(o,f),this}get names(){const o=super.names;return this.catch&&Q(o,this.catch.names),this.finally&&Q(o,this.finally.names),o}}class V extends v{constructor(o){super(),this.error=o}render(o){return`catch(${this.error})`+super.render(o)}}V.kind="catch";class H extends v{render(o){return"finally"+super.render(o)}}H.kind="finally";class ne{constructor(o,f={}){this._values={},this._blockStarts=[],this._constants={},this.opts={...f,_n:f.lines?`
`:""},this._extScope=o,this._scope=new r.Scope({parent:o}),this._nodes=[new g]}toString(){return this._root.render(this.opts)}name(o){return this._scope.name(o)}scopeName(o){return this._extScope.name(o)}scopeValue(o,f){const P=this._extScope.value(o,f);return(this._values[P.prefix]||(this._values[P.prefix]=new Set)).add(P),P}getScopeValue(o,f){return this._extScope.getValue(o,f)}scopeRefs(o){return this._extScope.scopeRefs(o,this._values)}scopeCode(){return this._extScope.scopeCode(this._values)}_def(o,f,P,j){const A=this._scope.toName(f);return P!==void 0&&j&&(this._constants[A.str]=P),this._leafNode(new i(o,A,P)),A}const(o,f,P){return this._def(r.varKinds.const,o,f,P)}let(o,f,P){return this._def(r.varKinds.let,o,f,P)}var(o,f,P){return this._def(r.varKinds.var,o,f,P)}assign(o,f,P){return this._leafNode(new d(o,f,P))}add(o,f){return this._leafNode(new l(o,e.operators.ADD,f))}code(o){return typeof o=="function"?o():o!==t.nil&&this._leafNode(new S(o)),this}object(...o){const f=["{"];for(const[P,j]of o)f.length>1&&f.push(","),f.push(P),(P!==j||this.opts.es5)&&(f.push(":"),(0,t.addCodeArg)(f,j));return f.push("}"),new t._Code(f)}if(o,f,P){if(this._blockNode(new p(o)),f&&P)this.code(f).else().code(P).endIf();else if(f)this.code(f).endIf();else if(P)throw new Error('CodeGen: "else" body without "then" body');return this}elseIf(o){return this._elseNode(new p(o))}else(){return this._elseNode(new $)}endIf(){return this._endBlockNode(p,$)}_for(o,f){return this._blockNode(o),f&&this.code(f).endFor(),this}for(o,f){return this._for(new N(o),f)}forRange(o,f,P,j,A=this.opts.es5?r.varKinds.var:r.varKinds.let){const q=this._scope.toName(o);return this._for(new O(A,q,f,P),()=>j(q))}forOf(o,f,P,j=r.varKinds.const){const A=this._scope.toName(o);if(this.opts.es5){const q=f instanceof t.Name?f:this.var("_arr",f);return this.forRange("_i",0,(0,t._)`${q}.length`,F=>{this.var(A,(0,t._)`${q}[${F}]`),P(A)})}return this._for(new T("of",j,A,f),()=>P(A))}forIn(o,f,P,j=this.opts.es5?r.varKinds.var:r.varKinds.const){if(this.opts.ownProperties)return this.forOf(o,(0,t._)`Object.keys(${f})`,P);const A=this._scope.toName(o);return this._for(new T("in",j,A,f),()=>P(A))}endFor(){return this._endBlockNode(w)}label(o){return this._leafNode(new u(o))}break(o){return this._leafNode(new c(o))}return(o){const f=new B;if(this._blockNode(f),this.code(o),f.nodes.length!==1)throw new Error('CodeGen: "return" should have one node');return this._endBlockNode(B)}try(o,f,P){if(!f&&!P)throw new Error('CodeGen: "try" without "catch" and "finally"');const j=new de;if(this._blockNode(j),this.code(o),f){const A=this.name("e");this._currNode=j.catch=new V(A),f(A)}return P&&(this._currNode=j.finally=new H,this.code(P)),this._endBlockNode(V,H)}throw(o){return this._leafNode(new h(o))}block(o,f){return this._blockStarts.push(this._nodes.length),o&&this.code(o).endBlock(f),this}endBlock(o){const f=this._blockStarts.pop();if(f===void 0)throw new Error("CodeGen: not in self-balancing block");const P=this._nodes.length-f;if(P<0||o!==void 0&&P!==o)throw new Error(`CodeGen: wrong number of nodes: ${P} vs ${o} expected`);return this._nodes.length=f,this}func(o,f=t.nil,P,j){return this._blockNode(new z(o,f,P)),j&&this.code(j).endFunc(),this}endFunc(){return this._endBlockNode(z)}optimize(o=1){for(;o-- >0;)this._root.optimizeNodes(),this._root.optimizeNames(this._root.names,this._constants)}_leafNode(o){return this._currNode.nodes.push(o),this}_blockNode(o){this._currNode.nodes.push(o),this._nodes.push(o)}_endBlockNode(o,f){const P=this._currNode;if(P instanceof o||f&&P instanceof f)return this._nodes.pop(),this;throw new Error(`CodeGen: not in block "${f?`${o.kind}/${f.kind}`:o.kind}"`)}_elseNode(o){const f=this._currNode;if(!(f instanceof p))throw new Error('CodeGen: "else" without "if"');return this._currNode=f.else=o,this}get _root(){return this._nodes[0]}get _currNode(){const o=this._nodes;return o[o.length-1]}set _currNode(o){const f=this._nodes;f[f.length-1]=o}}e.CodeGen=ne;function Q(_,o){for(const f in o)_[f]=(_[f]||0)+(o[f]||0);return _}function fe(_,o){return o instanceof t._CodeOrName?Q(_,o.names):_}function C(_,o,f){if(_ instanceof t.Name)return P(_);if(!j(_))return _;return new t._Code(_._items.reduce((A,q)=>(q instanceof t.Name&&(q=P(q)),q instanceof t._Code?A.push(...q._items):A.push(q),A),[]));function P(A){const q=f[A.str];return q===void 0||o[A.str]!==1?A:(delete o[A.str],q)}function j(A){return A instanceof t._Code&&A._items.some(q=>q instanceof t.Name&&o[q.str]===1&&f[q.str]!==void 0)}}function k(_,o){for(const f in o)_[f]=(_[f]||0)-(o[f]||0)}function U(_){return typeof _=="boolean"||typeof _=="number"||_===null?!_:(0,t._)`!${b(_)}`}e.not=U;const D=m(e.operators.AND);function R(..._){return _.reduce(D)}e.and=R;const I=m(e.operators.OR);function E(..._){return _.reduce(I)}e.or=E;function m(_){return(o,f)=>o===t.nil?f:f===t.nil?o:(0,t._)`${b(o)} ${_} ${b(f)}`}function b(_){return _ instanceof t.Name?_:(0,t._)`(${_})`}})(Z);var L={};Object.defineProperty(L,"__esModule",{value:!0});L.checkStrictMode=L.getErrorPath=L.Type=L.useFunc=L.setEvaluated=L.evaluatedPropsToName=L.mergeEvaluated=L.eachItem=L.unescapeJsonPointer=L.escapeJsonPointer=L.escapeFragment=L.unescapeFragment=L.schemaRefOrVal=L.schemaHasRulesButRef=L.schemaHasRules=L.checkUnknownRules=L.alwaysValidSchema=L.toHash=void 0;const le=Z,V$=Xr;function U$(e){const t={};for(const r of e)t[r]=!0;return t}L.toHash=U$;function z$(e,t){return typeof t=="boolean"?t:Object.keys(t).length===0?!0:(Xl(e,t),!Yl(t,e.self.RULES.all))}L.alwaysValidSchema=z$;function Xl(e,t=e.schema){const{opts:r,self:n}=e;if(!r.strictSchema||typeof t=="boolean")return;const s=n.RULES.keywords;for(const a in t)s[a]||xl(e,`unknown keyword: "${a}"`)}L.checkUnknownRules=Xl;function Yl(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(t[r])return!0;return!1}L.schemaHasRules=Yl;function q$(e,t){if(typeof e=="boolean")return!e;for(const r in e)if(r!=="$ref"&&t.all[r])return!0;return!1}L.schemaHasRulesButRef=q$;function K$({topSchemaRef:e,schemaPath:t},r,n,s){if(!s){if(typeof r=="number"||typeof r=="boolean")return r;if(typeof r=="string")return(0,le._)`${r}`}return(0,le._)`${e}${t}${(0,le.getProperty)(n)}`}L.schemaRefOrVal=K$;function G$(e){return Ql(decodeURIComponent(e))}L.unescapeFragment=G$;function H$(e){return encodeURIComponent(so(e))}L.escapeFragment=H$;function so(e){return typeof e=="number"?`${e}`:e.replace(/~/g,"~0").replace(/\//g,"~1")}L.escapeJsonPointer=so;function Ql(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}L.unescapeJsonPointer=Ql;function B$(e,t){if(Array.isArray(e))for(const r of e)t(r);else t(e)}L.eachItem=B$;function Mi({mergeNames:e,mergeToName:t,mergeValues:r,resultToName:n}){return(s,a,i,d)=>{const l=i===void 0?a:i instanceof le.Name?(a instanceof le.Name?e(s,a,i):t(s,a,i),i):a instanceof le.Name?(t(s,i,a),a):r(a,i);return d===le.Name&&!(l instanceof le.Name)?n(s,l):l}}L.mergeEvaluated={props:Mi({mergeNames:(e,t,r)=>e.if((0,le._)`${r} !== true && ${t} !== undefined`,()=>{e.if((0,le._)`${t} === true`,()=>e.assign(r,!0),()=>e.assign(r,(0,le._)`${r} || {}`).code((0,le._)`Object.assign(${r}, ${t})`))}),mergeToName:(e,t,r)=>e.if((0,le._)`${r} !== true`,()=>{t===!0?e.assign(r,!0):(e.assign(r,(0,le._)`${r} || {}`),ao(e,r,t))}),mergeValues:(e,t)=>e===!0?!0:{...e,...t},resultToName:Zl}),items:Mi({mergeNames:(e,t,r)=>e.if((0,le._)`${r} !== true && ${t} !== undefined`,()=>e.assign(r,(0,le._)`${t} === true ? true : ${r} > ${t} ? ${r} : ${t}`)),mergeToName:(e,t,r)=>e.if((0,le._)`${r} !== true`,()=>e.assign(r,t===!0?!0:(0,le._)`${r} > ${t} ? ${r} : ${t}`)),mergeValues:(e,t)=>e===!0?!0:Math.max(e,t),resultToName:(e,t)=>e.var("items",t)})};function Zl(e,t){if(t===!0)return e.var("props",!0);const r=e.var("props",(0,le._)`{}`);return t!==void 0&&ao(e,r,t),r}L.evaluatedPropsToName=Zl;function ao(e,t,r){Object.keys(r).forEach(n=>e.assign((0,le._)`${t}${(0,le.getProperty)(n)}`,!0))}L.setEvaluated=ao;const Li={};function W$(e,t){return e.scopeValue("func",{ref:t,code:Li[t.code]||(Li[t.code]=new V$._Code(t.code))})}L.useFunc=W$;var Hs;(function(e){e[e.Num=0]="Num",e[e.Str=1]="Str"})(Hs||(L.Type=Hs={}));function J$(e,t,r){if(e instanceof le.Name){const n=t===Hs.Num;return r?n?(0,le._)`"[" + ${e} + "]"`:(0,le._)`"['" + ${e} + "']"`:n?(0,le._)`"/" + ${e}`:(0,le._)`"/" + ${e}.replace(/~/g, "~0").replace(/\\//g, "~1")`}return r?(0,le.getProperty)(e).toString():"/"+so(e)}L.getErrorPath=J$;function xl(e,t,r=e.opts.strictSchema){if(r){if(t=`strict mode: ${t}`,r===!0)throw new Error(t);e.self.logger.warn(t)}}L.checkStrictMode=xl;var lt={};Object.defineProperty(lt,"__esModule",{value:!0});const Oe=Z,X$={data:new Oe.Name("data"),valCxt:new Oe.Name("valCxt"),instancePath:new Oe.Name("instancePath"),parentData:new Oe.Name("parentData"),parentDataProperty:new Oe.Name("parentDataProperty"),rootData:new Oe.Name("rootData"),dynamicAnchors:new Oe.Name("dynamicAnchors"),vErrors:new Oe.Name("vErrors"),errors:new Oe.Name("errors"),this:new Oe.Name("this"),self:new Oe.Name("self"),scope:new Oe.Name("scope"),json:new Oe.Name("json"),jsonPos:new Oe.Name("jsonPos"),jsonLen:new Oe.Name("jsonLen"),jsonPart:new Oe.Name("jsonPart")};lt.default=X$;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.extendErrors=e.resetErrorsCount=e.reportExtraError=e.reportError=e.keyword$DataError=e.keywordError=void 0;const t=Z,r=L,n=lt;e.keywordError={message:({keyword:$})=>(0,t.str)`must pass "${$}" keyword validation`},e.keyword$DataError={message:({keyword:$,schemaType:p})=>p?(0,t.str)`"${$}" keyword must be ${p} ($data)`:(0,t.str)`"${$}" keyword is invalid ($data)`};function s($,p=e.keywordError,w,N){const{it:O}=$,{gen:T,compositeRule:z,allErrors:B}=O,de=h($,p,w);N??(z||B)?l(T,de):u(O,(0,t._)`[${de}]`)}e.reportError=s;function a($,p=e.keywordError,w){const{it:N}=$,{gen:O,compositeRule:T,allErrors:z}=N,B=h($,p,w);l(O,B),T||z||u(N,n.default.vErrors)}e.reportExtraError=a;function i($,p){$.assign(n.default.errors,p),$.if((0,t._)`${n.default.vErrors} !== null`,()=>$.if(p,()=>$.assign((0,t._)`${n.default.vErrors}.length`,p),()=>$.assign(n.default.vErrors,null)))}e.resetErrorsCount=i;function d({gen:$,keyword:p,schemaValue:w,data:N,errsCount:O,it:T}){if(O===void 0)throw new Error("ajv implementation error");const z=$.name("err");$.forRange("i",O,n.default.errors,B=>{$.const(z,(0,t._)`${n.default.vErrors}[${B}]`),$.if((0,t._)`${z}.instancePath === undefined`,()=>$.assign((0,t._)`${z}.instancePath`,(0,t.strConcat)(n.default.instancePath,T.errorPath))),$.assign((0,t._)`${z}.schemaPath`,(0,t.str)`${T.errSchemaPath}/${p}`),T.opts.verbose&&($.assign((0,t._)`${z}.schema`,w),$.assign((0,t._)`${z}.data`,N))})}e.extendErrors=d;function l($,p){const w=$.const("err",p);$.if((0,t._)`${n.default.vErrors} === null`,()=>$.assign(n.default.vErrors,(0,t._)`[${w}]`),(0,t._)`${n.default.vErrors}.push(${w})`),$.code((0,t._)`${n.default.errors}++`)}function u($,p){const{gen:w,validateName:N,schemaEnv:O}=$;O.$async?w.throw((0,t._)`new ${$.ValidationError}(${p})`):(w.assign((0,t._)`${N}.errors`,p),w.return(!1))}const c={keyword:new t.Name("keyword"),schemaPath:new t.Name("schemaPath"),params:new t.Name("params"),propertyName:new t.Name("propertyName"),message:new t.Name("message"),schema:new t.Name("schema"),parentSchema:new t.Name("parentSchema")};function h($,p,w){const{createErrors:N}=$.it;return N===!1?(0,t._)`{}`:S($,p,w)}function S($,p,w={}){const{gen:N,it:O}=$,T=[y(O,w),v($,w)];return g($,p,T),N.object(...T)}function y({errorPath:$},{instancePath:p}){const w=p?(0,t.str)`${$}${(0,r.getErrorPath)(p,r.Type.Str)}`:$;return[n.default.instancePath,(0,t.strConcat)(n.default.instancePath,w)]}function v({keyword:$,it:{errSchemaPath:p}},{schemaPath:w,parentSchema:N}){let O=N?p:(0,t.str)`${p}/${$}`;return w&&(O=(0,t.str)`${O}${(0,r.getErrorPath)(w,r.Type.Str)}`),[c.schemaPath,O]}function g($,{params:p,message:w},N){const{keyword:O,data:T,schemaValue:z,it:B}=$,{opts:de,propertyName:V,topSchemaRef:H,schemaPath:ne}=B;N.push([c.keyword,O],[c.params,typeof p=="function"?p($):p||(0,t._)`{}`]),de.messages&&N.push([c.message,typeof w=="function"?w($):w]),de.verbose&&N.push([c.schema,z],[c.parentSchema,(0,t._)`${H}${ne}`],[n.default.data,T]),V&&N.push([c.propertyName,V])}})(xr);Object.defineProperty(_r,"__esModule",{value:!0});_r.boolOrEmptySchema=_r.topBoolOrEmptySchema=void 0;const Y$=xr,Q$=Z,Z$=lt,x$={message:"boolean schema is false"};function ey(e){const{gen:t,schema:r,validateName:n}=e;r===!1?eu(e,!1):typeof r=="object"&&r.$async===!0?t.return(Z$.default.data):(t.assign((0,Q$._)`${n}.errors`,null),t.return(!0))}_r.topBoolOrEmptySchema=ey;function ty(e,t){const{gen:r,schema:n}=e;n===!1?(r.var(t,!1),eu(e)):r.var(t,!0)}_r.boolOrEmptySchema=ty;function eu(e,t){const{gen:r,data:n}=e,s={gen:r,keyword:"false schema",data:n,schema:!1,schemaCode:!1,schemaValue:!1,params:{},it:e};(0,Y$.reportError)(s,x$,void 0,t)}var ge={},er={};Object.defineProperty(er,"__esModule",{value:!0});er.getRules=er.isJSONType=void 0;const ry=["string","number","integer","boolean","null","object","array"],ny=new Set(ry);function sy(e){return typeof e=="string"&&ny.has(e)}er.isJSONType=sy;function ay(){const e={number:{type:"number",rules:[]},string:{type:"string",rules:[]},array:{type:"array",rules:[]},object:{type:"object",rules:[]}};return{types:{...e,integer:!0,boolean:!0,null:!0},rules:[{rules:[]},e.number,e.string,e.array,e.object],post:{rules:[]},all:{},keywords:{}}}er.getRules=ay;var ft={};Object.defineProperty(ft,"__esModule",{value:!0});ft.shouldUseRule=ft.shouldUseGroup=ft.schemaHasRulesForType=void 0;function oy({schema:e,self:t},r){const n=t.RULES.types[r];return n&&n!==!0&&tu(e,n)}ft.schemaHasRulesForType=oy;function tu(e,t){return t.rules.some(r=>ru(e,r))}ft.shouldUseGroup=tu;function ru(e,t){var r;return e[t.keyword]!==void 0||((r=t.definition.implements)===null||r===void 0?void 0:r.some(n=>e[n]!==void 0))}ft.shouldUseRule=ru;Object.defineProperty(ge,"__esModule",{value:!0});ge.reportTypeError=ge.checkDataTypes=ge.checkDataType=ge.coerceAndCheckDataType=ge.getJSONTypes=ge.getSchemaTypes=ge.DataType=void 0;const iy=er,cy=ft,ly=xr,Y=Z,nu=L;var fr;(function(e){e[e.Correct=0]="Correct",e[e.Wrong=1]="Wrong"})(fr||(ge.DataType=fr={}));function uy(e){const t=su(e.type);if(t.includes("null")){if(e.nullable===!1)throw new Error("type: null contradicts nullable: false")}else{if(!t.length&&e.nullable!==void 0)throw new Error('"nullable" cannot be used without "type"');e.nullable===!0&&t.push("null")}return t}ge.getSchemaTypes=uy;function su(e){const t=Array.isArray(e)?e:e?[e]:[];if(t.every(iy.isJSONType))return t;throw new Error("type must be JSONType or JSONType[]: "+t.join(","))}ge.getJSONTypes=su;function dy(e,t){const{gen:r,data:n,opts:s}=e,a=fy(t,s.coerceTypes),i=t.length>0&&!(a.length===0&&t.length===1&&(0,cy.schemaHasRulesForType)(e,t[0]));if(i){const d=oo(t,n,s.strictNumbers,fr.Wrong);r.if(d,()=>{a.length?hy(e,t,a):io(e)})}return i}ge.coerceAndCheckDataType=dy;const au=new Set(["string","number","integer","boolean","null"]);function fy(e,t){return t?e.filter(r=>au.has(r)||t==="array"&&r==="array"):[]}function hy(e,t,r){const{gen:n,data:s,opts:a}=e,i=n.let("dataType",(0,Y._)`typeof ${s}`),d=n.let("coerced",(0,Y._)`undefined`);a.coerceTypes==="array"&&n.if((0,Y._)`${i} == 'object' && Array.isArray(${s}) && ${s}.length == 1`,()=>n.assign(s,(0,Y._)`${s}[0]`).assign(i,(0,Y._)`typeof ${s}`).if(oo(t,s,a.strictNumbers),()=>n.assign(d,s))),n.if((0,Y._)`${d} !== undefined`);for(const u of r)(au.has(u)||u==="array"&&a.coerceTypes==="array")&&l(u);n.else(),io(e),n.endIf(),n.if((0,Y._)`${d} !== undefined`,()=>{n.assign(s,d),py(e,d)});function l(u){switch(u){case"string":n.elseIf((0,Y._)`${i} == "number" || ${i} == "boolean"`).assign(d,(0,Y._)`"" + ${s}`).elseIf((0,Y._)`${s} === null`).assign(d,(0,Y._)`""`);return;case"number":n.elseIf((0,Y._)`${i} == "boolean" || ${s} === null
              || (${i} == "string" && ${s} && ${s} == +${s})`).assign(d,(0,Y._)`+${s}`);return;case"integer":n.elseIf((0,Y._)`${i} === "boolean" || ${s} === null
              || (${i} === "string" && ${s} && ${s} == +${s} && !(${s} % 1))`).assign(d,(0,Y._)`+${s}`);return;case"boolean":n.elseIf((0,Y._)`${s} === "false" || ${s} === 0 || ${s} === null`).assign(d,!1).elseIf((0,Y._)`${s} === "true" || ${s} === 1`).assign(d,!0);return;case"null":n.elseIf((0,Y._)`${s} === "" || ${s} === 0 || ${s} === false`),n.assign(d,null);return;case"array":n.elseIf((0,Y._)`${i} === "string" || ${i} === "number"
              || ${i} === "boolean" || ${s} === null`).assign(d,(0,Y._)`[${s}]`)}}}function py({gen:e,parentData:t,parentDataProperty:r},n){e.if((0,Y._)`${t} !== undefined`,()=>e.assign((0,Y._)`${t}[${r}]`,n))}function Bs(e,t,r,n=fr.Correct){const s=n===fr.Correct?Y.operators.EQ:Y.operators.NEQ;let a;switch(e){case"null":return(0,Y._)`${t} ${s} null`;case"array":a=(0,Y._)`Array.isArray(${t})`;break;case"object":a=(0,Y._)`${t} && typeof ${t} == "object" && !Array.isArray(${t})`;break;case"integer":a=i((0,Y._)`!(${t} % 1) && !isNaN(${t})`);break;case"number":a=i();break;default:return(0,Y._)`typeof ${t} ${s} ${e}`}return n===fr.Correct?a:(0,Y.not)(a);function i(d=Y.nil){return(0,Y.and)((0,Y._)`typeof ${t} == "number"`,d,r?(0,Y._)`isFinite(${t})`:Y.nil)}}ge.checkDataType=Bs;function oo(e,t,r,n){if(e.length===1)return Bs(e[0],t,r,n);let s;const a=(0,nu.toHash)(e);if(a.array&&a.object){const i=(0,Y._)`typeof ${t} != "object"`;s=a.null?i:(0,Y._)`!${t} || ${i}`,delete a.null,delete a.array,delete a.object}else s=Y.nil;a.number&&delete a.integer;for(const i in a)s=(0,Y.and)(s,Bs(i,t,r,n));return s}ge.checkDataTypes=oo;const my={message:({schema:e})=>`must be ${e}`,params:({schema:e,schemaValue:t})=>typeof e=="string"?(0,Y._)`{type: ${e}}`:(0,Y._)`{type: ${t}}`};function io(e){const t=$y(e);(0,ly.reportError)(t,my)}ge.reportTypeError=io;function $y(e){const{gen:t,data:r,schema:n}=e,s=(0,nu.schemaRefOrVal)(e,n,"type");return{gen:t,keyword:"type",data:r,schema:n.type,schemaCode:s,schemaValue:s,parentSchema:n,params:{},it:e}}var ts={};Object.defineProperty(ts,"__esModule",{value:!0});ts.assignDefaults=void 0;const sr=Z,yy=L;function _y(e,t){const{properties:r,items:n}=e.schema;if(t==="object"&&r)for(const s in r)Fi(e,s,r[s].default);else t==="array"&&Array.isArray(n)&&n.forEach((s,a)=>Fi(e,a,s.default))}ts.assignDefaults=_y;function Fi(e,t,r){const{gen:n,compositeRule:s,data:a,opts:i}=e;if(r===void 0)return;const d=(0,sr._)`${a}${(0,sr.getProperty)(t)}`;if(s){(0,yy.checkStrictMode)(e,`default is ignored for: ${d}`);return}let l=(0,sr._)`${d} === undefined`;i.useDefaults==="empty"&&(l=(0,sr._)`${l} || ${d} === null || ${d} === ""`),n.if(l,(0,sr._)`${d} = ${(0,sr.stringify)(r)}`)}var it={},ee={};Object.defineProperty(ee,"__esModule",{value:!0});ee.validateUnion=ee.validateArray=ee.usePattern=ee.callValidateCode=ee.schemaProperties=ee.allSchemaProperties=ee.noPropertyInData=ee.propertyInData=ee.isOwnProperty=ee.hasPropFunc=ee.reportMissingProp=ee.checkMissingProp=ee.checkReportMissingProp=void 0;const pe=Z,co=L,yt=lt,gy=L;function vy(e,t){const{gen:r,data:n,it:s}=e;r.if(uo(r,n,t,s.opts.ownProperties),()=>{e.setParams({missingProperty:(0,pe._)`${t}`},!0),e.error()})}ee.checkReportMissingProp=vy;function wy({gen:e,data:t,it:{opts:r}},n,s){return(0,pe.or)(...n.map(a=>(0,pe.and)(uo(e,t,a,r.ownProperties),(0,pe._)`${s} = ${a}`)))}ee.checkMissingProp=wy;function Ey(e,t){e.setParams({missingProperty:t},!0),e.error()}ee.reportMissingProp=Ey;function ou(e){return e.scopeValue("func",{ref:Object.prototype.hasOwnProperty,code:(0,pe._)`Object.prototype.hasOwnProperty`})}ee.hasPropFunc=ou;function lo(e,t,r){return(0,pe._)`${ou(e)}.call(${t}, ${r})`}ee.isOwnProperty=lo;function Sy(e,t,r,n){const s=(0,pe._)`${t}${(0,pe.getProperty)(r)} !== undefined`;return n?(0,pe._)`${s} && ${lo(e,t,r)}`:s}ee.propertyInData=Sy;function uo(e,t,r,n){const s=(0,pe._)`${t}${(0,pe.getProperty)(r)} === undefined`;return n?(0,pe.or)(s,(0,pe.not)(lo(e,t,r))):s}ee.noPropertyInData=uo;function iu(e){return e?Object.keys(e).filter(t=>t!=="__proto__"):[]}ee.allSchemaProperties=iu;function by(e,t){return iu(t).filter(r=>!(0,co.alwaysValidSchema)(e,t[r]))}ee.schemaProperties=by;function Py({schemaCode:e,data:t,it:{gen:r,topSchemaRef:n,schemaPath:s,errorPath:a},it:i},d,l,u){const c=u?(0,pe._)`${e}, ${t}, ${n}${s}`:t,h=[[yt.default.instancePath,(0,pe.strConcat)(yt.default.instancePath,a)],[yt.default.parentData,i.parentData],[yt.default.parentDataProperty,i.parentDataProperty],[yt.default.rootData,yt.default.rootData]];i.opts.dynamicRef&&h.push([yt.default.dynamicAnchors,yt.default.dynamicAnchors]);const S=(0,pe._)`${c}, ${r.object(...h)}`;return l!==pe.nil?(0,pe._)`${d}.call(${l}, ${S})`:(0,pe._)`${d}(${S})`}ee.callValidateCode=Py;const Ny=(0,pe._)`new RegExp`;function Oy({gen:e,it:{opts:t}},r){const n=t.unicodeRegExp?"u":"",{regExp:s}=t.code,a=s(r,n);return e.scopeValue("pattern",{key:a.toString(),ref:a,code:(0,pe._)`${s.code==="new RegExp"?Ny:(0,gy.useFunc)(e,s)}(${r}, ${n})`})}ee.usePattern=Oy;function Ry(e){const{gen:t,data:r,keyword:n,it:s}=e,a=t.name("valid");if(s.allErrors){const d=t.let("valid",!0);return i(()=>t.assign(d,!1)),d}return t.var(a,!0),i(()=>t.break()),a;function i(d){const l=t.const("len",(0,pe._)`${r}.length`);t.forRange("i",0,l,u=>{e.subschema({keyword:n,dataProp:u,dataPropType:co.Type.Num},a),t.if((0,pe.not)(a),d)})}}ee.validateArray=Ry;function Iy(e){const{gen:t,schema:r,keyword:n,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(r.some(l=>(0,co.alwaysValidSchema)(s,l))&&!s.opts.unevaluated)return;const i=t.let("valid",!1),d=t.name("_valid");t.block(()=>r.forEach((l,u)=>{const c=e.subschema({keyword:n,schemaProp:u,compositeRule:!0},d);t.assign(i,(0,pe._)`${i} || ${d}`),e.mergeValidEvaluated(c,d)||t.if((0,pe.not)(i))})),e.result(i,()=>e.reset(),()=>e.error(!0))}ee.validateUnion=Iy;Object.defineProperty(it,"__esModule",{value:!0});it.validateKeywordUsage=it.validSchemaType=it.funcKeywordCode=it.macroKeywordCode=void 0;const je=Z,Jt=lt,Ty=ee,jy=xr;function Ay(e,t){const{gen:r,keyword:n,schema:s,parentSchema:a,it:i}=e,d=t.macro.call(i.self,s,a,i),l=cu(r,n,d);i.opts.validateSchema!==!1&&i.self.validateSchema(d,!0);const u=r.name("valid");e.subschema({schema:d,schemaPath:je.nil,errSchemaPath:`${i.errSchemaPath}/${n}`,topSchemaRef:l,compositeRule:!0},u),e.pass(u,()=>e.error(!0))}it.macroKeywordCode=Ay;function ky(e,t){var r;const{gen:n,keyword:s,schema:a,parentSchema:i,$data:d,it:l}=e;Dy(l,t);const u=!d&&t.compile?t.compile.call(l.self,a,i,l):t.validate,c=cu(n,s,u),h=n.let("valid");e.block$data(h,S),e.ok((r=t.valid)!==null&&r!==void 0?r:h);function S(){if(t.errors===!1)g(),t.modifying&&Vi(e),$(()=>e.error());else{const p=t.async?y():v();t.modifying&&Vi(e),$(()=>Cy(e,p))}}function y(){const p=n.let("ruleErrs",null);return n.try(()=>g((0,je._)`await `),w=>n.assign(h,!1).if((0,je._)`${w} instanceof ${l.ValidationError}`,()=>n.assign(p,(0,je._)`${w}.errors`),()=>n.throw(w))),p}function v(){const p=(0,je._)`${c}.errors`;return n.assign(p,null),g(je.nil),p}function g(p=t.async?(0,je._)`await `:je.nil){const w=l.opts.passContext?Jt.default.this:Jt.default.self,N=!("compile"in t&&!d||t.schema===!1);n.assign(h,(0,je._)`${p}${(0,Ty.callValidateCode)(e,c,w,N)}`,t.modifying)}function $(p){var w;n.if((0,je.not)((w=t.valid)!==null&&w!==void 0?w:h),p)}}it.funcKeywordCode=ky;function Vi(e){const{gen:t,data:r,it:n}=e;t.if(n.parentData,()=>t.assign(r,(0,je._)`${n.parentData}[${n.parentDataProperty}]`))}function Cy(e,t){const{gen:r}=e;r.if((0,je._)`Array.isArray(${t})`,()=>{r.assign(Jt.default.vErrors,(0,je._)`${Jt.default.vErrors} === null ? ${t} : ${Jt.default.vErrors}.concat(${t})`).assign(Jt.default.errors,(0,je._)`${Jt.default.vErrors}.length`),(0,jy.extendErrors)(e)},()=>e.error())}function Dy({schemaEnv:e},t){if(t.async&&!e.$async)throw new Error("async keyword in sync schema")}function cu(e,t,r){if(r===void 0)throw new Error(`keyword "${t}" failed to compile`);return e.scopeValue("keyword",typeof r=="function"?{ref:r}:{ref:r,code:(0,je.stringify)(r)})}function My(e,t,r=!1){return!t.length||t.some(n=>n==="array"?Array.isArray(e):n==="object"?e&&typeof e=="object"&&!Array.isArray(e):typeof e==n||r&&typeof e>"u")}it.validSchemaType=My;function Ly({schema:e,opts:t,self:r,errSchemaPath:n},s,a){if(Array.isArray(s.keyword)?!s.keyword.includes(a):s.keyword!==a)throw new Error("ajv implementation error");const i=s.dependencies;if(i!=null&&i.some(d=>!Object.prototype.hasOwnProperty.call(e,d)))throw new Error(`parent schema must have dependencies of ${a}: ${i.join(",")}`);if(s.validateSchema&&!s.validateSchema(e[a])){const l=`keyword "${a}" value is invalid at path "${n}": `+r.errorsText(s.validateSchema.errors);if(t.validateSchema==="log")r.logger.error(l);else throw new Error(l)}}it.validateKeywordUsage=Ly;var Pt={};Object.defineProperty(Pt,"__esModule",{value:!0});Pt.extendSubschemaMode=Pt.extendSubschemaData=Pt.getSubschema=void 0;const nt=Z,lu=L;function Fy(e,{keyword:t,schemaProp:r,schema:n,schemaPath:s,errSchemaPath:a,topSchemaRef:i}){if(t!==void 0&&n!==void 0)throw new Error('both "keyword" and "schema" passed, only one allowed');if(t!==void 0){const d=e.schema[t];return r===void 0?{schema:d,schemaPath:(0,nt._)`${e.schemaPath}${(0,nt.getProperty)(t)}`,errSchemaPath:`${e.errSchemaPath}/${t}`}:{schema:d[r],schemaPath:(0,nt._)`${e.schemaPath}${(0,nt.getProperty)(t)}${(0,nt.getProperty)(r)}`,errSchemaPath:`${e.errSchemaPath}/${t}/${(0,lu.escapeFragment)(r)}`}}if(n!==void 0){if(s===void 0||a===void 0||i===void 0)throw new Error('"schemaPath", "errSchemaPath" and "topSchemaRef" are required with "schema"');return{schema:n,schemaPath:s,topSchemaRef:i,errSchemaPath:a}}throw new Error('either "keyword" or "schema" must be passed')}Pt.getSubschema=Fy;function Vy(e,t,{dataProp:r,dataPropType:n,data:s,dataTypes:a,propertyName:i}){if(s!==void 0&&r!==void 0)throw new Error('both "data" and "dataProp" passed, only one allowed');const{gen:d}=t;if(r!==void 0){const{errorPath:u,dataPathArr:c,opts:h}=t,S=d.let("data",(0,nt._)`${t.data}${(0,nt.getProperty)(r)}`,!0);l(S),e.errorPath=(0,nt.str)`${u}${(0,lu.getErrorPath)(r,n,h.jsPropertySyntax)}`,e.parentDataProperty=(0,nt._)`${r}`,e.dataPathArr=[...c,e.parentDataProperty]}if(s!==void 0){const u=s instanceof nt.Name?s:d.let("data",s,!0);l(u),i!==void 0&&(e.propertyName=i)}a&&(e.dataTypes=a);function l(u){e.data=u,e.dataLevel=t.dataLevel+1,e.dataTypes=[],t.definedProperties=new Set,e.parentData=t.data,e.dataNames=[...t.dataNames,u]}}Pt.extendSubschemaData=Vy;function Uy(e,{jtdDiscriminator:t,jtdMetadata:r,compositeRule:n,createErrors:s,allErrors:a}){n!==void 0&&(e.compositeRule=n),s!==void 0&&(e.createErrors=s),a!==void 0&&(e.allErrors=a),e.jtdDiscriminator=t,e.jtdMetadata=r}Pt.extendSubschemaMode=Uy;var Pe={},uu={exports:{}},St=uu.exports=function(e,t,r){typeof t=="function"&&(r=t,t={}),r=t.cb||r;var n=typeof r=="function"?r:r.pre||function(){},s=r.post||function(){};jn(t,n,s,e,"",e)};St.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0,if:!0,then:!0,else:!0};St.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0};St.propsKeywords={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependencies:!0};St.skipKeywords={default:!0,enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0};function jn(e,t,r,n,s,a,i,d,l,u){if(n&&typeof n=="object"&&!Array.isArray(n)){t(n,s,a,i,d,l,u);for(var c in n){var h=n[c];if(Array.isArray(h)){if(c in St.arrayKeywords)for(var S=0;S<h.length;S++)jn(e,t,r,h[S],s+"/"+c+"/"+S,a,s,c,n,S)}else if(c in St.propsKeywords){if(h&&typeof h=="object")for(var y in h)jn(e,t,r,h[y],s+"/"+c+"/"+zy(y),a,s,c,n,y)}else(c in St.keywords||e.allKeys&&!(c in St.skipKeywords))&&jn(e,t,r,h,s+"/"+c,a,s,c,n)}r(n,s,a,i,d,l,u)}}function zy(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}var qy=uu.exports;Object.defineProperty(Pe,"__esModule",{value:!0});Pe.getSchemaRefs=Pe.resolveUrl=Pe.normalizeId=Pe._getFullPath=Pe.getFullPath=Pe.inlineRef=void 0;const Ky=L,Gy=Xn,Hy=qy,By=new Set(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum","const"]);function Wy(e,t=!0){return typeof e=="boolean"?!0:t===!0?!Ws(e):t?du(e)<=t:!1}Pe.inlineRef=Wy;const Jy=new Set(["$ref","$recursiveRef","$recursiveAnchor","$dynamicRef","$dynamicAnchor"]);function Ws(e){for(const t in e){if(Jy.has(t))return!0;const r=e[t];if(Array.isArray(r)&&r.some(Ws)||typeof r=="object"&&Ws(r))return!0}return!1}function du(e){let t=0;for(const r in e){if(r==="$ref")return 1/0;if(t++,!By.has(r)&&(typeof e[r]=="object"&&(0,Ky.eachItem)(e[r],n=>t+=du(n)),t===1/0))return 1/0}return t}function fu(e,t="",r){r!==!1&&(t=hr(t));const n=e.parse(t);return hu(e,n)}Pe.getFullPath=fu;function hu(e,t){return e.serialize(t).split("#")[0]+"#"}Pe._getFullPath=hu;const Xy=/#\/?$/;function hr(e){return e?e.replace(Xy,""):""}Pe.normalizeId=hr;function Yy(e,t,r){return r=hr(r),e.resolve(t,r)}Pe.resolveUrl=Yy;const Qy=/^[a-z_][-a-z0-9._]*$/i;function Zy(e,t){if(typeof e=="boolean")return{};const{schemaId:r,uriResolver:n}=this.opts,s=hr(e[r]||t),a={"":s},i=fu(n,s,!1),d={},l=new Set;return Hy(e,{allKeys:!0},(h,S,y,v)=>{if(v===void 0)return;const g=i+S;let $=a[v];typeof h[r]=="string"&&($=p.call(this,h[r])),w.call(this,h.$anchor),w.call(this,h.$dynamicAnchor),a[S]=$;function p(N){const O=this.opts.uriResolver.resolve;if(N=hr($?O($,N):N),l.has(N))throw c(N);l.add(N);let T=this.refs[N];return typeof T=="string"&&(T=this.refs[T]),typeof T=="object"?u(h,T.schema,N):N!==hr(g)&&(N[0]==="#"?(u(h,d[N],N),d[N]=h):this.refs[N]=g),N}function w(N){if(typeof N=="string"){if(!Qy.test(N))throw new Error(`invalid anchor "${N}"`);p.call(this,`#${N}`)}}}),d;function u(h,S,y){if(S!==void 0&&!Gy(h,S))throw c(y)}function c(h){return new Error(`reference "${h}" resolves to more than one schema`)}}Pe.getSchemaRefs=Zy;Object.defineProperty(Ze,"__esModule",{value:!0});Ze.getData=Ze.KeywordCxt=Ze.validateFunctionCode=void 0;const pu=_r,Ui=ge,fo=ft,zn=ge,xy=ts,Kr=it,vs=Pt,G=Z,J=lt,e_=Pe,ht=L,Ar=xr;function t_(e){if(yu(e)&&(_u(e),$u(e))){s_(e);return}mu(e,()=>(0,pu.topBoolOrEmptySchema)(e))}Ze.validateFunctionCode=t_;function mu({gen:e,validateName:t,schema:r,schemaEnv:n,opts:s},a){s.code.es5?e.func(t,(0,G._)`${J.default.data}, ${J.default.valCxt}`,n.$async,()=>{e.code((0,G._)`"use strict"; ${zi(r,s)}`),n_(e,s),e.code(a)}):e.func(t,(0,G._)`${J.default.data}, ${r_(s)}`,n.$async,()=>e.code(zi(r,s)).code(a))}function r_(e){return(0,G._)`{${J.default.instancePath}="", ${J.default.parentData}, ${J.default.parentDataProperty}, ${J.default.rootData}=${J.default.data}${e.dynamicRef?(0,G._)`, ${J.default.dynamicAnchors}={}`:G.nil}}={}`}function n_(e,t){e.if(J.default.valCxt,()=>{e.var(J.default.instancePath,(0,G._)`${J.default.valCxt}.${J.default.instancePath}`),e.var(J.default.parentData,(0,G._)`${J.default.valCxt}.${J.default.parentData}`),e.var(J.default.parentDataProperty,(0,G._)`${J.default.valCxt}.${J.default.parentDataProperty}`),e.var(J.default.rootData,(0,G._)`${J.default.valCxt}.${J.default.rootData}`),t.dynamicRef&&e.var(J.default.dynamicAnchors,(0,G._)`${J.default.valCxt}.${J.default.dynamicAnchors}`)},()=>{e.var(J.default.instancePath,(0,G._)`""`),e.var(J.default.parentData,(0,G._)`undefined`),e.var(J.default.parentDataProperty,(0,G._)`undefined`),e.var(J.default.rootData,J.default.data),t.dynamicRef&&e.var(J.default.dynamicAnchors,(0,G._)`{}`)})}function s_(e){const{schema:t,opts:r,gen:n}=e;mu(e,()=>{r.$comment&&t.$comment&&vu(e),l_(e),n.let(J.default.vErrors,null),n.let(J.default.errors,0),r.unevaluated&&a_(e),gu(e),f_(e)})}function a_(e){const{gen:t,validateName:r}=e;e.evaluated=t.const("evaluated",(0,G._)`${r}.evaluated`),t.if((0,G._)`${e.evaluated}.dynamicProps`,()=>t.assign((0,G._)`${e.evaluated}.props`,(0,G._)`undefined`)),t.if((0,G._)`${e.evaluated}.dynamicItems`,()=>t.assign((0,G._)`${e.evaluated}.items`,(0,G._)`undefined`))}function zi(e,t){const r=typeof e=="object"&&e[t.schemaId];return r&&(t.code.source||t.code.process)?(0,G._)`/*# sourceURL=${r} */`:G.nil}function o_(e,t){if(yu(e)&&(_u(e),$u(e))){i_(e,t);return}(0,pu.boolOrEmptySchema)(e,t)}function $u({schema:e,self:t}){if(typeof e=="boolean")return!e;for(const r in e)if(t.RULES.all[r])return!0;return!1}function yu(e){return typeof e.schema!="boolean"}function i_(e,t){const{schema:r,gen:n,opts:s}=e;s.$comment&&r.$comment&&vu(e),u_(e),d_(e);const a=n.const("_errs",J.default.errors);gu(e,a),n.var(t,(0,G._)`${a} === ${J.default.errors}`)}function _u(e){(0,ht.checkUnknownRules)(e),c_(e)}function gu(e,t){if(e.opts.jtd)return qi(e,[],!1,t);const r=(0,Ui.getSchemaTypes)(e.schema),n=(0,Ui.coerceAndCheckDataType)(e,r);qi(e,r,!n,t)}function c_(e){const{schema:t,errSchemaPath:r,opts:n,self:s}=e;t.$ref&&n.ignoreKeywordsWithRef&&(0,ht.schemaHasRulesButRef)(t,s.RULES)&&s.logger.warn(`$ref: keywords ignored in schema at path "${r}"`)}function l_(e){const{schema:t,opts:r}=e;t.default!==void 0&&r.useDefaults&&r.strictSchema&&(0,ht.checkStrictMode)(e,"default is ignored in the schema root")}function u_(e){const t=e.schema[e.opts.schemaId];t&&(e.baseId=(0,e_.resolveUrl)(e.opts.uriResolver,e.baseId,t))}function d_(e){if(e.schema.$async&&!e.schemaEnv.$async)throw new Error("async schema in sync schema")}function vu({gen:e,schemaEnv:t,schema:r,errSchemaPath:n,opts:s}){const a=r.$comment;if(s.$comment===!0)e.code((0,G._)`${J.default.self}.logger.log(${a})`);else if(typeof s.$comment=="function"){const i=(0,G.str)`${n}/$comment`,d=e.scopeValue("root",{ref:t.root});e.code((0,G._)`${J.default.self}.opts.$comment(${a}, ${i}, ${d}.schema)`)}}function f_(e){const{gen:t,schemaEnv:r,validateName:n,ValidationError:s,opts:a}=e;r.$async?t.if((0,G._)`${J.default.errors} === 0`,()=>t.return(J.default.data),()=>t.throw((0,G._)`new ${s}(${J.default.vErrors})`)):(t.assign((0,G._)`${n}.errors`,J.default.vErrors),a.unevaluated&&h_(e),t.return((0,G._)`${J.default.errors} === 0`))}function h_({gen:e,evaluated:t,props:r,items:n}){r instanceof G.Name&&e.assign((0,G._)`${t}.props`,r),n instanceof G.Name&&e.assign((0,G._)`${t}.items`,n)}function qi(e,t,r,n){const{gen:s,schema:a,data:i,allErrors:d,opts:l,self:u}=e,{RULES:c}=u;if(a.$ref&&(l.ignoreKeywordsWithRef||!(0,ht.schemaHasRulesButRef)(a,c))){s.block(()=>Su(e,"$ref",c.all.$ref.definition));return}l.jtd||p_(e,t),s.block(()=>{for(const S of c.rules)h(S);h(c.post)});function h(S){(0,fo.shouldUseGroup)(a,S)&&(S.type?(s.if((0,zn.checkDataType)(S.type,i,l.strictNumbers)),Ki(e,S),t.length===1&&t[0]===S.type&&r&&(s.else(),(0,zn.reportTypeError)(e)),s.endIf()):Ki(e,S),d||s.if((0,G._)`${J.default.errors} === ${n||0}`))}}function Ki(e,t){const{gen:r,schema:n,opts:{useDefaults:s}}=e;s&&(0,xy.assignDefaults)(e,t.type),r.block(()=>{for(const a of t.rules)(0,fo.shouldUseRule)(n,a)&&Su(e,a.keyword,a.definition,t.type)})}function p_(e,t){e.schemaEnv.meta||!e.opts.strictTypes||(m_(e,t),e.opts.allowUnionTypes||$_(e,t),y_(e,e.dataTypes))}function m_(e,t){if(t.length){if(!e.dataTypes.length){e.dataTypes=t;return}t.forEach(r=>{wu(e.dataTypes,r)||ho(e,`type "${r}" not allowed by context "${e.dataTypes.join(",")}"`)}),g_(e,t)}}function $_(e,t){t.length>1&&!(t.length===2&&t.includes("null"))&&ho(e,"use allowUnionTypes to allow union type keyword")}function y_(e,t){const r=e.self.RULES.all;for(const n in r){const s=r[n];if(typeof s=="object"&&(0,fo.shouldUseRule)(e.schema,s)){const{type:a}=s.definition;a.length&&!a.some(i=>__(t,i))&&ho(e,`missing type "${a.join(",")}" for keyword "${n}"`)}}}function __(e,t){return e.includes(t)||t==="number"&&e.includes("integer")}function wu(e,t){return e.includes(t)||t==="integer"&&e.includes("number")}function g_(e,t){const r=[];for(const n of e.dataTypes)wu(t,n)?r.push(n):t.includes("integer")&&n==="number"&&r.push("integer");e.dataTypes=r}function ho(e,t){const r=e.schemaEnv.baseId+e.errSchemaPath;t+=` at "${r}" (strictTypes)`,(0,ht.checkStrictMode)(e,t,e.opts.strictTypes)}class Eu{constructor(t,r,n){if((0,Kr.validateKeywordUsage)(t,r,n),this.gen=t.gen,this.allErrors=t.allErrors,this.keyword=n,this.data=t.data,this.schema=t.schema[n],this.$data=r.$data&&t.opts.$data&&this.schema&&this.schema.$data,this.schemaValue=(0,ht.schemaRefOrVal)(t,this.schema,n,this.$data),this.schemaType=r.schemaType,this.parentSchema=t.schema,this.params={},this.it=t,this.def=r,this.$data)this.schemaCode=t.gen.const("vSchema",bu(this.$data,t));else if(this.schemaCode=this.schemaValue,!(0,Kr.validSchemaType)(this.schema,r.schemaType,r.allowUndefined))throw new Error(`${n} value must be ${JSON.stringify(r.schemaType)}`);("code"in r?r.trackErrors:r.errors!==!1)&&(this.errsCount=t.gen.const("_errs",J.default.errors))}result(t,r,n){this.failResult((0,G.not)(t),r,n)}failResult(t,r,n){this.gen.if(t),n?n():this.error(),r?(this.gen.else(),r(),this.allErrors&&this.gen.endIf()):this.allErrors?this.gen.endIf():this.gen.else()}pass(t,r){this.failResult((0,G.not)(t),void 0,r)}fail(t){if(t===void 0){this.error(),this.allErrors||this.gen.if(!1);return}this.gen.if(t),this.error(),this.allErrors?this.gen.endIf():this.gen.else()}fail$data(t){if(!this.$data)return this.fail(t);const{schemaCode:r}=this;this.fail((0,G._)`${r} !== undefined && (${(0,G.or)(this.invalid$data(),t)})`)}error(t,r,n){if(r){this.setParams(r),this._error(t,n),this.setParams({});return}this._error(t,n)}_error(t,r){(t?Ar.reportExtraError:Ar.reportError)(this,this.def.error,r)}$dataError(){(0,Ar.reportError)(this,this.def.$dataError||Ar.keyword$DataError)}reset(){if(this.errsCount===void 0)throw new Error('add "trackErrors" to keyword definition');(0,Ar.resetErrorsCount)(this.gen,this.errsCount)}ok(t){this.allErrors||this.gen.if(t)}setParams(t,r){r?Object.assign(this.params,t):this.params=t}block$data(t,r,n=G.nil){this.gen.block(()=>{this.check$data(t,n),r()})}check$data(t=G.nil,r=G.nil){if(!this.$data)return;const{gen:n,schemaCode:s,schemaType:a,def:i}=this;n.if((0,G.or)((0,G._)`${s} === undefined`,r)),t!==G.nil&&n.assign(t,!0),(a.length||i.validateSchema)&&(n.elseIf(this.invalid$data()),this.$dataError(),t!==G.nil&&n.assign(t,!1)),n.else()}invalid$data(){const{gen:t,schemaCode:r,schemaType:n,def:s,it:a}=this;return(0,G.or)(i(),d());function i(){if(n.length){if(!(r instanceof G.Name))throw new Error("ajv implementation error");const l=Array.isArray(n)?n:[n];return(0,G._)`${(0,zn.checkDataTypes)(l,r,a.opts.strictNumbers,zn.DataType.Wrong)}`}return G.nil}function d(){if(s.validateSchema){const l=t.scopeValue("validate$data",{ref:s.validateSchema});return(0,G._)`!${l}(${r})`}return G.nil}}subschema(t,r){const n=(0,vs.getSubschema)(this.it,t);(0,vs.extendSubschemaData)(n,this.it,t),(0,vs.extendSubschemaMode)(n,t);const s={...this.it,...n,items:void 0,props:void 0};return o_(s,r),s}mergeEvaluated(t,r){const{it:n,gen:s}=this;n.opts.unevaluated&&(n.props!==!0&&t.props!==void 0&&(n.props=ht.mergeEvaluated.props(s,t.props,n.props,r)),n.items!==!0&&t.items!==void 0&&(n.items=ht.mergeEvaluated.items(s,t.items,n.items,r)))}mergeValidEvaluated(t,r){const{it:n,gen:s}=this;if(n.opts.unevaluated&&(n.props!==!0||n.items!==!0))return s.if(r,()=>this.mergeEvaluated(t,G.Name)),!0}}Ze.KeywordCxt=Eu;function Su(e,t,r,n){const s=new Eu(e,r,t);"code"in r?r.code(s,n):s.$data&&r.validate?(0,Kr.funcKeywordCode)(s,r):"macro"in r?(0,Kr.macroKeywordCode)(s,r):(r.compile||r.validate)&&(0,Kr.funcKeywordCode)(s,r)}const v_=/^\/(?:[^~]|~0|~1)*$/,w_=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/;function bu(e,{dataLevel:t,dataNames:r,dataPathArr:n}){let s,a;if(e==="")return J.default.rootData;if(e[0]==="/"){if(!v_.test(e))throw new Error(`Invalid JSON-pointer: ${e}`);s=e,a=J.default.rootData}else{const u=w_.exec(e);if(!u)throw new Error(`Invalid JSON-pointer: ${e}`);const c=+u[1];if(s=u[2],s==="#"){if(c>=t)throw new Error(l("property/index",c));return n[t-c]}if(c>t)throw new Error(l("data",c));if(a=r[t-c],!s)return a}let i=a;const d=s.split("/");for(const u of d)u&&(a=(0,G._)`${a}${(0,G.getProperty)((0,ht.unescapeJsonPointer)(u))}`,i=(0,G._)`${i} && ${a}`);return i;function l(u,c){return`Cannot access ${u} ${c} levels up, current level is ${t}`}}Ze.getData=bu;var en={};Object.defineProperty(en,"__esModule",{value:!0});class E_ extends Error{constructor(t){super("validation failed"),this.errors=t,this.ajv=this.validation=!0}}en.default=E_;var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0});const ws=Pe;class S_ extends Error{constructor(t,r,n,s){super(s||`can't resolve reference ${n} from id ${r}`),this.missingRef=(0,ws.resolveUrl)(t,r,n),this.missingSchema=(0,ws.normalizeId)((0,ws.getFullPath)(t,this.missingRef))}}Sr.default=S_;var Fe={};Object.defineProperty(Fe,"__esModule",{value:!0});Fe.resolveSchema=Fe.getCompilingSchema=Fe.resolveRef=Fe.compileSchema=Fe.SchemaEnv=void 0;const Be=Z,b_=en,Kt=lt,Ye=Pe,Gi=L,P_=Ze;class rs{constructor(t){var r;this.refs={},this.dynamicAnchors={};let n;typeof t.schema=="object"&&(n=t.schema),this.schema=t.schema,this.schemaId=t.schemaId,this.root=t.root||this,this.baseId=(r=t.baseId)!==null&&r!==void 0?r:(0,Ye.normalizeId)(n==null?void 0:n[t.schemaId||"$id"]),this.schemaPath=t.schemaPath,this.localRefs=t.localRefs,this.meta=t.meta,this.$async=n==null?void 0:n.$async,this.refs={}}}Fe.SchemaEnv=rs;function po(e){const t=Pu.call(this,e);if(t)return t;const r=(0,Ye.getFullPath)(this.opts.uriResolver,e.root.baseId),{es5:n,lines:s}=this.opts.code,{ownProperties:a}=this.opts,i=new Be.CodeGen(this.scope,{es5:n,lines:s,ownProperties:a});let d;e.$async&&(d=i.scopeValue("Error",{ref:b_.default,code:(0,Be._)`require("ajv/dist/runtime/validation_error").default`}));const l=i.scopeName("validate");e.validateName=l;const u={gen:i,allErrors:this.opts.allErrors,data:Kt.default.data,parentData:Kt.default.parentData,parentDataProperty:Kt.default.parentDataProperty,dataNames:[Kt.default.data],dataPathArr:[Be.nil],dataLevel:0,dataTypes:[],definedProperties:new Set,topSchemaRef:i.scopeValue("schema",this.opts.code.source===!0?{ref:e.schema,code:(0,Be.stringify)(e.schema)}:{ref:e.schema}),validateName:l,ValidationError:d,schema:e.schema,schemaEnv:e,rootId:r,baseId:e.baseId||r,schemaPath:Be.nil,errSchemaPath:e.schemaPath||(this.opts.jtd?"":"#"),errorPath:(0,Be._)`""`,opts:this.opts,self:this};let c;try{this._compilations.add(e),(0,P_.validateFunctionCode)(u),i.optimize(this.opts.code.optimize);const h=i.toString();c=`${i.scopeRefs(Kt.default.scope)}return ${h}`,this.opts.code.process&&(c=this.opts.code.process(c,e));const y=new Function(`${Kt.default.self}`,`${Kt.default.scope}`,c)(this,this.scope.get());if(this.scope.value(l,{ref:y}),y.errors=null,y.schema=e.schema,y.schemaEnv=e,e.$async&&(y.$async=!0),this.opts.code.source===!0&&(y.source={validateName:l,validateCode:h,scopeValues:i._values}),this.opts.unevaluated){const{props:v,items:g}=u;y.evaluated={props:v instanceof Be.Name?void 0:v,items:g instanceof Be.Name?void 0:g,dynamicProps:v instanceof Be.Name,dynamicItems:g instanceof Be.Name},y.source&&(y.source.evaluated=(0,Be.stringify)(y.evaluated))}return e.validate=y,e}catch(h){throw delete e.validate,delete e.validateName,c&&this.logger.error("Error compiling schema, function code:",c),h}finally{this._compilations.delete(e)}}Fe.compileSchema=po;function N_(e,t,r){var n;r=(0,Ye.resolveUrl)(this.opts.uriResolver,t,r);const s=e.refs[r];if(s)return s;let a=I_.call(this,e,r);if(a===void 0){const i=(n=e.localRefs)===null||n===void 0?void 0:n[r],{schemaId:d}=this.opts;i&&(a=new rs({schema:i,schemaId:d,root:e,baseId:t}))}if(a!==void 0)return e.refs[r]=O_.call(this,a)}Fe.resolveRef=N_;function O_(e){return(0,Ye.inlineRef)(e.schema,this.opts.inlineRefs)?e.schema:e.validate?e:po.call(this,e)}function Pu(e){for(const t of this._compilations)if(R_(t,e))return t}Fe.getCompilingSchema=Pu;function R_(e,t){return e.schema===t.schema&&e.root===t.root&&e.baseId===t.baseId}function I_(e,t){let r;for(;typeof(r=this.refs[t])=="string";)t=r;return r||this.schemas[t]||ns.call(this,e,t)}function ns(e,t){const r=this.opts.uriResolver.parse(t),n=(0,Ye._getFullPath)(this.opts.uriResolver,r);let s=(0,Ye.getFullPath)(this.opts.uriResolver,e.baseId,void 0);if(Object.keys(e.schema).length>0&&n===s)return Es.call(this,r,e);const a=(0,Ye.normalizeId)(n),i=this.refs[a]||this.schemas[a];if(typeof i=="string"){const d=ns.call(this,e,i);return typeof(d==null?void 0:d.schema)!="object"?void 0:Es.call(this,r,d)}if(typeof(i==null?void 0:i.schema)=="object"){if(i.validate||po.call(this,i),a===(0,Ye.normalizeId)(t)){const{schema:d}=i,{schemaId:l}=this.opts,u=d[l];return u&&(s=(0,Ye.resolveUrl)(this.opts.uriResolver,s,u)),new rs({schema:d,schemaId:l,root:e,baseId:s})}return Es.call(this,r,i)}}Fe.resolveSchema=ns;const T_=new Set(["properties","patternProperties","enum","dependencies","definitions"]);function Es(e,{baseId:t,schema:r,root:n}){var s;if(((s=e.fragment)===null||s===void 0?void 0:s[0])!=="/")return;for(const d of e.fragment.slice(1).split("/")){if(typeof r=="boolean")return;const l=r[(0,Gi.unescapeFragment)(d)];if(l===void 0)return;r=l;const u=typeof r=="object"&&r[this.opts.schemaId];!T_.has(d)&&u&&(t=(0,Ye.resolveUrl)(this.opts.uriResolver,t,u))}let a;if(typeof r!="boolean"&&r.$ref&&!(0,Gi.schemaHasRulesButRef)(r,this.RULES)){const d=(0,Ye.resolveUrl)(this.opts.uriResolver,t,r.$ref);a=ns.call(this,n,d)}const{schemaId:i}=this.opts;if(a=a||new rs({schema:r,schemaId:i,root:n,baseId:t}),a.schema!==a.root.schema)return a}const j_="https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#",A_="Meta-schema for $data reference (JSON AnySchema extension proposal)",k_="object",C_=["$data"],D_={$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},M_=!1,L_={$id:j_,description:A_,type:k_,required:C_,properties:D_,additionalProperties:M_};var mo={};Object.defineProperty(mo,"__esModule",{value:!0});const Nu=Ml;Nu.code='require("ajv/dist/runtime/uri").default';mo.default=Nu;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.CodeGen=e.Name=e.nil=e.stringify=e.str=e._=e.KeywordCxt=void 0;var t=Ze;Object.defineProperty(e,"KeywordCxt",{enumerable:!0,get:function(){return t.KeywordCxt}});var r=Z;Object.defineProperty(e,"_",{enumerable:!0,get:function(){return r._}}),Object.defineProperty(e,"str",{enumerable:!0,get:function(){return r.str}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return r.stringify}}),Object.defineProperty(e,"nil",{enumerable:!0,get:function(){return r.nil}}),Object.defineProperty(e,"Name",{enumerable:!0,get:function(){return r.Name}}),Object.defineProperty(e,"CodeGen",{enumerable:!0,get:function(){return r.CodeGen}});const n=en,s=Sr,a=er,i=Fe,d=Z,l=Pe,u=ge,c=L,h=L_,S=mo,y=(E,m)=>new RegExp(E,m);y.code="new RegExp";const v=["removeAdditional","useDefaults","coerceTypes"],g=new Set(["validate","serialize","parse","wrapper","root","schema","keyword","pattern","formats","validate$data","func","obj","Error"]),$={errorDataPath:"",format:"`validateFormats: false` can be used instead.",nullable:'"nullable" keyword is supported by default.',jsonPointers:"Deprecated jsPropertySyntax can be used instead.",extendRefs:"Deprecated ignoreKeywordsWithRef can be used instead.",missingRefs:"Pass empty schema with $id that should be ignored to ajv.addSchema.",processCode:"Use option `code: {process: (code, schemaEnv: object) => string}`",sourceCode:"Use option `code: {source: true}`",strictDefaults:"It is default now, see option `strict`.",strictKeywords:"It is default now, see option `strict`.",uniqueItems:'"uniqueItems" keyword is always validated.',unknownFormats:"Disable strict mode or pass `true` to `ajv.addFormat` (or `formats` option).",cache:"Map is used as cache, schema object as key.",serialize:"Map is used as cache, schema object as key.",ajvErrors:"It is default now."},p={ignoreKeywordsWithRef:"",jsPropertySyntax:"",unicode:'"minLength"/"maxLength" account for unicode characters by default.'},w=200;function N(E){var m,b,_,o,f,P,j,A,q,F,re,Ve,Ot,Rt,It,Tt,jt,At,kt,Ct,Dt,Mt,Lt,Ft,Vt;const Ge=E.strict,Ut=(m=E.code)===null||m===void 0?void 0:m.optimize,Or=Ut===!0||Ut===void 0?1:Ut||0,Rr=(_=(b=E.code)===null||b===void 0?void 0:b.regExp)!==null&&_!==void 0?_:y,fs=(o=E.uriResolver)!==null&&o!==void 0?o:S.default;return{strictSchema:(P=(f=E.strictSchema)!==null&&f!==void 0?f:Ge)!==null&&P!==void 0?P:!0,strictNumbers:(A=(j=E.strictNumbers)!==null&&j!==void 0?j:Ge)!==null&&A!==void 0?A:!0,strictTypes:(F=(q=E.strictTypes)!==null&&q!==void 0?q:Ge)!==null&&F!==void 0?F:"log",strictTuples:(Ve=(re=E.strictTuples)!==null&&re!==void 0?re:Ge)!==null&&Ve!==void 0?Ve:"log",strictRequired:(Rt=(Ot=E.strictRequired)!==null&&Ot!==void 0?Ot:Ge)!==null&&Rt!==void 0?Rt:!1,code:E.code?{...E.code,optimize:Or,regExp:Rr}:{optimize:Or,regExp:Rr},loopRequired:(It=E.loopRequired)!==null&&It!==void 0?It:w,loopEnum:(Tt=E.loopEnum)!==null&&Tt!==void 0?Tt:w,meta:(jt=E.meta)!==null&&jt!==void 0?jt:!0,messages:(At=E.messages)!==null&&At!==void 0?At:!0,inlineRefs:(kt=E.inlineRefs)!==null&&kt!==void 0?kt:!0,schemaId:(Ct=E.schemaId)!==null&&Ct!==void 0?Ct:"$id",addUsedSchema:(Dt=E.addUsedSchema)!==null&&Dt!==void 0?Dt:!0,validateSchema:(Mt=E.validateSchema)!==null&&Mt!==void 0?Mt:!0,validateFormats:(Lt=E.validateFormats)!==null&&Lt!==void 0?Lt:!0,unicodeRegExp:(Ft=E.unicodeRegExp)!==null&&Ft!==void 0?Ft:!0,int32range:(Vt=E.int32range)!==null&&Vt!==void 0?Vt:!0,uriResolver:fs}}class O{constructor(m={}){this.schemas={},this.refs={},this.formats={},this._compilations=new Set,this._loading={},this._cache=new Map,m=this.opts={...m,...N(m)};const{es5:b,lines:_}=this.opts.code;this.scope=new d.ValueScope({scope:{},prefixes:g,es5:b,lines:_}),this.logger=Q(m.logger);const o=m.validateFormats;m.validateFormats=!1,this.RULES=(0,a.getRules)(),T.call(this,$,m,"NOT SUPPORTED"),T.call(this,p,m,"DEPRECATED","warn"),this._metaOpts=H.call(this),m.formats&&de.call(this),this._addVocabularies(),this._addDefaultMetaSchema(),m.keywords&&V.call(this,m.keywords),typeof m.meta=="object"&&this.addMetaSchema(m.meta),B.call(this),m.validateFormats=o}_addVocabularies(){this.addKeyword("$async")}_addDefaultMetaSchema(){const{$data:m,meta:b,schemaId:_}=this.opts;let o=h;_==="id"&&(o={...h},o.id=o.$id,delete o.$id),b&&m&&this.addMetaSchema(o,o[_],!1)}defaultMeta(){const{meta:m,schemaId:b}=this.opts;return this.opts.defaultMeta=typeof m=="object"?m[b]||m:void 0}validate(m,b){let _;if(typeof m=="string"){if(_=this.getSchema(m),!_)throw new Error(`no schema with key or ref "${m}"`)}else _=this.compile(m);const o=_(b);return"$async"in _||(this.errors=_.errors),o}compile(m,b){const _=this._addSchema(m,b);return _.validate||this._compileSchemaEnv(_)}compileAsync(m,b){if(typeof this.opts.loadSchema!="function")throw new Error("options.loadSchema should be a function");const{loadSchema:_}=this.opts;return o.call(this,m,b);async function o(F,re){await f.call(this,F.$schema);const Ve=this._addSchema(F,re);return Ve.validate||P.call(this,Ve)}async function f(F){F&&!this.getSchema(F)&&await o.call(this,{$ref:F},!0)}async function P(F){try{return this._compileSchemaEnv(F)}catch(re){if(!(re instanceof s.default))throw re;return j.call(this,re),await A.call(this,re.missingSchema),P.call(this,F)}}function j({missingSchema:F,missingRef:re}){if(this.refs[F])throw new Error(`AnySchema ${F} is loaded but ${re} cannot be resolved`)}async function A(F){const re=await q.call(this,F);this.refs[F]||await f.call(this,re.$schema),this.refs[F]||this.addSchema(re,F,b)}async function q(F){const re=this._loading[F];if(re)return re;try{return await(this._loading[F]=_(F))}finally{delete this._loading[F]}}}addSchema(m,b,_,o=this.opts.validateSchema){if(Array.isArray(m)){for(const P of m)this.addSchema(P,void 0,_,o);return this}let f;if(typeof m=="object"){const{schemaId:P}=this.opts;if(f=m[P],f!==void 0&&typeof f!="string")throw new Error(`schema ${P} must be string`)}return b=(0,l.normalizeId)(b||f),this._checkUnique(b),this.schemas[b]=this._addSchema(m,_,b,o,!0),this}addMetaSchema(m,b,_=this.opts.validateSchema){return this.addSchema(m,b,!0,_),this}validateSchema(m,b){if(typeof m=="boolean")return!0;let _;if(_=m.$schema,_!==void 0&&typeof _!="string")throw new Error("$schema must be a string");if(_=_||this.opts.defaultMeta||this.defaultMeta(),!_)return this.logger.warn("meta-schema not available"),this.errors=null,!0;const o=this.validate(_,m);if(!o&&b){const f="schema is invalid: "+this.errorsText();if(this.opts.validateSchema==="log")this.logger.error(f);else throw new Error(f)}return o}getSchema(m){let b;for(;typeof(b=z.call(this,m))=="string";)m=b;if(b===void 0){const{schemaId:_}=this.opts,o=new i.SchemaEnv({schema:{},schemaId:_});if(b=i.resolveSchema.call(this,o,m),!b)return;this.refs[m]=b}return b.validate||this._compileSchemaEnv(b)}removeSchema(m){if(m instanceof RegExp)return this._removeAllSchemas(this.schemas,m),this._removeAllSchemas(this.refs,m),this;switch(typeof m){case"undefined":return this._removeAllSchemas(this.schemas),this._removeAllSchemas(this.refs),this._cache.clear(),this;case"string":{const b=z.call(this,m);return typeof b=="object"&&this._cache.delete(b.schema),delete this.schemas[m],delete this.refs[m],this}case"object":{const b=m;this._cache.delete(b);let _=m[this.opts.schemaId];return _&&(_=(0,l.normalizeId)(_),delete this.schemas[_],delete this.refs[_]),this}default:throw new Error("ajv.removeSchema: invalid parameter")}}addVocabulary(m){for(const b of m)this.addKeyword(b);return this}addKeyword(m,b){let _;if(typeof m=="string")_=m,typeof b=="object"&&(this.logger.warn("these parameters are deprecated, see docs for addKeyword"),b.keyword=_);else if(typeof m=="object"&&b===void 0){if(b=m,_=b.keyword,Array.isArray(_)&&!_.length)throw new Error("addKeywords: keyword must be string or non-empty array")}else throw new Error("invalid addKeywords parameters");if(C.call(this,_,b),!b)return(0,c.eachItem)(_,f=>k.call(this,f)),this;D.call(this,b);const o={...b,type:(0,u.getJSONTypes)(b.type),schemaType:(0,u.getJSONTypes)(b.schemaType)};return(0,c.eachItem)(_,o.type.length===0?f=>k.call(this,f,o):f=>o.type.forEach(P=>k.call(this,f,o,P))),this}getKeyword(m){const b=this.RULES.all[m];return typeof b=="object"?b.definition:!!b}removeKeyword(m){const{RULES:b}=this;delete b.keywords[m],delete b.all[m];for(const _ of b.rules){const o=_.rules.findIndex(f=>f.keyword===m);o>=0&&_.rules.splice(o,1)}return this}addFormat(m,b){return typeof b=="string"&&(b=new RegExp(b)),this.formats[m]=b,this}errorsText(m=this.errors,{separator:b=", ",dataVar:_="data"}={}){return!m||m.length===0?"No errors":m.map(o=>`${_}${o.instancePath} ${o.message}`).reduce((o,f)=>o+b+f)}$dataMetaSchema(m,b){const _=this.RULES.all;m=JSON.parse(JSON.stringify(m));for(const o of b){const f=o.split("/").slice(1);let P=m;for(const j of f)P=P[j];for(const j in _){const A=_[j];if(typeof A!="object")continue;const{$data:q}=A.definition,F=P[j];q&&F&&(P[j]=I(F))}}return m}_removeAllSchemas(m,b){for(const _ in m){const o=m[_];(!b||b.test(_))&&(typeof o=="string"?delete m[_]:o&&!o.meta&&(this._cache.delete(o.schema),delete m[_]))}}_addSchema(m,b,_,o=this.opts.validateSchema,f=this.opts.addUsedSchema){let P;const{schemaId:j}=this.opts;if(typeof m=="object")P=m[j];else{if(this.opts.jtd)throw new Error("schema must be object");if(typeof m!="boolean")throw new Error("schema must be object or boolean")}let A=this._cache.get(m);if(A!==void 0)return A;_=(0,l.normalizeId)(P||_);const q=l.getSchemaRefs.call(this,m,_);return A=new i.SchemaEnv({schema:m,schemaId:j,meta:b,baseId:_,localRefs:q}),this._cache.set(A.schema,A),f&&!_.startsWith("#")&&(_&&this._checkUnique(_),this.refs[_]=A),o&&this.validateSchema(m,!0),A}_checkUnique(m){if(this.schemas[m]||this.refs[m])throw new Error(`schema with key or id "${m}" already exists`)}_compileSchemaEnv(m){if(m.meta?this._compileMetaSchema(m):i.compileSchema.call(this,m),!m.validate)throw new Error("ajv implementation error");return m.validate}_compileMetaSchema(m){const b=this.opts;this.opts=this._metaOpts;try{i.compileSchema.call(this,m)}finally{this.opts=b}}}O.ValidationError=n.default,O.MissingRefError=s.default,e.default=O;function T(E,m,b,_="error"){for(const o in E){const f=o;f in m&&this.logger[_](`${b}: option ${o}. ${E[f]}`)}}function z(E){return E=(0,l.normalizeId)(E),this.schemas[E]||this.refs[E]}function B(){const E=this.opts.schemas;if(E)if(Array.isArray(E))this.addSchema(E);else for(const m in E)this.addSchema(E[m],m)}function de(){for(const E in this.opts.formats){const m=this.opts.formats[E];m&&this.addFormat(E,m)}}function V(E){if(Array.isArray(E)){this.addVocabulary(E);return}this.logger.warn("keywords option as map is deprecated, pass array");for(const m in E){const b=E[m];b.keyword||(b.keyword=m),this.addKeyword(b)}}function H(){const E={...this.opts};for(const m of v)delete E[m];return E}const ne={log(){},warn(){},error(){}};function Q(E){if(E===!1)return ne;if(E===void 0)return console;if(E.log&&E.warn&&E.error)return E;throw new Error("logger must implement log, warn and error methods")}const fe=/^[a-z_$][a-z0-9_$:-]*$/i;function C(E,m){const{RULES:b}=this;if((0,c.eachItem)(E,_=>{if(b.keywords[_])throw new Error(`Keyword ${_} is already defined`);if(!fe.test(_))throw new Error(`Keyword ${_} has invalid name`)}),!!m&&m.$data&&!("code"in m||"validate"in m))throw new Error('$data keyword must have "code" or "validate" function')}function k(E,m,b){var _;const o=m==null?void 0:m.post;if(b&&o)throw new Error('keyword with "post" flag cannot have "type"');const{RULES:f}=this;let P=o?f.post:f.rules.find(({type:A})=>A===b);if(P||(P={type:b,rules:[]},f.rules.push(P)),f.keywords[E]=!0,!m)return;const j={keyword:E,definition:{...m,type:(0,u.getJSONTypes)(m.type),schemaType:(0,u.getJSONTypes)(m.schemaType)}};m.before?U.call(this,P,j,m.before):P.rules.push(j),f.all[E]=j,(_=m.implements)===null||_===void 0||_.forEach(A=>this.addKeyword(A))}function U(E,m,b){const _=E.rules.findIndex(o=>o.keyword===b);_>=0?E.rules.splice(_,0,m):(E.rules.push(m),this.logger.warn(`rule ${b} is not defined`))}function D(E){let{metaSchema:m}=E;m!==void 0&&(E.$data&&this.opts.$data&&(m=I(m)),E.validateSchema=this.compile(m,!0))}const R={$ref:"https://raw.githubusercontent.com/ajv-validator/ajv/master/lib/refs/data.json#"};function I(E){return{anyOf:[E,R]}}})(Jl);var $o={},yo={},_o={};Object.defineProperty(_o,"__esModule",{value:!0});const F_={keyword:"id",code(){throw new Error('NOT SUPPORTED: keyword "id", use "$id" for schema ID')}};_o.default=F_;var tr={};Object.defineProperty(tr,"__esModule",{value:!0});tr.callRef=tr.getValidate=void 0;const V_=Sr,Hi=ee,Me=Z,ar=lt,Bi=Fe,$n=L,U_={keyword:"$ref",schemaType:"string",code(e){const{gen:t,schema:r,it:n}=e,{baseId:s,schemaEnv:a,validateName:i,opts:d,self:l}=n,{root:u}=a;if((r==="#"||r==="#/")&&s===u.baseId)return h();const c=Bi.resolveRef.call(l,u,s,r);if(c===void 0)throw new V_.default(n.opts.uriResolver,s,r);if(c instanceof Bi.SchemaEnv)return S(c);return y(c);function h(){if(a===u)return An(e,i,a,a.$async);const v=t.scopeValue("root",{ref:u});return An(e,(0,Me._)`${v}.validate`,u,u.$async)}function S(v){const g=Ou(e,v);An(e,g,v,v.$async)}function y(v){const g=t.scopeValue("schema",d.code.source===!0?{ref:v,code:(0,Me.stringify)(v)}:{ref:v}),$=t.name("valid"),p=e.subschema({schema:v,dataTypes:[],schemaPath:Me.nil,topSchemaRef:g,errSchemaPath:r},$);e.mergeEvaluated(p),e.ok($)}}};function Ou(e,t){const{gen:r}=e;return t.validate?r.scopeValue("validate",{ref:t.validate}):(0,Me._)`${r.scopeValue("wrapper",{ref:t})}.validate`}tr.getValidate=Ou;function An(e,t,r,n){const{gen:s,it:a}=e,{allErrors:i,schemaEnv:d,opts:l}=a,u=l.passContext?ar.default.this:Me.nil;n?c():h();function c(){if(!d.$async)throw new Error("async schema referenced by sync schema");const v=s.let("valid");s.try(()=>{s.code((0,Me._)`await ${(0,Hi.callValidateCode)(e,t,u)}`),y(t),i||s.assign(v,!0)},g=>{s.if((0,Me._)`!(${g} instanceof ${a.ValidationError})`,()=>s.throw(g)),S(g),i||s.assign(v,!1)}),e.ok(v)}function h(){e.result((0,Hi.callValidateCode)(e,t,u),()=>y(t),()=>S(t))}function S(v){const g=(0,Me._)`${v}.errors`;s.assign(ar.default.vErrors,(0,Me._)`${ar.default.vErrors} === null ? ${g} : ${ar.default.vErrors}.concat(${g})`),s.assign(ar.default.errors,(0,Me._)`${ar.default.vErrors}.length`)}function y(v){var g;if(!a.opts.unevaluated)return;const $=(g=r==null?void 0:r.validate)===null||g===void 0?void 0:g.evaluated;if(a.props!==!0)if($&&!$.dynamicProps)$.props!==void 0&&(a.props=$n.mergeEvaluated.props(s,$.props,a.props));else{const p=s.var("props",(0,Me._)`${v}.evaluated.props`);a.props=$n.mergeEvaluated.props(s,p,a.props,Me.Name)}if(a.items!==!0)if($&&!$.dynamicItems)$.items!==void 0&&(a.items=$n.mergeEvaluated.items(s,$.items,a.items));else{const p=s.var("items",(0,Me._)`${v}.evaluated.items`);a.items=$n.mergeEvaluated.items(s,p,a.items,Me.Name)}}}tr.callRef=An;tr.default=U_;Object.defineProperty(yo,"__esModule",{value:!0});const z_=_o,q_=tr,K_=["$schema","$id","$defs","$vocabulary",{keyword:"$comment"},"definitions",z_.default,q_.default];yo.default=K_;var go={},vo={};Object.defineProperty(vo,"__esModule",{value:!0});const qn=Z,_t=qn.operators,Kn={maximum:{okStr:"<=",ok:_t.LTE,fail:_t.GT},minimum:{okStr:">=",ok:_t.GTE,fail:_t.LT},exclusiveMaximum:{okStr:"<",ok:_t.LT,fail:_t.GTE},exclusiveMinimum:{okStr:">",ok:_t.GT,fail:_t.LTE}},G_={message:({keyword:e,schemaCode:t})=>(0,qn.str)`must be ${Kn[e].okStr} ${t}`,params:({keyword:e,schemaCode:t})=>(0,qn._)`{comparison: ${Kn[e].okStr}, limit: ${t}}`},H_={keyword:Object.keys(Kn),type:"number",schemaType:"number",$data:!0,error:G_,code(e){const{keyword:t,data:r,schemaCode:n}=e;e.fail$data((0,qn._)`${r} ${Kn[t].fail} ${n} || isNaN(${r})`)}};vo.default=H_;var wo={};Object.defineProperty(wo,"__esModule",{value:!0});const Gr=Z,B_={message:({schemaCode:e})=>(0,Gr.str)`must be multiple of ${e}`,params:({schemaCode:e})=>(0,Gr._)`{multipleOf: ${e}}`},W_={keyword:"multipleOf",type:"number",schemaType:"number",$data:!0,error:B_,code(e){const{gen:t,data:r,schemaCode:n,it:s}=e,a=s.opts.multipleOfPrecision,i=t.let("res"),d=a?(0,Gr._)`Math.abs(Math.round(${i}) - ${i}) > 1e-${a}`:(0,Gr._)`${i} !== parseInt(${i})`;e.fail$data((0,Gr._)`(${n} === 0 || (${i} = ${r}/${n}, ${d}))`)}};wo.default=W_;var Eo={},So={};Object.defineProperty(So,"__esModule",{value:!0});function Ru(e){const t=e.length;let r=0,n=0,s;for(;n<t;)r++,s=e.charCodeAt(n++),s>=55296&&s<=56319&&n<t&&(s=e.charCodeAt(n),(s&64512)===56320&&n++);return r}So.default=Ru;Ru.code='require("ajv/dist/runtime/ucs2length").default';Object.defineProperty(Eo,"__esModule",{value:!0});const Xt=Z,J_=L,X_=So,Y_={message({keyword:e,schemaCode:t}){const r=e==="maxLength"?"more":"fewer";return(0,Xt.str)`must NOT have ${r} than ${t} characters`},params:({schemaCode:e})=>(0,Xt._)`{limit: ${e}}`},Q_={keyword:["maxLength","minLength"],type:"string",schemaType:"number",$data:!0,error:Y_,code(e){const{keyword:t,data:r,schemaCode:n,it:s}=e,a=t==="maxLength"?Xt.operators.GT:Xt.operators.LT,i=s.opts.unicode===!1?(0,Xt._)`${r}.length`:(0,Xt._)`${(0,J_.useFunc)(e.gen,X_.default)}(${r})`;e.fail$data((0,Xt._)`${i} ${a} ${n}`)}};Eo.default=Q_;var bo={};Object.defineProperty(bo,"__esModule",{value:!0});const Z_=ee,Gn=Z,x_={message:({schemaCode:e})=>(0,Gn.str)`must match pattern "${e}"`,params:({schemaCode:e})=>(0,Gn._)`{pattern: ${e}}`},eg={keyword:"pattern",type:"string",schemaType:"string",$data:!0,error:x_,code(e){const{data:t,$data:r,schema:n,schemaCode:s,it:a}=e,i=a.opts.unicodeRegExp?"u":"",d=r?(0,Gn._)`(new RegExp(${s}, ${i}))`:(0,Z_.usePattern)(e,n);e.fail$data((0,Gn._)`!${d}.test(${t})`)}};bo.default=eg;var Po={};Object.defineProperty(Po,"__esModule",{value:!0});const Hr=Z,tg={message({keyword:e,schemaCode:t}){const r=e==="maxProperties"?"more":"fewer";return(0,Hr.str)`must NOT have ${r} than ${t} properties`},params:({schemaCode:e})=>(0,Hr._)`{limit: ${e}}`},rg={keyword:["maxProperties","minProperties"],type:"object",schemaType:"number",$data:!0,error:tg,code(e){const{keyword:t,data:r,schemaCode:n}=e,s=t==="maxProperties"?Hr.operators.GT:Hr.operators.LT;e.fail$data((0,Hr._)`Object.keys(${r}).length ${s} ${n}`)}};Po.default=rg;var No={};Object.defineProperty(No,"__esModule",{value:!0});const kr=ee,Br=Z,ng=L,sg={message:({params:{missingProperty:e}})=>(0,Br.str)`must have required property '${e}'`,params:({params:{missingProperty:e}})=>(0,Br._)`{missingProperty: ${e}}`},ag={keyword:"required",type:"object",schemaType:"array",$data:!0,error:sg,code(e){const{gen:t,schema:r,schemaCode:n,data:s,$data:a,it:i}=e,{opts:d}=i;if(!a&&r.length===0)return;const l=r.length>=d.loopRequired;if(i.allErrors?u():c(),d.strictRequired){const y=e.parentSchema.properties,{definedProperties:v}=e.it;for(const g of r)if((y==null?void 0:y[g])===void 0&&!v.has(g)){const $=i.schemaEnv.baseId+i.errSchemaPath,p=`required property "${g}" is not defined at "${$}" (strictRequired)`;(0,ng.checkStrictMode)(i,p,i.opts.strictRequired)}}function u(){if(l||a)e.block$data(Br.nil,h);else for(const y of r)(0,kr.checkReportMissingProp)(e,y)}function c(){const y=t.let("missing");if(l||a){const v=t.let("valid",!0);e.block$data(v,()=>S(y,v)),e.ok(v)}else t.if((0,kr.checkMissingProp)(e,r,y)),(0,kr.reportMissingProp)(e,y),t.else()}function h(){t.forOf("prop",n,y=>{e.setParams({missingProperty:y}),t.if((0,kr.noPropertyInData)(t,s,y,d.ownProperties),()=>e.error())})}function S(y,v){e.setParams({missingProperty:y}),t.forOf(y,n,()=>{t.assign(v,(0,kr.propertyInData)(t,s,y,d.ownProperties)),t.if((0,Br.not)(v),()=>{e.error(),t.break()})},Br.nil)}}};No.default=ag;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0});const Wr=Z,og={message({keyword:e,schemaCode:t}){const r=e==="maxItems"?"more":"fewer";return(0,Wr.str)`must NOT have ${r} than ${t} items`},params:({schemaCode:e})=>(0,Wr._)`{limit: ${e}}`},ig={keyword:["maxItems","minItems"],type:"array",schemaType:"number",$data:!0,error:og,code(e){const{keyword:t,data:r,schemaCode:n}=e,s=t==="maxItems"?Wr.operators.GT:Wr.operators.LT;e.fail$data((0,Wr._)`${r}.length ${s} ${n}`)}};Oo.default=ig;var Ro={},tn={};Object.defineProperty(tn,"__esModule",{value:!0});const Iu=Xn;Iu.code='require("ajv/dist/runtime/equal").default';tn.default=Iu;Object.defineProperty(Ro,"__esModule",{value:!0});const Ss=ge,Ee=Z,cg=L,lg=tn,ug={message:({params:{i:e,j:t}})=>(0,Ee.str)`must NOT have duplicate items (items ## ${t} and ${e} are identical)`,params:({params:{i:e,j:t}})=>(0,Ee._)`{i: ${e}, j: ${t}}`},dg={keyword:"uniqueItems",type:"array",schemaType:"boolean",$data:!0,error:ug,code(e){const{gen:t,data:r,$data:n,schema:s,parentSchema:a,schemaCode:i,it:d}=e;if(!n&&!s)return;const l=t.let("valid"),u=a.items?(0,Ss.getSchemaTypes)(a.items):[];e.block$data(l,c,(0,Ee._)`${i} === false`),e.ok(l);function c(){const v=t.let("i",(0,Ee._)`${r}.length`),g=t.let("j");e.setParams({i:v,j:g}),t.assign(l,!0),t.if((0,Ee._)`${v} > 1`,()=>(h()?S:y)(v,g))}function h(){return u.length>0&&!u.some(v=>v==="object"||v==="array")}function S(v,g){const $=t.name("item"),p=(0,Ss.checkDataTypes)(u,$,d.opts.strictNumbers,Ss.DataType.Wrong),w=t.const("indices",(0,Ee._)`{}`);t.for((0,Ee._)`;${v}--;`,()=>{t.let($,(0,Ee._)`${r}[${v}]`),t.if(p,(0,Ee._)`continue`),u.length>1&&t.if((0,Ee._)`typeof ${$} == "string"`,(0,Ee._)`${$} += "_"`),t.if((0,Ee._)`typeof ${w}[${$}] == "number"`,()=>{t.assign(g,(0,Ee._)`${w}[${$}]`),e.error(),t.assign(l,!1).break()}).code((0,Ee._)`${w}[${$}] = ${v}`)})}function y(v,g){const $=(0,cg.useFunc)(t,lg.default),p=t.name("outer");t.label(p).for((0,Ee._)`;${v}--;`,()=>t.for((0,Ee._)`${g} = ${v}; ${g}--;`,()=>t.if((0,Ee._)`${$}(${r}[${v}], ${r}[${g}])`,()=>{e.error(),t.assign(l,!1).break(p)})))}}};Ro.default=dg;var Io={};Object.defineProperty(Io,"__esModule",{value:!0});const Js=Z,fg=L,hg=tn,pg={message:"must be equal to constant",params:({schemaCode:e})=>(0,Js._)`{allowedValue: ${e}}`},mg={keyword:"const",$data:!0,error:pg,code(e){const{gen:t,data:r,$data:n,schemaCode:s,schema:a}=e;n||a&&typeof a=="object"?e.fail$data((0,Js._)`!${(0,fg.useFunc)(t,hg.default)}(${r}, ${s})`):e.fail((0,Js._)`${a} !== ${r}`)}};Io.default=mg;var To={};Object.defineProperty(To,"__esModule",{value:!0});const Lr=Z,$g=L,yg=tn,_g={message:"must be equal to one of the allowed values",params:({schemaCode:e})=>(0,Lr._)`{allowedValues: ${e}}`},gg={keyword:"enum",schemaType:"array",$data:!0,error:_g,code(e){const{gen:t,data:r,$data:n,schema:s,schemaCode:a,it:i}=e;if(!n&&s.length===0)throw new Error("enum must have non-empty array");const d=s.length>=i.opts.loopEnum;let l;const u=()=>l??(l=(0,$g.useFunc)(t,yg.default));let c;if(d||n)c=t.let("valid"),e.block$data(c,h);else{if(!Array.isArray(s))throw new Error("ajv implementation error");const y=t.const("vSchema",a);c=(0,Lr.or)(...s.map((v,g)=>S(y,g)))}e.pass(c);function h(){t.assign(c,!1),t.forOf("v",a,y=>t.if((0,Lr._)`${u()}(${r}, ${y})`,()=>t.assign(c,!0).break()))}function S(y,v){const g=s[v];return typeof g=="object"&&g!==null?(0,Lr._)`${u()}(${r}, ${y}[${v}])`:(0,Lr._)`${r} === ${g}`}}};To.default=gg;Object.defineProperty(go,"__esModule",{value:!0});const vg=vo,wg=wo,Eg=Eo,Sg=bo,bg=Po,Pg=No,Ng=Oo,Og=Ro,Rg=Io,Ig=To,Tg=[vg.default,wg.default,Eg.default,Sg.default,bg.default,Pg.default,Ng.default,Og.default,{keyword:"type",schemaType:["string","array"]},{keyword:"nullable",schemaType:"boolean"},Rg.default,Ig.default];go.default=Tg;var jo={},br={};Object.defineProperty(br,"__esModule",{value:!0});br.validateAdditionalItems=void 0;const Yt=Z,Xs=L,jg={message:({params:{len:e}})=>(0,Yt.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,Yt._)`{limit: ${e}}`},Ag={keyword:"additionalItems",type:"array",schemaType:["boolean","object"],before:"uniqueItems",error:jg,code(e){const{parentSchema:t,it:r}=e,{items:n}=t;if(!Array.isArray(n)){(0,Xs.checkStrictMode)(r,'"additionalItems" is ignored when "items" is not an array of schemas');return}Tu(e,n)}};function Tu(e,t){const{gen:r,schema:n,data:s,keyword:a,it:i}=e;i.items=!0;const d=r.const("len",(0,Yt._)`${s}.length`);if(n===!1)e.setParams({len:t.length}),e.pass((0,Yt._)`${d} <= ${t.length}`);else if(typeof n=="object"&&!(0,Xs.alwaysValidSchema)(i,n)){const u=r.var("valid",(0,Yt._)`${d} <= ${t.length}`);r.if((0,Yt.not)(u),()=>l(u)),e.ok(u)}function l(u){r.forRange("i",t.length,d,c=>{e.subschema({keyword:a,dataProp:c,dataPropType:Xs.Type.Num},u),i.allErrors||r.if((0,Yt.not)(u),()=>r.break())})}}br.validateAdditionalItems=Tu;br.default=Ag;var Ao={},Pr={};Object.defineProperty(Pr,"__esModule",{value:!0});Pr.validateTuple=void 0;const Wi=Z,kn=L,kg=ee,Cg={keyword:"items",type:"array",schemaType:["object","array","boolean"],before:"uniqueItems",code(e){const{schema:t,it:r}=e;if(Array.isArray(t))return ju(e,"additionalItems",t);r.items=!0,!(0,kn.alwaysValidSchema)(r,t)&&e.ok((0,kg.validateArray)(e))}};function ju(e,t,r=e.schema){const{gen:n,parentSchema:s,data:a,keyword:i,it:d}=e;c(s),d.opts.unevaluated&&r.length&&d.items!==!0&&(d.items=kn.mergeEvaluated.items(n,r.length,d.items));const l=n.name("valid"),u=n.const("len",(0,Wi._)`${a}.length`);r.forEach((h,S)=>{(0,kn.alwaysValidSchema)(d,h)||(n.if((0,Wi._)`${u} > ${S}`,()=>e.subschema({keyword:i,schemaProp:S,dataProp:S},l)),e.ok(l))});function c(h){const{opts:S,errSchemaPath:y}=d,v=r.length,g=v===h.minItems&&(v===h.maxItems||h[t]===!1);if(S.strictTuples&&!g){const $=`"${i}" is ${v}-tuple, but minItems or maxItems/${t} are not specified or different at path "${y}"`;(0,kn.checkStrictMode)(d,$,S.strictTuples)}}}Pr.validateTuple=ju;Pr.default=Cg;Object.defineProperty(Ao,"__esModule",{value:!0});const Dg=Pr,Mg={keyword:"prefixItems",type:"array",schemaType:["array"],before:"uniqueItems",code:e=>(0,Dg.validateTuple)(e,"items")};Ao.default=Mg;var ko={};Object.defineProperty(ko,"__esModule",{value:!0});const Ji=Z,Lg=L,Fg=ee,Vg=br,Ug={message:({params:{len:e}})=>(0,Ji.str)`must NOT have more than ${e} items`,params:({params:{len:e}})=>(0,Ji._)`{limit: ${e}}`},zg={keyword:"items",type:"array",schemaType:["object","boolean"],before:"uniqueItems",error:Ug,code(e){const{schema:t,parentSchema:r,it:n}=e,{prefixItems:s}=r;n.items=!0,!(0,Lg.alwaysValidSchema)(n,t)&&(s?(0,Vg.validateAdditionalItems)(e,s):e.ok((0,Fg.validateArray)(e)))}};ko.default=zg;var Co={};Object.defineProperty(Co,"__esModule",{value:!0});const Ke=Z,yn=L,qg={message:({params:{min:e,max:t}})=>t===void 0?(0,Ke.str)`must contain at least ${e} valid item(s)`:(0,Ke.str)`must contain at least ${e} and no more than ${t} valid item(s)`,params:({params:{min:e,max:t}})=>t===void 0?(0,Ke._)`{minContains: ${e}}`:(0,Ke._)`{minContains: ${e}, maxContains: ${t}}`},Kg={keyword:"contains",type:"array",schemaType:["object","boolean"],before:"uniqueItems",trackErrors:!0,error:qg,code(e){const{gen:t,schema:r,parentSchema:n,data:s,it:a}=e;let i,d;const{minContains:l,maxContains:u}=n;a.opts.next?(i=l===void 0?1:l,d=u):i=1;const c=t.const("len",(0,Ke._)`${s}.length`);if(e.setParams({min:i,max:d}),d===void 0&&i===0){(0,yn.checkStrictMode)(a,'"minContains" == 0 without "maxContains": "contains" keyword ignored');return}if(d!==void 0&&i>d){(0,yn.checkStrictMode)(a,'"minContains" > "maxContains" is always invalid'),e.fail();return}if((0,yn.alwaysValidSchema)(a,r)){let g=(0,Ke._)`${c} >= ${i}`;d!==void 0&&(g=(0,Ke._)`${g} && ${c} <= ${d}`),e.pass(g);return}a.items=!0;const h=t.name("valid");d===void 0&&i===1?y(h,()=>t.if(h,()=>t.break())):i===0?(t.let(h,!0),d!==void 0&&t.if((0,Ke._)`${s}.length > 0`,S)):(t.let(h,!1),S()),e.result(h,()=>e.reset());function S(){const g=t.name("_valid"),$=t.let("count",0);y(g,()=>t.if(g,()=>v($)))}function y(g,$){t.forRange("i",0,c,p=>{e.subschema({keyword:"contains",dataProp:p,dataPropType:yn.Type.Num,compositeRule:!0},g),$()})}function v(g){t.code((0,Ke._)`${g}++`),d===void 0?t.if((0,Ke._)`${g} >= ${i}`,()=>t.assign(h,!0).break()):(t.if((0,Ke._)`${g} > ${d}`,()=>t.assign(h,!1).break()),i===1?t.assign(h,!0):t.if((0,Ke._)`${g} >= ${i}`,()=>t.assign(h,!0)))}}};Co.default=Kg;var Au={};(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.validateSchemaDeps=e.validatePropertyDeps=e.error=void 0;const t=Z,r=L,n=ee;e.error={message:({params:{property:l,depsCount:u,deps:c}})=>{const h=u===1?"property":"properties";return(0,t.str)`must have ${h} ${c} when property ${l} is present`},params:({params:{property:l,depsCount:u,deps:c,missingProperty:h}})=>(0,t._)`{property: ${l},
    missingProperty: ${h},
    depsCount: ${u},
    deps: ${c}}`};const s={keyword:"dependencies",type:"object",schemaType:"object",error:e.error,code(l){const[u,c]=a(l);i(l,u),d(l,c)}};function a({schema:l}){const u={},c={};for(const h in l){if(h==="__proto__")continue;const S=Array.isArray(l[h])?u:c;S[h]=l[h]}return[u,c]}function i(l,u=l.schema){const{gen:c,data:h,it:S}=l;if(Object.keys(u).length===0)return;const y=c.let("missing");for(const v in u){const g=u[v];if(g.length===0)continue;const $=(0,n.propertyInData)(c,h,v,S.opts.ownProperties);l.setParams({property:v,depsCount:g.length,deps:g.join(", ")}),S.allErrors?c.if($,()=>{for(const p of g)(0,n.checkReportMissingProp)(l,p)}):(c.if((0,t._)`${$} && (${(0,n.checkMissingProp)(l,g,y)})`),(0,n.reportMissingProp)(l,y),c.else())}}e.validatePropertyDeps=i;function d(l,u=l.schema){const{gen:c,data:h,keyword:S,it:y}=l,v=c.name("valid");for(const g in u)(0,r.alwaysValidSchema)(y,u[g])||(c.if((0,n.propertyInData)(c,h,g,y.opts.ownProperties),()=>{const $=l.subschema({keyword:S,schemaProp:g},v);l.mergeValidEvaluated($,v)},()=>c.var(v,!0)),l.ok(v))}e.validateSchemaDeps=d,e.default=s})(Au);var Do={};Object.defineProperty(Do,"__esModule",{value:!0});const ku=Z,Gg=L,Hg={message:"property name must be valid",params:({params:e})=>(0,ku._)`{propertyName: ${e.propertyName}}`},Bg={keyword:"propertyNames",type:"object",schemaType:["object","boolean"],error:Hg,code(e){const{gen:t,schema:r,data:n,it:s}=e;if((0,Gg.alwaysValidSchema)(s,r))return;const a=t.name("valid");t.forIn("key",n,i=>{e.setParams({propertyName:i}),e.subschema({keyword:"propertyNames",data:i,dataTypes:["string"],propertyName:i,compositeRule:!0},a),t.if((0,ku.not)(a),()=>{e.error(!0),s.allErrors||t.break()})}),e.ok(a)}};Do.default=Bg;var ss={};Object.defineProperty(ss,"__esModule",{value:!0});const _n=ee,Je=Z,Wg=lt,gn=L,Jg={message:"must NOT have additional properties",params:({params:e})=>(0,Je._)`{additionalProperty: ${e.additionalProperty}}`},Xg={keyword:"additionalProperties",type:["object"],schemaType:["boolean","object"],allowUndefined:!0,trackErrors:!0,error:Jg,code(e){const{gen:t,schema:r,parentSchema:n,data:s,errsCount:a,it:i}=e;if(!a)throw new Error("ajv implementation error");const{allErrors:d,opts:l}=i;if(i.props=!0,l.removeAdditional!=="all"&&(0,gn.alwaysValidSchema)(i,r))return;const u=(0,_n.allSchemaProperties)(n.properties),c=(0,_n.allSchemaProperties)(n.patternProperties);h(),e.ok((0,Je._)`${a} === ${Wg.default.errors}`);function h(){t.forIn("key",s,$=>{!u.length&&!c.length?v($):t.if(S($),()=>v($))})}function S($){let p;if(u.length>8){const w=(0,gn.schemaRefOrVal)(i,n.properties,"properties");p=(0,_n.isOwnProperty)(t,w,$)}else u.length?p=(0,Je.or)(...u.map(w=>(0,Je._)`${$} === ${w}`)):p=Je.nil;return c.length&&(p=(0,Je.or)(p,...c.map(w=>(0,Je._)`${(0,_n.usePattern)(e,w)}.test(${$})`))),(0,Je.not)(p)}function y($){t.code((0,Je._)`delete ${s}[${$}]`)}function v($){if(l.removeAdditional==="all"||l.removeAdditional&&r===!1){y($);return}if(r===!1){e.setParams({additionalProperty:$}),e.error(),d||t.break();return}if(typeof r=="object"&&!(0,gn.alwaysValidSchema)(i,r)){const p=t.name("valid");l.removeAdditional==="failing"?(g($,p,!1),t.if((0,Je.not)(p),()=>{e.reset(),y($)})):(g($,p),d||t.if((0,Je.not)(p),()=>t.break()))}}function g($,p,w){const N={keyword:"additionalProperties",dataProp:$,dataPropType:gn.Type.Str};w===!1&&Object.assign(N,{compositeRule:!0,createErrors:!1,allErrors:!1}),e.subschema(N,p)}}};ss.default=Xg;var Mo={};Object.defineProperty(Mo,"__esModule",{value:!0});const Yg=Ze,Xi=ee,bs=L,Yi=ss,Qg={keyword:"properties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,parentSchema:n,data:s,it:a}=e;a.opts.removeAdditional==="all"&&n.additionalProperties===void 0&&Yi.default.code(new Yg.KeywordCxt(a,Yi.default,"additionalProperties"));const i=(0,Xi.allSchemaProperties)(r);for(const h of i)a.definedProperties.add(h);a.opts.unevaluated&&i.length&&a.props!==!0&&(a.props=bs.mergeEvaluated.props(t,(0,bs.toHash)(i),a.props));const d=i.filter(h=>!(0,bs.alwaysValidSchema)(a,r[h]));if(d.length===0)return;const l=t.name("valid");for(const h of d)u(h)?c(h):(t.if((0,Xi.propertyInData)(t,s,h,a.opts.ownProperties)),c(h),a.allErrors||t.else().var(l,!0),t.endIf()),e.it.definedProperties.add(h),e.ok(l);function u(h){return a.opts.useDefaults&&!a.compositeRule&&r[h].default!==void 0}function c(h){e.subschema({keyword:"properties",schemaProp:h,dataProp:h},l)}}};Mo.default=Qg;var Lo={};Object.defineProperty(Lo,"__esModule",{value:!0});const Qi=ee,vn=Z,Zi=L,xi=L,Zg={keyword:"patternProperties",type:"object",schemaType:"object",code(e){const{gen:t,schema:r,data:n,parentSchema:s,it:a}=e,{opts:i}=a,d=(0,Qi.allSchemaProperties)(r),l=d.filter(g=>(0,Zi.alwaysValidSchema)(a,r[g]));if(d.length===0||l.length===d.length&&(!a.opts.unevaluated||a.props===!0))return;const u=i.strictSchema&&!i.allowMatchingProperties&&s.properties,c=t.name("valid");a.props!==!0&&!(a.props instanceof vn.Name)&&(a.props=(0,xi.evaluatedPropsToName)(t,a.props));const{props:h}=a;S();function S(){for(const g of d)u&&y(g),a.allErrors?v(g):(t.var(c,!0),v(g),t.if(c))}function y(g){for(const $ in u)new RegExp(g).test($)&&(0,Zi.checkStrictMode)(a,`property ${$} matches pattern ${g} (use allowMatchingProperties)`)}function v(g){t.forIn("key",n,$=>{t.if((0,vn._)`${(0,Qi.usePattern)(e,g)}.test(${$})`,()=>{const p=l.includes(g);p||e.subschema({keyword:"patternProperties",schemaProp:g,dataProp:$,dataPropType:xi.Type.Str},c),a.opts.unevaluated&&h!==!0?t.assign((0,vn._)`${h}[${$}]`,!0):!p&&!a.allErrors&&t.if((0,vn.not)(c),()=>t.break())})})}}};Lo.default=Zg;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0});const xg=L,e0={keyword:"not",schemaType:["object","boolean"],trackErrors:!0,code(e){const{gen:t,schema:r,it:n}=e;if((0,xg.alwaysValidSchema)(n,r)){e.fail();return}const s=t.name("valid");e.subschema({keyword:"not",compositeRule:!0,createErrors:!1,allErrors:!1},s),e.failResult(s,()=>e.reset(),()=>e.error())},error:{message:"must NOT be valid"}};Fo.default=e0;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0});const t0=ee,r0={keyword:"anyOf",schemaType:"array",trackErrors:!0,code:t0.validateUnion,error:{message:"must match a schema in anyOf"}};Vo.default=r0;var Uo={};Object.defineProperty(Uo,"__esModule",{value:!0});const Cn=Z,n0=L,s0={message:"must match exactly one schema in oneOf",params:({params:e})=>(0,Cn._)`{passingSchemas: ${e.passing}}`},a0={keyword:"oneOf",schemaType:"array",trackErrors:!0,error:s0,code(e){const{gen:t,schema:r,parentSchema:n,it:s}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");if(s.opts.discriminator&&n.discriminator)return;const a=r,i=t.let("valid",!1),d=t.let("passing",null),l=t.name("_valid");e.setParams({passing:d}),t.block(u),e.result(i,()=>e.reset(),()=>e.error(!0));function u(){a.forEach((c,h)=>{let S;(0,n0.alwaysValidSchema)(s,c)?t.var(l,!0):S=e.subschema({keyword:"oneOf",schemaProp:h,compositeRule:!0},l),h>0&&t.if((0,Cn._)`${l} && ${i}`).assign(i,!1).assign(d,(0,Cn._)`[${d}, ${h}]`).else(),t.if(l,()=>{t.assign(i,!0),t.assign(d,h),S&&e.mergeEvaluated(S,Cn.Name)})})}}};Uo.default=a0;var zo={};Object.defineProperty(zo,"__esModule",{value:!0});const o0=L,i0={keyword:"allOf",schemaType:"array",code(e){const{gen:t,schema:r,it:n}=e;if(!Array.isArray(r))throw new Error("ajv implementation error");const s=t.name("valid");r.forEach((a,i)=>{if((0,o0.alwaysValidSchema)(n,a))return;const d=e.subschema({keyword:"allOf",schemaProp:i},s);e.ok(s),e.mergeEvaluated(d)})}};zo.default=i0;var qo={};Object.defineProperty(qo,"__esModule",{value:!0});const Hn=Z,Cu=L,c0={message:({params:e})=>(0,Hn.str)`must match "${e.ifClause}" schema`,params:({params:e})=>(0,Hn._)`{failingKeyword: ${e.ifClause}}`},l0={keyword:"if",schemaType:["object","boolean"],trackErrors:!0,error:c0,code(e){const{gen:t,parentSchema:r,it:n}=e;r.then===void 0&&r.else===void 0&&(0,Cu.checkStrictMode)(n,'"if" without "then" and "else" is ignored');const s=ec(n,"then"),a=ec(n,"else");if(!s&&!a)return;const i=t.let("valid",!0),d=t.name("_valid");if(l(),e.reset(),s&&a){const c=t.let("ifClause");e.setParams({ifClause:c}),t.if(d,u("then",c),u("else",c))}else s?t.if(d,u("then")):t.if((0,Hn.not)(d),u("else"));e.pass(i,()=>e.error(!0));function l(){const c=e.subschema({keyword:"if",compositeRule:!0,createErrors:!1,allErrors:!1},d);e.mergeEvaluated(c)}function u(c,h){return()=>{const S=e.subschema({keyword:c},d);t.assign(i,d),e.mergeValidEvaluated(S,i),h?t.assign(h,(0,Hn._)`${c}`):e.setParams({ifClause:c})}}}};function ec(e,t){const r=e.schema[t];return r!==void 0&&!(0,Cu.alwaysValidSchema)(e,r)}qo.default=l0;var Ko={};Object.defineProperty(Ko,"__esModule",{value:!0});const u0=L,d0={keyword:["then","else"],schemaType:["object","boolean"],code({keyword:e,parentSchema:t,it:r}){t.if===void 0&&(0,u0.checkStrictMode)(r,`"${e}" without "if" is ignored`)}};Ko.default=d0;Object.defineProperty(jo,"__esModule",{value:!0});const f0=br,h0=Ao,p0=Pr,m0=ko,$0=Co,y0=Au,_0=Do,g0=ss,v0=Mo,w0=Lo,E0=Fo,S0=Vo,b0=Uo,P0=zo,N0=qo,O0=Ko;function R0(e=!1){const t=[E0.default,S0.default,b0.default,P0.default,N0.default,O0.default,_0.default,g0.default,y0.default,v0.default,w0.default];return e?t.push(h0.default,m0.default):t.push(f0.default,p0.default),t.push($0.default),t}jo.default=R0;var Go={},Ho={};Object.defineProperty(Ho,"__esModule",{value:!0});const ye=Z,I0={message:({schemaCode:e})=>(0,ye.str)`must match format "${e}"`,params:({schemaCode:e})=>(0,ye._)`{format: ${e}}`},T0={keyword:"format",type:["number","string"],schemaType:"string",$data:!0,error:I0,code(e,t){const{gen:r,data:n,$data:s,schema:a,schemaCode:i,it:d}=e,{opts:l,errSchemaPath:u,schemaEnv:c,self:h}=d;if(!l.validateFormats)return;s?S():y();function S(){const v=r.scopeValue("formats",{ref:h.formats,code:l.code.formats}),g=r.const("fDef",(0,ye._)`${v}[${i}]`),$=r.let("fType"),p=r.let("format");r.if((0,ye._)`typeof ${g} == "object" && !(${g} instanceof RegExp)`,()=>r.assign($,(0,ye._)`${g}.type || "string"`).assign(p,(0,ye._)`${g}.validate`),()=>r.assign($,(0,ye._)`"string"`).assign(p,g)),e.fail$data((0,ye.or)(w(),N()));function w(){return l.strictSchema===!1?ye.nil:(0,ye._)`${i} && !${p}`}function N(){const O=c.$async?(0,ye._)`(${g}.async ? await ${p}(${n}) : ${p}(${n}))`:(0,ye._)`${p}(${n})`,T=(0,ye._)`(typeof ${p} == "function" ? ${O} : ${p}.test(${n}))`;return(0,ye._)`${p} && ${p} !== true && ${$} === ${t} && !${T}`}}function y(){const v=h.formats[a];if(!v){w();return}if(v===!0)return;const[g,$,p]=N(v);g===t&&e.pass(O());function w(){if(l.strictSchema===!1){h.logger.warn(T());return}throw new Error(T());function T(){return`unknown format "${a}" ignored in schema at path "${u}"`}}function N(T){const z=T instanceof RegExp?(0,ye.regexpCode)(T):l.code.formats?(0,ye._)`${l.code.formats}${(0,ye.getProperty)(a)}`:void 0,B=r.scopeValue("formats",{key:a,ref:T,code:z});return typeof T=="object"&&!(T instanceof RegExp)?[T.type||"string",T.validate,(0,ye._)`${B}.validate`]:["string",T,B]}function O(){if(typeof v=="object"&&!(v instanceof RegExp)&&v.async){if(!c.$async)throw new Error("async format in sync schema");return(0,ye._)`await ${p}(${n})`}return typeof $=="function"?(0,ye._)`${p}(${n})`:(0,ye._)`${p}.test(${n})`}}}};Ho.default=T0;Object.defineProperty(Go,"__esModule",{value:!0});const j0=Ho,A0=[j0.default];Go.default=A0;var gr={};Object.defineProperty(gr,"__esModule",{value:!0});gr.contentVocabulary=gr.metadataVocabulary=void 0;gr.metadataVocabulary=["title","description","default","deprecated","readOnly","writeOnly","examples"];gr.contentVocabulary=["contentMediaType","contentEncoding","contentSchema"];Object.defineProperty($o,"__esModule",{value:!0});const k0=yo,C0=go,D0=jo,M0=Go,tc=gr,L0=[k0.default,C0.default,(0,D0.default)(),M0.default,tc.metadataVocabulary,tc.contentVocabulary];$o.default=L0;var Bo={},as={};Object.defineProperty(as,"__esModule",{value:!0});as.DiscrError=void 0;var rc;(function(e){e.Tag="tag",e.Mapping="mapping"})(rc||(as.DiscrError=rc={}));Object.defineProperty(Bo,"__esModule",{value:!0});const cr=Z,Ys=as,nc=Fe,F0=Sr,V0=L,U0={message:({params:{discrError:e,tagName:t}})=>e===Ys.DiscrError.Tag?`tag "${t}" must be string`:`value of tag "${t}" must be in oneOf`,params:({params:{discrError:e,tag:t,tagName:r}})=>(0,cr._)`{error: ${e}, tag: ${r}, tagValue: ${t}}`},z0={keyword:"discriminator",type:"object",schemaType:"object",error:U0,code(e){const{gen:t,data:r,schema:n,parentSchema:s,it:a}=e,{oneOf:i}=s;if(!a.opts.discriminator)throw new Error("discriminator: requires discriminator option");const d=n.propertyName;if(typeof d!="string")throw new Error("discriminator: requires propertyName");if(n.mapping)throw new Error("discriminator: mapping is not supported");if(!i)throw new Error("discriminator: requires oneOf keyword");const l=t.let("valid",!1),u=t.const("tag",(0,cr._)`${r}${(0,cr.getProperty)(d)}`);t.if((0,cr._)`typeof ${u} == "string"`,()=>c(),()=>e.error(!1,{discrError:Ys.DiscrError.Tag,tag:u,tagName:d})),e.ok(l);function c(){const y=S();t.if(!1);for(const v in y)t.elseIf((0,cr._)`${u} === ${v}`),t.assign(l,h(y[v]));t.else(),e.error(!1,{discrError:Ys.DiscrError.Mapping,tag:u,tagName:d}),t.endIf()}function h(y){const v=t.name("valid"),g=e.subschema({keyword:"oneOf",schemaProp:y},v);return e.mergeEvaluated(g,cr.Name),v}function S(){var y;const v={},g=p(s);let $=!0;for(let O=0;O<i.length;O++){let T=i[O];if(T!=null&&T.$ref&&!(0,V0.schemaHasRulesButRef)(T,a.self.RULES)){const B=T.$ref;if(T=nc.resolveRef.call(a.self,a.schemaEnv.root,a.baseId,B),T instanceof nc.SchemaEnv&&(T=T.schema),T===void 0)throw new F0.default(a.opts.uriResolver,a.baseId,B)}const z=(y=T==null?void 0:T.properties)===null||y===void 0?void 0:y[d];if(typeof z!="object")throw new Error(`discriminator: oneOf subschemas (or referenced schemas) must have "properties/${d}"`);$=$&&(g||p(T)),w(z,O)}if(!$)throw new Error(`discriminator: "${d}" must be required`);return v;function p({required:O}){return Array.isArray(O)&&O.includes(d)}function w(O,T){if(O.const)N(O.const,T);else if(O.enum)for(const z of O.enum)N(z,T);else throw new Error(`discriminator: "properties/${d}" must have "const" or "enum"`)}function N(O,T){if(typeof O!="string"||O in v)throw new Error(`discriminator: "${d}" values must be unique strings`);v[O]=T}}}};Bo.default=z0;const q0="http://json-schema.org/draft-07/schema#",K0="http://json-schema.org/draft-07/schema#",G0="Core schema meta-schema",H0={schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},B0=["object","boolean"],W0={$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},$comment:{type:"string"},title:{type:"string"},description:{type:"string"},default:!0,readOnly:{type:"boolean",default:!1},examples:{type:"array",items:!0},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:!0},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},propertyNames:{format:"regex"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:!0,enum:{type:"array",items:!0,minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},contentMediaType:{type:"string"},contentEncoding:{type:"string"},if:{$ref:"#"},then:{$ref:"#"},else:{$ref:"#"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},J0={$schema:q0,$id:K0,title:G0,definitions:H0,type:B0,properties:W0,default:!0};(function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.MissingRefError=t.ValidationError=t.CodeGen=t.Name=t.nil=t.stringify=t.str=t._=t.KeywordCxt=t.Ajv=void 0;const r=Jl,n=$o,s=Bo,a=J0,i=["/properties"],d="http://json-schema.org/draft-07/schema";class l extends r.default{_addVocabularies(){super._addVocabularies(),n.default.forEach(v=>this.addVocabulary(v)),this.opts.discriminator&&this.addKeyword(s.default)}_addDefaultMetaSchema(){if(super._addDefaultMetaSchema(),!this.opts.meta)return;const v=this.opts.$data?this.$dataMetaSchema(a,i):a;this.addMetaSchema(v,d,!1),this.refs["http://json-schema.org/schema"]=d}defaultMeta(){return this.opts.defaultMeta=super.defaultMeta()||(this.getSchema(d)?d:void 0)}}t.Ajv=l,e.exports=t=l,e.exports.Ajv=l,Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;var u=Ze;Object.defineProperty(t,"KeywordCxt",{enumerable:!0,get:function(){return u.KeywordCxt}});var c=Z;Object.defineProperty(t,"_",{enumerable:!0,get:function(){return c._}}),Object.defineProperty(t,"str",{enumerable:!0,get:function(){return c.str}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return c.stringify}}),Object.defineProperty(t,"nil",{enumerable:!0,get:function(){return c.nil}}),Object.defineProperty(t,"Name",{enumerable:!0,get:function(){return c.Name}}),Object.defineProperty(t,"CodeGen",{enumerable:!0,get:function(){return c.CodeGen}});var h=en;Object.defineProperty(t,"ValidationError",{enumerable:!0,get:function(){return h.default}});var S=Sr;Object.defineProperty(t,"MissingRefError",{enumerable:!0,get:function(){return S.default}})})(Ks,Ks.exports);var X0=Ks.exports;(function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.formatLimitDefinition=void 0;const t=X0,r=Z,n=r.operators,s={formatMaximum:{okStr:"<=",ok:n.LTE,fail:n.GT},formatMinimum:{okStr:">=",ok:n.GTE,fail:n.LT},formatExclusiveMaximum:{okStr:"<",ok:n.LT,fail:n.GTE},formatExclusiveMinimum:{okStr:">",ok:n.GT,fail:n.LTE}},a={message:({keyword:d,schemaCode:l})=>r.str`should be ${s[d].okStr} ${l}`,params:({keyword:d,schemaCode:l})=>r._`{comparison: ${s[d].okStr}, limit: ${l}}`};e.formatLimitDefinition={keyword:Object.keys(s),type:"string",schemaType:"string",$data:!0,error:a,code(d){const{gen:l,data:u,schemaCode:c,keyword:h,it:S}=d,{opts:y,self:v}=S;if(!y.validateFormats)return;const g=new t.KeywordCxt(S,v.RULES.all.format.definition,"format");g.$data?$():p();function $(){const N=l.scopeValue("formats",{ref:v.formats,code:y.code.formats}),O=l.const("fmt",r._`${N}[${g.schemaCode}]`);d.fail$data(r.or(r._`typeof ${O} != "object"`,r._`${O} instanceof RegExp`,r._`typeof ${O}.compare != "function"`,w(O)))}function p(){const N=g.schema,O=v.formats[N];if(!O||O===!0)return;if(typeof O!="object"||O instanceof RegExp||typeof O.compare!="function")throw new Error(`"${h}": format "${N}" does not define "compare" function`);const T=l.scopeValue("formats",{key:N,ref:O,code:y.code.formats?r._`${y.code.formats}${r.getProperty(N)}`:void 0});d.fail$data(w(T))}function w(N){return r._`${N}.compare(${u}, ${c}) ${s[h].fail} 0`}},dependencies:["format"]};const i=d=>(d.addKeyword(e.formatLimitDefinition),d);e.default=i})(Wl);(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const r=Bl,n=Wl,s=Z,a=new s.Name("fullFormats"),i=new s.Name("fastFormats"),d=(u,c={keywords:!0})=>{if(Array.isArray(c))return l(u,c,r.fullFormats,a),u;const[h,S]=c.mode==="fast"?[r.fastFormats,i]:[r.fullFormats,a],y=c.formats||r.formatNames;return l(u,y,h,S),c.keywords&&n.default(u),u};d.get=(u,c="full")=>{const S=(c==="fast"?r.fastFormats:r.fullFormats)[u];if(!S)throw new Error(`Unknown format "${u}"`);return S};function l(u,c,h,S){var y,v;(y=(v=u.opts.code).formats)!==null&&y!==void 0||(v.formats=s._`require("ajv-formats/dist/formats").${S}`);for(const g of c)u.addFormat(g,h[g])}e.exports=t=d,Object.defineProperty(t,"__esModule",{value:!0}),t.default=d})(qs,qs.exports);var Y0=qs.exports;const Q0=(e,t,r,n)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;const s=Object.getOwnPropertyDescriptor(e,r),a=Object.getOwnPropertyDescriptor(t,r);!Z0(s,a)&&n||Object.defineProperty(e,r,a)},Z0=function(e,t){return e===void 0||e.configurable||e.writable===t.writable&&e.enumerable===t.enumerable&&e.configurable===t.configurable&&(e.writable||e.value===t.value)},x0=(e,t)=>{const r=Object.getPrototypeOf(t);r!==Object.getPrototypeOf(e)&&Object.setPrototypeOf(e,r)},ev=(e,t)=>`/* Wrapped ${e}*/
${t}`,tv=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),rv=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),nv=(e,t,r)=>{const n=r===""?"":`with ${r.trim()}() `,s=ev.bind(null,n,t.toString());Object.defineProperty(s,"name",rv),Object.defineProperty(e,"toString",{...tv,value:s})},sv=(e,t,{ignoreNonConfigurable:r=!1}={})=>{const{name:n}=e;for(const s of Reflect.ownKeys(t))Q0(e,t,s,r);return x0(e,t),nv(e,t,n),e};var av=sv;const ov=av;var iv=(e,t={})=>{if(typeof e!="function")throw new TypeError(`Expected the first argument to be a function, got \`${typeof e}\``);const{wait:r=0,before:n=!1,after:s=!0}=t;if(!n&&!s)throw new Error("Both `before` and `after` are false, function wouldn't be called.");let a,i;const d=function(...l){const u=this,c=()=>{a=void 0,s&&(i=e.apply(u,l))},h=n&&!a;return clearTimeout(a),a=setTimeout(c,r),h&&(i=e.apply(u,l)),i};return ov(d,e),d.cancel=()=>{a&&(clearTimeout(a),a=void 0)},d},Qs={exports:{}};const cv="2.0.0",Du=256,lv=Number.MAX_SAFE_INTEGER||9007199254740991,uv=16,dv=Du-6,fv=["major","premajor","minor","preminor","patch","prepatch","prerelease"];var os={MAX_LENGTH:Du,MAX_SAFE_COMPONENT_LENGTH:uv,MAX_SAFE_BUILD_LENGTH:dv,MAX_SAFE_INTEGER:lv,RELEASE_TYPES:fv,SEMVER_SPEC_VERSION:cv,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};const hv=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};var is=hv;(function(e,t){const{MAX_SAFE_COMPONENT_LENGTH:r,MAX_SAFE_BUILD_LENGTH:n,MAX_LENGTH:s}=os,a=is;t=e.exports={};const i=t.re=[],d=t.safeRe=[],l=t.src=[],u=t.safeSrc=[],c=t.t={};let h=0;const S="[a-zA-Z0-9-]",y=[["\\s",1],["\\d",s],[S,n]],v=$=>{for(const[p,w]of y)$=$.split(`${p}*`).join(`${p}{0,${w}}`).split(`${p}+`).join(`${p}{1,${w}}`);return $},g=($,p,w)=>{const N=v(p),O=h++;a($,O,p),c[$]=O,l[O]=p,u[O]=N,i[O]=new RegExp(p,w?"g":void 0),d[O]=new RegExp(N,w?"g":void 0)};g("NUMERICIDENTIFIER","0|[1-9]\\d*"),g("NUMERICIDENTIFIERLOOSE","\\d+"),g("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${S}*`),g("MAINVERSION",`(${l[c.NUMERICIDENTIFIER]})\\.(${l[c.NUMERICIDENTIFIER]})\\.(${l[c.NUMERICIDENTIFIER]})`),g("MAINVERSIONLOOSE",`(${l[c.NUMERICIDENTIFIERLOOSE]})\\.(${l[c.NUMERICIDENTIFIERLOOSE]})\\.(${l[c.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASEIDENTIFIER",`(?:${l[c.NUMERICIDENTIFIER]}|${l[c.NONNUMERICIDENTIFIER]})`),g("PRERELEASEIDENTIFIERLOOSE",`(?:${l[c.NUMERICIDENTIFIERLOOSE]}|${l[c.NONNUMERICIDENTIFIER]})`),g("PRERELEASE",`(?:-(${l[c.PRERELEASEIDENTIFIER]}(?:\\.${l[c.PRERELEASEIDENTIFIER]})*))`),g("PRERELEASELOOSE",`(?:-?(${l[c.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${l[c.PRERELEASEIDENTIFIERLOOSE]})*))`),g("BUILDIDENTIFIER",`${S}+`),g("BUILD",`(?:\\+(${l[c.BUILDIDENTIFIER]}(?:\\.${l[c.BUILDIDENTIFIER]})*))`),g("FULLPLAIN",`v?${l[c.MAINVERSION]}${l[c.PRERELEASE]}?${l[c.BUILD]}?`),g("FULL",`^${l[c.FULLPLAIN]}$`),g("LOOSEPLAIN",`[v=\\s]*${l[c.MAINVERSIONLOOSE]}${l[c.PRERELEASELOOSE]}?${l[c.BUILD]}?`),g("LOOSE",`^${l[c.LOOSEPLAIN]}$`),g("GTLT","((?:<|>)?=?)"),g("XRANGEIDENTIFIERLOOSE",`${l[c.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),g("XRANGEIDENTIFIER",`${l[c.NUMERICIDENTIFIER]}|x|X|\\*`),g("XRANGEPLAIN",`[v=\\s]*(${l[c.XRANGEIDENTIFIER]})(?:\\.(${l[c.XRANGEIDENTIFIER]})(?:\\.(${l[c.XRANGEIDENTIFIER]})(?:${l[c.PRERELEASE]})?${l[c.BUILD]}?)?)?`),g("XRANGEPLAINLOOSE",`[v=\\s]*(${l[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[c.XRANGEIDENTIFIERLOOSE]})(?:\\.(${l[c.XRANGEIDENTIFIERLOOSE]})(?:${l[c.PRERELEASELOOSE]})?${l[c.BUILD]}?)?)?`),g("XRANGE",`^${l[c.GTLT]}\\s*${l[c.XRANGEPLAIN]}$`),g("XRANGELOOSE",`^${l[c.GTLT]}\\s*${l[c.XRANGEPLAINLOOSE]}$`),g("COERCEPLAIN",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?`),g("COERCE",`${l[c.COERCEPLAIN]}(?:$|[^\\d])`),g("COERCEFULL",l[c.COERCEPLAIN]+`(?:${l[c.PRERELEASE]})?(?:${l[c.BUILD]})?(?:$|[^\\d])`),g("COERCERTL",l[c.COERCE],!0),g("COERCERTLFULL",l[c.COERCEFULL],!0),g("LONETILDE","(?:~>?)"),g("TILDETRIM",`(\\s*)${l[c.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",g("TILDE",`^${l[c.LONETILDE]}${l[c.XRANGEPLAIN]}$`),g("TILDELOOSE",`^${l[c.LONETILDE]}${l[c.XRANGEPLAINLOOSE]}$`),g("LONECARET","(?:\\^)"),g("CARETTRIM",`(\\s*)${l[c.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",g("CARET",`^${l[c.LONECARET]}${l[c.XRANGEPLAIN]}$`),g("CARETLOOSE",`^${l[c.LONECARET]}${l[c.XRANGEPLAINLOOSE]}$`),g("COMPARATORLOOSE",`^${l[c.GTLT]}\\s*(${l[c.LOOSEPLAIN]})$|^$`),g("COMPARATOR",`^${l[c.GTLT]}\\s*(${l[c.FULLPLAIN]})$|^$`),g("COMPARATORTRIM",`(\\s*)${l[c.GTLT]}\\s*(${l[c.LOOSEPLAIN]}|${l[c.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",g("HYPHENRANGE",`^\\s*(${l[c.XRANGEPLAIN]})\\s+-\\s+(${l[c.XRANGEPLAIN]})\\s*$`),g("HYPHENRANGELOOSE",`^\\s*(${l[c.XRANGEPLAINLOOSE]})\\s+-\\s+(${l[c.XRANGEPLAINLOOSE]})\\s*$`),g("STAR","(<|>)?=?\\s*\\*"),g("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),g("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")})(Qs,Qs.exports);var rn=Qs.exports;const pv=Object.freeze({loose:!0}),mv=Object.freeze({}),$v=e=>e?typeof e!="object"?pv:e:mv;var Wo=$v;const sc=/^[0-9]+$/,Mu=(e,t)=>{const r=sc.test(e),n=sc.test(t);return r&&n&&(e=+e,t=+t),e===t?0:r&&!n?-1:n&&!r?1:e<t?-1:1},yv=(e,t)=>Mu(t,e);var Lu={compareIdentifiers:Mu,rcompareIdentifiers:yv};const wn=is,{MAX_LENGTH:ac,MAX_SAFE_INTEGER:En}=os,{safeRe:oc,safeSrc:ic,t:Sn}=rn,_v=Wo,{compareIdentifiers:or}=Lu;let gv=class tt{constructor(t,r){if(r=_v(r),t instanceof tt){if(t.loose===!!r.loose&&t.includePrerelease===!!r.includePrerelease)return t;t=t.version}else if(typeof t!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof t}".`);if(t.length>ac)throw new TypeError(`version is longer than ${ac} characters`);wn("SemVer",t,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;const n=t.trim().match(r.loose?oc[Sn.LOOSE]:oc[Sn.FULL]);if(!n)throw new TypeError(`Invalid Version: ${t}`);if(this.raw=t,this.major=+n[1],this.minor=+n[2],this.patch=+n[3],this.major>En||this.major<0)throw new TypeError("Invalid major version");if(this.minor>En||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>En||this.patch<0)throw new TypeError("Invalid patch version");n[4]?this.prerelease=n[4].split(".").map(s=>{if(/^[0-9]+$/.test(s)){const a=+s;if(a>=0&&a<En)return a}return s}):this.prerelease=[],this.build=n[5]?n[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(t){if(wn("SemVer.compare",this.version,this.options,t),!(t instanceof tt)){if(typeof t=="string"&&t===this.version)return 0;t=new tt(t,this.options)}return t.version===this.version?0:this.compareMain(t)||this.comparePre(t)}compareMain(t){return t instanceof tt||(t=new tt(t,this.options)),or(this.major,t.major)||or(this.minor,t.minor)||or(this.patch,t.patch)}comparePre(t){if(t instanceof tt||(t=new tt(t,this.options)),this.prerelease.length&&!t.prerelease.length)return-1;if(!this.prerelease.length&&t.prerelease.length)return 1;if(!this.prerelease.length&&!t.prerelease.length)return 0;let r=0;do{const n=this.prerelease[r],s=t.prerelease[r];if(wn("prerelease compare",r,n,s),n===void 0&&s===void 0)return 0;if(s===void 0)return 1;if(n===void 0)return-1;if(n===s)continue;return or(n,s)}while(++r)}compareBuild(t){t instanceof tt||(t=new tt(t,this.options));let r=0;do{const n=this.build[r],s=t.build[r];if(wn("build compare",r,n,s),n===void 0&&s===void 0)return 0;if(s===void 0)return 1;if(n===void 0)return-1;if(n===s)continue;return or(n,s)}while(++r)}inc(t,r,n){if(t.startsWith("pre")){if(!r&&n===!1)throw new Error("invalid increment argument: identifier is empty");if(r){const s=new RegExp(`^${this.options.loose?ic[Sn.PRERELEASELOOSE]:ic[Sn.PRERELEASE]}$`),a=`-${r}`.match(s);if(!a||a[1]!==r)throw new Error(`invalid identifier: ${r}`)}}switch(t){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,n);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,n);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,n),this.inc("pre",r,n);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",r,n),this.inc("pre",r,n);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{const s=Number(n)?1:0;if(this.prerelease.length===0)this.prerelease=[s];else{let a=this.prerelease.length;for(;--a>=0;)typeof this.prerelease[a]=="number"&&(this.prerelease[a]++,a=-2);if(a===-1){if(r===this.prerelease.join(".")&&n===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(s)}}if(r){let a=[r,s];n===!1&&(a=[r]),or(this.prerelease[0],r)===0?isNaN(this.prerelease[1])&&(this.prerelease=a):this.prerelease=a}break}default:throw new Error(`invalid increment argument: ${t}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};var ke=gv;const cc=ke,vv=(e,t,r=!1)=>{if(e instanceof cc)return e;try{return new cc(e,t)}catch(n){if(!r)return null;throw n}};var Nr=vv;const wv=Nr,Ev=(e,t)=>{const r=wv(e,t);return r?r.version:null};var Sv=Ev;const bv=Nr,Pv=(e,t)=>{const r=bv(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null};var Nv=Pv;const lc=ke,Ov=(e,t,r,n,s)=>{typeof r=="string"&&(s=n,n=r,r=void 0);try{return new lc(e instanceof lc?e.version:e,r).inc(t,n,s).version}catch{return null}};var Rv=Ov;const uc=Nr,Iv=(e,t)=>{const r=uc(e,null,!0),n=uc(t,null,!0),s=r.compare(n);if(s===0)return null;const a=s>0,i=a?r:n,d=a?n:r,l=!!i.prerelease.length;if(!!d.prerelease.length&&!l){if(!d.patch&&!d.minor)return"major";if(d.compareMain(i)===0)return d.minor&&!d.patch?"minor":"patch"}const c=l?"pre":"";return r.major!==n.major?c+"major":r.minor!==n.minor?c+"minor":r.patch!==n.patch?c+"patch":"prerelease"};var Tv=Iv;const jv=ke,Av=(e,t)=>new jv(e,t).major;var kv=Av;const Cv=ke,Dv=(e,t)=>new Cv(e,t).minor;var Mv=Dv;const Lv=ke,Fv=(e,t)=>new Lv(e,t).patch;var Vv=Fv;const Uv=Nr,zv=(e,t)=>{const r=Uv(e,t);return r&&r.prerelease.length?r.prerelease:null};var qv=zv;const dc=ke,Kv=(e,t,r)=>new dc(e,r).compare(new dc(t,r));var xe=Kv;const Gv=xe,Hv=(e,t,r)=>Gv(t,e,r);var Bv=Hv;const Wv=xe,Jv=(e,t)=>Wv(e,t,!0);var Xv=Jv;const fc=ke,Yv=(e,t,r)=>{const n=new fc(e,r),s=new fc(t,r);return n.compare(s)||n.compareBuild(s)};var Jo=Yv;const Qv=Jo,Zv=(e,t)=>e.sort((r,n)=>Qv(r,n,t));var xv=Zv;const ew=Jo,tw=(e,t)=>e.sort((r,n)=>ew(n,r,t));var rw=tw;const nw=xe,sw=(e,t,r)=>nw(e,t,r)>0;var cs=sw;const aw=xe,ow=(e,t,r)=>aw(e,t,r)<0;var Xo=ow;const iw=xe,cw=(e,t,r)=>iw(e,t,r)===0;var Fu=cw;const lw=xe,uw=(e,t,r)=>lw(e,t,r)!==0;var Vu=uw;const dw=xe,fw=(e,t,r)=>dw(e,t,r)>=0;var Yo=fw;const hw=xe,pw=(e,t,r)=>hw(e,t,r)<=0;var Qo=pw;const mw=Fu,$w=Vu,yw=cs,_w=Yo,gw=Xo,vw=Qo,ww=(e,t,r,n)=>{switch(t){case"===":return typeof e=="object"&&(e=e.version),typeof r=="object"&&(r=r.version),e===r;case"!==":return typeof e=="object"&&(e=e.version),typeof r=="object"&&(r=r.version),e!==r;case"":case"=":case"==":return mw(e,r,n);case"!=":return $w(e,r,n);case">":return yw(e,r,n);case">=":return _w(e,r,n);case"<":return gw(e,r,n);case"<=":return vw(e,r,n);default:throw new TypeError(`Invalid operator: ${t}`)}};var Uu=ww;const Ew=ke,Sw=Nr,{safeRe:bn,t:Pn}=rn,bw=(e,t)=>{if(e instanceof Ew)return e;if(typeof e=="number"&&(e=String(e)),typeof e!="string")return null;t=t||{};let r=null;if(!t.rtl)r=e.match(t.includePrerelease?bn[Pn.COERCEFULL]:bn[Pn.COERCE]);else{const l=t.includePrerelease?bn[Pn.COERCERTLFULL]:bn[Pn.COERCERTL];let u;for(;(u=l.exec(e))&&(!r||r.index+r[0].length!==e.length);)(!r||u.index+u[0].length!==r.index+r[0].length)&&(r=u),l.lastIndex=u.index+u[1].length+u[2].length;l.lastIndex=-1}if(r===null)return null;const n=r[2],s=r[3]||"0",a=r[4]||"0",i=t.includePrerelease&&r[5]?`-${r[5]}`:"",d=t.includePrerelease&&r[6]?`+${r[6]}`:"";return Sw(`${n}.${s}.${a}${i}${d}`,t)};var Pw=bw;class Nw{constructor(){this.max=1e3,this.map=new Map}get(t){const r=this.map.get(t);if(r!==void 0)return this.map.delete(t),this.map.set(t,r),r}delete(t){return this.map.delete(t)}set(t,r){if(!this.delete(t)&&r!==void 0){if(this.map.size>=this.max){const s=this.map.keys().next().value;this.delete(s)}this.map.set(t,r)}return this}}var Ow=Nw,Ps,hc;function et(){if(hc)return Ps;hc=1;const e=/\s+/g;class t{constructor(k,U){if(U=s(U),k instanceof t)return k.loose===!!U.loose&&k.includePrerelease===!!U.includePrerelease?k:new t(k.raw,U);if(k instanceof a)return this.raw=k.value,this.set=[[k]],this.formatted=void 0,this;if(this.options=U,this.loose=!!U.loose,this.includePrerelease=!!U.includePrerelease,this.raw=k.trim().replace(e," "),this.set=this.raw.split("||").map(D=>this.parseRange(D.trim())).filter(D=>D.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const D=this.set[0];if(this.set=this.set.filter(R=>!g(R[0])),this.set.length===0)this.set=[D];else if(this.set.length>1){for(const R of this.set)if(R.length===1&&$(R[0])){this.set=[R];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let k=0;k<this.set.length;k++){k>0&&(this.formatted+="||");const U=this.set[k];for(let D=0;D<U.length;D++)D>0&&(this.formatted+=" "),this.formatted+=U[D].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(k){const D=((this.options.includePrerelease&&y)|(this.options.loose&&v))+":"+k,R=n.get(D);if(R)return R;const I=this.options.loose,E=I?l[u.HYPHENRANGELOOSE]:l[u.HYPHENRANGE];k=k.replace(E,Q(this.options.includePrerelease)),i("hyphen replace",k),k=k.replace(l[u.COMPARATORTRIM],c),i("comparator trim",k),k=k.replace(l[u.TILDETRIM],h),i("tilde trim",k),k=k.replace(l[u.CARETTRIM],S),i("caret trim",k);let m=k.split(" ").map(f=>w(f,this.options)).join(" ").split(/\s+/).map(f=>ne(f,this.options));I&&(m=m.filter(f=>(i("loose invalid filter",f,this.options),!!f.match(l[u.COMPARATORLOOSE])))),i("range list",m);const b=new Map,_=m.map(f=>new a(f,this.options));for(const f of _){if(g(f))return[f];b.set(f.value,f)}b.size>1&&b.has("")&&b.delete("");const o=[...b.values()];return n.set(D,o),o}intersects(k,U){if(!(k instanceof t))throw new TypeError("a Range is required");return this.set.some(D=>p(D,U)&&k.set.some(R=>p(R,U)&&D.every(I=>R.every(E=>I.intersects(E,U)))))}test(k){if(!k)return!1;if(typeof k=="string")try{k=new d(k,this.options)}catch{return!1}for(let U=0;U<this.set.length;U++)if(fe(this.set[U],k,this.options))return!0;return!1}}Ps=t;const r=Ow,n=new r,s=Wo,a=ls(),i=is,d=ke,{safeRe:l,t:u,comparatorTrimReplace:c,tildeTrimReplace:h,caretTrimReplace:S}=rn,{FLAG_INCLUDE_PRERELEASE:y,FLAG_LOOSE:v}=os,g=C=>C.value==="<0.0.0-0",$=C=>C.value==="",p=(C,k)=>{let U=!0;const D=C.slice();let R=D.pop();for(;U&&D.length;)U=D.every(I=>R.intersects(I,k)),R=D.pop();return U},w=(C,k)=>(i("comp",C,k),C=z(C,k),i("caret",C),C=O(C,k),i("tildes",C),C=de(C,k),i("xrange",C),C=H(C,k),i("stars",C),C),N=C=>!C||C.toLowerCase()==="x"||C==="*",O=(C,k)=>C.trim().split(/\s+/).map(U=>T(U,k)).join(" "),T=(C,k)=>{const U=k.loose?l[u.TILDELOOSE]:l[u.TILDE];return C.replace(U,(D,R,I,E,m)=>{i("tilde",C,D,R,I,E,m);let b;return N(R)?b="":N(I)?b=`>=${R}.0.0 <${+R+1}.0.0-0`:N(E)?b=`>=${R}.${I}.0 <${R}.${+I+1}.0-0`:m?(i("replaceTilde pr",m),b=`>=${R}.${I}.${E}-${m} <${R}.${+I+1}.0-0`):b=`>=${R}.${I}.${E} <${R}.${+I+1}.0-0`,i("tilde return",b),b})},z=(C,k)=>C.trim().split(/\s+/).map(U=>B(U,k)).join(" "),B=(C,k)=>{i("caret",C,k);const U=k.loose?l[u.CARETLOOSE]:l[u.CARET],D=k.includePrerelease?"-0":"";return C.replace(U,(R,I,E,m,b)=>{i("caret",C,R,I,E,m,b);let _;return N(I)?_="":N(E)?_=`>=${I}.0.0${D} <${+I+1}.0.0-0`:N(m)?I==="0"?_=`>=${I}.${E}.0${D} <${I}.${+E+1}.0-0`:_=`>=${I}.${E}.0${D} <${+I+1}.0.0-0`:b?(i("replaceCaret pr",b),I==="0"?E==="0"?_=`>=${I}.${E}.${m}-${b} <${I}.${E}.${+m+1}-0`:_=`>=${I}.${E}.${m}-${b} <${I}.${+E+1}.0-0`:_=`>=${I}.${E}.${m}-${b} <${+I+1}.0.0-0`):(i("no pr"),I==="0"?E==="0"?_=`>=${I}.${E}.${m}${D} <${I}.${E}.${+m+1}-0`:_=`>=${I}.${E}.${m}${D} <${I}.${+E+1}.0-0`:_=`>=${I}.${E}.${m} <${+I+1}.0.0-0`),i("caret return",_),_})},de=(C,k)=>(i("replaceXRanges",C,k),C.split(/\s+/).map(U=>V(U,k)).join(" ")),V=(C,k)=>{C=C.trim();const U=k.loose?l[u.XRANGELOOSE]:l[u.XRANGE];return C.replace(U,(D,R,I,E,m,b)=>{i("xRange",C,D,R,I,E,m,b);const _=N(I),o=_||N(E),f=o||N(m),P=f;return R==="="&&P&&(R=""),b=k.includePrerelease?"-0":"",_?R===">"||R==="<"?D="<0.0.0-0":D="*":R&&P?(o&&(E=0),m=0,R===">"?(R=">=",o?(I=+I+1,E=0,m=0):(E=+E+1,m=0)):R==="<="&&(R="<",o?I=+I+1:E=+E+1),R==="<"&&(b="-0"),D=`${R+I}.${E}.${m}${b}`):o?D=`>=${I}.0.0${b} <${+I+1}.0.0-0`:f&&(D=`>=${I}.${E}.0${b} <${I}.${+E+1}.0-0`),i("xRange return",D),D})},H=(C,k)=>(i("replaceStars",C,k),C.trim().replace(l[u.STAR],"")),ne=(C,k)=>(i("replaceGTE0",C,k),C.trim().replace(l[k.includePrerelease?u.GTE0PRE:u.GTE0],"")),Q=C=>(k,U,D,R,I,E,m,b,_,o,f,P)=>(N(D)?U="":N(R)?U=`>=${D}.0.0${C?"-0":""}`:N(I)?U=`>=${D}.${R}.0${C?"-0":""}`:E?U=`>=${U}`:U=`>=${U}${C?"-0":""}`,N(_)?b="":N(o)?b=`<${+_+1}.0.0-0`:N(f)?b=`<${_}.${+o+1}.0-0`:P?b=`<=${_}.${o}.${f}-${P}`:C?b=`<${_}.${o}.${+f+1}-0`:b=`<=${b}`,`${U} ${b}`.trim()),fe=(C,k,U)=>{for(let D=0;D<C.length;D++)if(!C[D].test(k))return!1;if(k.prerelease.length&&!U.includePrerelease){for(let D=0;D<C.length;D++)if(i(C[D].semver),C[D].semver!==a.ANY&&C[D].semver.prerelease.length>0){const R=C[D].semver;if(R.major===k.major&&R.minor===k.minor&&R.patch===k.patch)return!0}return!1}return!0};return Ps}var Ns,pc;function ls(){if(pc)return Ns;pc=1;const e=Symbol("SemVer ANY");class t{static get ANY(){return e}constructor(c,h){if(h=r(h),c instanceof t){if(c.loose===!!h.loose)return c;c=c.value}c=c.trim().split(/\s+/).join(" "),i("comparator",c,h),this.options=h,this.loose=!!h.loose,this.parse(c),this.semver===e?this.value="":this.value=this.operator+this.semver.version,i("comp",this)}parse(c){const h=this.options.loose?n[s.COMPARATORLOOSE]:n[s.COMPARATOR],S=c.match(h);if(!S)throw new TypeError(`Invalid comparator: ${c}`);this.operator=S[1]!==void 0?S[1]:"",this.operator==="="&&(this.operator=""),S[2]?this.semver=new d(S[2],this.options.loose):this.semver=e}toString(){return this.value}test(c){if(i("Comparator.test",c,this.options.loose),this.semver===e||c===e)return!0;if(typeof c=="string")try{c=new d(c,this.options)}catch{return!1}return a(c,this.operator,this.semver,this.options)}intersects(c,h){if(!(c instanceof t))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new l(c.value,h).test(this.value):c.operator===""?c.value===""?!0:new l(this.value,h).test(c.semver):(h=r(h),h.includePrerelease&&(this.value==="<0.0.0-0"||c.value==="<0.0.0-0")||!h.includePrerelease&&(this.value.startsWith("<0.0.0")||c.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&c.operator.startsWith(">")||this.operator.startsWith("<")&&c.operator.startsWith("<")||this.semver.version===c.semver.version&&this.operator.includes("=")&&c.operator.includes("=")||a(this.semver,"<",c.semver,h)&&this.operator.startsWith(">")&&c.operator.startsWith("<")||a(this.semver,">",c.semver,h)&&this.operator.startsWith("<")&&c.operator.startsWith(">")))}}Ns=t;const r=Wo,{safeRe:n,t:s}=rn,a=Uu,i=is,d=ke,l=et();return Ns}const Rw=et(),Iw=(e,t,r)=>{try{t=new Rw(t,r)}catch{return!1}return t.test(e)};var us=Iw;const Tw=et(),jw=(e,t)=>new Tw(e,t).set.map(r=>r.map(n=>n.value).join(" ").trim().split(" "));var Aw=jw;const kw=ke,Cw=et(),Dw=(e,t,r)=>{let n=null,s=null,a=null;try{a=new Cw(t,r)}catch{return null}return e.forEach(i=>{a.test(i)&&(!n||s.compare(i)===-1)&&(n=i,s=new kw(n,r))}),n};var Mw=Dw;const Lw=ke,Fw=et(),Vw=(e,t,r)=>{let n=null,s=null,a=null;try{a=new Fw(t,r)}catch{return null}return e.forEach(i=>{a.test(i)&&(!n||s.compare(i)===1)&&(n=i,s=new Lw(n,r))}),n};var Uw=Vw;const Os=ke,zw=et(),mc=cs,qw=(e,t)=>{e=new zw(e,t);let r=new Os("0.0.0");if(e.test(r)||(r=new Os("0.0.0-0"),e.test(r)))return r;r=null;for(let n=0;n<e.set.length;++n){const s=e.set[n];let a=null;s.forEach(i=>{const d=new Os(i.semver.version);switch(i.operator){case">":d.prerelease.length===0?d.patch++:d.prerelease.push(0),d.raw=d.format();case"":case">=":(!a||mc(d,a))&&(a=d);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${i.operator}`)}}),a&&(!r||mc(r,a))&&(r=a)}return r&&e.test(r)?r:null};var Kw=qw;const Gw=et(),Hw=(e,t)=>{try{return new Gw(e,t).range||"*"}catch{return null}};var Bw=Hw;const Ww=ke,zu=ls(),{ANY:Jw}=zu,Xw=et(),Yw=us,$c=cs,yc=Xo,Qw=Qo,Zw=Yo,xw=(e,t,r,n)=>{e=new Ww(e,n),t=new Xw(t,n);let s,a,i,d,l;switch(r){case">":s=$c,a=Qw,i=yc,d=">",l=">=";break;case"<":s=yc,a=Zw,i=$c,d="<",l="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Yw(e,t,n))return!1;for(let u=0;u<t.set.length;++u){const c=t.set[u];let h=null,S=null;if(c.forEach(y=>{y.semver===Jw&&(y=new zu(">=0.0.0")),h=h||y,S=S||y,s(y.semver,h.semver,n)?h=y:i(y.semver,S.semver,n)&&(S=y)}),h.operator===d||h.operator===l||(!S.operator||S.operator===d)&&a(e,S.semver))return!1;if(S.operator===l&&i(e,S.semver))return!1}return!0};var Zo=xw;const eE=Zo,tE=(e,t,r)=>eE(e,t,">",r);var rE=tE;const nE=Zo,sE=(e,t,r)=>nE(e,t,"<",r);var aE=sE;const _c=et(),oE=(e,t,r)=>(e=new _c(e,r),t=new _c(t,r),e.intersects(t,r));var iE=oE;const cE=us,lE=xe;var uE=(e,t,r)=>{const n=[];let s=null,a=null;const i=e.sort((c,h)=>lE(c,h,r));for(const c of i)cE(c,t,r)?(a=c,s||(s=c)):(a&&n.push([s,a]),a=null,s=null);s&&n.push([s,null]);const d=[];for(const[c,h]of n)c===h?d.push(c):!h&&c===i[0]?d.push("*"):h?c===i[0]?d.push(`<=${h}`):d.push(`${c} - ${h}`):d.push(`>=${c}`);const l=d.join(" || "),u=typeof t.raw=="string"?t.raw:String(t);return l.length<u.length?l:t};const gc=et(),xo=ls(),{ANY:Rs}=xo,Cr=us,ei=xe,dE=(e,t,r={})=>{if(e===t)return!0;e=new gc(e,r),t=new gc(t,r);let n=!1;e:for(const s of e.set){for(const a of t.set){const i=hE(s,a,r);if(n=n||i!==null,i)continue e}if(n)return!1}return!0},fE=[new xo(">=0.0.0-0")],vc=[new xo(">=0.0.0")],hE=(e,t,r)=>{if(e===t)return!0;if(e.length===1&&e[0].semver===Rs){if(t.length===1&&t[0].semver===Rs)return!0;r.includePrerelease?e=fE:e=vc}if(t.length===1&&t[0].semver===Rs){if(r.includePrerelease)return!0;t=vc}const n=new Set;let s,a;for(const y of e)y.operator===">"||y.operator===">="?s=wc(s,y,r):y.operator==="<"||y.operator==="<="?a=Ec(a,y,r):n.add(y.semver);if(n.size>1)return null;let i;if(s&&a){if(i=ei(s.semver,a.semver,r),i>0)return null;if(i===0&&(s.operator!==">="||a.operator!=="<="))return null}for(const y of n){if(s&&!Cr(y,String(s),r)||a&&!Cr(y,String(a),r))return null;for(const v of t)if(!Cr(y,String(v),r))return!1;return!0}let d,l,u,c,h=a&&!r.includePrerelease&&a.semver.prerelease.length?a.semver:!1,S=s&&!r.includePrerelease&&s.semver.prerelease.length?s.semver:!1;h&&h.prerelease.length===1&&a.operator==="<"&&h.prerelease[0]===0&&(h=!1);for(const y of t){if(c=c||y.operator===">"||y.operator===">=",u=u||y.operator==="<"||y.operator==="<=",s){if(S&&y.semver.prerelease&&y.semver.prerelease.length&&y.semver.major===S.major&&y.semver.minor===S.minor&&y.semver.patch===S.patch&&(S=!1),y.operator===">"||y.operator===">="){if(d=wc(s,y,r),d===y&&d!==s)return!1}else if(s.operator===">="&&!Cr(s.semver,String(y),r))return!1}if(a){if(h&&y.semver.prerelease&&y.semver.prerelease.length&&y.semver.major===h.major&&y.semver.minor===h.minor&&y.semver.patch===h.patch&&(h=!1),y.operator==="<"||y.operator==="<="){if(l=Ec(a,y,r),l===y&&l!==a)return!1}else if(a.operator==="<="&&!Cr(a.semver,String(y),r))return!1}if(!y.operator&&(a||s)&&i!==0)return!1}return!(s&&u&&!a&&i!==0||a&&c&&!s&&i!==0||S||h)},wc=(e,t,r)=>{if(!e)return t;const n=ei(e.semver,t.semver,r);return n>0?e:n<0||t.operator===">"&&e.operator===">="?t:e},Ec=(e,t,r)=>{if(!e)return t;const n=ei(e.semver,t.semver,r);return n<0?e:n>0||t.operator==="<"&&e.operator==="<="?t:e};var pE=dE;const Is=rn,Sc=os,mE=ke,bc=Lu,$E=Nr,yE=Sv,_E=Nv,gE=Rv,vE=Tv,wE=kv,EE=Mv,SE=Vv,bE=qv,PE=xe,NE=Bv,OE=Xv,RE=Jo,IE=xv,TE=rw,jE=cs,AE=Xo,kE=Fu,CE=Vu,DE=Yo,ME=Qo,LE=Uu,FE=Pw,VE=ls(),UE=et(),zE=us,qE=Aw,KE=Mw,GE=Uw,HE=Kw,BE=Bw,WE=Zo,JE=rE,XE=aE,YE=iE,QE=uE,ZE=pE;var xE={parse:$E,valid:yE,clean:_E,inc:gE,diff:vE,major:wE,minor:EE,patch:SE,prerelease:bE,compare:PE,rcompare:NE,compareLoose:OE,compareBuild:RE,sort:IE,rsort:TE,gt:jE,lt:AE,eq:kE,neq:CE,gte:DE,lte:ME,cmp:LE,coerce:FE,Comparator:VE,Range:UE,satisfies:zE,toComparators:qE,maxSatisfying:KE,minSatisfying:GE,minVersion:HE,validRange:BE,outside:WE,gtr:JE,ltr:XE,intersects:YE,simplifyRange:QE,subset:ZE,SemVer:mE,re:Is.re,src:Is.src,tokens:Is.t,SEMVER_SPEC_VERSION:Sc.SEMVER_SPEC_VERSION,RELEASE_TYPES:Sc.RELEASE_TYPES,compareIdentifiers:bc.compareIdentifiers,rcompareIdentifiers:bc.rcompareIdentifiers},ds={exports:{}},ti={exports:{}};const qu=(e,t)=>{for(const r of Reflect.ownKeys(t))Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r));return e};ti.exports=qu;ti.exports.default=qu;var eS=ti.exports;const tS=eS,Bn=new WeakMap,Ku=(e,t={})=>{if(typeof e!="function")throw new TypeError("Expected a function");let r,n=0;const s=e.displayName||e.name||"<anonymous>",a=function(...i){if(Bn.set(a,++n),n===1)r=e.apply(this,i),e=null;else if(t.throw===!0)throw new Error(`Function \`${s}\` can only be called once`);return r};return tS(a,e),Bn.set(a,n),a};ds.exports=Ku;ds.exports.default=Ku;ds.exports.callCount=e=>{if(!Bn.has(e))throw new Error(`The given function \`${e.name}\` is not wrapped by the \`onetime\` package`);return Bn.get(e)};var rS=ds.exports;(function(e,t){var r=nn&&nn.__classPrivateFieldSet||function(D,R,I,E,m){if(E==="m")throw new TypeError("Private method is not writable");if(E==="a"&&!m)throw new TypeError("Private accessor was defined without a setter");if(typeof R=="function"?D!==R||!m:!R.has(D))throw new TypeError("Cannot write private member to an object whose class did not declare it");return E==="a"?m.call(D,I):m?m.value=I:R.set(D,I),I},n=nn&&nn.__classPrivateFieldGet||function(D,R,I,E){if(I==="a"&&!E)throw new TypeError("Private accessor was defined without a getter");if(typeof R=="function"?D!==R||!E:!R.has(D))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?E:I==="a"?E.call(D):E?E.value:R.get(D)},s,a,i,d,l,u;Object.defineProperty(t,"__esModule",{value:!0});const c=xs,h=Qt,S=Nt,y=Ju,v=Xu,g=Yu,$=ld,p=vd,w=bd,N=st,O=F$,T=Y0,z=iv,B=xE,de=rS,V="aes-256-cbc",H=()=>Object.create(null),ne=D=>D!=null;let Q="";try{delete require.cache[__filename],Q=S.dirname((a=(s=e.parent)===null||s===void 0?void 0:s.filename)!==null&&a!==void 0?a:".")}catch{}const fe=(D,R)=>{const I=new Set(["undefined","symbol","function"]),E=typeof R;if(I.has(E))throw new TypeError(`Setting a value of type \`${E}\` for key \`${D}\` is not allowed as it's not supported by JSON`)},C="__internal__",k=`${C}.migrations.version`;class U{constructor(R={}){var I;i.set(this,void 0),d.set(this,void 0),l.set(this,void 0),u.set(this,{}),this._deserialize=f=>JSON.parse(f),this._serialize=f=>JSON.stringify(f,void 0,"	");const E={configName:"config",fileExtension:"json",projectSuffix:"nodejs",clearInvalidConfig:!1,accessPropertiesByDotNotation:!0,configFileMode:438,...R},m=de(()=>{const f=p.sync({cwd:Q}),P=f&&JSON.parse(h.readFileSync(f,"utf8"));return P??{}});if(!E.cwd){if(E.projectName||(E.projectName=m().name),!E.projectName)throw new Error("Project name could not be inferred. Please specify the `projectName` option.");E.cwd=w(E.projectName,{suffix:E.projectSuffix}).config}if(r(this,l,E,"f"),E.schema){if(typeof E.schema!="object")throw new TypeError("The `schema` option must be an object.");const f=new O.default({allErrors:!0,useDefaults:!0});(0,T.default)(f);const P={type:"object",properties:E.schema};r(this,i,f.compile(P),"f");for(const[j,A]of Object.entries(E.schema))A!=null&&A.default&&(n(this,u,"f")[j]=A.default)}E.defaults&&r(this,u,{...n(this,u,"f"),...E.defaults},"f"),E.serialize&&(this._serialize=E.serialize),E.deserialize&&(this._deserialize=E.deserialize),this.events=new g.EventEmitter,r(this,d,E.encryptionKey,"f");const b=E.fileExtension?`.${E.fileExtension}`:"";this.path=S.resolve(E.cwd,`${(I=E.configName)!==null&&I!==void 0?I:"config"}${b}`);const _=this.store,o=Object.assign(H(),E.defaults,_);this._validate(o);try{v.deepEqual(_,o)}catch{this.store=o}if(E.watch&&this._watch(),E.migrations){if(E.projectVersion||(E.projectVersion=m().version),!E.projectVersion)throw new Error("Project version could not be inferred. Please specify the `projectVersion` option.");this._migrate(E.migrations,E.projectVersion,E.beforeEachMigration)}}get(R,I){if(n(this,l,"f").accessPropertiesByDotNotation)return this._get(R,I);const{store:E}=this;return R in E?E[R]:I}set(R,I){if(typeof R!="string"&&typeof R!="object")throw new TypeError(`Expected \`key\` to be of type \`string\` or \`object\`, got ${typeof R}`);if(typeof R!="object"&&I===void 0)throw new TypeError("Use `delete()` to clear values");if(this._containsReservedKey(R))throw new TypeError(`Please don't use the ${C} key, as it's used to manage this module internal operations.`);const{store:E}=this,m=(b,_)=>{fe(b,_),n(this,l,"f").accessPropertiesByDotNotation?$.set(E,b,_):E[b]=_};if(typeof R=="object"){const b=R;for(const[_,o]of Object.entries(b))m(_,o)}else m(R,I);this.store=E}has(R){return n(this,l,"f").accessPropertiesByDotNotation?$.has(this.store,R):R in this.store}reset(...R){for(const I of R)ne(n(this,u,"f")[I])&&this.set(I,n(this,u,"f")[I])}delete(R){const{store:I}=this;n(this,l,"f").accessPropertiesByDotNotation?$.delete(I,R):delete I[R],this.store=I}clear(){this.store=H();for(const R of Object.keys(n(this,u,"f")))this.reset(R)}onDidChange(R,I){if(typeof R!="string")throw new TypeError(`Expected \`key\` to be of type \`string\`, got ${typeof R}`);if(typeof I!="function")throw new TypeError(`Expected \`callback\` to be of type \`function\`, got ${typeof I}`);return this._handleChange(()=>this.get(R),I)}onDidAnyChange(R){if(typeof R!="function")throw new TypeError(`Expected \`callback\` to be of type \`function\`, got ${typeof R}`);return this._handleChange(()=>this.store,R)}get size(){return Object.keys(this.store).length}get store(){try{const R=h.readFileSync(this.path,n(this,d,"f")?null:"utf8"),I=this._encryptData(R),E=this._deserialize(I);return this._validate(E),Object.assign(H(),E)}catch(R){if((R==null?void 0:R.code)==="ENOENT")return this._ensureDirectory(),H();if(n(this,l,"f").clearInvalidConfig&&R.name==="SyntaxError")return H();throw R}}set store(R){this._ensureDirectory(),this._validate(R),this._write(R),this.events.emit("change")}*[(i=new WeakMap,d=new WeakMap,l=new WeakMap,u=new WeakMap,Symbol.iterator)](){for(const[R,I]of Object.entries(this.store))yield[R,I]}_encryptData(R){if(!n(this,d,"f"))return R.toString();try{if(n(this,d,"f"))try{if(R.slice(16,17).toString()===":"){const I=R.slice(0,16),E=y.pbkdf2Sync(n(this,d,"f"),I.toString(),1e4,32,"sha512"),m=y.createDecipheriv(V,E,I);R=Buffer.concat([m.update(Buffer.from(R.slice(17))),m.final()]).toString("utf8")}else{const I=y.createDecipher(V,n(this,d,"f"));R=Buffer.concat([I.update(Buffer.from(R)),I.final()]).toString("utf8")}}catch{}}catch{}return R.toString()}_handleChange(R,I){let E=R();const m=()=>{const b=E,_=R();(0,c.isDeepStrictEqual)(_,b)||(E=_,I.call(this,_,b))};return this.events.on("change",m),()=>this.events.removeListener("change",m)}_validate(R){if(!n(this,i,"f")||n(this,i,"f").call(this,R)||!n(this,i,"f").errors)return;const E=n(this,i,"f").errors.map(({instancePath:m,message:b=""})=>`\`${m.slice(1)}\` ${b}`);throw new Error("Config schema violation: "+E.join("; "))}_ensureDirectory(){h.mkdirSync(S.dirname(this.path),{recursive:!0})}_write(R){let I=this._serialize(R);if(n(this,d,"f")){const E=y.randomBytes(16),m=y.pbkdf2Sync(n(this,d,"f"),E.toString(),1e4,32,"sha512"),b=y.createCipheriv(V,m,E);I=Buffer.concat([E,Buffer.from(":"),b.update(Buffer.from(I)),b.final()])}if(process.env.SNAP)h.writeFileSync(this.path,I,{mode:n(this,l,"f").configFileMode});else try{N.writeFileSync(this.path,I,{mode:n(this,l,"f").configFileMode})}catch(E){if((E==null?void 0:E.code)==="EXDEV"){h.writeFileSync(this.path,I,{mode:n(this,l,"f").configFileMode});return}throw E}}_watch(){this._ensureDirectory(),h.existsSync(this.path)||this._write(H()),process.platform==="win32"?h.watch(this.path,{persistent:!1},z(()=>{this.events.emit("change")},{wait:100})):h.watchFile(this.path,{persistent:!1},z(()=>{this.events.emit("change")},{wait:5e3}))}_migrate(R,I,E){let m=this._get(k,"0.0.0");const b=Object.keys(R).filter(o=>this._shouldPerformMigration(o,m,I));let _={...this.store};for(const o of b)try{E&&E(this,{fromVersion:m,toVersion:o,finalVersion:I,versions:b});const f=R[o];f(this),this._set(k,o),m=o,_={...this.store}}catch(f){throw this.store=_,new Error(`Something went wrong during the migration! Changes applied to the store until this failed migration will be restored. ${f}`)}(this._isVersionInRangeFormat(m)||!B.eq(m,I))&&this._set(k,I)}_containsReservedKey(R){return typeof R=="object"&&Object.keys(R)[0]===C?!0:typeof R!="string"?!1:n(this,l,"f").accessPropertiesByDotNotation?!!R.startsWith(`${C}.`):!1}_isVersionInRangeFormat(R){return B.clean(R)===null}_shouldPerformMigration(R,I,E){return this._isVersionInRangeFormat(R)?I!=="0.0.0"&&B.satisfies(I,R)?!1:B.satisfies(E,R):!(B.lte(R,I)||B.gt(R,E))}_get(R,I){return $.get(this.store,R,I)}_set(R,I){const{store:E}=this;$.set(E,R,I),this.store=E}}t.default=U,e.exports=U,e.exports.default=U})(As,As.exports);var nS=As.exports;const Pc=Nt,{app:Dn,ipcMain:Zs,ipcRenderer:Nc,shell:sS}=ae,aS=nS;let Oc=!1;const Rc=()=>{if(!Zs||!Dn)throw new Error("Electron Store: You need to call `.initRenderer()` from the main process.");const e={defaultCwd:Dn.getPath("userData"),appVersion:Dn.getVersion()};return Oc||(Zs.on("electron-store-get-data",t=>{t.returnValue=e}),Oc=!0),e};class oS extends aS{constructor(t){let r,n;if(Nc){const s=Nc.sendSync("electron-store-get-data");if(!s)throw new Error("Electron Store: You need to call `.initRenderer()` from the main process.");({defaultCwd:r,appVersion:n}=s)}else Zs&&Dn&&({defaultCwd:r,appVersion:n}=Rc());t={name:"config",...t},t.projectVersion||(t.projectVersion=n),t.cwd?t.cwd=Pc.isAbsolute(t.cwd)?t.cwd:Pc.join(r,t.cwd):t.cwd=r,t.configName=t.name,delete t.name,super(t)}static initRenderer(){Rc()}async openInEditor(){const t=await sS.openPath(this.path);if(t)throw new Error(t)}}var iS=oS;const cS=Ic(iS);ad&&ae.app.quit();ae.app.setName("QU Scheduler");process.platform==="win32"&&ae.app.setAppUserModelId("qa.edu.qu.scheduler");ae.app.isPackaged?(console.log("Running in production mode"),ae.app.commandLine.appendSwitch("--disable-web-security","false"),ae.app.commandLine.appendSwitch("--disable-features","VizDisplayCompositor")):console.log("Running in development mode");const Gt=new cS;let Ae=null;const lS=()=>{ae.ipcMain.handle("store:get",(e,t)=>Gt.get(t)),ae.ipcMain.handle("store:set",(e,t,r)=>{if(t==="uiState"){const s={...Gt.get("uiState"),...r};Gt.set(t,s)}else Gt.set(t,r)}),ae.ipcMain.handle("store:delete",(e,t)=>{Gt.delete(t)}),ae.ipcMain.handle("store:clear",()=>{Gt.clear()}),ae.ipcMain.handle("dialog:showSaveDialog",async(e,t)=>{if(!Ae)throw new Error("Main window not available");return await ae.dialog.showSaveDialog(Ae,t)}),ae.ipcMain.handle("dialog:showOpenDialog",async(e,t)=>{if(!Ae)throw new Error("Main window not available");return await ae.dialog.showOpenDialog(Ae,t)}),ae.ipcMain.handle("fs:writeFile",async(e,t,r)=>{try{return await Qt.promises.writeFile(t,r,"utf8"),{success:!0}}catch(n){return{success:!1,error:n instanceof Error?n.message:"Unknown error"}}}),ae.ipcMain.handle("fs:readFile",async(e,t)=>{try{return{success:!0,data:await Qt.promises.readFile(t,"utf8")}}catch(r){return{success:!1,error:r instanceof Error?r.message:"Unknown error"}}}),console.log("Registering PDF generation handler..."),ae.ipcMain.handle("pdf:generate",async(e,t,r)=>{console.log("PDF generation handler called with filename:",r);try{if(!Ae)throw console.error("Main window not available"),new Error("Main window not available");console.log("Main window available, proceeding with PDF generation...");const n=new ae.BrowserWindow({width:800,height:600,show:!1,webPreferences:{nodeIntegration:!1,contextIsolation:!0}});await n.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(t)}`),await new Promise(i=>{n.webContents.once("did-finish-load",()=>{setTimeout(i,2e3)})});const s=await n.webContents.printToPDF({pageSize:"A4",printBackground:!0,margins:{top:.5,bottom:.5,left:.5,right:.5}});n.close();const a=await ae.dialog.showSaveDialog(Ae,{defaultPath:r,filters:[{name:"PDF Files",extensions:["pdf"]}]});return!a.canceled&&a.filePath?(await Qt.promises.writeFile(a.filePath,s),{success:!0,filePath:a.filePath}):{success:!1,error:"Save operation was cancelled"}}catch(n){return console.error("Error in PDF generation:",n),{success:!1,error:n instanceof Error?n.message:"Unknown error"}}})},Gu=()=>{Ae=new ae.BrowserWindow({width:1200,height:800,title:"QU Scheduler",icon:Ir.join(__dirname,"../../assets/icons/icon.ico"),webPreferences:{preload:Ir.join(__dirname,"preload.js"),nodeIntegration:!1,contextIsolation:!0}}),ae.session.defaultSession.webRequest.onHeadersReceived((e,t)=>{const n=!ae.app.isPackaged?"default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' http://localhost:* ws://localhost:*; style-src 'self' 'unsafe-inline'; font-src 'self' data:; img-src 'self' data: blob:; connect-src 'self' http://localhost:* ws://localhost:*; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none';":"default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; font-src 'self' data:; img-src 'self' data: blob:; connect-src 'self'; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none';";t({responseHeaders:{...e.responseHeaders,"Content-Security-Policy":[n],"X-Content-Type-Options":["nosniff"],"X-Frame-Options":["DENY"],"X-XSS-Protection":["1; mode=block"],"Referrer-Policy":["strict-origin-when-cross-origin"]}})}),Ae.webContents.once("did-finish-load",()=>{try{const e=Gt.get("uiState"),t=(e==null?void 0:e.zoomFactor)||.8;Ae==null||Ae.webContents.setZoomFactor(t)}catch(e){console.error("Error setting initial zoom factor:",e),Ae==null||Ae.webContents.setZoomFactor(.8)}});{const e=ae.app.getAppPath(),t=Ir.dirname(Ir.dirname(e)),r=Ir.join(t,"renderer","main_window","index.html");console.log("Loading renderer from:",r),console.log("MAIN_WINDOW_VITE_NAME:","main_window"),console.log("__dirname:",__dirname),console.log("app.getAppPath():",e),console.log("appRoot:",t),Ae.loadFile(r)}};ae.app.on("ready",()=>{lS(),Gu()});ae.app.on("window-all-closed",()=>{process.platform!=="darwin"&&ae.app.quit()});ae.app.on("activate",()=>{ae.BrowserWindow.getAllWindows().length===0&&Gu()});
"use strict";const o=require("electron"),a=async e=>{try{const t={...await o.ipcRenderer.invoke("store:get","uiState"),zoomFactor:Math.round(e*10)/10};await o.ipcRenderer.invoke("store:set","uiState",t)}catch(r){console.error("Error saving zoom factor:",r)}};o.contextBridge.exposeInMainWorld("electronAPI",{store:{get:e=>o.ipcRenderer.invoke("store:get",e),set:(e,r)=>o.ipcRenderer.invoke("store:set",e,r),delete:e=>o.ipcRenderer.invoke("store:delete",e),clear:()=>o.ipcRenderer.invoke("store:clear")},dialog:{showSaveDialog:e=>o.ipcRenderer.invoke("dialog:showSaveDialog",e),showOpenDialog:e=>o.ipcRenderer.invoke("dialog:showOpenDialog",e)},fs:{writeFile:(e,r)=>o.ipcRenderer.invoke("fs:writeFile",e,r),readFile:e=>o.ipcRenderer.invoke("fs:readFile",e)},zoom:{zoomIn:()=>{const e=o.webFrame.getZoomFactor(),r=Math.min(e+.1,2);o.webFrame.setZoomFactor(r);const t=o.webFrame.getZoomFactor();return a(t),t},zoomOut:()=>{const e=o.webFrame.getZoomFactor(),r=Math.max(e-.1,.5);o.webFrame.setZoomFactor(r);const t=o.webFrame.getZoomFactor();return a(t),t},resetZoom:()=>{o.webFrame.setZoomFactor(1);const e=o.webFrame.getZoomFactor();return a(e),e},setZoomFactor:e=>{o.webFrame.setZoomFactor(e);const r=o.webFrame.getZoomFactor();return a(r),r},getZoomFactor:()=>o.webFrame.getZoomFactor()},pdf:{generate:(e,r)=>o.ipcRenderer.invoke("pdf:generate",e,r)},shell:{openExternal:e=>o.shell.openExternal(e)}});
{
  "name": "qu-scheduler",
  "productName": "QU Scheduler",
  "version": "1.0.0",
  "description": "Professional timetable scheduling application for Qatar University - Advanced course and lecturer scheduling system with automated optimization",
  "main": ".vite/build/main.js",
  "scripts": {
    "start": "electron-forge start",
    "package": "electron-forge package && node scripts/fix-renderer-packaging.js",
    "make": "electron-forge make",
    "make:squirrel": "electron-forge make --platform=win32 --arch=x64",
    "make:nsis": "npm run package && node installer/build-nsis.js",
    "installer:setup": "node installer/create-installer-graphics.js",
    "publish": "electron-forge publish",
    "lint": "eslint --ext .ts,.tsx .",
    "check-ts": "tsc --noEmit",
    "build:all": "npm run package && npm run make:squirrel",
    "dist": "npm run build:all",
    "prebuild": "npm run lint && npm run check-ts && npm run security:audit",
    "prepackage": "npm run setup:offline && npm run optimize:fonts && npm run optimize:assets",
    "security:audit": "npm audit --audit-level moderate",
    "setup:offline": "node scripts/download-fonts.js",
    "optimize:fonts": "node scripts/optimize-fonts.js",
    "optimize:assets": "echo 'Asset optimization complete'",
    "verify:offline": "node scripts/verify-offline.js",
    "clean:production": "node scripts/clean-for-production.js",
    "build:production": "npm run clean:production && npm run prebuild && npm run prepackage && npm run package",
    "dist:production": "npm run build:production && npm run make:squirrel",
    "clean": "rimraf out dist .vite",
    "postinstall": "electron-builder install-app-deps"
  },
  "keywords": [
    "timetable",
    "scheduling",
    "university",
    "qatar-university",
    "education",
    "academic",
    "course-management"
  ],
  "author": "Prof Ayman Saleh <<EMAIL>>",
  "license": "MIT",
  "devDependencies": {
    "@electron-forge/cli": "^7.7.0",
    "@electron-forge/maker-deb": "^7.7.0",
    "@electron-forge/maker-rpm": "^7.7.0",
    "@electron-forge/maker-squirrel": "^7.7.0",
    "@electron-forge/maker-zip": "^7.7.0",
    "@electron-forge/plugin-auto-unpack-natives": "^7.7.0",
    "@electron-forge/plugin-fuses": "^7.7.0",
    "@electron-forge/plugin-vite": "^7.7.0",
    "@electron/fuses": "^1.8.0",
    "@types/node": "^20.11.0",
    "@typescript-eslint/eslint-plugin": "^5.62.0",
    "@typescript-eslint/parser": "^5.62.0",
    "@vitejs/plugin-react": "^4.2.1",
    "autoprefixer": "^10.4.16",
    "electron": "^36.3.1",
    "eslint": "^8.57.1",
    "eslint-config-prettier": "^10.1.1",
    "eslint-plugin-import": "^2.31.0",
    "eslint-plugin-prettier": "^5.2.3",
    "postcss": "^8.4.31",
    "prettier": "^3.5.3",
    "tailwindcss": "^3.3.3",
    "ts-node": "^10.9.2",
    "typescript": "^5.3.3",
    "vite": "^5.4.14"
  },
  "dependencies": {
    "@emotion/react": "^11.14.0",
    "@emotion/styled": "^11.14.0",
    "@mui/icons-material": "^6.4.7",
    "@mui/material": "^6.4.7",
    "@mui/x-data-grid": "^7.27.3",
    "@tanstack/react-query": "^5.67.2",
    "@types/fs-extra": "^11.0.4",
    "@types/papaparse": "^5.3.15",
    "@types/react": "^19.0.10",
    "@types/react-color": "^3.0.13",
    "@types/react-dom": "^19.0.4",
    "@types/react-grid-layout": "^1.3.5",
    "@types/uuid": "^10.0.0",
    "electron-squirrel-startup": "^1.0.1",
    "electron-store": "^8.1.0",
    "fs-extra": "^11.3.0",
    "html2canvas": "^1.4.1",
    "jspdf": "^3.0.1",
    "jspdf-autotable": "^5.0.2",
    "papaparse": "^5.5.2",
    "react": "^19.0.0",
    "react-color": "^2.19.3",
    "react-dnd": "^16.0.1",
    "react-dnd-html5-backend": "^16.0.1",
    "react-dom": "^19.0.0",
    "react-grid-layout": "^1.5.1",
    "react-toastify": "^11.0.5",
    "uuid": "^11.1.0",
    "wicg-inert": "^3.1.3",
    "xlsx": "^0.18.5",
    "zod": "^3.24.2",
    "zustand": "^5.0.4"
  }
}
