import { useRuleSystemStore } from '../store/ruleSystem';
import { Rule } from '../types/rules';
import {
  isUserDefinedBreak as checkUserDefinedBreak,
  isSystemBlockedTimeslot as checkSystemBlockedTimeslot
} from './breakValidation';

// Function to extract academic level from course code
export const getAcademicLevel = (courseCode: string): string => {
  const numericPart = courseCode.match(/\d+/)?.[0] || '';

  if (numericPart.length >= 3) {
    const firstTwoDigits = parseInt(numericPart.substring(0, 2));

    if (firstTwoDigits >= 10 && firstTwoDigits <= 19) return '1st-year';
    if (firstTwoDigits >= 20 && firstTwoDigits <= 29) return '2nd-year';
    if (firstTwoDigits >= 30 && firstTwoDigits <= 39) return '3rd-year';
    if (firstTwoDigits >= 40 && firstTwoDigits <= 49) return '4th-year';
    if (firstTwoDigits >= 50 && firstTwoDigits <= 59) return 'diploma';
    if (firstTwoDigits >= 60 && firstTwoDigits <= 69) return 'masters';
    if (firstTwoDigits >= 70 && firstTwoDigits <= 89) return 'phd';
  }

  return 'unknown';
};

// Function to check if a timeslot is a morning slot
export const isMorningSlot = (_day: string, period: number): boolean => {
  // Periods 1-6 are morning for all days
  return period >= 1 && period <= 6;
};

// Function to check if a timeslot is an evening slot
export const isEveningSlot = (_day: string, period: number): boolean => {
  // Periods 7-12 are evening for all days
  return period >= 7 && period <= 12;
};

// Function to check if a timeslot is blocked (system or user defined)
// This is a wrapper around the centralized functions in breakValidation.ts
export const isBlockedTimeslot = (
  day: string,
  period: number,
  userDefinedBreaks: string[]
): { blocked: boolean; reason: string } => {
  // First check if it's system-blocked
  const systemBlocked = checkSystemBlockedTimeslot(day, period);
  if (systemBlocked.blocked) {
    return systemBlocked;
  }

  // Then check if it's a user-defined break
  // IMPORTANT: User-defined breaks have top priority and should always be respected
  // We convert the day format to short format to match the format in userDefinedBreaks
  const shortDay = day.length > 3 ? day.substring(0, 3) : day;
  const isUserBreak = userDefinedBreaks.includes(`${shortDay}-${period}`);
  if (isUserBreak) {
    return { blocked: true, reason: 'user-defined-break' };
  }

  return { blocked: false, reason: '' };
};

// Define basic session type for rule validation
export interface Session {
  id: string;
  day: string;
  period?: number;
  startPeriod: number;
  endPeriod: number;
  sectionId: string;
  courseCode?: string;
  courseType?: 'Theory' | 'Lab';
  academicLevel?: string;
  gender?: string;
  lecturerId: string;
  lecturerIds?: string[]; // Array of lecturer IDs for multiple lecturers
  viewType?: 'week' | 'regular' | 'long';
  timeOfDay?: 'morning' | 'evening';
  isAutoGenerated?: boolean;
}

// Check if a lecturer is teaching in a specific day
export const isLecturerTeachingInDay = (
  lecturerId: string,
  day: string,
  existingSchedule: Session[]
): boolean => {
  return existingSchedule.some(session =>
    session.lecturerId === lecturerId && session.day === day
  );
};

// Count how many days a lecturer is teaching
export const getLecturerTeachingDaysCount = (
  lecturerId: string,
  existingSchedule: Session[]
): number => {
  const teachingDays = new Set<string>();

  existingSchedule.forEach(session => {
    if (session.lecturerId === lecturerId) {
      teachingDays.add(session.day);
    }
  });

  return teachingDays.size;
};

// Get consecutive teaching periods for a lecturer in a day
export const getLecturerConsecutivePeriods = (
  lecturerId: string,
  day: string,
  existingSchedule: Session[]
): number => {
  // Get all periods the lecturer is teaching in this day
  const periods = existingSchedule
    .filter(session => session.lecturerId === lecturerId && session.day === day)
    .map(session => session.period || session.startPeriod) // Use period if available, otherwise startPeriod
    .sort((a, b) => (a || 0) - (b || 0));

  if (periods.length === 0) return 0;

  let maxConsecutive = 1;
  let currentConsecutive = 1;

  for (let i = 1; i < periods.length; i++) {
    const current = periods[i] || 0;
    const previous = periods[i-1] || 0;
    if (current === previous + 1) {
      currentConsecutive++;
    } else {
      maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
      currentConsecutive = 1;
    }
  }

  return Math.max(maxConsecutive, currentConsecutive);
};

// Get total empty hours between first and last session in a day for a lecturer
export const getLecturerMaxGap = (
  lecturerId: string,
  day: string,
  existingSchedule: Session[]
): number => {
  // Get all sessions for this lecturer on this day, including those where the lecturer is in lecturerIds array
  const lecturerSessions = existingSchedule.filter(
    session => (session.lecturerId === lecturerId ||
               (session.lecturerIds && session.lecturerIds.includes(lecturerId))) &&
               session.day === day
  );

  // If there are less than 2 sessions, there's no empty hours
  if (lecturerSessions.length < 2) return 0;

  // Sort sessions by start period
  const sortedSessions = [...lecturerSessions].sort((a, b) => a.startPeriod - b.startPeriod);

  // Get first and last session
  const firstSession = sortedSessions[0];
  const lastSession = sortedSessions[sortedSessions.length - 1];

  // Check if this is a long day (Monday or Wednesday)
  const isLongDay = ['Monday', 'Wednesday'].includes(day);

  // Create an array of all periods between first and last session
  const allPeriods = [];
  for (let p = firstSession.startPeriod; p <= lastSession.endPeriod; p++) {
    allPeriods.push(p);
  }

  // Create an array of all periods occupied by sessions
  const occupiedPeriods: number[] = [];
  for (const session of sortedSessions) {
    for (let p = session.startPeriod; p <= session.endPeriod; p++) {
      occupiedPeriods.push(p);
    }
  }

  // Find empty periods (periods that are in allPeriods but not in occupiedPeriods)
  const emptyPeriods = allPeriods.filter(p => !occupiedPeriods.includes(p));

  // For long days, remove periods 5 and 6 from empty periods if they exist
  let adjustedEmptyPeriods = [...emptyPeriods];
  if (isLongDay) {
    adjustedEmptyPeriods = emptyPeriods.filter(p => p !== 5 && p !== 6);
  }

  // Calculate empty hours based on day type
  let emptyHours = 0;
  if (isLongDay) {
    // On long days, each period is 1.5 hours
    emptyHours = adjustedEmptyPeriods.length * 1.5;
  } else {
    // On regular days, each period is 1 hour
    emptyHours = adjustedEmptyPeriods.length;
  }

  return emptyHours;
};

// Get maximum gap between sessions of the same academic level and gender
export const getMaxGapForAcademicLevel = (
  academicLevel: string,
  gender: string,
  day: string,
  existingSchedule: Session[]
): number => {
  // Get all periods for this academic level, gender, and day
  const periods = existingSchedule
    .filter(session =>
      session.academicLevel === academicLevel &&
      session.gender === gender &&
      session.day === day
    )
    .map(session => session.period || session.startPeriod) // Use period if available, otherwise startPeriod
    .sort((a, b) => (a || 0) - (b || 0));

  if (periods.length <= 1) return 0;

  let maxGap = 0;

  for (let i = 1; i < periods.length; i++) {
    const current = periods[i] || 0;
    const previous = periods[i-1] || 0;
    const gap = current - previous - 1;
    maxGap = Math.max(maxGap, gap);
  }

  return maxGap;
};

// Helper function to calculate semester load for a lecturer
export const calculateSemesterLoad = (
  lecturer: {
    id: string;
    supervisionHoursFall: number;
    supervisionHoursSpring: number;
  },
  currentSemester = 'Fall',
  existingSchedule: Session[] = []
): number => {
  // Get supervision hours for current semester
  // Don't include supervision hours for Summer semester
  const supervisionHours = currentSemester === 'Summer' ? 0 :
                          currentSemester === 'Fall' ?
                          lecturer.supervisionHoursFall :
                          lecturer.supervisionHoursSpring;

  // Calculate teaching hours from existing schedule
  let teachingHours = 0;
  existingSchedule.forEach(session => {
    if (session.lecturerId === lecturer.id) {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      teachingHours += isLongDay ? 1.5 : 1.0;
    }
  });

  return supervisionHours + teachingHours;
};

// Helper function to calculate max semester load for a lecturer
export const calculateMaxSemesterLoad = (
  lecturer: {
    maxYearLoad: number;
  },
  currentSemester = 'Fall'
): number => {
  switch (currentSemester) {
    case 'Fall':
      return lecturer.maxYearLoad / 2; // Half of the max year load
    case 'Spring': {
      // For simplicity, we'll use half of the max year load for Spring as well
      // In a real implementation, we would calculate remaining load after Fall
      return lecturer.maxYearLoad / 2;
    }
    case 'Summer':
      return 6; // Fixed 6 hours for Summer
    default:
      return 0;
  }
};

// Batch validation interfaces and functions
interface ValidationRequest {
  sectionId: string;
  courseCode: string;
  courseType: 'Theory' | 'Lab';
  contactHours: number;
  academicLevel: string;
  gender: string;
  lecturerId: string;
  day: string;
  period: number;
  existingSchedule: Session[];
  existingSectionSessions: Session[];
  lecturers: {
    id: string;
    firstName?: string;
    lastName?: string;
    maxTeachingDaysPerWeek?: number;
    maxGapBetweenPeriods?: number;
    maxYearLoad: number;
    supervisionHoursFall: number;
    supervisionHoursSpring: number;
  }[];
  currentSemester?: string;
}

interface ValidationResult {
  valid: boolean;
  violatedRules: string[];
  sectionId: string;
  day: string;
  period: number;
}

// Batch validation processing for multiple timeslots
export const validateMultipleTimeslots = (
  validations: ValidationRequest[]
): ValidationResult[] => {
  // Group validations by rule type for batch processing
  const groupedValidations = groupValidationsByRule(validations);
  const results: ValidationResult[] = [];

  // Process each rule type in batch
  for (const [ruleType, requests] of groupedValidations) {
    const batchResults = processBatchValidation(ruleType, requests);
    results.push(...batchResults);
  }

  return results;
};

function groupValidationsByRule(
  validations: ValidationRequest[]
): Map<string, ValidationRequest[]> {
  const grouped = new Map<string, ValidationRequest[]>();

  // Group by common rule patterns
  validations.forEach(validation => {
    // Determine primary rule category
    let category = 'general';

    if (validation.courseType === 'Theory' &&
        !validation.academicLevel.includes('diploma') &&
        !validation.academicLevel.includes('masters') &&
        !validation.academicLevel.includes('phd')) {
      category = 'undergraduate-theory';
    } else if (validation.academicLevel.includes('diploma') ||
               validation.academicLevel.includes('masters') ||
               validation.academicLevel.includes('phd')) {
      category = 'postgraduate';
    }

    if (!grouped.has(category)) {
      grouped.set(category, []);
    }
    grouped.get(category)!.push(validation);
  });

  return grouped;
}

function processBatchValidation(
  ruleType: string,
  requests: ValidationRequest[]
): ValidationResult[] {
  const results: ValidationResult[] = [];

  // Pre-compute common data for this batch
  const ruleSystemState = useRuleSystemStore.getState();
  const { userDefinedBreaks, maxSessionsPerTimeslot } = ruleSystemState;

  // Create lookup maps for faster processing
  const breakLookup = new Set(userDefinedBreaks);

  for (const request of requests) {
    const result = validateSingleRequestOptimized(
      request,
      breakLookup,
      maxSessionsPerTimeslot,
      ruleType
    );
    results.push(result);
  }

  return results;
}

function validateSingleRequestOptimized(
  request: ValidationRequest,
  breakLookup: Set<string>,
  maxSessionsPerTimeslot: Record<string, number>,
  ruleType: string
): ValidationResult {
  const violatedRules: string[] = [];

  // Fast break check
  const shortDay = request.day.length > 3 ? request.day.substring(0, 3) : request.day;
  const breakKey = `${shortDay}-${request.period}`;

  if (breakLookup.has(breakKey)) {
    violatedRules.push('Cannot schedule in a user-defined break timeslot');
    return {
      valid: false,
      violatedRules,
      sectionId: request.sectionId,
      day: request.day,
      period: request.period
    };
  }

  // Fast system block check
  const isLongDay = ['Mon', 'Wed'].includes(request.day);
  if ((isLongDay && (request.period === 5 || request.period === 6)) ||
      (!isLongDay && request.period === 12)) {
    violatedRules.push('Cannot schedule in a system-blocked timeslot');
    return {
      valid: false,
      violatedRules,
      sectionId: request.sectionId,
      day: request.day,
      period: request.period
    };
  }

  // Rule-specific optimized validation
  if (ruleType === 'undergraduate-theory') {
    const valid = validateUndergraduateTheoryOptimized(
      request,
      maxSessionsPerTimeslot,
      violatedRules
    );

    if (!valid) {
      return {
        valid: false,
        violatedRules,
        sectionId: request.sectionId,
        day: request.day,
        period: request.period
      };
    }
  }

  // If we get here, validation passed
  return {
    valid: true,
    violatedRules: [],
    sectionId: request.sectionId,
    day: request.day,
    period: request.period
  };
}

function validateUndergraduateTheoryOptimized(
  request: ValidationRequest,
  maxSessionsPerTimeslot: Record<string, number>,
  violatedRules: string[]
): boolean {
  const timeslotKey = `${request.day}-${request.period}`;
  const shortDay = request.day.length > 3 ? request.day.substring(0, 3) : request.day;
  const shortTimeslotKey = `${shortDay}-${request.period}`;

  // Get the maximum allowed sessions for this timeslot
  const maxAllowed = maxSessionsPerTimeslot[timeslotKey] || maxSessionsPerTimeslot[shortTimeslotKey] || 0;

  if (maxAllowed === 0) {
    violatedRules.push('Timeslot has maximum sessions set to 0');
    return false;
  }

  // Count existing undergraduate theory sessions in this timeslot
  const existingSessions = request.existingSchedule.filter(session => {
    const sessionShortDay = session.day.length > 3 ? session.day.substring(0, 3) : session.day;
    return sessionShortDay === shortDay &&
           session.period === request.period &&
           session.courseType === 'Theory' &&
           session.academicLevel && (
             !session.academicLevel.includes('diploma') &&
             !session.academicLevel.includes('masters') &&
             !session.academicLevel.includes('phd')
           ) &&
           session.sectionId !== request.sectionId;
  });

  if (existingSessions.length >= maxAllowed) {
    violatedRules.push(`Maximum sessions (${maxAllowed}) reached for this timeslot`);
    return false;
  }

  return true;
}

// Main rule validation function
export const validateTimeslotForSection = (
  _sectionId: string,
  _courseCode: string,
  courseType: 'Theory' | 'Lab',
  _contactHours: number,
  academicLevel: string,
  gender: string,
  lecturerId: string,
  day: string,
  period: number,
  existingSchedule: Session[],
  existingSectionSessions: Session[],
  lecturers: {
    id: string;
    firstName?: string;
    lastName?: string;
    maxTeachingDaysPerWeek?: number;
    maxGapBetweenPeriods?: number;
    maxYearLoad: number;
    supervisionHoursFall: number;
    supervisionHoursSpring: number;
    coursesAbleToTeach?: string[];
    preferredTiming?: string;
  }[] = [],
  currentSemester = 'Fall'
): { valid: boolean; violatedRules: string[] } => {
  const {
    rules,
    maxSessionsPerTimeslot,
    maxSessionsPerDay,
    userDefinedBreaks,
    maxPostgradLevelPerDay,
    maxPostgradCoursePerDay,
    maxPostgradCoursesPerDay,
    maxGap4thYear,
    maxGap3rdYear
  } = useRuleSystemStore.getState();

  const violatedRules: string[] = [];

  // Check for overscheduling - calculate current scheduled hours for this section
  if (_contactHours > 0) {
    let currentScheduledHours = 0;
    existingSectionSessions.forEach(session => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      currentScheduledHours += isLongDay ? 1.5 : 1.0;
    });

    // Calculate what would be added by this new session
    const isLongDay = ['Mon', 'Wed'].includes(day);
    const hoursToAdd = isLongDay ? 1.5 : 1.0;

    // Check if adding this session would exceed contact hours
    if (currentScheduledHours + hoursToAdd > _contactHours) {
      violatedRules.push(`Adding this session would exceed the ${_contactHours} contact hours for this section`);
      return { valid: false, violatedRules };
    }
  }

  // First, check if this is a system-blocked timeslot (always enforced)
  // Removed pre-validation check logs to reduce console output

  // Use our centralized function to check if this is a system-blocked timeslot
  const systemBlockedCheck = checkSystemBlockedTimeslot(day, period);
  if (systemBlockedCheck.blocked) {
    // Removed system-blocked critical logs to reduce console output
    return {
      valid: false,
      violatedRules: [`Cannot schedule in a system-blocked timeslot`]
    };
  }

  // IMPORTANT: Always check for user-defined breaks with top priority
  // First, check directly using the userDefinedBreaks array
  const shortDay = day.length > 3 ? day.substring(0, 3) : day;
  const breakKey = `${shortDay}-${period}`;
  const isDirectBreak = userDefinedBreaks.includes(breakKey);

  if (isDirectBreak) {
    return {
      valid: false,
      violatedRules: [`Cannot schedule in a user-defined break timeslot (top priority rule)`]
    };
  }

  // Also use our centralized function as a backup check
  // We pass true to ignoreRuleStatus to ensure we always check for breaks regardless of rule status
  const userBreakCheck = checkUserDefinedBreak(day, period, true);
  if (userBreakCheck.blocked) {
    return {
      valid: false,
      violatedRules: [`Cannot schedule in a user-defined break timeslot (top priority rule)`]
    };
  }

  // Get enabled rules sorted by priority
  const enabledRules = rules
    .filter((rule: Rule) => rule.enabled)
    .sort((a: Rule, b: Rule) => a.priority - b.priority);

  // Silent mode - no rule validation logging

  // Apply each rule in priority order
  for (const rule of enabledRules) {
    switch (rule.id) {
      // Course/Section rules
      case 'block-break-timeslots': {
        // First, check directly using the userDefinedBreaks array
        const shortDay = day.length > 3 ? day.substring(0, 3) : day;
        const breakKey = `${shortDay}-${period}`;
        const isDirectBreak = userDefinedBreaks.includes(breakKey);

        if (isDirectBreak) {
          violatedRules.push(`Cannot schedule in a user-defined break timeslot (top priority rule)`);
          // Early return for blocked timeslots - this is a high priority rule
          return { valid: false, violatedRules };
        }

        // We already checked for user-defined breaks in the pre-validation with top priority
        // This is a redundant check, but we'll keep it for consistency and as a fallback
        // IMPORTANT: Always check with ignoreRuleStatus=true to ensure breaks are always respected
        const userBreakCheck = checkUserDefinedBreak(day, period, true);
        if (userBreakCheck.blocked) {
          violatedRules.push(`Cannot schedule in a user-defined break timeslot (top priority rule)`);
          // Early return for blocked timeslots - this is a high priority rule
          return { valid: false, violatedRules };
        }
        break;
      }

      case 'no-duplicate-sessions': {
        if (existingSectionSessions.some(session =>
          session.day === day && session.period === period
        )) {
          violatedRules.push('Session already exists for this section in this timeslot');
        }
        break;
      }

      case 'no-same-course-gender-overlap': {
        // Check for same course-gender conflicts in the same timeslot
        const sameCourseGenderConflicts = existingSchedule.filter(
          session => session.courseCode === _courseCode &&
                     session.gender === gender &&
                     session.day === day &&
                     session.period === period &&
                     session.sectionId !== _sectionId // Exclude the current section itself
        );

        if (sameCourseGenderConflicts.length > 0) {
          violatedRules.push(`Another section of ${_courseCode} (${gender}) is already scheduled in this timeslot`);
        }
        break;
      }

      case 'undergrad-theory-max-per-slot': {
        // Only apply this rule to undergraduate theory courses
        if (
          courseType === 'Theory' &&
          !academicLevel.includes('diploma') &&
          !academicLevel.includes('masters') &&
          !academicLevel.includes('phd')
        ) {
          const timeslotKey = `${day}-${period}`;
          const shortDay = day.length > 3 ? day.substring(0, 3) : day;
          const shortTimeslotKey = `${shortDay}-${period}`;

          // Silent mode - no max sessions logging

          // Silent mode - no session debugging

          // Count all undergraduate theory sessions in this timeslot
          // EXCLUDING the current section being validated
          const undergraduateSessionsInTimeslot = existingSchedule.filter(
            session => {
              const sessionShortDay = session.day.length > 3 ? session.day.substring(0, 3) : session.day;
              return sessionShortDay === shortDay &&
                session.period === period &&
                session.courseType === 'Theory' &&
                session.academicLevel && (
                  !session.academicLevel.includes('diploma') &&
                  !session.academicLevel.includes('masters') &&
                  !session.academicLevel.includes('phd')
                ) &&
                session.sectionId !== _sectionId; // Exclude the current section
            }
          );

          const currentSessionsInTimeslot = undergraduateSessionsInTimeslot.length;

          // Check both formats of the timeslot key
          const maxAllowed = maxSessionsPerTimeslot[timeslotKey] || maxSessionsPerTimeslot[shortTimeslotKey] || 0;

          // Silent mode - no detailed session logging

          // Check if adding this session would exceed the maximum allowed
          // We add 1 to currentSessionsInTimeslot to account for the session being validated

          // If maxAllowed is explicitly set to 0, it means no sessions are allowed in this timeslot
          if (maxAllowed === 0) {
            violatedRules.push(`No sessions allowed in timeslot ${shortTimeslotKey} (maximum set to 0)`);
            // This is a critical rule - return immediately to prevent scheduling in this timeslot
            return { valid: false, violatedRules };
          }
          // If maxAllowed is greater than 0, check if we've reached the limit
          else if (maxAllowed > 0 && (currentSessionsInTimeslot + 1) > maxAllowed) {
            violatedRules.push(`Maximum undergraduate theory sessions (${maxAllowed}) reached for timeslot ${shortTimeslotKey}`);
            // This is a critical rule - return immediately to prevent scheduling in this timeslot
            return { valid: false, violatedRules };
          }
        }
        break;
      }

      case 'max-sessions-per-day': {
        // Only apply this rule to undergraduate theory courses
        if (
          courseType === 'Theory' &&
          !academicLevel.includes('diploma') &&
          !academicLevel.includes('masters') &&
          !academicLevel.includes('phd')
        ) {
          const dayType = ['Mon', 'Wed'].includes(day) ? 'long' : 'regular';

          // Count all undergraduate theory sessions in this day
          // EXCLUDING the current section being validated
          const currentSessionsInDay = existingSchedule.filter(
            session =>
              session.day === day &&
              session.courseType === 'Theory' &&
              session.academicLevel && (
                !session.academicLevel.includes('diploma') &&
                !session.academicLevel.includes('masters') &&
                !session.academicLevel.includes('phd')
              ) &&
              session.sectionId !== _sectionId // Exclude the current section
          ).length;

          // Silent mode - no max sessions per day logging

          // Check if adding this session would exceed the maximum allowed
          // We add 1 to currentSessionsInDay to account for the session being validated
          if (
            maxSessionsPerDay[dayType] !== undefined &&
            (currentSessionsInDay + 1) > maxSessionsPerDay[dayType]
          ) {
            violatedRules.push(`Maximum undergraduate theory sessions (${maxSessionsPerDay[dayType]}) reached for ${day}`);
            // This is a critical rule - return immediately to prevent scheduling on this day
            return { valid: false, violatedRules };
          }
        }
        break;
      }

      case 'no-overlap-4th-year': {
        if (academicLevel === '4th-year' && courseType === 'Theory') {
          const overlappingSessions = existingSchedule.filter(
            session =>
              session.day === day &&
              session.period === period &&
              session.academicLevel === '4th-year' &&
              session.courseType === 'Theory'
          );

          if (overlappingSessions.length > 0) {
            violatedRules.push('4th-year theory courses should not overlap');
          }
        }
        break;
      }

      case 'no-overlap-3rd-year': {
        if (academicLevel === '3rd-year' && courseType === 'Theory') {
          const overlappingSessions = existingSchedule.filter(
            session =>
              session.day === day &&
              session.period === period &&
              session.academicLevel === '3rd-year' &&
              session.courseType === 'Theory'
          );

          if (overlappingSessions.length > 0) {
            violatedRules.push('3rd-year theory courses should not overlap');
          }
        }
        break;
      }

      case 'no-overlap-2nd-year': {
        if (academicLevel === '2nd-year' && courseType === 'Theory') {
          const overlappingSessions = existingSchedule.filter(
            session =>
              session.day === day &&
              session.period === period &&
              session.academicLevel === '2nd-year' &&
              session.courseType === 'Theory'
          );

          if (overlappingSessions.length > 0) {
            violatedRules.push('2nd-year theory courses should not overlap');
          }
        }
        break;
      }

      case 'no-overlap-1st-year': {
        if (academicLevel === '1st-year' && courseType === 'Theory') {
          const overlappingSessions = existingSchedule.filter(
            session =>
              session.day === day &&
              session.period === period &&
              session.academicLevel === '1st-year' &&
              session.courseType === 'Theory'
          );

          if (overlappingSessions.length > 0) {
            violatedRules.push('1st-year theory courses should not overlap');
          }
        }
        break;
      }

      case 'max-gap-4th-year': {
        if (academicLevel === '4th-year' && courseType === 'Theory') {
          const maxGap = getMaxGapForAcademicLevel('4th-year', gender, day, existingSchedule);
          if (maxGap > maxGap4thYear) { // Use configurable max gap value
            violatedRules.push(`Maximum gap between 4th-year sessions exceeded (${maxGap} > ${maxGap4thYear})`);
          }
        }
        break;
      }

      case 'max-gap-3rd-year': {
        if (academicLevel === '3rd-year' && courseType === 'Theory') {
          const maxGap = getMaxGapForAcademicLevel('3rd-year', gender, day, existingSchedule);
          if (maxGap > maxGap3rdYear) { // Use configurable max gap value
            violatedRules.push(`Maximum gap between 3rd-year sessions exceeded (${maxGap} > ${maxGap3rdYear})`);
          }
        }
        break;
      }

      case 'undergrad-2ch-pattern': {
        // Only apply to undergraduate theory courses with 2 contact hours
        if (
          courseType === 'Theory' &&
          _contactHours === 2 &&
          !academicLevel.includes('diploma') &&
          !academicLevel.includes('masters') &&
          !academicLevel.includes('phd')
        ) {
          // 2CH courses should be scheduled over two regular days at the same time
          // This is enforced by the scheduling patterns, but we can validate consistency here
          const sectionSessions = existingSectionSessions.filter(s => s.sectionId === _sectionId);

          if (sectionSessions.length > 0) {
            // Check if all sessions are at the same period
            const periods = sectionSessions.map(s => s.period || s.startPeriod).filter((p): p is number => p !== undefined);
            const uniquePeriods = new Set(periods);

            if (uniquePeriods.size > 1) {
              violatedRules.push('2CH undergraduate courses should be scheduled at the same time on different days');
            }

            // Check if sessions are on regular days only (not long days)
            const days = sectionSessions.map(s => s.day);
            const hasLongDays = days.some(d => ['Mon', 'Wed'].includes(d));

            if (hasLongDays) {
              violatedRules.push('2CH undergraduate courses should be scheduled on regular days only');
            }
          }
        }
        break;
      }

      case 'undergrad-3ch-pattern': {
        // Only apply to undergraduate theory courses with 3 contact hours
        if (
          courseType === 'Theory' &&
          _contactHours === 3 &&
          !academicLevel.includes('diploma') &&
          !academicLevel.includes('masters') &&
          !academicLevel.includes('phd')
        ) {
          // 3CH courses should be scheduled over three regular days or two long days
          const sectionSessions = existingSectionSessions.filter(s => s.sectionId === _sectionId);

          if (sectionSessions.length > 0) {
            const days = sectionSessions.map(s => s.day);
            const uniqueDays = new Set(days);
            const hasLongDays = days.some(d => ['Mon', 'Wed'].includes(d));
            const hasRegularDays = days.some(d => !['Mon', 'Wed'].includes(d));

            // Don't mix long and regular days
            if (hasLongDays && hasRegularDays) {
              violatedRules.push('3CH undergraduate courses should not mix long and regular days');
            }

            // If using long days, should be exactly 2 days
            if (hasLongDays && uniqueDays.size > 2) {
              violatedRules.push('3CH undergraduate courses on long days should use exactly 2 days');
            }

            // If using regular days, should be exactly 3 days
            if (!hasLongDays && uniqueDays.size > 3) {
              violatedRules.push('3CH undergraduate courses on regular days should use exactly 3 days');
            }
          }
        }
        break;
      }

      case 'undergrad-4ch-pattern': {
        // Only apply to undergraduate theory courses with 4 contact hours
        if (
          courseType === 'Theory' &&
          _contactHours === 4 &&
          !academicLevel.includes('diploma') &&
          !academicLevel.includes('masters') &&
          !academicLevel.includes('phd')
        ) {
          // 4CH courses should follow defined patterns
          const sectionSessions = existingSectionSessions.filter(s => s.sectionId === _sectionId);

          if (sectionSessions.length > 0) {
            const days = sectionSessions.map(s => s.day);
            const uniqueDays = new Set(days);

            // Should not exceed 4 days
            if (uniqueDays.size > 4) {
              violatedRules.push('4CH undergraduate courses should not exceed 4 days');
            }
          }
        }
        break;
      }

      case 'undergrad-5ch-pattern': {
        // Only apply to undergraduate theory courses with 5 contact hours
        if (
          courseType === 'Theory' &&
          _contactHours === 5 &&
          !academicLevel.includes('diploma') &&
          !academicLevel.includes('masters') &&
          !academicLevel.includes('phd')
        ) {
          // 5CH courses should be scheduled over at least three days
          const sectionSessions = existingSectionSessions.filter(s => s.sectionId === _sectionId);

          if (sectionSessions.length > 0) {
            const days = sectionSessions.map(s => s.day);
            const uniqueDays = new Set(days);

            // Should use at least 3 days
            if (uniqueDays.size < 3) {
              violatedRules.push('5CH undergraduate courses should be scheduled over at least 3 days');
            }
          }
        }
        break;
      }

      case 'contact-hours': {
        // Validate that contact hours are positive
        if (_contactHours <= 0) {
          violatedRules.push('Contact hours must be greater than 0');
        }
        break;
      }

      case 'postgrad-pattern': {
        // Only apply to postgraduate theory courses with 3 contact hours
        if (
          (academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma') &&
          courseType === 'Theory' &&
          _contactHours === 3
        ) {
          // Silent mode - no postgrad pattern logging

          // Check if the period is valid for postgraduate courses
          const isLongDay = ['Mon', 'Wed'].includes(day);

          if (isLongDay) {
            // For long days, periods should be exactly 9-10
            if (period !== 9 && period !== 10) {
              violatedRules.push('Postgraduate theory courses should be scheduled in periods 9-10 on long days');
            }
          } else {
            // For regular days, periods should be exactly 10-12
            if (period !== 10 && period !== 11 && period !== 12) {
              violatedRules.push('Postgraduate theory courses should be scheduled in periods 10-12 on regular days');
            }
          }

          // Check if all sessions for this section are on the same day
          const sectionSessions = existingSectionSessions.filter(s => s.sectionId === _sectionId);

          if (sectionSessions.length > 0) {
            const uniqueDays = new Set(sectionSessions.map(s => s.day));

            // If adding this session would create a new day, it's a violation
            if (!uniqueDays.has(day) && uniqueDays.size > 0) {
              violatedRules.push('Postgraduate theory courses should be scheduled on one day only');
            }

            // Check if periods are consecutive
            if (uniqueDays.has(day)) {
              const periodsOnThisDay = sectionSessions
                .filter(s => s.day === day)
                .map(s => s.period || s.startPeriod) // Use period if available, otherwise startPeriod
                .filter((p): p is number => p !== undefined) // Filter out undefined values
                .sort((a, b) => a - b);

              // Check if adding this period would break consecutiveness
              if (periodsOnThisDay.length > 0) {
                const min = Math.min(...periodsOnThisDay);
                const max = Math.max(...periodsOnThisDay);

                // If the new period is not adjacent to existing periods, it's a violation
                if (period !== min - 1 && period !== max + 1) {
                  violatedRules.push('Postgraduate theory courses should have consecutive periods');
                }
              }
            }
          }
        }
        break;
      }

      case 'limit-postgrad-level-per-day': {
        // Only apply to postgraduate courses
        if (
          (academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma') &&
          courseType === 'Theory'
        ) {
          // Silent mode - no postgrad level logging

          // Count existing sessions of the same postgraduate level and gender on this day
          const samePostgradLevelSessions = existingSchedule.filter(
            session =>
              session.day === day &&
              session.academicLevel === academicLevel &&
              session.gender === gender &&
              session.sectionId !== _sectionId // Don't count sessions from the same section
          );

          const samePostgradLevelCount = samePostgradLevelSessions.length;

          // Check if adding this session would exceed the maximum allowed
          if (samePostgradLevelCount >= maxPostgradLevelPerDay) {
            violatedRules.push(`Maximum ${maxPostgradLevelPerDay} ${academicLevel} sections of ${gender} gender allowed per day`);
          }
        }
        break;
      }

      case 'limit-postgrad-course-per-day': {
        // Only apply to postgraduate courses
        if (
          (academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma') &&
          courseType === 'Theory'
        ) {
          // Silent mode - no postgrad course logging

          // Extract the course code prefix (e.g., "CS" from "CS601")
          const courseCodePrefix = _courseCode.match(/^[A-Za-z]+/)?.[0] || '';
          const courseCodeNumber = _courseCode.match(/\d+/)?.[0] || '';

          if (courseCodePrefix && courseCodeNumber) {
            // Count existing sessions of the same postgraduate course on this day
            const samePostgradCourseSessions = existingSchedule.filter(
              session =>
                session.day === day &&
                session.courseCode &&
                session.courseCode.startsWith(courseCodePrefix) &&
                session.courseCode.includes(courseCodeNumber) &&
                session.sectionId !== _sectionId // Don't count sessions from the same section
            );

            const samePostgradCourseCount = samePostgradCourseSessions.length;

            // Check if adding this session would exceed the maximum allowed
            if (samePostgradCourseCount >= maxPostgradCoursePerDay) {
              violatedRules.push(`Maximum ${maxPostgradCoursePerDay} sections of ${_courseCode} allowed per day`);
            }
          }
        }
        break;
      }

      case 'distribute-postgrad-courses': {
        // Only apply to postgraduate courses
        if (
          (academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma') &&
          courseType === 'Theory'
        ) {
          // Silent mode - no distribution logging

          // 1. Check if there are too many courses of the same level on this day
          const otherPostgradCoursesOnDay = existingSchedule.filter(
            session =>
              session.day === day &&
              session.academicLevel === academicLevel && // Same academic level
              session.courseCode && // Make sure courseCode exists
              !session.courseCode.includes(_courseCode) && // Different course
              session.sectionId !== _sectionId // Not the same section
          );

          // Get unique course codes
          const uniqueCoursesOnDay = new Set(otherPostgradCoursesOnDay.map(s => s.courseCode));

          // Check if adding this course would exceed the maximum allowed
          if (uniqueCoursesOnDay.size >= maxPostgradCoursesPerDay) {
            // Get the course codes of the other postgrad courses on this day
            const otherCourses = [...uniqueCoursesOnDay];
            violatedRules.push(`Maximum ${maxPostgradCoursesPerDay} ${academicLevel} courses allowed per day. Already have: ${otherCourses.join(', ')}`);
          }

          // 2. Check if there's already a section of the same course with different gender on this day
          const otherGenderSectionsOnDay = existingSchedule.filter(
            session =>
              session.day === day &&
              session.courseCode && // Make sure courseCode exists
              session.courseCode.includes(_courseCode) && // Same course
              session.gender !== gender && // Different gender
              session.sectionId !== _sectionId // Not the same section
          );

          if (otherGenderSectionsOnDay.length > 0) {
            const otherGenders = [...new Set(otherGenderSectionsOnDay.map(s => s.gender))];
            violatedRules.push(`A ${otherGenders.join('/')} section of ${_courseCode} is already scheduled on ${day}`);
          }

          // 3. Check if we're trying to schedule too many courses of this level
          // Count how many days are already used by this course level
          const daysWithThisLevel = new Set();
          existingSchedule.forEach(session => {
            if (session.academicLevel === academicLevel) {
              daysWithThisLevel.add(session.day);
            }
          });

          // If we're adding a new day and we already have 5 days, that's too many
          if (!daysWithThisLevel.has(day) && daysWithThisLevel.size >= 5) {
            violatedRules.push(`Cannot schedule more than 5 days for ${academicLevel} courses`);
          }
        }
        break;
      }

      // Lecturer rules
      case 'no-lecturer-duplicate': {
        if (lecturerId) {
          const lecturerSessions = existingSchedule.filter(
            session => session.lecturerId === lecturerId &&
                       session.day === day &&
                       session.period === period
          );

          if (lecturerSessions.length > 0) {
            violatedRules.push('Lecturer already assigned to another session in this timeslot');
          }
        }
        break;
      }

      case 'lecturer-capability': {
        // Skip if no lecturer is assigned
        if (!lecturerId) {
          break;
        }

        // Find the lecturer object to check their capabilities
        const lecturer = lecturers.find(l => l.id === lecturerId);
        if (lecturer) {
          // Check if the lecturer can teach this course
          if (lecturer.coursesAbleToTeach && !lecturer.coursesAbleToTeach.includes(_courseCode)) {
            violatedRules.push(`Lecturer ${lecturer.firstName} ${lecturer.lastName || ''} is not qualified to teach ${_courseCode}`);
          }
        }
        break;
      }

      case 'lecturer-preferred-time': {
        // Skip if no lecturer is assigned
        if (!lecturerId) {
          break;
        }

        // Find the lecturer object to check their time preferences
        const lecturer = lecturers.find(l => l.id === lecturerId);
        if (lecturer && lecturer.preferredTiming !== 'Both') {
          // Check if this is a morning or evening slot
          const isMorning = isMorningSlot(day, period);

          // Check if the time preference matches
          if (lecturer.preferredTiming === 'Morning' && !isMorning) {
            violatedRules.push(`Lecturer ${lecturer.firstName} ${lecturer.lastName || ''} prefers morning sessions but this is an evening slot`);
          } else if (lecturer.preferredTiming === 'Evening' && isMorning) {
            violatedRules.push(`Lecturer ${lecturer.firstName} ${lecturer.lastName || ''} prefers evening sessions but this is a morning slot`);
          }
        }
        break;
      }

      case 'lecturer-max-days': {
        if (lecturerId) {
          // Get the lecturer object from the lecturers array to get their max teaching days setting
          const lecturer = lecturers.find(l => l.id === lecturerId);

          // Use the lecturer's maxTeachingDaysPerWeek if available, otherwise default to 5
          const maxDays = lecturer?.maxTeachingDaysPerWeek || 5;

          // If the lecturer is not already teaching on this day
          if (!isLecturerTeachingInDay(lecturerId, day, existingSchedule)) {
            const currentDaysCount = getLecturerTeachingDaysCount(lecturerId, existingSchedule);

            if (currentDaysCount >= maxDays) {
              violatedRules.push(`Lecturer maximum teaching days of ${maxDays} exceeded (currently teaching on ${currentDaysCount} days)`);
            }
          }
        }
        break;
      }

      case 'lecturer-max-consecutive': {
        if (lecturerId) {
          // Check if adding this session would exceed the lecturer's max consecutive periods
          // For now, we'll use a default of 4 consecutive periods max
          const maxConsecutive = 4;

          // Get current consecutive periods
          const currentConsecutive = getLecturerConsecutivePeriods(lecturerId, day, existingSchedule);

          // Check if this new period would create a consecutive sequence
          const lecturerPeriods = existingSchedule
            .filter(session => session.lecturerId === lecturerId && session.day === day)
            .map(session => session.period);

          const wouldCreateConsecutive =
            lecturerPeriods.includes(period - 1) ||
            lecturerPeriods.includes(period + 1);

          if (wouldCreateConsecutive && currentConsecutive >= maxConsecutive) {
            violatedRules.push('Lecturer maximum consecutive periods exceeded');
          }
        }
        break;
      }

      case 'lecturer-max-gap': {
        if (lecturerId) {
          // Get the lecturer object from the lecturers array to get their max empty hours setting
          const lecturer = lecturers.find(l => l.id === lecturerId);

          // Use the lecturer's maxGapBetweenPeriods if available, otherwise default to 2
          const maxEmptyHours = lecturer?.maxGapBetweenPeriods || 2;

          // Removed lecturer-max-gap rule logging to reduce console output

          // Create a copy of the existing schedule with the new session added
          const newSchedule = [
            ...existingSchedule,
            {
              lecturerId,
              day,
              period,
              startPeriod: period,
              endPeriod: period,
              sectionId: _sectionId,
              courseCode: _courseCode,
              academicLevel,
              gender,
              courseType,
              id: 'temp-session'
            }
          ];

          // Calculate the total empty hours with the new session added
          const emptyHours = getLecturerMaxGap(lecturerId, day, newSchedule);

          // Removed lecturer validation logs to reduce console output

          // Check if the empty hours exceed the maximum allowed
          if (emptyHours > maxEmptyHours) {
            // Removed violation logs to reduce console output
            violatedRules.push(`Lecturer maximum empty hours of ${maxEmptyHours} exceeded (found ${emptyHours.toFixed(1)} hours)`);
          }
        }
        break;
      }

      case 'lecturer-max-load': {
        // Skip if no lecturer is assigned
        if (!lecturerId) {
          break;
        }

        // This rule requires access to the lecturer data and current semester load
        // We need to calculate the current load and check if adding this session would exceed the max load

        // Get the lecturer object from the lecturers array
        const lecturer = lecturers.find(l => l.id === lecturerId);
        if (!lecturer) {
          console.log(`Lecturer with ID ${lecturerId} not found`);
          break;
        }

        // Calculate the current semester load for this lecturer
        // This includes both teaching and supervision hours
        const currentSemesterLoad = calculateSemesterLoad(lecturer, currentSemester, existingSchedule);

        // Calculate the maximum semester load for this lecturer
        const maxSemesterLoad = calculateMaxSemesterLoad(lecturer, currentSemester);

        // Calculate the load hours for this session
        // For regular days (Su, Tu, Th), each period is 1 hour
        // For long days (Mo, We), each period is 1.5 hours
        const isLongDay = ['Mon', 'Wed'].includes(day);
        const sessionLoadHours = isLongDay ? 1.5 : 1.0;

        // Check if adding this session would exceed the maximum semester load
        if (currentSemesterLoad + sessionLoadHours > maxSemesterLoad) {
          // Removed semester load exceeded logs to reduce console output
          violatedRules.push(`Lecturer maximum semester load exceeded (${currentSemesterLoad.toFixed(1)}/${maxSemesterLoad.toFixed(1)})`);
        } else {
          // Removed lecturer load check passed logs to reduce console output
        }

        break;
      }

      default:
        // Other rules to be implemented based on application's data structure
        break;
    }

    // If we've found violations, no need to check further rules
    if (violatedRules.length > 0) {
      break;
    }
  }

  return {
    valid: violatedRules.length === 0,
    violatedRules
  };
};