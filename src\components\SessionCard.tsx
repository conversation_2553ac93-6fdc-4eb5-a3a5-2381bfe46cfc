import React from 'react';

// Define session interface based on usage
interface Session {
  id: string;
  isAutoGenerated?: boolean;
  // Add other properties that might be needed
}

interface SessionCardProps {
  session: Session;
  // Define specific props instead of using 'any'
  className?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
  style?: React.CSSProperties;
  // Add other specific props as needed
}

export const SessionCard: React.FC<SessionCardProps> = ({ session, ...otherProps }) => {
  // ... existing code ...

  // Add a class if the session was auto-generated
  const sessionCardClasses = `session-card ${session.isAutoGenerated ? 'auto-generated' : ''}`;

  return (
    <div
      className={sessionCardClasses}
      {...otherProps}
    >
      {/* ... existing session card content ... */}
    </div>
  );
};

// Add this to your CSS file
// .auto-generated {
//   border: 2px dashed #3b82f6; /* Blue dashed border */
// }