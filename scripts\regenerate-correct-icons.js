#!/usr/bin/env node

/**
 * QU Scheduler Correct Icon Regeneration Script
 * 
 * This script properly converts the correct QU Scheduler SVG to PNG files
 * and then creates a proper ICO file for Windows executable embedding.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎨 QU Scheduler Correct Icon Regeneration');
console.log('=========================================');

async function installSharp() {
    console.log('\n📥 Installing Sharp for SVG conversion...');
    
    try {
        execSync('npm install --no-save sharp', { stdio: 'inherit' });
        console.log('✅ Sharp installed successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to install Sharp:', error.message);
        return false;
    }
}

async function convertSvgToPng() {
    console.log('\n🔄 Converting SVG to PNG files...');
    
    try {
        const sharp = require('sharp');
        const svgPath = path.resolve('assets/icons/icon.svg');
        
        if (!fs.existsSync(svgPath)) {
            throw new Error('SVG file not found: ' + svgPath);
        }
        
        console.log('📄 Reading SVG file:', svgPath);
        const svgBuffer = fs.readFileSync(svgPath);
        
        // Define the sizes we need
        const sizes = [16, 24, 32, 48, 64, 128, 256, 512];
        
        for (const size of sizes) {
            const outputPath = path.resolve(`assets/icons/icon-${size}x${size}.png`);
            
            console.log(`🔄 Converting to ${size}x${size}...`);
            
            await sharp(svgBuffer)
                .resize(size, size)
                .png()
                .toFile(outputPath);
            
            const stats = fs.statSync(outputPath);
            console.log(`✅ Created: icon-${size}x${size}.png (${(stats.size / 1024).toFixed(1)} KB)`);
        }
        
        console.log('✅ All PNG files converted successfully');
        return true;
    } catch (error) {
        console.error('❌ SVG to PNG conversion failed:', error.message);
        return false;
    }
}

async function createCorrectIco() {
    console.log('\n🎨 Creating correct ICO file...');
    
    try {
        const toIco = require('to-ico');
        
        // Read the PNG files in order of preference
        const pngFiles = [
            'assets/icons/icon-256x256.png',
            'assets/icons/icon-128x128.png',
            'assets/icons/icon-64x64.png',
            'assets/icons/icon-48x48.png',
            'assets/icons/icon-32x32.png',
            'assets/icons/icon-16x16.png'
        ];
        
        const pngBuffers = [];
        
        for (const pngFile of pngFiles) {
            if (fs.existsSync(pngFile)) {
                const buffer = fs.readFileSync(pngFile);
                pngBuffers.push(buffer);
                console.log(`✅ Loaded: ${path.basename(pngFile)} (${(buffer.length / 1024).toFixed(1)} KB)`);
            }
        }
        
        if (pngBuffers.length === 0) {
            throw new Error('No PNG files found');
        }
        
        console.log(`🎨 Converting ${pngBuffers.length} PNG files to ICO...`);
        
        // Convert to ICO
        const icoBuffer = await toIco(pngBuffers);
        
        // Backup original ICO if it exists
        const originalIcoPath = 'assets/icons/icon.ico';
        if (fs.existsSync(originalIcoPath)) {
            fs.copyFileSync(originalIcoPath, 'assets/icons/icon-backup-wrong.ico');
            console.log('✅ Wrong ICO backed up as icon-backup-wrong.ico');
        }
        
        // Write new ICO file
        fs.writeFileSync(originalIcoPath, icoBuffer);
        
        const stats = fs.statSync(originalIcoPath);
        console.log(`✅ Correct ICO file created: ${(stats.size / 1024).toFixed(1)} KB`);
        
        return true;
    } catch (error) {
        console.error('❌ ICO creation failed:', error.message);
        return false;
    }
}

async function embedCorrectIcon() {
    console.log('\n🔧 Embedding correct icon in executable...');
    
    const executablePath = 'out/QU Scheduler-win32-x64/qu-scheduler.exe';
    const iconPath = 'assets/icons/icon.ico';
    
    if (!fs.existsSync(executablePath)) {
        console.error('❌ Executable not found:', executablePath);
        console.log('💡 Run: npm run package');
        return false;
    }
    
    try {
        const command = `node_modules\\rcedit\\bin\\rcedit.exe "${executablePath}" --set-icon "${iconPath}"`;
        console.log('Command:', command);
        
        execSync(command, { stdio: 'inherit' });
        console.log('✅ Correct icon embedded successfully!');
        
        return true;
    } catch (error) {
        console.error('❌ Icon embedding failed:', error.message);
        return false;
    }
}

async function main() {
    console.log('\n🚀 Starting correct icon regeneration process...');
    
    // Step 1: Install Sharp for SVG conversion
    if (!await installSharp()) {
        console.error('❌ Cannot proceed without Sharp');
        process.exit(1);
    }
    
    // Step 2: Convert SVG to PNG files
    if (!await convertSvgToPng()) {
        console.error('❌ SVG to PNG conversion failed');
        process.exit(1);
    }
    
    // Step 3: Create correct ICO file
    if (!await createCorrectIco()) {
        console.error('❌ ICO creation failed');
        process.exit(1);
    }
    
    // Step 4: Embed correct icon in executable
    if (!await embedCorrectIcon()) {
        console.error('❌ Icon embedding failed');
        process.exit(1);
    }
    
    // Step 5: Success message
    console.log('\n🎉 CORRECT ICON REGENERATION COMPLETE!');
    console.log('======================================');
    console.log('\n📋 What was fixed:');
    console.log('   ✅ Converted correct QU Scheduler SVG to PNG files');
    console.log('   ✅ Created proper ICO file from correct PNG files');
    console.log('   ✅ Embedded correct icon in Windows executable');
    console.log('\n📋 Next Steps:');
    console.log('   1. Build installer: node installer/build-nsis.js');
    console.log('   2. Test installation to verify correct icon displays');
    console.log('   3. Clear Windows icon cache if needed: npm run clear:icon-cache');
    
    console.log('\n✅ The CORRECT QU Scheduler icon should now be embedded!');
    console.log('🎨 Expected: Maroon background, white center, 3 golden sections, 3×3 grid');
}

// Run the correct icon regeneration
main().catch(error => {
    console.error('\n❌ REGENERATION FAILED:', error.message);
    process.exit(1);
});
