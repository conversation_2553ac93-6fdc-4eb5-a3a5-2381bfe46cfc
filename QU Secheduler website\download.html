<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download - QU Scheduler</title>
    <meta name="description" content="Download QU Scheduler for Windows. Get the latest version with professional installer. Professional Timetable Scheduling for Qatar University Departments Departments.">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/favicon-16x16.png">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="assets/qu-scheduler-logo.svg" alt="QU Scheduler Logo" class="logo-img">
                <span class="logo-text">QU Scheduler</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="features.html" class="nav-link">Features</a>
                </li>
                <li class="nav-item">
                    <a href="download.html" class="nav-link active">Download</a>
                </li>

            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">Download QU Scheduler</h1>
            <p class="page-description">
                Get the latest version of QU Scheduler for Windows with professional installation
                and seamless integration into your system.
            </p>
        </div>
    </section>

    <!-- Version Information -->
    <section class="version-info">
        <div class="container">
            <div class="version-card">
                <div class="version-header">
                    <h2>QU Scheduler v1.0.0</h2>
                    <span class="version-badge">Latest Release</span>
                </div>
                <div class="version-details">
                    <div class="version-item">
                        <strong>Release Date:</strong> January 2025
                    </div>
                    <div class="version-item">
                        <strong>Publisher:</strong> Qatar University
                    </div>
                    <div class="version-item">
                        <strong>Developer:</strong> Prof Ayman Saleh
                    </div>
                    <div class="version-item">
                        <strong>Platform:</strong> Windows 10/11 (64-bit)
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Options -->
    <section class="download-options-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Download QU Scheduler</h2>
                <p class="section-description">
                    Get the latest version of QU Scheduler with professional installation and integration.
                </p>
            </div>
            <div class="download-grid" style="display: flex; justify-content: center;">
                <!-- Installer Version -->
                <div class="download-option" style="max-width: 500px;">
                    <div class="download-header">
                        <div class="download-icon installer-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                        </div>
                        <h3>QU Scheduler Installer</h3>
                        <p class="download-subtitle">Professional installation for Windows</p>
                    </div>
                    <div class="download-details">
                        <div class="download-size">Size: ~117 MB</div>
                        <div class="download-type">NSIS Installer</div>
                        <ul class="download-features">
                            <li>Automatic installation process</li>
                            <li>Desktop and Start Menu shortcuts</li>
                            <li>Integrated uninstaller</li>
                            <li>Windows registry integration</li>
                            <li>Professional installation experience</li>
                            <li>Automatic updates support</li>
                        </ul>
                    </div>
                    <div class="download-action">
                        <a href="QU-Scheduler-Setup.exe" class="btn btn-primary download-btn">
                            Download QU Scheduler
                        </a>
                        <p class="download-note">Requires administrator privileges for installation</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- System Requirements -->
    <section class="system-requirements">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">System Requirements</h2>
                <p class="section-description">
                    Ensure your system meets the minimum requirements for optimal performance.
                </p>
            </div>
            <div class="requirements-grid">
                <div class="requirement-category">
                    <h3>Minimum Requirements</h3>
                    <ul class="requirement-list">
                        <li><strong>Operating System:</strong> Windows 7 (64-bit) or later</li>
                        <li><strong>Processor:</strong> Intel Core i3 or AMD equivalent</li>
                        <li><strong>Memory:</strong> 4 GB RAM</li>
                        <li><strong>Storage:</strong> 500 MB available space</li>
                        <li><strong>Display:</strong> 1024x768 resolution</li>
                    </ul>
                </div>
                <div class="requirement-category">
                    <h3>Recommended Requirements</h3>
                    <ul class="requirement-list">
                        <li><strong>Operating System:</strong> Windows 10/11 (64-bit)</li>
                        <li><strong>Processor:</strong> Intel Core i5 or AMD equivalent</li>
                        <li><strong>Memory:</strong> 8 GB RAM</li>
                        <li><strong>Storage:</strong> 1 GB available space</li>
                        <li><strong>Display:</strong> 1920x1080 resolution</li>
                    </ul>
                </div>
            </div>
            <div class="additional-requirements">
                <h3>Additional Information</h3>
                <div class="requirement-notes">
                    <div class="requirement-note">
                        <strong>Internet Connection:</strong> Not required for operation (offline-first design)
                    </div>
                    <div class="requirement-note">
                        <strong>Arabic Font Support:</strong> Built-in system fonts used for Arabic text rendering
                    </div>
                    <div class="requirement-note">
                        <strong>PDF Generation:</strong> Built-in PDF engine with embedded Arabic font support
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Instructions -->
    <section class="installation-instructions">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Installation Instructions</h2>
                <p class="section-description">
                    Follow these simple steps to get QU Scheduler running on your system.
                </p>
            </div>
            <div class="instructions-grid" style="display: flex; justify-content: center;">
                <div class="instruction-set" style="max-width: 600px;">
                    <h3>Installation Steps</h3>
                    <ol class="instruction-list">
                        <li>Download the QU-Scheduler-Setup.exe file</li>
                        <li>Right-click the file and select "Run as administrator"</li>
                        <li>Follow the installation wizard prompts</li>
                        <li>Choose installation directory (default recommended)</li>
                        <li>Select additional options (shortcuts, file associations)</li>
                        <li>Click "Install" to begin the installation process</li>
                        <li>Launch QU Scheduler from the desktop shortcut or Start Menu</li>
                        <li>Import your course and lecturer data to get started</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Information -->
    <section class="security-info">
        <div class="container">
            <div class="security-content">
                <div class="security-text">
                    <h2>Security & Trust</h2>
                    <p>
                        QU Scheduler is developed by Qatar University and follows enterprise-grade
                        security practices. The application is designed for offline use and does
                        not transmit any data over the internet.
                    </p>
                    <ul class="security-features">
                        <li>No external network dependencies</li>
                        <li>Local data storage only</li>
                        <li>Secure IPC communication</li>
                        <li>Enterprise-grade security implementation</li>
                        <li>Code signing ready (certificate pending)</li>
                    </ul>
                </div>
                <div class="security-badges">
                    <div class="security-badge">
                        <div class="badge-icon">🔒</div>
                        <div class="badge-text">
                            <strong>Offline First</strong>
                            <span>No internet required</span>
                        </div>
                    </div>
                    <div class="security-badge">
                        <div class="badge-icon">🏛️</div>
                        <div class="badge-text">
                            <strong>University Approved</strong>
                            <span>Qatar University official</span>
                        </div>
                    </div>
                    <div class="security-badge">
                        <div class="badge-icon">🛡️</div>
                        <div class="badge-text">
                            <strong>Enterprise Security</strong>
                            <span>Professional grade protection</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Support Section -->
    <section class="download-support">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Need Help?</h2>
                <p class="section-description">
                    Get assistance with downloading, installing, or using QU Scheduler.
                </p>
            </div>
            <div class="support-options">
                <div class="support-option">
                    <h3>Technical Support</h3>
                    <p>For installation issues and technical problems</p>
                    <a href="mailto:<EMAIL>" class="support-link"><EMAIL></a>
                </div>
                <div class="support-option">
                    <h3>System Requirements</h3>
                    <p>Detailed system compatibility information</p>
                    <a href="assets/docs/system-requirements.html" class="support-link">View Requirements</a>
                </div>
                <div class="support-option">
                    <h3>Qatar University</h3>
                    <p>Official university information and resources</p>
                    <a href="https://qu.edu.qa" class="support-link">Visit QU Website</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/qu-scheduler-logo.svg" alt="QU Scheduler Logo" class="footer-logo-img">
                        <span class="footer-logo-text">QU Scheduler</span>
                    </div>
                    <p class="footer-description">
                        Professional timetable scheduling application designed specifically
                        for Qatar University's academic departments.
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Download</h4>
                    <ul class="footer-links">
                        <li><a href="#installer">Download Installer</a></li>
                        <li><a href="#requirements">System Requirements</a></li>
                        <li><a href="#installation">Installation Guide</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <ul class="footer-links">
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a href="https://qu.edu.qa">Qatar University</a></li>
                        <li>Version 1.0.0</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Qatar University. All rights reserved. | Developed by Prof Ayman Saleh</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
