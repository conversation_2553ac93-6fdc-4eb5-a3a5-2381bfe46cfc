import React, { useState, useEffect, useRef } from 'react';
import {
  Paper,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Collapse,
  Tooltip,
  Box
} from '@mui/material';
import { useAppContext, isPostgraduateCourse } from '../context/AppContext';
import { Course, Section } from '../types/models';
import CourseImportModal from './modals/CourseImportModal';
import CourseAddEditModal from './modals/CourseAddEditModal';
import DraggableSectionCard from './draggable/DraggableSectionCard';
import { getArabicTextClass, getArabicFontFamily } from '../utils/arabicUtils';

// Importing icons separately to avoid issues with missing module declarations
import AddIcon from '@mui/icons-material/Add';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import FilterListIcon from '@mui/icons-material/FilterList';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import PersonAddIcon from '@mui/icons-material/PersonAdd';

// Define the course data structure for the modal
// This matches the structure expected by CourseAddEditModal
interface CourseData {
  id?: string;
  courseCode: string;
  courseName: string;
  courseType: 'Theory' | 'Lab';
  contactHours: number;
  loadHours: number;
  color: string;
  maleSectionsCount: number;
  femaleSectionsCount: number;
  academicLevel?: string;
}

// Import CourseData type from CourseImportModal
type ImportCourseData = {
  courseCode: string;
  courseName: string;
  loadHours: number;
  contactHours: number;
  courseType: 'Theory' | 'Lab';
  maleSectionsCount: number;
  femaleSectionsCount: number;
  color?: string;
  academicLevel?: string;
};

/**
 * CoursesPanel component that displays the left panel for courses
 * Shows a list of courses and their sections for the current semester
 */
const CoursesPanel: React.FC = () => {
  // Get context data
  const {
    currentSemester,
    courses,
    sections,
    sessions,
    addCourse,
    updateCourse,
    deleteCourse,
    importCourses,
    reorderCourses,
    courseFilter,
    setCourseFilter,
    isCourseModalOpen,
    setIsCourseModalOpen,
    isCourseImportModalOpen,
    setIsCourseImportModalOpen,
    currentCourse,
    setCurrentCourse,
    addSection,
    deleteSection,
    showPgCanvas,
    isDataLoaded
  } = useAppContext();

  // State for filter menu
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  // State for expanded course cards
  const [expandedCourses, setExpandedCourses] = useState<Record<string, boolean>>({});

  // State for filtered course
  const [filteredCourseId, setFilteredCourseId] = useState<string | null>(null);

  // State for drag and drop
  const [draggedCourse, setDraggedCourse] = useState<string | null>(null);
  const [dragOverCourse, setDragOverCourse] = useState<string | null>(null);

  // Ref to track if we're currently reordering
  const isReordering = useRef(false);

  // Load UI state from store on component mount
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const uiState = await window.electronAPI.store.get('uiState') as any;
        if (uiState && uiState.expandedCourses) {
          setExpandedCourses(uiState.expandedCourses);
        }
      } catch (error) {
        console.error('Error loading UI state:', error);
      }
    };

    loadUIState();

    // Listen for course filter changes from TimetableCanvas
    const handleCourseFilterChange = (event: CustomEvent) => {
      const { courseId } = event.detail;
      setFilteredCourseId(courseId);
    };

    // Listen for reset filter events
    const handleResetCoursePanelFilter = () => {
      setCourseFilter('all');
    };

    // Add event listeners
    window.addEventListener('courseFilterChanged', handleCourseFilterChange as EventListener);
    window.addEventListener('resetCoursePanelFilter', handleResetCoursePanelFilter as EventListener);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('courseFilterChanged', handleCourseFilterChange as EventListener);
      window.removeEventListener('resetCoursePanelFilter', handleResetCoursePanelFilter as EventListener);
    };
  }, []);

  // Save expanded courses state to store when it changes
  useEffect(() => {
    const saveUIState = async () => {
      try {
        // Get the current UI state
        const currentUIState = await window.electronAPI.store.get('uiState') as any;
        // Create a new UI state object with only the expandedCourses updated
        // Preserve all other properties, especially panel visibility managed by App.tsx
        const newUIState = {
          ...currentUIState,
          expandedCourses
          // Do NOT touch showCoursesPanel, showLecturersPanel, or darkMode
          // These are managed by App.tsx
        };
        // Set the new UI state
        await window.electronAPI.store.set('uiState', newUIState);
      } catch (error) {
        console.error('Error saving UI state:', error);
      }
    };

    saveUIState();
  }, [expandedCourses]);

  // Filter options
  const filterOptions = [
    { value: 'all', label: 'All sections' },
    { value: 'fully', label: 'Fully scheduled' },
    { value: 'partially', label: 'Not fully scheduled' },
    { value: 'male', label: 'Male sections' },
    { value: 'female', label: 'Female sections' }
  ];

  // Handler for filter menu open
  const handleFilterMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  // Handler for filter menu close
  const handleFilterMenuClose = () => {
    setFilterAnchorEl(null);
  };

  // Handler for filter selection
  const handleFilterSelect = (filter: string) => {
    setCourseFilter(filter);
    handleFilterMenuClose();
  };

  // Toggle course expansion
  const toggleCourseExpansion = (courseId: string) => {
    setExpandedCourses(prev => ({
      ...prev,
      [courseId]: !prev[courseId]
    }));
  };

  // Filter courses based on selected filter and PG Canvas view
  const getFilteredCourses = () => {
    // First filter courses based on PG Canvas view
    let filteredCoursesList = courses[currentSemester];

    // When in PG Canvas view, only show postgraduate courses
    if (showPgCanvas) {
      filteredCoursesList = filteredCoursesList.filter(course => isPostgraduateCourse(course.courseCode));
    }

    return filteredCoursesList.map(course => {
      const courseSections = sections[currentSemester].filter(section => section.courseId === course.id);

      const filteredSections = courseSections.filter(section => {
        switch (courseFilter) {
          case 'fully':
            return section.scheduledHours === section.totalHours;
          case 'partially':
            return section.scheduledHours < section.totalHours;
          case 'male':
            return section.gender === 'M';
          case 'female':
            return section.gender === 'F';
          default:
            return true;
        }
      });

      return {
        ...course,
        sections: filteredSections
      };
    }).filter(course => course.sections.length > 0 || courseFilter === 'all');
  };

  // Handle add course button click
  const handleAddCourse = () => {
    setCurrentCourse(null);
    setIsCourseModalOpen(true);
  };

  // Handle import courses button click
  const handleImportCoursesClick = () => {
    setIsCourseImportModalOpen(true);
  };

  // Handle edit course button click
  const handleEditCourse = (course: Course) => {
    // Convert the Course to CourseData for the modal
    const courseData: CourseData = {
      id: course.id,
      courseCode: course.courseCode,
      courseName: course.courseName,
      courseType: course.courseType,
      contactHours: course.contactHours,
      loadHours: course.loadHours,
      color: course.color,
      maleSectionsCount: course.maleSectionsCount,
      femaleSectionsCount: course.femaleSectionsCount,
      academicLevel: course.academicLevel
    };

    setCurrentCourse(courseData as unknown as Course);
    setIsCourseModalOpen(true);
  };

  // Handle delete course button click
  const handleDeleteCourse = (courseId: string) => {
    if (window.confirm('Are you sure you want to delete this course?')) {
      deleteCourse(courseId, currentSemester);
    }
  };

  // Handle add male section
  const handleAddMaleSection = (courseId: string) => {
    const course = courses[currentSemester].find(c => c.id === courseId);
    if (course) {
      // Find the highest male section number for this course
      const maleSections = sections[currentSemester].filter(
        s => s.courseId === courseId && s.gender === 'M'
      );
      const nextSectionNumber = maleSections.length > 0
        ? Math.max(...maleSections.map(s => s.sectionNumber)) + 1
        : 1;

      addSection({
        courseId,
        gender: 'M',
        sectionNumber: nextSectionNumber,
        totalHours: course.contactHours,
        scheduledHours: 0
      }, currentSemester);
      setCourseFilter('all'); // Reset filter after adding section
    }
  };

  // Handle add female section
  const handleAddFemaleSection = (courseId: string) => {
    const course = courses[currentSemester].find(c => c.id === courseId);
    if (course) {
      // Find the highest female section number for this course
      const femaleSections = sections[currentSemester].filter(
        s => s.courseId === courseId && s.gender === 'F'
      );
      const nextSectionNumber = femaleSections.length > 0
        ? Math.max(...femaleSections.map(s => s.sectionNumber)) + 1
        : 51;

      addSection({
        courseId,
        gender: 'F',
        sectionNumber: nextSectionNumber,
        totalHours: course.contactHours,
        scheduledHours: 0
      }, currentSemester);
      setCourseFilter('all'); // Reset filter after adding section
    }
  };

  // Handle delete section
  const handleDeleteSection = (sectionId: string) => {
    if (window.confirm('Are you sure you want to delete this section?')) {
      deleteSection(sectionId, currentSemester);
    }
  };

  // Handle filter timetable by course
  const handleFilterByCourse = (course: Course, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent expansion toggle

    // If the course is already filtered, clear the filter
    if (filteredCourseId === course.id) {
      setFilteredCourseId(null);

      // Dispatch a custom event to notify TimetableCanvas about the filter change
      window.dispatchEvent(new CustomEvent('courseFilterChanged', {
        detail: {
          courseId: null
        }
      }));
    } else {
      setFilteredCourseId(course.id);

      // Dispatch a custom event to notify TimetableCanvas about the filter change
      window.dispatchEvent(new CustomEvent('courseFilterChanged', {
        detail: {
          courseId: course.id
        }
      }));
    }
  };

  // Handle save course (from modal)
  const handleSaveCourse = (courseData: CourseData) => {
    try {
      if (currentCourse) {
        // Update existing course with the id from currentCourse
        const updatedCourse: Course = {
          ...courseData,
          id: currentCourse.id || '',
        };
        updateCourse(updatedCourse, currentSemester);
      } else {
        // Add new course
        addCourse(courseData as unknown as Course, currentSemester);
        setCourseFilter('all'); // Reset filter after adding new course
      }
      setIsCourseModalOpen(false);
    } catch (error) {
      console.error('Error saving course in CoursesPanel:', error);
      // Keep the modal open if there's an error so the user can try again
    }
  };

  // Handle import courses (from modal)
  const handleImportCoursesSubmit = (coursesData: ImportCourseData[]) => {
    // Ensure all courses have a color property
    const processedCourses = coursesData.map(course => ({
      ...course,
      color: course.color || '#3b82f6' // Default to blue if color is undefined
    }));

    importCourses(processedCourses, currentSemester);
    setIsCourseImportModalOpen(false);
    setCourseFilter('all'); // Reset filter after import
  };

  // Group sections by gender
  const groupSectionsByGender = (sections: Section[]) => {
    return {
      male: sections.filter(section => section.gender === 'M')
        .sort((a, b) => a.sectionNumber - b.sectionNumber),
      female: sections.filter(section => section.gender === 'F')
        .sort((a, b) => a.sectionNumber - b.sectionNumber)
    };
  };

  // Handle drag start for course reordering
  const handleDragStart = (courseId: string, e: React.DragEvent<HTMLDivElement>) => {
    // Check if the drag originated from a section card
    const target = e.target as HTMLElement;
    if (target.closest('.section-card')) {
      // If it's a section card being dragged, don't handle it here
      return;
    }

    // Set a custom drag image to make it look better
    const dragImage = document.createElement('div');
    const course = courses[currentSemester].find(c => c.id === courseId);

    if (course) {
      dragImage.className = 'p-2 rounded-md shadow-md';
      dragImage.style.backgroundColor = course.color + '40'; // Add transparency
      dragImage.style.width = '200px';
      dragImage.style.height = '40px';
      dragImage.textContent = `${course.courseCode} - ${course.courseName}`;
      document.body.appendChild(dragImage);
      e.dataTransfer.setDragImage(dragImage, 100, 20);
      setTimeout(() => document.body.removeChild(dragImage), 0);
    }

    // Mark that we're reordering to distinguish from other drag operations
    isReordering.current = true;
    setDraggedCourse(courseId);

    // Set data for the drag operation
    e.dataTransfer.setData('text/plain', courseId);
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over for course reordering
  const handleDragOver = (courseId: string, e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    // Only handle if we're in reordering mode and not dragging over the same course
    if (isReordering.current && draggedCourse !== courseId) {
      setDragOverCourse(courseId);
      e.dataTransfer.dropEffect = 'move';
    }
  };

  // Handle drag end for course reordering
  const handleDragEnd = () => {
    setDraggedCourse(null);
    setDragOverCourse(null);
    isReordering.current = false;
  };

  // Handle drop for course reordering
  const handleDrop = (targetCourseId: string, e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();

    // Only handle if we're in reordering mode
    if (!isReordering.current || !draggedCourse) return;

    // Get the current order of courses
    const currentCourses = getFilteredCourses();
    const sourceIndex = currentCourses.findIndex(c => c.id === draggedCourse);
    const targetIndex = currentCourses.findIndex(c => c.id === targetCourseId);

    if (sourceIndex === -1 || targetIndex === -1 || sourceIndex === targetIndex) {
      return;
    }

    // Create a new array with the reordered courses
    const newOrder = [...currentCourses];
    const [movedCourse] = newOrder.splice(sourceIndex, 1);
    newOrder.splice(targetIndex, 0, movedCourse);

    // Update the order in the context
    reorderCourses(newOrder.map(c => c.id), currentSemester);

    // Reset drag state
    setDraggedCourse(null);
    setDragOverCourse(null);
    isReordering.current = false;
  };

  return (
    <Paper className="h-full flex flex-col overflow-hidden">
      {/* Header with title and action buttons */}
      <div className="p-3 pr-10 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800">
        <Typography variant="subtitle1" className="font-semibold">
          Courses
        </Typography>
        <div className="flex space-x-1">
          <Tooltip title="Add new course">
            <IconButton
              size="small"
              onClick={handleAddCourse}
              className="bg-blue-50 hover:bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800"
            >
              <AddIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Import courses">
            <IconButton
              size="small"
              onClick={handleImportCoursesClick}
              className="bg-green-50 hover:bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-200 dark:hover:bg-green-800"
            >
              <UploadFileIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Filter sections">
            <IconButton
              size="small"
              onClick={handleFilterMenuOpen}
              sx={{
                backgroundColor: courseFilter && courseFilter !== 'all' ? 'primary.main' : 'inherit',
                color: courseFilter && courseFilter !== 'all' ? 'common.white' : 'inherit',
                '&:hover': {
                  backgroundColor: courseFilter && courseFilter !== 'all' ? 'primary.dark' : 'action.hover'
                }
              }}
              aria-controls="filter-menu"
              aria-haspopup="true"
            >
              <FilterListIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
              {courseFilter !== 'all' && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
              )}
            </IconButton>
          </Tooltip>
          <Menu
            id="filter-menu"
            anchorEl={filterAnchorEl}
            keepMounted
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterMenuClose}
          >
            {filterOptions.map((option) => (
              <MenuItem
                key={option.value}
                onClick={() => handleFilterSelect(option.value)}
                selected={courseFilter === option.value}
              >
                {option.label}
              </MenuItem>
            ))}
          </Menu>
        </div>
      </div>

      {/* Course cards list */}
      <div className="flex-1 overflow-y-auto p-2 bg-gray-100 dark:bg-gray-900">
        {!isDataLoaded ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <Typography variant="body2">
              Loading courses...
            </Typography>
          </div>
        ) : getFilteredCourses().length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <Typography variant="body2">
              No courses found for the current semester.
            </Typography>
          </div>
        ) : (
          getFilteredCourses().map(course => {
            const isExpanded = expandedCourses[course.id] || false;
            const groupedSections = groupSectionsByGender(course.sections);

            // Check if all sections of a course are fully scheduled (for top border indicator)
            const isFullyScheduledCourse = (sections: Section[]) => {
              return sections.length > 0 && sections.every(section => section.scheduledHours === section.totalHours);
            };

            // Get course status color based on sections scheduling status
            const getCourseStatusColor = (sections: Section[]) => {
              // Check if any section is overscheduled
              const hasOverscheduledSection = sections.some(section => section.scheduledHours > section.totalHours);
              if (hasOverscheduledSection) return 'bg-red-100 dark:bg-red-900/40 text-red-800 dark:text-red-200'; // Red background - more distinct

              // Check if all sections are fully scheduled
              const allSectionsFullyScheduled = sections.length > 0 &&
                sections.every(section => section.scheduledHours === section.totalHours);

              // Check if all sections have lecturers assigned to all their sessions
              const allSectionsHaveLecturers = sections.length > 0 &&
                sections.every(section => {
                  // Get all sessions for this section
                  const sectionSessions = sessions[currentSemester].filter(
                    session => session.sectionId === section.id
                  );

                  // If there are no sessions, the section is not fully scheduled
                  if (sectionSessions.length === 0) {
                    return false;
                  }

                  // Check if all sessions have a lecturer assigned
                  return sectionSessions.every(session =>
                    session.lecturerId && session.lecturerId.trim() !== ''
                  );
                });

              // Only show green if both conditions are met: fully scheduled AND all have lecturers
              if (allSectionsFullyScheduled && allSectionsHaveLecturers)
                return 'bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-200'; // Green background - more distinct

              // Default: use orange as fallback
              return 'bg-orange-100 dark:bg-orange-900/40 text-orange-800 dark:text-orange-200'; // Orange background as fallback
            };

            return (
              <div
                key={course.id}
                className={`mb-2 border rounded overflow-hidden shadow-md bg-amber-50 dark:bg-gray-800 ${dragOverCourse === course.id ? 'border-blue-500 border-2' : 'border-gray-300 dark:border-gray-600'} ${draggedCourse === course.id ? 'opacity-50' : ''} ${isFullyScheduledCourse(course.sections) ? 'border-t-4 border-t-green-500' : ''}`}
                draggable
                onDragStart={(e) => handleDragStart(course.id, e)}
                onDragOver={(e) => handleDragOver(course.id, e)}
                onDragEnd={handleDragEnd}
                onDrop={(e) => handleDrop(course.id, e)}
              >
                {/* Upper part: Course header */}
                <div
                  className={`p-2 cursor-pointer ${getCourseStatusColor(course.sections)}`}
                  onClick={() => toggleCourseExpansion(course.id)}
                >
                  <div className="flex justify-between items-center">
                    {/* Course code pill - reduced by 20% */}
                    <div
                      className="px-3 py-1 rounded-full font-medium"
                      style={{
                        backgroundColor: course.color,
                        color: '#fff',
                        fontSize: '0.8rem' // 20% smaller than text-sm (0.875rem)
                      }}
                    >
                      {course.courseCode}
                    </div>

                    {/* Action buttons */}
                    <div className="flex">
                      <Tooltip title={filteredCourseId === course.id ? "Clear filter" : "Filter timetable to show only this course's sessions"}>
                        <IconButton
                          size="small"
                          sx={{
                            color: filteredCourseId === course.id ? 'primary.main' : 'text.secondary',
                            position: 'relative'
                          }}
                          onClick={(e) => handleFilterByCourse(course, e)}
                        >
                          <FilterListIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                          {filteredCourseId === course.id && (
                            <Box
                              sx={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                width: 6,
                                height: 6,
                                bgcolor: 'error.main',
                                borderRadius: '50%'
                              }}
                            />
                          )}
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit course">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent expansion toggle
                            handleEditCourse(course);
                          }}
                        >
                          <EditIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete course">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent expansion toggle
                            handleDeleteCourse(course.id);
                          }}
                        >
                          <DeleteIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                        </IconButton>
                      </Tooltip>
                    </div>
                  </div>

                  {/* Course name and section add buttons */}
                  <div className="flex justify-between items-center mt-1">
                    <Typography
                      variant="body2"
                      className={`text-gray-700 dark:text-gray-300 capitalize ${getArabicTextClass(course.courseName)}`}
                      sx={{
                        fontFamily: getArabicFontFamily(course.courseName) || undefined
                      }}
                    >
                      {course.courseName.toLowerCase()}
                    </Typography>
                    <div className="flex">
                      <Tooltip title="Add male section">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent expansion toggle
                            handleAddMaleSection(course.id);
                          }}
                        >
                          <PersonAddIcon fontSize="small" className="text-blue-500" sx={{ fontSize: '0.85rem' }} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Add female section">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent expansion toggle
                            handleAddFemaleSection(course.id);
                          }}
                        >
                          <PersonAddIcon fontSize="small" className="text-pink-500" sx={{ fontSize: '0.85rem' }} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={isExpanded ? "Collapse sections" : "Expand sections"}>
                        <IconButton size="small" onClick={(e) => {
                          e.stopPropagation(); // Prevent double triggering
                          toggleCourseExpansion(course.id);
                        }}>
                          {isExpanded ?
                            <ExpandLessIcon fontSize="small" sx={{ fontSize: '0.85rem' }} /> :
                            <ExpandMoreIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                          }
                        </IconButton>
                      </Tooltip>
                    </div>
                  </div>
                </div>

                {/* Lower part: Collapsible section cards */}
                <Collapse in={isExpanded}>
                  <div className="px-2 pb-2">
                    {/* Male sections */}
                    {groupedSections.male.length > 0 && (
                      <div className="mb-2">
                        <Typography variant="caption" className="text-gray-500 dark:text-gray-400 block mb-1">
                          Male Sections
                        </Typography>
                        <div className="grid grid-cols-1 gap-1">
                          {groupedSections.male.map(section => (
                            <DraggableSectionCard
                              key={section.id}
                              section={section}
                              courseCode={course.courseCode}
                              courseColor={course.color}
                              courseType={course.courseType}
                              contactHours={course.contactHours}
                              onDelete={handleDeleteSection}
                            />
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Female sections */}
                    {groupedSections.female.length > 0 && (
                      <div>
                        <Typography variant="caption" className="text-gray-500 dark:text-gray-400 block mb-1">
                          Female Sections
                        </Typography>
                        <div className="grid grid-cols-1 gap-1">
                          {groupedSections.female.map(section => (
                            <DraggableSectionCard
                              key={section.id}
                              section={section}
                              courseCode={course.courseCode}
                              courseColor={course.color}
                              courseType={course.courseType}
                              contactHours={course.contactHours}
                              onDelete={handleDeleteSection}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Collapse>
              </div>
            );
          })
        )}
      </div>

      {/* Course add/edit modal */}
      <CourseAddEditModal
        open={isCourseModalOpen}
        onClose={() => {
          try {
            setIsCourseModalOpen(false);
            setCurrentCourse(null);
          } catch (error) {
            console.error('Error closing course modal:', error);
          }
        }}
        onSave={handleSaveCourse}
        course={currentCourse ? {
          id: currentCourse.id,
          courseCode: currentCourse.courseCode || '',
          courseName: currentCourse.courseName || '',
          loadHours: currentCourse.loadHours || 3,
          contactHours: currentCourse.contactHours || 3,
          courseType: currentCourse.courseType || 'Theory',
          maleSectionsCount: currentCourse.maleSectionsCount || 0,
          femaleSectionsCount: currentCourse.femaleSectionsCount || 0,
          color: currentCourse.color || '#3b82f6',
          academicLevel: currentCourse.academicLevel || ''
        } : undefined}
      />

      {/* Course import modal */}
      {isCourseImportModalOpen && (
        <CourseImportModal
          open={isCourseImportModalOpen}
          onClose={() => setIsCourseImportModalOpen(false)}
          onImport={handleImportCoursesSubmit}
        />
      )}
    </Paper>
  );
};

export default CoursesPanel;
