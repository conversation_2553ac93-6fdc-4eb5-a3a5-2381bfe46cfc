import { Lecturer, Session, Section, Course, Semester } from '../types/models';
import { generateLecturerPdfTemplate, type PdfTemplateData } from './pdfTemplates';

// SVG paths for Material-UI icons
const MAN_ICON_PATH = "M12 7c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2 2h-4c-1.1 0-2 .9-2 2v7h2v7h4v-7h2v-7c0-1.1-.9-2-2-2z";
const WOMAN_ICON_PATH = "M13.94 8.31C13.62 7.52 12.85 7 12 7s-1.62.52-1.94 1.31L7 16h3v6h4v-6h3l-3.06-7.69zM12 4c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2z";

// Define morning and evening periods
const MORNING_PERIODS = [
  { period: 1, regularTime: '8 AM', longTime: '8 AM' },
  { period: 2, regularTime: '9 AM', longTime: '9:30 AM' },
  { period: 3, regularTime: '10 AM', longTime: '11 AM' },
  { period: 4, regularTime: '11 AM', longTime: '12:30 PM' },
  { period: 5, regularTime: '12 PM', longTime: '2 PM' },
  { period: 6, regularTime: '1 PM', longTime: '3:30 PM' }
];

// Define evening periods
const EVENING_PERIODS = [
  { period: 7, regularTime: '2 PM', longTime: '2 PM' },
  { period: 8, regularTime: '3 PM', longTime: '3:30 PM' },
  { period: 9, regularTime: '4 PM', longTime: '5 PM' },
  { period: 10, regularTime: '5 PM', longTime: '6:30 PM' },
  { period: 11, regularTime: '6 PM', longTime: '8 PM' },
  { period: 12, regularTime: '7 PM', longTime: '9:30 PM' }
];

/**
 * Generates HTML content for a lecturer timetable
 * @param exportLecturer The lecturer to export
 * @param currentSemester The current semester
 * @param lecturers All lecturers
 * @param sessions All sessions
 * @param sections All sections
 * @param courses All courses
 * @param getFilteredPeriods Function to get filtered periods
 * @param days Days of the week
 * @param semesterLoad Current semester load
 * @param fallLoad Fall semester load
 * @param springLoad Spring semester load
 * @param totalYearLoad Total year load
 * @param yearWorkload Maximum year workload
 * @param overload Overload value
 * @param findSessionsForDayAndPeriod Function to find sessions for a day and period
 * @returns HTML content as a string
 */
/**
 * Exports a card or section as PDF
 * @param title The title of the PDF
 * @param elementId The ID of the element to export
 * @param fileName The name of the file to save
 */
export const exportCardAsPdf = (title: string, element: HTMLElement, fileName: string): void => {
  // Import jsPDF dynamically
  import('jspdf').then(({ jsPDF }) => {
    // Import html2canvas dynamically
    import('html2canvas').then((html2canvasModule) => {
      const html2canvas = html2canvasModule.default;

      // Create a new PDF document
      const doc = new jsPDF('p', 'mm', 'a4');

      // Add title
      doc.setFontSize(16);
      doc.text(title, 14, 20);

      // Convert the element to canvas
      html2canvas(element, {
        scale: 2, // Higher scale for better quality
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff'
      }).then(canvas => {
        // Convert canvas to image
        const imgData = canvas.toDataURL('image/png');

        // Calculate dimensions to fit on PDF
        const imgWidth = 190; // mm
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // Add image to PDF
        doc.addImage(imgData, 'PNG', 10, 30, imgWidth, imgHeight);

        // Save PDF
        doc.save(fileName);
      });
    });
  });
};

/**
 * Exports lecturer timetable as PDF using Electron's built-in PDF generation
 * @param data The template data for generating the PDF
 * @param filename The filename for the PDF
 */
export const exportLecturerTimetablePdf = async (data: PdfTemplateData, filename: string): Promise<void> => {
  try {
    // Generate HTML content
    const htmlContent = generateLecturerPdfTemplate(data);

    // Use browser print functionality for reliable PDF generation
    await generatePdfUsingBrowserPrint(htmlContent, filename);

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF. Please try again.');
  }
};

/**
 * Fallback PDF generation using browser's print functionality
 */
const generatePdfUsingBrowserPrint = async (htmlContent: string, filename: string): Promise<void> => {
  // Enhance the HTML content with print controls
  const enhancedHtmlContent = addPrintControlsToHtml(htmlContent, filename);

  // Create a new window for preview
  const previewWindow = window.open('', '_blank', 'width=1000,height=700,scrollbars=yes,resizable=yes');
  if (!previewWindow) {
    throw new Error('Could not open preview window. Please allow popups and try again.');
  }

  // Write the enhanced HTML content to the new window
  previewWindow.document.open();
  previewWindow.document.write(enhancedHtmlContent);
  previewWindow.document.close();

  // Set the document title to the desired filename
  previewWindow.document.title = `${filename.replace('.pdf', '')} - Preview`;

  // Wait for content to load and then add event listeners
  await new Promise<void>((resolve) => {
    const setupEventListeners = () => {
      // Add event listeners for buttons
      const printBtn = previewWindow.document.getElementById('printBtn');
      const closeBtn = previewWindow.document.getElementById('closeBtn');

      if (printBtn) {
        printBtn.addEventListener('click', () => {
          previewWindow.print();
        });
      }

      if (closeBtn) {
        closeBtn.addEventListener('click', () => {
          previewWindow.close();
        });
      }

      // Add keyboard shortcuts
      previewWindow.document.addEventListener('keydown', (e) => {
        // Ctrl+P or Cmd+P for print
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
          e.preventDefault();
          previewWindow.print();
        }

        // Escape to close
        if (e.key === 'Escape') {
          previewWindow.close();
        }
      });

      // Focus the window
      previewWindow.focus();
      resolve();
    };

    // Wait for DOM to be ready
    if (previewWindow.document.readyState === 'loading') {
      previewWindow.document.addEventListener('DOMContentLoaded', setupEventListeners);
    } else {
      // DOM is already ready
      setTimeout(setupEventListeners, 100);
    }

    // Fallback timeout
    setTimeout(() => {
      setupEventListeners();
    }, 2000);
  });
};

/**
 * Add print controls to the HTML content
 */
const addPrintControlsToHtml = (htmlContent: string, _filename: string): string => {
  // Extract the content between <body> tags
  const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*)<\/body>/i);
  const bodyContent = bodyMatch ? bodyMatch[1] : htmlContent;

  // Extract the head content and remove Google Fonts imports to avoid CSP issues
  const headMatch = htmlContent.match(/<head[^>]*>([\s\S]*)<\/head>/i);
  let headContent = headMatch ? headMatch[1] : '';

  // Remove Google Fonts imports that cause CSP issues
  headContent = headContent.replace(/@import url\([^)]+\);/g, '');

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Using system fonts for offline capability -->
  ${headContent}
  <style>
    /* Use system fonts to avoid CSP issues */
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    }

    .arabic {
      font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
      direction: rtl;
      text-align: right;
    }

    /* Print controls styling */
    .print-controls {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      background: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      border: 1px solid #ddd;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .print-controls h3 {
      margin: 0 0 10px 0;
      font-size: 16px;
      color: #333;
      font-weight: 600;
    }

    .print-controls button {
      display: block;
      width: 100%;
      margin: 8px 0;
      padding: 10px 16px;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .print-btn {
      background-color: #1976d2;
      color: white;
    }

    .print-btn:hover {
      background-color: #1565c0;
    }

    .close-btn {
      background-color: #f5f5f5;
      color: #666;
      border: 1px solid #ddd;
    }

    .close-btn:hover {
      background-color: #eeeeee;
    }

    /* Hide print controls when printing */
    @media print {
      .print-controls {
        display: none !important;
      }

      body {
        margin: 0;
        padding: 0;
      }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .print-controls {
        position: relative;
        top: auto;
        right: auto;
        margin: 20px;
        width: calc(100% - 40px);
      }
    }
  </style>
</head>
<body>
  <!-- Print Controls -->
  <div class="print-controls">
    <h3>📄 Print Options</h3>
    <button class="print-btn" id="printBtn">
      🖨️ Print / Save as PDF
    </button>
    <button class="close-btn" id="closeBtn">
      ❌ Close Preview
    </button>
  </div>

  <!-- Original Content -->
  ${bodyContent}

</body>
</html>`;
};

export const generateLecturerTimetableHtml = (
  exportLecturer: Lecturer,
  currentSemester: Semester,
  lecturers: Lecturer[],
  sessions: Record<Semester, Session[]>,
  sections: Record<Semester, Section[]>,
  courses: Record<Semester, Course[]>,
  getFilteredPeriods: () => { period: number; regularTime: string; longTime: string }[],
  days: string[],
  semesterLoad: number,
  fallLoad: number,
  springLoad: number,
  totalYearLoad: number,
  yearWorkload: number,
  overload: number,
  findSessionsForDayAndPeriod: (day: string, period: number) => Session[],
  genderFilter: 'M' | 'F' | null = null,
  departmentName = '',
  academicYear = ''
): string => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${exportLecturer.title} ${exportLecturer.firstName} ${exportLecturer.lastName}'s Timetable</title>
  <style>
    * {
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    body {
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
    }
    .container {
      display: flex;
      min-height: 100vh;
      padding: 16px;
      gap: 16px;
    }
    .sidebar {
      width: 200px;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      padding: 16px;
      overflow-y: auto;
    }
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow: hidden;
      min-height: 0; /* Allow flex container to shrink */
    }
    /* Reduce gap for printing */
    @media print {
      .main-content {
        gap: 0 !important;
      }
    }
    .header {
      background-color: white;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      padding: 10px;
    }
    .timetable {
      flex: 1;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #e0e0e0;
      padding: 16px;
      overflow: auto;
      min-height: 0; /* Allow flex container to shrink */
    }
    .lecturer-list {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .lecturer-item {
      padding: 8px 12px;
      margin-bottom: 4px;
      border-radius: 4px;
      cursor: pointer;
    }
    .lecturer-item:hover {
      background-color: #f0f7ff;
    }
    .lecturer-item.selected {
      background-color: #bbdefb;
    }
    h1 {
      font-size: 20px;
      margin-top: 0;
      margin-bottom: 16px;
    }
    h2 {
      font-size: 16px;
      margin-top: 0;
      margin-bottom: 0;
      margin-right: 8px;
    }
    .title-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .title-info {
      display: flex;
      align-items: baseline;
    }
    .department-info {
      font-size: 0.7em;
      color: #666;
      margin-left: 8px;
    }
    .header-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .info-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 4px;
      margin-top: 8px;
    }
    .info-item {
      margin-bottom: 4px;
      display: flex;
    }
    .info-label {
      font-weight: bold;
      font-size: 12px;
      color: #555;
      margin-right: 4px;
    }
    .info-value {
      font-size: 12px;
    }
    .current-semester {
      font-weight: bold;
      color: #1976d2;
    }
    .overload-positive {
      color: #d32f2f;
    }
    .overload-negative {
      color: #388e3c;
    }
    .timetable-grid {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed; /* Ensures column widths are respected */
      border: 1px solid #e0e0e0;
    }
    /* Distribute remaining width evenly among day columns */
    .timetable-grid th:not(:first-child),
    .timetable-grid td:not(:first-child) {
      width: calc((100% - 30px) / 5); /* Distribute remaining space evenly */
    }
    .timetable-grid th, .timetable-grid td {
      border: 1px solid #e0e0e0;
      padding: 8px;
      vertical-align: top;
    }
    /* Reduce padding in period cells */
    .timetable-grid td.period-cell {
      padding: 4px 2px;
    }
    .timetable-grid th {
      background-color: #f5f5f5;
      font-weight: bold;
      text-align: center;
    }
    .timetable-grid thead {
      display: table-header-group;
    }
    .timetable-grid tr {
      page-break-inside: avoid;
      break-inside: avoid;
    }
    .period-cell {
      font-weight: bold;
      background-color: #f5f5f5;
      text-align: center;
      width: 30px; /* Reduced from 60px to 30px (50% reduction) */
    }
    /* Set first column width in the table */
    .timetable-grid th:first-child,
    .timetable-grid td:first-child {
      width: 30px;
      max-width: 30px;
    }
    .day-cell {
      position: relative;
      min-height: 60px;
    }
    .period-number {
      font-size: 18px; /* Reduced from 24px */
      font-weight: bold;
      line-height: 1;
      padding: 0;
      margin: 0;
    }
    .session-card {
      background-color: #e3f2fd;
      border-radius: 4px;
      padding: 6px; /* Reduced from 8px */
      margin-bottom: 3px; /* Reduced from 4px */
      font-size: 14px;
    }
    .session-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2px; /* Reduced from 4px */
    }
    .session-title {
      font-weight: bold;
    }
    .gender-icon {
      font-size: 14px;
      display: flex;
      align-items: center;
    }
    .male-icon {
      color: #1976d2;
    }
    .female-icon {
      color: #e91e63;
    }
    .session-lecturers {
      display: flex;
      flex-wrap: wrap;
      gap: 2px;
      margin-top: 2px; /* Reduced from 4px */
    }
    .lecturer-chip {
      background-color: #f0f0f0;
      border-radius: 10px; /* Reduced from 12px */
      padding: 1px 4px; /* Reduced horizontal padding from 6px to 4px */
      font-size: 9px; /* Reduced from 10px */
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      line-height: 1.2; /* Added to make it more compact */
    }
    .time-pillbox {
      position: absolute;
      top: 2px;
      right: 4px;
      background-color: rgba(0, 0, 0, 0.04);
      color: #666;
      padding: 1px 6px;
      border-radius: 10px;
      font-size: 10px;
      opacity: 0.7;
      transition: opacity 0.2s;
    }
    .time-pillbox:hover {
      opacity: 1;
    }
    .time-pill {
      background-color: #f5f5f5;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 12px;
      display: inline-block;
    }
    .all-lecturers-btn {
      display: block;
      width: 100%;
      padding: 8px;
      margin-bottom: 16px;
      background-color: #1976d2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    .all-lecturers-btn:hover {
      background-color: #1565c0;
    }
    .gender-filter {
      display: flex;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 4px;
    }
    .filter-btn {
      background: none;
      border: none;
      border-radius: 4px;
      padding: 4px 8px;
      margin: 0 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .filter-btn.active {
      background-color: #bbdefb;
    }
    .filter-btn:hover {
      background-color: #e3f2fd;
    }
    .print-btn {
      background: none;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 4px 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    .print-btn:hover {
      background-color: #f5f5f5;
    }
    @media print {
      .sidebar, .header-controls {
        display: none !important;
      }
      .main-content {
        width: 100% !important;
        height: auto !important;
        overflow: visible !important;
        gap: 0 !important; /* Remove gap between header and timetable */
      }
      body {
        padding: 0 !important;
        height: auto !important;
        overflow: visible !important;
      }
      .container {
        padding: 0 !important;
        height: auto !important;
        display: block !important;
        overflow: visible !important;
      }
      .timetable {
        overflow: visible !important;
        height: auto !important;
        padding-top: 0 !important; /* Reduce space at the top */
      }
      .header {
        margin-bottom: 0 !important; /* Remove bottom margin */
      }
      /* Prevent rows from breaking across pages */
      .grid-row {
        page-break-inside: avoid !important;
        break-inside: avoid !important;
      }
      /* Make table headers repeat on each page */
      .grid-header-row {
        display: table-header-group !important;
      }
      /* Convert grid to table for better printing */
      .timetable-grid {
        display: table !important;
        width: 100% !important;
        border-collapse: collapse !important;
      }
    }
    .svg-icon {
      width: 1.5em; /* Reduced by 25% from 2em */
      height: 1.5em; /* Reduced by 25% from 2em */
      display: inline-block;
      fill: currentColor;
      vertical-align: middle;
    }
    /* Smaller icons in session cards */
    .session-card .svg-icon {
      width: 1.2em; /* Even smaller in session cards */
      height: 1.2em;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <button id="all-lecturers-btn" class="all-lecturers-btn">All Lecturers</button>
      <ul class="lecturer-list" id="lecturer-list">
        ${lecturers
          .filter(l => l && typeof l === 'object' && l.firstName)
          .sort((a, b) => a.firstName.localeCompare(b.firstName))
          .map(l => `
          <li class="lecturer-item ${l.id === exportLecturer.id ? 'selected' : ''}" data-id="${l.id}">
            ${l.firstName}${l.lastName ? ' ' + l.lastName : ''}
          </li>
        `).join('')}
      </ul>
    </div>
    <div class="main-content">
      <div class="header" id="lecturer-info">
        <div class="title-row">
          <div class="title-info">
            <h2 id="lecturer-name">${exportLecturer.title} ${exportLecturer.firstName}${exportLecturer.lastName ? ' ' + exportLecturer.lastName : ''}'s Timetable</h2>
            <span class="department-info">${departmentName} | ${academicYear} | ${currentSemester}</span>
          </div>
          <div class="header-controls">
            <div class="gender-filter">
              <button id="male-filter" class="filter-btn ${genderFilter === 'M' ? 'active' : ''}" title="Show Male Sections Only">
                <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: ${genderFilter === 'M' ? '#1976d2' : '#666'};" viewBox="0 0 24 24">
                  <path d="${MAN_ICON_PATH}" fill="currentColor"/>
                </svg>
              </button>
              <button id="female-filter" class="filter-btn ${genderFilter === 'F' ? 'active' : ''}" title="Show Female Sections Only">
                <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: ${genderFilter === 'F' ? '#e91e63' : '#666'};" viewBox="0 0 24 24">
                  <path d="${WOMAN_ICON_PATH}" fill="currentColor"/>
                </svg>
              </button>
              <button id="clear-filter" class="filter-btn" title="Clear Gender Filter" ${genderFilter === null ? 'disabled' : ''}>
                <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: #666;" viewBox="0 0 24 24">
                  <path d="M19.07 4.93l-1.41 1.41A8.014 8.014 0 0 0 12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8c4.41 0 8-3.59 8-8 0-1.48-.41-2.86-1.12-4.06l1.41-1.41C21.32 8.14 22 10.48 22 13c0 5.52-4.48 10-10 10S2 18.52 2 13 6.48 3 12 3c2.52 0 4.86.68 6.86 1.81l.21.12zm-7.07.97c-4.03 0-7.3 3.27-7.3 7.3s3.27 7.3 7.3 7.3 7.3-3.27 7.3-7.3c0-1.09-.24-2.13-.67-3.07L15.5 12.3c.31.55.5 1.18.5 1.85 0 2.12-1.73 3.85-3.85 3.85S8.3 16.27 8.3 14.15s1.73-3.85 3.85-3.85c.67 0 1.3.18 1.85.5l2.17-2.17c-.94-.43-1.98-.67-3.07-.67z" fill="currentColor"/>
                </svg>
              </button>
            </div>
            <button id="print-btn" class="print-btn" title="Print Timetable">
              <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: #666; margin-right: 4px;" viewBox="0 0 24 24">
                <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z" fill="currentColor"/>
              </svg>
              Print
            </button>
          </div>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">Fall Load:</span>
            <span class="info-value">${Number(fallLoad).toFixed(1)} hrs${currentSemester === 'Fall' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Spring Load:</span>
            <span class="info-value">${Number(springLoad).toFixed(1)} hrs${currentSemester === 'Spring' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Summer Load:</span>
            <span class="info-value">${currentSemester === 'Summer' ? Number(semesterLoad).toFixed(1) : '0.0'} hrs${currentSemester === 'Summer' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Fall Supervision:</span>
            <span class="info-value">${Number(exportLecturer.supervisionHoursFall).toFixed(1)} hrs${currentSemester === 'Fall' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Spring Supervision:</span>
            <span class="info-value">${Number(exportLecturer.supervisionHoursSpring).toFixed(1)} hrs${currentSemester === 'Spring' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
          </div>
          <div class="info-item">
            <span class="info-label">Year Load:</span>
            <span class="info-value">${Number(totalYearLoad).toFixed(1)}/${Number(yearWorkload).toFixed(1)} hrs</span>
          </div>
          <div class="info-item">
            <span class="info-label">Overload:</span>
            <span class="info-value ${overload > 0 ? 'overload-positive' : 'overload-negative'}">${overload > 0 ? '+' : ''}${Number(overload).toFixed(1)} hrs</span>
          </div>
        </div>
      </div>
      <div class="timetable">
        <div class="timetable-grid">
          <div class="grid-header">Period</div>
          ${days.map(day => `<div class="grid-header">${day}</div>`).join('')}

          ${getFilteredPeriods().map(period => `
            <div class="grid-cell period-cell">
              <div class="period-number">${period.period}</div>
            </div>
            ${days.map(day => {
              const sessionsForCell = findSessionsForDayAndPeriod(day, period.period);

              // Calculate time display for the pill
              const isLongDay = day === 'Monday' || day === 'Wednesday';
              const timeDisplay = isLongDay ? period.longTime : period.regularTime;

              // Calculate end time
              let endTimeDisplay = '';
              const periods = [...MORNING_PERIODS, ...EVENING_PERIODS];
              const nextPeriodIndex = periods.findIndex(p => p.period === period.period) + 1;

              if (isLongDay) {
                if (nextPeriodIndex < periods.length) {
                  endTimeDisplay = periods[nextPeriodIndex].longTime;
                } else if (period.period === 6) {
                  endTimeDisplay = '3:30 PM';
                } else if (period.period === 12) {
                  endTimeDisplay = '9:30 PM';
                }
              } else {
                if (nextPeriodIndex < periods.length) {
                  endTimeDisplay = periods[nextPeriodIndex].regularTime;
                } else if (period.period === 6) {
                  endTimeDisplay = '2 PM';
                } else if (period.period === 12) {
                  endTimeDisplay = '8 PM';
                }
              }

              // Check if this is a blocked time slot
              const isBlockedTimeSlot = (day === 'Monday' || day === 'Wednesday') &&
                ((period.period === 5 || period.period === 6) ||
                 (period.period === 11 || period.period === 12));

              // Create the time pill HTML
              const timePillHtml = !isBlockedTimeSlot && timeDisplay && endTimeDisplay ?
                `<div class="time-pillbox">${timeDisplay} - ${endTimeDisplay}</div>` : '';

              if (sessionsForCell.length === 0) {
                return `<div class="grid-cell">${timePillHtml}</div>`;
              } else {
                return `
                  <div class="grid-cell">
                    ${sessionsForCell.map(session => {
                      const section = sections[currentSemester].find(s => s.id === session.sectionId);
                      if (!section) return '';

                      const course = courses[currentSemester].find(c => c.id === section.courseId);
                      if (!course) return '';

                      // Get lecturer information for this session
                      const sessionLecturers = [];

                      // Process the main lecturerId
                      if (session.lecturerId) {
                        const lecturer = lecturers.find(l => l.id === session.lecturerId);
                        if (lecturer) {
                          sessionLecturers.push({
                            id: session.id + '-' + lecturer.id,
                            lecturerId: lecturer.id,
                            firstName: lecturer.firstName,
                            lastName: lecturer.lastName
                          });
                        }
                      }

                      // Process additional lecturerIds
                      if (session.lecturerIds && Array.isArray(session.lecturerIds)) {
                        const additionalLecturerIds = session.lecturerIds.filter((id: string) => id !== session.lecturerId);
                        additionalLecturerIds.forEach((lecturerId: string) => {
                          const lecturer = lecturers.find(l => l.id === lecturerId);
                          if (lecturer) {
                            sessionLecturers.push({
                              id: session.id + '-' + lecturer.id,
                              lecturerId: lecturer.id,
                              firstName: lecturer.firstName,
                              lastName: lecturer.lastName
                            });
                          }
                        });
                      }

                      return `
                        <div class="session-card" style="background-color: ${course.color}20;">
                          <div class="session-header">
                            <span class="session-title" title="${course.courseName}">${course.courseCode} (${section.sectionNumber})</span>
                            <span class="gender-icon ${section.gender === 'M' ? 'male-icon' : 'female-icon'}">
                              <svg class="svg-icon" viewBox="0 0 24 24">
                                <path d="${section.gender === 'M' ? MAN_ICON_PATH : WOMAN_ICON_PATH}"/>
                              </svg>
                            </span>
                          </div>
                          ${sessionLecturers.length > 0 ? `
                            <div class="session-lecturers">
                              ${sessionLecturers.map(lecturer => {
                                const displayName = lecturer.lastName
                                  ? `${lecturer.firstName} ${lecturer.lastName}`
                                  : lecturer.firstName;
                                return `<span class="lecturer-chip">${displayName}</span>`;
                              }).join('')}
                            </div>
                          ` : ''}
                        </div>
                      `;
                    }).join('')}
                  </div>
                `;
              }
            }).join('')}
          `).join('')}

          <script>
            // Initialize the timetable with the current lecturer
            document.addEventListener('DOMContentLoaded', function() {
              // Get the ID of the initially selected lecturer
              const selectedLecturerItem = document.querySelector('.lecturer-item.selected');
              if (selectedLecturerItem) {
                const lecturerId = selectedLecturerItem.dataset.id;
                updateTimetable(lecturerId);
              }
            });
          </script>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Store all lecturers data
    const lecturers = ${JSON.stringify(lecturers)};
    const currentSemester = "${currentSemester}";

    // Store sessions, sections, and courses for all semesters
    const allSessions = ${JSON.stringify(sessions)};
    const allSections = ${JSON.stringify(sections)};
    const allCourses = ${JSON.stringify(courses)};

    // Current semester data for backward compatibility
    const sessions = allSessions["${currentSemester}"];
    const sections = allSections["${currentSemester}"];
    const courses = allCourses["${currentSemester}"];

    const initialPeriods = ${JSON.stringify(getFilteredPeriods())};
    const days = ${JSON.stringify(days)};
    const manIconPath = "${MAN_ICON_PATH}";
    const womanIconPath = "${WOMAN_ICON_PATH}";

    // Define morning and evening periods for the HTML file
    const morningPeriods = [
      { period: 1, regularTime: '8 AM', longTime: '8 AM' },
      { period: 2, regularTime: '9 AM', longTime: '9:30 AM' },
      { period: 3, regularTime: '10 AM', longTime: '11 AM' },
      { period: 4, regularTime: '11 AM', longTime: '12:30 PM' },
      { period: 5, regularTime: '12 PM', longTime: '2 PM' },
      { period: 6, regularTime: '1 PM', longTime: '3:30 PM' }
    ];

    // Define evening periods for the HTML file
    const eveningPeriods = [
      { period: 7, regularTime: '2 PM', longTime: '2 PM' },
      { period: 8, regularTime: '3 PM', longTime: '3:30 PM' },
      { period: 9, regularTime: '4 PM', longTime: '5 PM' },
      { period: 10, regularTime: '5 PM', longTime: '6:30 PM' },
      { period: 11, regularTime: '6 PM', longTime: '8 PM' },
      { period: 12, regularTime: '7 PM', longTime: '9:30 PM' }
    ];

    // Get all periods for the full timetable
    function getAllPeriods() {
      return [...morningPeriods, ...eveningPeriods];
    };

    // Function to calculate semester load
    function calculateSemesterLoad(lecturer) {
      // Get supervision hours for current semester
      const supervisionHours = currentSemester === 'Summer' ? 0 :
                              currentSemester === 'Fall' ? lecturer.supervisionHoursFall :
                              lecturer.supervisionHoursSpring;

      // Get teaching hours for current semester
      const teachingHours = getTeachingHours(lecturer);

      return supervisionHours + teachingHours;
    }

    // Function to get teaching hours
    function getTeachingHours(lecturer) {
      // Find all sessions assigned to this lecturer
      const lecturerSessions = sessions.filter(
        session => session.lecturerId === lecturer.id ||
                  (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
      );

      // Calculate total teaching load based on sessions
      let totalLoadHours = 0;

      lecturerSessions.forEach(session => {
        // Find the section and course for this session
        const section = sections.find(s => s.id === session.sectionId);
        if (!section) return;

        const course = courses.find(c => c.id === section.courseId);
        if (!course) return;

        // Calculate contact hours based on day
        const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

        // Calculate load hours based on course's load-to-contact hours ratio
        const loadToContactRatio = course.loadHours / course.contactHours;
        let sessionLoadHours = contactHours * loadToContactRatio;

        // If multiple lecturers are assigned to this session, divide the load equally
        const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                             session.lecturerIds.length :
                             (session.lecturerId ? 1 : 0);

        if (lecturerCount > 1) {
          sessionLoadHours = sessionLoadHours / lecturerCount;
        }

        totalLoadHours += sessionLoadHours;
      });

      return totalLoadHours;
    }

    // Function to get teaching hours for a specific semester
    function getTeachingHoursForSemester(lecturer, semester) {
      // Find all sessions assigned to this lecturer in the specified semester
      const semesterSessions = allSessions[semester] || [];
      const semesterSections = allSections[semester] || [];
      const semesterCourses = allCourses[semester] || [];

      const lecturerSessions = semesterSessions.filter(
        session => session.lecturerId === lecturer.id ||
                  (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
      );

      // Calculate total teaching load based on sessions
      let totalLoadHours = 0;

      lecturerSessions.forEach(session => {
        // Find the section and course for this session
        const section = semesterSections.find(s => s.id === session.sectionId);
        if (!section) return;

        const course = semesterCourses.find(c => c.id === section.courseId);
        if (!course) return;

        // Calculate contact hours based on day
        const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

        // Calculate load hours based on course's load-to-contact hours ratio
        const loadToContactRatio = course.loadHours / course.contactHours;
        let sessionLoadHours = contactHours * loadToContactRatio;

        // If multiple lecturers are assigned to this session, divide the load equally
        const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                             session.lecturerIds.length :
                             (session.lecturerId ? 1 : 0);

        if (lecturerCount > 1) {
          sessionLoadHours = sessionLoadHours / lecturerCount;
        }

        totalLoadHours += sessionLoadHours;
      });

      return totalLoadHours;
    }

    // Function to calculate yearly load
    function calculateYearlyLoad(lecturer) {
      // Supervision hours from both semesters
      const supervisionHours = lecturer.supervisionHoursFall + lecturer.supervisionHoursSpring;

      // Calculate Fall teaching load - simplified for this example
      const fallTeachingLoad = getTeachingHoursForSemester(lecturer, 'Fall');

      // Calculate Spring teaching load - simplified for this example
      const springTeachingLoad = getTeachingHoursForSemester(lecturer, 'Spring');

      // Summer is not included in yearly load
      return supervisionHours + fallTeachingLoad + springTeachingLoad;
    }

    // Function to find sessions for a specific day and period
    function findSessionsForDayAndPeriod(day, period, lecturerId = null) {
      let filteredSessions = sessions.filter(session =>
        session.day === day &&
        session.startPeriod !== undefined &&
        session.endPeriod !== undefined &&
        period >= session.startPeriod &&
        period <= session.endPeriod
      );

      if (lecturerId) {
        filteredSessions = filteredSessions.filter(session =>
          session.lecturerId === lecturerId ||
          (session.lecturerIds ? session.lecturerIds.includes(lecturerId) : false)
        );
      }

      return filteredSessions;
    }

    // Function to get periods that have sessions for the lecturer
    function getActivePeriods(lecturerId = null) {
      // Get all sessions for the lecturer
      let lecturerSessions = sessions;

      if (lecturerId) {
        lecturerSessions = sessions.filter(session =>
          session.lecturerId === lecturerId ||
          (session.lecturerIds && session.lecturerIds.includes(lecturerId))
        );
      }

      // Collect all periods that have sessions
      const activePeriods = new Set();

      lecturerSessions.forEach(session => {
        if (session.startPeriod !== undefined && session.endPeriod !== undefined) {
          for (let i = session.startPeriod; i <= session.endPeriod; i++) {
            activePeriods.add(i);
          }
        }
      });

      return Array.from(activePeriods).sort((a, b) => a - b);
    }

    // Function to filter periods to only show those with sessions
    function getFilteredPeriods(lecturerId = null) {
      const activePeriods = getActivePeriods(lecturerId);
      const allPeriods = getAllPeriods();

      // If viewing all lecturers or no active periods, show all periods
      if (!lecturerId || activePeriods.length === 0) return allPeriods;

      return allPeriods.filter(period => activePeriods.includes(period.period));
    }

    // Function to update the timetable for a specific lecturer
    function updateTimetable(lecturerId = null) {
      const lecturer = lecturerId ? lecturers.find(l => l.id === lecturerId) : null;

      // Update lecturer info header
      const lecturerInfoElement = document.getElementById('lecturer-info');
      const lecturerNameElement = document.getElementById('lecturer-name');

      if (lecturer) {
        // Calculate loads
        const semesterLoad = calculateSemesterLoad(lecturer);
        const fallTeachingHours = getTeachingHoursForSemester(lecturer, 'Fall');
        const fallLoad = lecturer.supervisionHoursFall + fallTeachingHours;
        const springTeachingHours = getTeachingHoursForSemester(lecturer, 'Spring');
        const springLoad = lecturer.supervisionHoursSpring + springTeachingHours;
        const totalYearLoad = calculateYearlyLoad(lecturer);
        const yearWorkload = lecturer.maxYearLoad;
        const overload = totalYearLoad - lecturer.maxYearLoad;

        // Update lecturer name
        lecturerNameElement.textContent = \`\${lecturer.title} \${lecturer.firstName}\${lecturer.lastName ? ' ' + lecturer.lastName : ''}'s Timetable\`;

        // Update lecturer info
        lecturerInfoElement.innerHTML = \`
          <div class="title-row">
            <div class="title-info">
              <h2 id="lecturer-name">\${lecturer.title} \${lecturer.firstName}\${lecturer.lastName ? ' ' + lecturer.lastName : ''}'s Timetable</h2>
              <span class="department-info">${departmentName} | ${academicYear} | \${currentSemester}</span>
            </div>
            <div class="header-controls">
              <div class="gender-filter">
                <button id="male-filter" class="filter-btn \${currentGenderFilter === 'M' ? 'active' : ''}" title="Show Male Sections Only">
                  <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: \${currentGenderFilter === 'M' ? '#1976d2' : '#666'};" viewBox="0 0 24 24">
                    <path d="\${manIconPath}" fill="currentColor"/>
                  </svg>
                </button>
                <button id="female-filter" class="filter-btn \${currentGenderFilter === 'F' ? 'active' : ''}" title="Show Female Sections Only">
                  <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: \${currentGenderFilter === 'F' ? '#e91e63' : '#666'};" viewBox="0 0 24 24">
                    <path d="\${womanIconPath}" fill="currentColor"/>
                  </svg>
                </button>
                <button id="clear-filter" class="filter-btn" title="Clear Gender Filter" \${currentGenderFilter === null ? 'disabled' : ''}>
                  <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: #666;" viewBox="0 0 24 24">
                    <path d="M19.07 4.93l-1.41 1.41A8.014 8.014 0 0 0 12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8c4.41 0 8-3.59 8-8 0-1.48-.41-2.86-1.12-4.06l1.41-1.41C21.32 8.14 22 10.48 22 13c0 5.52-4.48 10-10 10S2 18.52 2 13 6.48 3 12 3c2.52 0 4.86.68 6.86 1.81l.21.12zm-7.07.97c-4.03 0-7.3 3.27-7.3 7.3s3.27 7.3 7.3 7.3 7.3-3.27 7.3-7.3c0-1.09-.24-2.13-.67-3.07L15.5 12.3c.31.55.5 1.18.5 1.85 0 2.12-1.73 3.85-3.85 3.85S8.3 16.27 8.3 14.15s1.73-3.85 3.85-3.85c.67 0 1.3.18 1.85.5l2.17-2.17c-.94-.43-1.98-.67-3.07-.67z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
              <button id="print-btn" class="print-btn" title="Print Timetable">
                <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: #666; margin-right: 4px;" viewBox="0 0 24 24">
                  <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z" fill="currentColor"/>
                </svg>
                Print
              </button>
            </div>
          </div>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">Fall Load:</span>
              <span class="info-value">\${fallLoad.toFixed(1)} hrs\${currentSemester === 'Fall' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Spring Load:</span>
              <span class="info-value">\${springLoad.toFixed(1)} hrs\${currentSemester === 'Spring' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Summer Load:</span>
              <span class="info-value">\${currentSemester === 'Summer' ? semesterLoad.toFixed(1) : '0.0'} hrs\${currentSemester === 'Summer' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Fall Supervision:</span>
              <span class="info-value">\${lecturer.supervisionHoursFall.toFixed(1)} hrs\${currentSemester === 'Fall' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Spring Supervision:</span>
              <span class="info-value">\${lecturer.supervisionHoursSpring.toFixed(1)} hrs\${currentSemester === 'Spring' ? ' <span class="current-semester">(Current)</span>' : ''}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Year Load:</span>
              <span class="info-value">\${totalYearLoad.toFixed(1)}/\${yearWorkload.toFixed(1)} hrs</span>
            </div>
            <div class="info-item">
              <span class="info-label">Overload:</span>
              <span class="info-value \${overload > 0 ? 'overload-positive' : 'overload-negative'}">\${overload > 0 ? '+' : ''}\${overload.toFixed(1)} hrs</span>
            </div>
          </div>
        \`;
      } else {
        // Show all lecturers timetable
        lecturerNameElement.textContent = 'All Lecturers Timetable';
        lecturerInfoElement.innerHTML = \`
          <div class="title-row">
            <div class="title-info">
              <h2 id="lecturer-name">All Lecturers Timetable</h2>
              <span class="department-info">${departmentName} | ${academicYear} | \${currentSemester}</span>
            </div>
            <div class="header-controls">
              <div class="gender-filter">
                <button id="male-filter" class="filter-btn \${currentGenderFilter === 'M' ? 'active' : ''}" title="Show Male Sections Only">
                  <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: \${currentGenderFilter === 'M' ? '#1976d2' : '#666'};" viewBox="0 0 24 24">
                    <path d="\${manIconPath}" fill="currentColor"/>
                  </svg>
                </button>
                <button id="female-filter" class="filter-btn \${currentGenderFilter === 'F' ? 'active' : ''}" title="Show Female Sections Only">
                  <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: \${currentGenderFilter === 'F' ? '#e91e63' : '#666'};" viewBox="0 0 24 24">
                    <path d="\${womanIconPath}" fill="currentColor"/>
                  </svg>
                </button>
                <button id="clear-filter" class="filter-btn" title="Clear Gender Filter" \${currentGenderFilter === null ? 'disabled' : ''}>
                  <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: #666;" viewBox="0 0 24 24">
                    <path d="M19.07 4.93l-1.41 1.41A8.014 8.014 0 0 0 12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8c4.41 0 8-3.59 8-8 0-1.48-.41-2.86-1.12-4.06l1.41-1.41C21.32 8.14 22 10.48 22 13c0 5.52-4.48 10-10 10S2 18.52 2 13 6.48 3 12 3c2.52 0 4.86.68 6.86 1.81l.21.12zm-7.07.97c-4.03 0-7.3 3.27-7.3 7.3s3.27 7.3 7.3 7.3 7.3-3.27 7.3-7.3c0-1.09-.24-2.13-.67-3.07L15.5 12.3c.31.55.5 1.18.5 1.85 0 2.12-1.73 3.85-3.85 3.85S8.3 16.27 8.3 14.15s1.73-3.85 3.85-3.85c.67 0 1.3.18 1.85.5l2.17-2.17c-.94-.43-1.98-.67-3.07-.67z" fill="currentColor"/>
                  </svg>
                </button>
              </div>
              <button id="print-btn" class="print-btn" title="Print Timetable">
                <svg class="svg-icon" style="width: 1.5em; height: 1.5em; color: #666; margin-right: 4px;" viewBox="0 0 24 24">
                  <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z" fill="currentColor"/>
                </svg>
                Print
              </button>
            </div>
          </div>
        \`;
      }

      // Get filtered periods based on the selected lecturer
      const filteredPeriods = getFilteredPeriods(lecturerId);

      // Reattach event listeners for filter buttons and print button
      document.getElementById('male-filter').addEventListener('click', function() {
        currentGenderFilter = currentGenderFilter === 'M' ? null : 'M';
        updateGenderFilter();
        updateTimetable(document.querySelector('.lecturer-item.selected')?.dataset.id || null);
      });

      document.getElementById('female-filter').addEventListener('click', function() {
        currentGenderFilter = currentGenderFilter === 'F' ? null : 'F';
        updateGenderFilter();
        updateTimetable(document.querySelector('.lecturer-item.selected')?.dataset.id || null);
      });

      document.getElementById('clear-filter').addEventListener('click', function() {
        currentGenderFilter = null;
        updateGenderFilter();
        updateTimetable(document.querySelector('.lecturer-item.selected')?.dataset.id || null);
      });

      document.getElementById('print-btn').addEventListener('click', function() {
        // Prepare the document for printing by ensuring all content is visible
        const container = document.querySelector('.container');
        const timetable = document.querySelector('.timetable');
        const mainContent = document.querySelector('.main-content');

        // Store original styles to restore after printing
        const originalStyles = {
          container: container.style.cssText,
          timetable: timetable.style.cssText,
          mainContent: mainContent.style.cssText,
          body: document.body.style.cssText
        };

        // Apply print-friendly styles
        document.body.style.overflow = 'visible';
        document.body.style.height = 'auto';
        container.style.height = 'auto';
        container.style.overflow = 'visible';
        mainContent.style.height = 'auto';
        mainContent.style.overflow = 'visible';
        mainContent.style.gap = '0';
        timetable.style.height = 'auto';
        timetable.style.overflow = 'visible';
        timetable.style.paddingTop = '0';

        // Ensure table headers repeat on each page
        const tableHead = document.querySelector('.timetable-grid thead');
        if (tableHead) {
          tableHead.style.display = 'table-header-group';
        }

        // Prevent rows from breaking across pages
        const tableRows = document.querySelectorAll('.timetable-grid tbody tr');
        tableRows.forEach(row => {
          row.style.pageBreakInside = 'avoid';
          row.style.breakInside = 'avoid';
        });

        // Delay printing to ensure styles are applied
        setTimeout(() => {
          window.print();

          // Restore original styles after print dialog closes
          setTimeout(() => {
            document.body.style.cssText = originalStyles.body;
            container.style.cssText = originalStyles.container;
            timetable.style.cssText = originalStyles.timetable;
            mainContent.style.cssText = originalStyles.mainContent;
          }, 500);
        }, 300);
      });

      // Update timetable grid
      const timetableElement = document.querySelector('.timetable');

      let timetableHtml = '<table class="timetable-grid">';

      // Create a header row that will repeat on each printed page
      timetableHtml += '<thead><tr>';
      timetableHtml += '<th>Period</th>';

      // Add day headers
      days.forEach(day => {
        timetableHtml += '<th>' + day + '</th>';
      });
      timetableHtml += '</tr></thead>';

      // Create table body
      timetableHtml += '<tbody>';

      // Add rows for each period
      filteredPeriods.forEach(period => {
        // Start a new row for each period
        timetableHtml += '<tr>';

        // Add period cell
        timetableHtml += '<td class="period-cell">' +
          '<div class="period-number">' + period.period + '</div>' +
        '</td>';

        // Add cells for each day
        days.forEach(day => {
          const sessionsForCell = findSessionsForDayAndPeriod(day, period.period, lecturerId);

          // Calculate time display for the pill
          const isLongDay = day === 'Monday' || day === 'Wednesday';
          const timeDisplay = isLongDay ? period.longTime : period.regularTime;

          // Calculate end time
          let endTimeDisplay = '';
          const periods = getAllPeriods();
          const nextPeriodIndex = periods.findIndex(p => p.period === period.period) + 1;

          if (isLongDay) {
            if (nextPeriodIndex < periods.length) {
              endTimeDisplay = periods[nextPeriodIndex].longTime;
            } else if (period.period === 6) {
              endTimeDisplay = '3:30 PM';
            } else if (period.period === 12) {
              endTimeDisplay = '9:30 PM';
            }
          } else {
            if (nextPeriodIndex < periods.length) {
              endTimeDisplay = periods[nextPeriodIndex].regularTime;
            } else if (period.period === 6) {
              endTimeDisplay = '2 PM';
            } else if (period.period === 12) {
              endTimeDisplay = '8 PM';
            }
          }

          // Check if this is a blocked time slot
          const isBlockedTimeSlot = (day === 'Monday' || day === 'Wednesday') &&
            ((period.period === 5 || period.period === 6) ||
             (period.period === 11 || period.period === 12));

          // Create the time pill HTML
          const timePillHtml = !isBlockedTimeSlot && timeDisplay && endTimeDisplay ?
            '<div class="time-pillbox">' + timeDisplay + ' - ' + endTimeDisplay + '</div>' : '';

          // Start the day cell
          timetableHtml += '<td class="day-cell">' + timePillHtml;

          // Add sessions if any
          if (sessionsForCell.length > 0) {
            sessionsForCell.forEach(session => {
              const section = sections.find(s => s.id === session.sectionId);
              if (!section) return;

              const course = courses.find(c => c.id === section.courseId);
              if (!course) return;

              const cardBgColor = course.color + '20';
              const genderClass = section.gender === 'M' ? 'male-icon' : 'female-icon';
              const genderPath = section.gender === 'M' ? manIconPath : womanIconPath;

              timetableHtml += '<div class="session-card" style="background-color: ' + cardBgColor + ';">' +
                '<div class="session-header">' +
                  '<span class="session-title" title="' + course.courseName + '">' + course.courseCode + ' (' + section.sectionNumber + ')</span>' +
                  '<span class="gender-icon ' + genderClass + '">' +
                    '<svg class="svg-icon" viewBox="0 0 24 24">' +
                      '<path d="' + genderPath + '"/>' +
                    '</svg>' +
                  '</span>' +
                '</div>';

              // Add lecturer information
              timetableHtml += '<div class="session-lecturers">';

              // Get lecturer information for this session
              const sessionLecturers = [];

              // Process the main lecturerId
              if (session.lecturerId) {
                const lecturer = lecturers.find(l => l.id === session.lecturerId);
                if (lecturer) {
                  sessionLecturers.push({
                    firstName: lecturer.firstName,
                    lastName: lecturer.lastName
                  });
                }
              }

              // Process additional lecturerIds
              if (session.lecturerIds && Array.isArray(session.lecturerIds)) {
                const additionalLecturerIds = session.lecturerIds.filter(id => id !== session.lecturerId);
                additionalLecturerIds.forEach(lecturerId => {
                  const lecturer = lecturers.find(l => l.id === lecturerId);
                  if (lecturer) {
                    sessionLecturers.push({
                      firstName: lecturer.firstName,
                      lastName: lecturer.lastName
                    });
                  }
                });
              }

              // Add lecturer chips
              sessionLecturers.forEach(lecturer => {
                const displayName = lecturer.lastName
                  ? lecturer.firstName + ' ' + lecturer.lastName
                  : lecturer.firstName;
                timetableHtml += '<span class="lecturer-chip">' + displayName + '</span>';
              });

              timetableHtml += '</div></div>'; // Close session-lecturers and session-card
            });
          }

          // Close the day cell
          timetableHtml += '</td>';
        });

        // Close the row
        timetableHtml += '</tr>';
      });

      // Close the table body and table
      timetableHtml += '</tbody></table>';
      timetableElement.innerHTML = timetableHtml;
    }

    // Add event listeners to lecturer list items
    document.querySelectorAll('.lecturer-item').forEach(item => {
      item.addEventListener('click', function() {
        // Remove selected class from all items
        document.querySelectorAll('.lecturer-item').forEach(i => i.classList.remove('selected'));

        // Add selected class to clicked item
        this.classList.add('selected');

        // Update timetable for selected lecturer
        updateTimetable(this.dataset.id);
      });
    });

    // Add event listener to "All Lecturers" button
    document.getElementById('all-lecturers-btn').addEventListener('click', function() {
      // Remove selected class from all items
      document.querySelectorAll('.lecturer-item').forEach(i => i.classList.remove('selected'));

      // Update timetable for all lecturers
      updateTimetable();
    });

    // Gender filter state
    let currentGenderFilter = ${genderFilter ? "'" + genderFilter + "'" : 'null'};

    // Function to update gender filter UI
    function updateGenderFilter() {
      // Update male filter button
      const maleBtn = document.getElementById('male-filter');
      maleBtn.classList.toggle('active', currentGenderFilter === 'M');
      maleBtn.querySelector('svg').style.color = currentGenderFilter === 'M' ? '#1976d2' : '#666';

      // Update female filter button
      const femaleBtn = document.getElementById('female-filter');
      femaleBtn.classList.toggle('active', currentGenderFilter === 'F');
      femaleBtn.querySelector('svg').style.color = currentGenderFilter === 'F' ? '#e91e63' : '#666';

      // Update clear filter button
      const clearBtn = document.getElementById('clear-filter');
      clearBtn.disabled = currentGenderFilter === null;
    }

    // Auto-print if URL has print parameter
    if (window.location.search.includes('print=true')) {
      // Add a delay to ensure everything is loaded
      setTimeout(() => {
        // Prepare the document for printing by ensuring all content is visible
        const container = document.querySelector('.container');
        const timetable = document.querySelector('.timetable');
        const mainContent = document.querySelector('.main-content');

        // Store original styles to restore after printing
        const originalStyles = {
          container: container.style.cssText,
          timetable: timetable.style.cssText,
          mainContent: mainContent.style.cssText,
          body: document.body.style.cssText
        };

        // Apply print-friendly styles
        document.body.style.overflow = 'visible';
        document.body.style.height = 'auto';
        container.style.height = 'auto';
        container.style.overflow = 'visible';
        mainContent.style.height = 'auto';
        mainContent.style.overflow = 'visible';
        mainContent.style.gap = '0';
        timetable.style.height = 'auto';
        timetable.style.overflow = 'visible';
        timetable.style.paddingTop = '0';

        // Ensure table headers repeat on each page
        const tableHead = document.querySelector('.timetable-grid thead');
        if (tableHead) {
          tableHead.style.display = 'table-header-group';
        }

        // Prevent rows from breaking across pages
        const tableRows = document.querySelectorAll('.timetable-grid tbody tr');
        tableRows.forEach(row => {
          row.style.pageBreakInside = 'avoid';
          row.style.breakInside = 'avoid';
        });

        // Print the document
        window.print();

        // Restore original styles after print dialog closes
        setTimeout(() => {
          document.body.style.cssText = originalStyles.body;
          container.style.cssText = originalStyles.container;
          timetable.style.cssText = originalStyles.timetable;
          mainContent.style.cssText = originalStyles.mainContent;
        }, 500);
      }, 500);
    }

    // Function to apply gender filter to sessions
    function applyGenderFilter(sessions) {
      if (!currentGenderFilter) return sessions;

      return sessions.filter(session => {
        const section = allSections[currentSemester].find(s => s.id === session.sectionId);
        return section && section.gender === currentGenderFilter;
      });
    }

    // Update the findSessionsForDayAndPeriod function to apply gender filter
    const originalFindSessionsForDayAndPeriod = findSessionsForDayAndPeriod;
    findSessionsForDayAndPeriod = function(day, period, lecturerId = null) {
      let sessions = originalFindSessionsForDayAndPeriod(day, period, lecturerId);
      return applyGenderFilter(sessions);
    };

    // Clear all localStorage data when window is closed to prevent data persistence between exports
    window.addEventListener('beforeunload', function() {
      // Clear all localStorage items related to timetable exports
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('timetable_export_')) {
          localStorage.removeItem(key);
        }
      });

      // Clear any sessionStorage flags
      sessionStorage.removeItem('clear_export_data');
    });

    // Clear all localStorage data when the page loads to ensure a fresh start
    window.addEventListener('load', function() {
      // Clear all localStorage items that start with 'timetable_export_'
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('timetable_export_')) {
          localStorage.removeItem(key);
        }
      });

      // Clear any sessionStorage flags
      sessionStorage.removeItem('clear_export_data');

      // Set a flag to indicate this is a fresh session
      localStorage.setItem('timetable_export_current', JSON.stringify({
        currentLecturerId: currentLecturerId,
        currentGenderFilter: currentGenderFilter,
        currentSemester: currentSemester,
        timestamp: new Date().getTime()
      }));
    });
  </script>
</body>
</html>
  `;
};
