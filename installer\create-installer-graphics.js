/**
 * Create installer graphics for QU Scheduler NSIS installer
 * Generates header and welcome images for professional installer appearance
 */

const fs = require('fs');
const path = require('path');

// Create installer graphics directory
const graphicsDir = path.join(__dirname, '../assets/icons');
if (!fs.existsSync(graphicsDir)) {
  fs.mkdirSync(graphicsDir, { recursive: true });
}

// Create header image (150x57 pixels) for installer header
const headerSVG = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="150" height="57" viewBox="0 0 150 57" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="150" height="57" fill="url(#headerGradient)"/>
  
  <!-- QU Logo area -->
  <circle cx="28" cy="28" r="20" fill="#FFFFFF" opacity="0.9"/>
  <text x="28" y="33" text-anchor="middle" font-family="serif" font-size="12" font-weight="bold" fill="#8B1538">QU</text>
  
  <!-- Text -->
  <text x="55" y="25" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">QU Scheduler</text>
  <text x="55" y="40" font-family="Arial, sans-serif" font-size="9" fill="#FFFFFF" opacity="0.9">Qatar University</text>
</svg>`;

// Create welcome image (164x314 pixels) for installer welcome page
const welcomeSVG = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="164" height="314" viewBox="0 0 164 314" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="welcomeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#B91C3C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DC2626;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="goldAccent" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="164" height="314" fill="url(#welcomeGradient)"/>
  
  <!-- Decorative pattern -->
  <g opacity="0.1">
    <circle cx="20" cy="50" r="15" fill="#FFFFFF"/>
    <circle cx="144" cy="100" r="10" fill="#FFFFFF"/>
    <circle cx="30" cy="200" r="8" fill="#FFFFFF"/>
    <circle cx="134" cy="250" r="12" fill="#FFFFFF"/>
  </g>
  
  <!-- Main logo area -->
  <g transform="translate(82, 120)">
    <!-- Background circle -->
    <circle cx="0" cy="0" r="35" fill="#FFFFFF" opacity="0.95"/>
    
    <!-- Academic building -->
    <g transform="scale(0.6)">
      <!-- Building base -->
      <rect x="-25" y="-10" width="50" height="25" fill="url(#welcomeGradient)" rx="3"/>
      
      <!-- Columns -->
      <rect x="-20" y="-8" width="4" height="20" fill="url(#goldAccent)"/>
      <rect x="-10" y="-8" width="4" height="20" fill="url(#goldAccent)"/>
      <rect x="0" y="-8" width="4" height="20" fill="url(#goldAccent)"/>
      <rect x="10" y="-8" width="4" height="20" fill="url(#goldAccent)"/>
      <rect x="16" y="-8" width="4" height="20" fill="url(#goldAccent)"/>
      
      <!-- Roof -->
      <polygon points="-30,-15 0,-25 30,-15" fill="url(#goldAccent)"/>
      
      <!-- QU emblem -->
      <circle cx="0" cy="0" r="8" fill="#FFFFFF" stroke="url(#goldAccent)" stroke-width="1"/>
      <text x="0" y="2" text-anchor="middle" font-family="serif" font-size="8" font-weight="bold" fill="url(#welcomeGradient)">QU</text>
    </g>
  </g>
  
  <!-- Calendar element -->
  <g transform="translate(82, 200)">
    <rect x="-20" y="-15" width="40" height="30" fill="#FFFFFF" stroke="url(#welcomeGradient)" stroke-width="1" rx="2"/>
    <!-- Grid lines -->
    <g stroke="url(#welcomeGradient)" stroke-width="0.5" opacity="0.6">
      <line x1="-15" y1="-5" x2="15" y2="-5"/>
      <line x1="-15" y1="0" x2="15" y2="0"/>
      <line x1="-15" y1="5" x2="15" y2="5"/>
      <line x1="-10" y1="-10" x2="-10" y2="10"/>
      <line x1="0" y1="-10" x2="0" y2="10"/>
      <line x1="10" y1="-10" x2="10" y2="10"/>
    </g>
    <!-- Sample blocks -->
    <rect x="-18" y="-12" width="8" height="4" fill="url(#goldAccent)" opacity="0.8" rx="1"/>
    <rect x="-5" y="-2" width="8" height="4" fill="url(#goldAccent)" opacity="0.8" rx="1"/>
    <rect x="8" y="3" width="8" height="4" fill="url(#goldAccent)" opacity="0.8" rx="1"/>
  </g>
  
  <!-- Bottom text -->
  <text x="82" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF">QU Scheduler</text>
  <text x="82" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#FFFFFF" opacity="0.9">Professional Timetable</text>
  <text x="82" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#FFFFFF" opacity="0.9">Scheduling Application</text>
</svg>`;

// Write SVG files
fs.writeFileSync(path.join(graphicsDir, 'header.svg'), headerSVG);
fs.writeFileSync(path.join(graphicsDir, 'welcome.svg'), welcomeSVG);

// Create a simple LICENSE.txt file for the installer
const licenseText = `QU SCHEDULER LICENSE AGREEMENT

Copyright © 2025 Qatar University. All rights reserved.

This software is licensed for use by Qatar University and its authorized personnel.

TERMS AND CONDITIONS:

1. GRANT OF LICENSE
   Qatar University grants you a non-exclusive license to use QU Scheduler
   for academic and administrative purposes within Qatar University.

2. RESTRICTIONS
   You may not:
   - Distribute this software outside Qatar University
   - Reverse engineer, decompile, or disassemble the software
   - Remove or alter any copyright notices

3. SUPPORT
   For technical support, contact: <EMAIL>

4. WARRANTY DISCLAIMER
   This software is provided "as is" without warranty of any kind.

5. LIMITATION OF LIABILITY
   Qatar University shall not be liable for any damages arising from
   the use of this software.

By installing this software, you agree to these terms and conditions.

Qatar University
Doha, Qatar
https://qu.edu.qa`;

fs.writeFileSync(path.join(__dirname, 'LICENSE.txt'), licenseText);

// Create build script for NSIS installer
const buildScript = `@echo off
echo Building QU Scheduler Installer...
echo.

REM Check if NSIS is installed
where makensis >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: NSIS is not installed or not in PATH
    echo Please install NSIS from https://nsis.sourceforge.io/
    echo and add it to your system PATH
    pause
    exit /b 1
)

REM Check if application is built
if not exist "out\\QU Scheduler-win32-x64\\qu-scheduler.exe" (
    echo ERROR: Application not found. Please run 'npm run package' first.
    pause
    exit /b 1
)

REM Convert SVG images to BMP (requires ImageMagick or manual conversion)
echo Converting installer graphics...
if exist "assets\\icons\\header.svg" (
    echo Please convert assets/icons/header.svg to assets/icons/header.bmp ^(150x57^)
    echo Please convert assets/icons/welcome.svg to assets/icons/welcome.bmp ^(164x314^)
    echo You can use online converters or ImageMagick for this conversion.
    echo.
)

REM Build the installer
echo Building NSIS installer...
makensis installer\\qu-scheduler-installer.nsi

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: QU-Scheduler-Setup.exe created successfully!
    echo Location: installer\\QU-Scheduler-Setup.exe
    echo.
    echo Installer features:
    echo - Custom installation location
    echo - Desktop shortcut option
    echo - Start menu shortcuts option
    echo - Quick launch shortcut option
    echo - File associations option
    echo - Professional uninstaller
    echo.
) else (
    echo.
    echo ERROR: Failed to build installer
    echo Check the NSIS script for errors
)

pause`;

fs.writeFileSync(path.join(__dirname, 'build-installer.bat'), buildScript);

// Create instructions
const instructions = `# QU Scheduler Advanced Installer Setup

## Overview
This creates a professional NSIS installer with user options for:
- Custom installation location
- Desktop shortcut creation
- Start menu shortcuts
- Quick launch shortcut
- File associations (.qus files)
- Professional uninstaller

## Prerequisites

### 1. Install NSIS (Nullsoft Scriptable Install System)
- Download from: https://nsis.sourceforge.io/
- Install and add to system PATH
- Verify installation: \`makensis /VERSION\`

### 2. Convert Graphics (Optional but Recommended)
Convert the generated SVG files to BMP format:
- \`assets/icons/header.svg\` → \`assets/icons/header.bmp\` (150x57 pixels)
- \`assets/icons/welcome.svg\` → \`assets/icons/welcome.bmp\` (164x314 pixels)

You can use:
- Online converters (convertio.co, cloudconvert.com)
- ImageMagick: \`convert header.svg -resize 150x57 header.bmp\`
- GIMP or Photoshop

## Building the Installer

### Method 1: Using the Build Script
1. Ensure the Electron app is packaged: \`npm run package\`
2. Run: \`installer\\build-installer.bat\`

### Method 2: Manual Build
1. Package the app: \`npm run package\`
2. Convert graphics to BMP format
3. Run: \`makensis installer\\qu-scheduler-installer.nsi\`

## Installer Features

### User Options During Installation:
1. **Installation Type Selection**:
   - Full Installation (Recommended)
   - Minimal Installation
   - Custom Installation

2. **Component Selection**:
   - Core Application (Required)
   - Desktop Shortcut
   - Start Menu Shortcuts
   - Quick Launch Shortcut
   - File Associations

3. **Installation Location**:
   - Default: \`C:\\Program Files\\Qatar University\\QU Scheduler\`
   - User can choose custom location

4. **System Integration**:
   - Windows Add/Remove Programs entry
   - File associations for .qus files
   - Proper uninstaller

### Professional Features:
- License agreement display
- System requirements check (Windows 7+, 64-bit)
- Prevents multiple installer instances
- Professional graphics and branding
- Complete uninstallation support

## Testing the Installer

1. Build the installer using above methods
2. Test on a clean Windows system
3. Verify all options work correctly:
   - Installation location selection
   - Shortcut creation options
   - File associations
   - Uninstallation process

## Customization

Edit \`installer/qu-scheduler-installer.nsi\` to:
- Add more installation options
- Modify default installation path
- Add additional file associations
- Customize installer appearance
- Add pre/post installation scripts

## Troubleshooting

### Common Issues:
1. **NSIS not found**: Install NSIS and add to PATH
2. **Graphics missing**: Convert SVG to BMP format
3. **App not found**: Run \`npm run package\` first
4. **Permission errors**: Run as administrator

### Build Verification:
- Installer size should be ~150MB+ (includes Electron runtime)
- Test installation on different Windows versions
- Verify all shortcuts and associations work
- Test uninstallation completely removes the app

## Distribution

The final installer \`QU-Scheduler-Setup.exe\` can be:
- Distributed to Qatar University staff
- Deployed via group policy
- Hosted on university software portal
- Included in system images

For enterprise deployment, consider:
- Code signing the installer
- Creating MSI packages
- Setting up automatic updates
- Adding silent installation options`;

fs.writeFileSync(path.join(__dirname, 'INSTALLER_INSTRUCTIONS.md'), instructions);

console.log('✅ NSIS Installer Setup Complete!');
console.log('');
console.log('📁 Created Files:');
console.log('   • installer/qu-scheduler-installer.nsi (NSIS script)');
console.log('   • installer/LICENSE.txt (License agreement)');
console.log('   • installer/build-installer.bat (Build script)');
console.log('   • installer/INSTALLER_INSTRUCTIONS.md (Setup guide)');
console.log('   • assets/icons/header.svg (Installer header graphic)');
console.log('   • assets/icons/welcome.svg (Installer welcome graphic)');
console.log('');
console.log('🎯 Installer Features:');
console.log('   ✅ Custom installation location');
console.log('   ✅ Desktop shortcut option');
console.log('   ✅ Start menu shortcuts');
console.log('   ✅ Quick launch shortcut');
console.log('   ✅ File associations (.qus files)');
console.log('   ✅ Professional uninstaller');
console.log('   ✅ License agreement display');
console.log('   ✅ System requirements check');
console.log('');
console.log('📋 Next Steps:');
console.log('   1. Install NSIS: https://nsis.sourceforge.io/');
console.log('   2. Convert SVG graphics to BMP format');
console.log('   3. Run: npm run package');
console.log('   4. Run: installer\\build-installer.bat');
console.log('');
console.log('📖 See installer/INSTALLER_INSTRUCTIONS.md for detailed setup guide');
