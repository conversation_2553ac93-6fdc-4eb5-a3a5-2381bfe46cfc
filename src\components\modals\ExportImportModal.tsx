import React, { useState } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import AccessibleDialog from '../common/AccessibleDialog';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import FileUploadIcon from '@mui/icons-material/FileUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { Semester } from '../../types/models';
import { ImportSummary } from '../../utils/dataExportImport';

interface ExportImportModalProps {
  open: boolean;
  onClose: () => void;
  onExport: (semester: Semester | 'all', departmentName: string, academicYear: string) => Promise<void>;
  onImport: (filePath: string) => Promise<{ success: boolean; summary?: ImportSummary; error?: string }>;
  departmentName: string;
  academicYear: string;
}

const ExportImportModal: React.FC<ExportImportModalProps> = ({
  open,
  onClose,
  onExport,
  onImport,
  departmentName,
  academicYear
}) => {
  const [activeTab, setActiveTab] = useState<'export' | 'import'>('export');
  const [selectedSemester, setSelectedSemester] = useState<Semester | 'all'>('all');
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResult, setImportResult] = useState<{
    success: boolean;
    summary?: ImportSummary;
    error?: string;
  } | null>(null);

  const handleClose = () => {
    if (!isProcessing) {
      setImportResult(null);
      onClose();
    }
  };

  const handleExport = async () => {
    if (!departmentName.trim()) {
      alert('Please enter a department name before exporting.');
      return;
    }
    if (!academicYear.trim()) {
      alert('Please enter an academic year before exporting.');
      return;
    }

    setIsProcessing(true);
    try {
      await onExport(selectedSemester, departmentName, academicYear);
    } catch (error) {
      console.error('Export error:', error);
      alert('Failed to export data. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImport = async () => {
    setIsProcessing(true);
    setImportResult(null);

    try {
      // Show file open dialog
      const result = await window.electronAPI.dialog.showOpenDialog({
        title: 'Import Timetable Data',
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if ((result as any).canceled || !(result as any).filePaths || (result as any).filePaths.length === 0) {
        setIsProcessing(false);
        return;
      }

      const filePath = (result as any).filePaths[0];
      const importResult = await onImport(filePath);
      setImportResult(importResult);
    } catch (error) {
      console.error('Import error:', error);
      setImportResult({
        success: false,
        error: 'Failed to import data. Please check the file and try again.'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <AccessibleDialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle className="text-lg font-semibold pb-2">
        Export / Import Timetable Data
      </DialogTitle>

      <DialogContent className="pt-2">
        {/* Tab Selection */}
        <Box className="mb-4">
          <Box className="flex border-b border-gray-200 dark:border-gray-700">
            <Button
              variant={activeTab === 'export' ? 'contained' : 'text'}
              onClick={() => setActiveTab('export')}
              startIcon={<FileDownloadIcon />}
              className="mr-2"
              size="small"
            >
              Export
            </Button>
            <Button
              variant={activeTab === 'import' ? 'contained' : 'text'}
              onClick={() => setActiveTab('import')}
              startIcon={<FileUploadIcon />}
              size="small"
            >
              Import
            </Button>
          </Box>
        </Box>

        {/* Export Tab */}
        {activeTab === 'export' && (
          <Box>
            <Typography variant="body2" className="mb-4 text-gray-600 dark:text-gray-400">
              Export your timetable data including courses, lecturers, and sessions for backup or sharing.
            </Typography>

            {/* Department and Academic Year Info */}
            <Box className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded">
              <Typography variant="body2" className="font-medium mb-1">Export Information:</Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Department: {departmentName || 'Not specified'}
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Academic Year: {academicYear || 'Not specified'}
              </Typography>
            </Box>

            {/* Semester Selection */}
            <FormControl fullWidth className="mb-4">
              <InputLabel>Semester to Export</InputLabel>
              <Select
                value={selectedSemester}
                onChange={(e) => setSelectedSemester(e.target.value as Semester | 'all')}
                label="Semester to Export"
              >
                <MenuItem value="all">All Semesters (Fall, Spring, Summer)</MenuItem>
                <MenuItem value="Fall">Fall Semester Only</MenuItem>
                <MenuItem value="Spring">Spring Semester Only</MenuItem>
                <MenuItem value="Summer">Summer Semester Only</MenuItem>
              </Select>
            </FormControl>

            {/* Export Info */}
            <Alert severity="info" className="mb-4">
              <Typography variant="body2">
                The export will include all courses, lecturers, and timetable sessions for the selected semester(s).
                The file will be saved in JSON format for easy import later.
              </Typography>
            </Alert>
          </Box>
        )}

        {/* Import Tab */}
        {activeTab === 'import' && (
          <Box>
            <Typography variant="body2" className="mb-4 text-gray-600 dark:text-gray-400">
              Import timetable data from a previously exported JSON file.
            </Typography>

            {/* Import Warnings */}
            <Alert severity="warning" className="mb-4">
              <Typography variant="body2" className="font-medium mb-1">Important:</Typography>
              <ul className="list-disc pl-4 text-sm">
                <li>Courses and lecturers will be added to existing data (no duplicates)</li>
                <li>Existing timetable sessions for the target semester will be cleared</li>
                <li>This action cannot be undone</li>
              </ul>
            </Alert>

            {/* Import Result */}
            {importResult && (
              <Box className="mb-4">
                {importResult.success ? (
                  <Alert severity="success" icon={<CheckCircleIcon />}>
                    <Typography variant="body2" className="font-medium mb-2">Import Successful!</Typography>
                    {importResult.summary && (
                      <Box>
                        <Typography variant="body2">Target Semester: {importResult.summary.targetSemester}</Typography>
                        <Typography variant="body2">Courses Added: {importResult.summary.coursesAdded}</Typography>
                        <Typography variant="body2">Lecturers Added: {importResult.summary.lecturersAdded}</Typography>
                        <Typography variant="body2">Sessions Imported: {importResult.summary.sessionsImported}</Typography>
                        <Typography variant="body2">Sections Imported: {importResult.summary.sectionsImported}</Typography>
                      </Box>
                    )}
                  </Alert>
                ) : (
                  <Alert severity="error" icon={<ErrorIcon />}>
                    <Typography variant="body2" className="font-medium mb-1">Import Failed</Typography>
                    <Typography variant="body2">{importResult.error}</Typography>
                  </Alert>
                )}
              </Box>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions className="p-4 border-t border-gray-200 dark:border-gray-700">
        <Button onClick={handleClose} disabled={isProcessing}>
          {importResult?.success ? 'Close' : 'Cancel'}
        </Button>

        {activeTab === 'export' ? (
          <Button
            onClick={handleExport}
            variant="contained"
            startIcon={isProcessing ? <CircularProgress size={16} /> : <FileDownloadIcon />}
            disabled={isProcessing}
          >
            {isProcessing ? 'Exporting...' : 'Export Data'}
          </Button>
        ) : (
          <Button
            onClick={handleImport}
            variant="contained"
            startIcon={isProcessing ? <CircularProgress size={16} /> : <FileUploadIcon />}
            disabled={isProcessing}
          >
            {isProcessing ? 'Importing...' : 'Select File to Import'}
          </Button>
        )}
      </DialogActions>
    </AccessibleDialog>
  );
};

export default ExportImportModal;
