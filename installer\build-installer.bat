@echo off
echo Building QU Scheduler Installer...
echo.

REM Check if NSIS is installed
where makensis >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: NSIS is not installed or not in PATH
    echo Please install NSIS from https://nsis.sourceforge.io/
    echo and add it to your system PATH
    pause
    exit /b 1
)

REM Check if application is built
if not exist "out\QU Scheduler-win32-x64\qu-scheduler.exe" (
    echo ERROR: Application not found. Please run 'npm run package' first.
    pause
    exit /b 1
)

REM Convert SVG images to BMP (requires ImageMagick or manual conversion)
echo Converting installer graphics...
if exist "assets\icons\header.svg" (
    echo Please convert assets/icons/header.svg to assets/icons/header.bmp ^(150x57^)
    echo Please convert assets/icons/welcome.svg to assets/icons/welcome.bmp ^(164x314^)
    echo You can use online converters or ImageMagick for this conversion.
    echo.
)

REM Build the installer
echo Building NSIS installer...
makensis installer\qu-scheduler-installer.nsi

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: QU-Scheduler-Setup.exe created successfully!
    echo Location: installer\QU-Scheduler-Setup.exe
    echo.
    echo Installer features:
    echo - Custom installation location
    echo - Desktop shortcut option
    echo - Start menu shortcuts option
    echo - Quick launch shortcut option
    echo - File associations option
    echo - Professional uninstaller
    echo.
) else (
    echo.
    echo ERROR: Failed to build installer
    echo Check the NSIS script for errors
)

pause