/* Offline-Ready Arabic Font Configuration for QU Scheduler */

/* Use system fonts that support Arabic text for complete offline capability */
.arabic-text {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
  direction: rtl;
  text-align: right;
  font-feature-settings: 'liga' 1, 'kern' 1;
}

/* Arabic text styling for left-to-right contexts (mixed content) */
.arabic-text-ltr {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1;
}

/* Optimize text rendering for all fonts */
.text-optimize {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Font weight classes for consistent styling */
.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

/* Ensure proper Arabic text display in all contexts */
[lang="ar"], .arabic {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
  direction: rtl;
}

/* Mixed content (Arabic + English) styling */
.mixed-content {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
}