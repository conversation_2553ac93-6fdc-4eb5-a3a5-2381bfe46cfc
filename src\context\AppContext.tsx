import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Course, Lecturer, Section, Session, Semester } from '../types/models';
import { toast } from 'react-toastify';
import {
  prepareExportData,
  generateExportFilename,
  validateImportData,
  processImportData,
  ImportSummary
} from '../utils/dataExportImport';

// Utility function to check if a course is a postgraduate course
export const isPostgraduateCourse = (courseCode: string): boolean => {
  const numericPart = courseCode.match(/\d+/)?.[0] || '';
  const firstDigit = numericPart.length > 0 ? parseInt(numericPart[0]) : -1;
  return firstDigit >= 5; // 5xx, 6xx, 7xx, 8xx, 9xx are postgraduate courses
};

// Define the shape of our context
interface AppContextType {
  // Current semester
  currentSemester: Semester;
  setCurrentSemester: (semester: Semester) => void;

  // Courses
  courses: Record<Semester, Course[]>;
  addCourse: (course: Omit<Course, 'id'>, semester: Semester) => void;
  updateCourse: (course: Course, semester: Semester) => void;
  deleteCourse: (courseId: string, semester: Semester) => void;
  importCourses: (courses: Omit<Course, 'id'>[], semester: Semester) => void;
  reorderCourses: (courseIds: string[], semester: Semester) => void;

  // Sections
  sections: Record<Semester, Section[]>;
  addSection: (section: Omit<Section, 'id'>, semester: Semester) => void;
  updateSection: (section: Section, semester: Semester) => void;
  deleteSection: (sectionId: string, semester: Semester) => void;

  // Lecturers (shared across semesters)
  lecturers: Lecturer[];
  addLecturer: (lecturer: Omit<Lecturer, 'id'>) => void;
  updateLecturer: (lecturer: Lecturer) => void;
  deleteLecturer: (lecturerId: string) => void;
  importLecturers: (lecturers: Omit<Lecturer, 'id'>[]) => void;
  reorderLecturers: (lecturerIds: string[]) => void;

  // Sessions
  sessions: Record<Semester, Session[]>;
  setSessions: React.Dispatch<React.SetStateAction<Record<Semester, Session[]>>>;
  addSession: (session: Omit<Session, 'id'> & { id?: string }, semester: Semester) => void;
  updateSession: (session: Session, semester: Semester) => void;
  deleteSession: (sessionId: string, semester: Semester) => void;

  // UI state
  isCourseModalOpen: boolean;
  setIsCourseModalOpen: (isOpen: boolean) => void;
  isCourseImportModalOpen: boolean;
  setIsCourseImportModalOpen: (isOpen: boolean) => void;
  isLecturerModalOpen: boolean;
  setIsLecturerModalOpen: (isOpen: boolean) => void;
  isLecturerImportModalOpen: boolean;
  setIsLecturerImportModalOpen: (isOpen: boolean) => void;
  currentCourse: Course | null;
  setCurrentCourse: (course: Course | null) => void;
  currentLecturer: Lecturer | null;
  setCurrentLecturer: (lecturer: Lecturer | null) => void;

  // Filters
  courseFilter: string;
  setCourseFilter: (filter: string) => void;
  lecturerFilter: string;
  setLecturerFilter: (filter: string) => void;

  // View
  currentView: 'week' | 'day';
  setCurrentView: (view: 'week' | 'day') => void;
  viewMode: 'week' | 'regular' | 'long';
  setViewMode: (view: 'week' | 'regular' | 'long') => void;
  activeTab: number; // 0 for morning, 1 for evening, 2 for all day (default)
  setActiveTab: (tab: number) => void;

  // PG Canvas
  showPgCanvas: boolean;
  setShowPgCanvas: (show: boolean) => void;

  // Data loading state
  isDataLoaded: boolean;

  // Reset current view
  resetCurrentView: () => void;

  // Export/Import functions
  exportTimetableData: (semester: Semester | 'all', departmentName: string, academicYear: string) => Promise<void>;
  importTimetableData: (filePath: string) => Promise<{ success: boolean; summary?: ImportSummary; error?: string }>;
}

// Create the context with a default value
const AppContext = createContext<AppContextType | undefined>(undefined);

// Create a provider component
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  // Current semester state - initialize from store
  const [currentSemester, setCurrentSemester] = useState<Semester>('Fall');

  // Courses state (per semester) - initialize with empty arrays
  const [courses, setCourses] = useState<Record<Semester, Course[]>>({
    Fall: [],
    Spring: [],
    Summer: []
  });

  // Sections state (per semester) - initialize with empty arrays
  const [sections, setSections] = useState<Record<Semester, Section[]>>({
    Fall: [],
    Spring: [],
    Summer: []
  });

  // Lecturers state (shared across semesters) - initialize with empty array
  const [lecturers, setLecturers] = useState<Lecturer[]>([]);

  // Sessions state (per semester) - initialize with empty arrays
  const [sessions, setSessions] = useState<Record<Semester, Session[]>>({
    Fall: [],
    Spring: [],
    Summer: []
  });

  // Loading state to track data initialization
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // UI state
  const [isCourseModalOpen, setIsCourseModalOpen] = useState(false);
  const [isCourseImportModalOpen, setIsCourseImportModalOpen] = useState(false);
  const [isLecturerModalOpen, setIsLecturerModalOpen] = useState(false);
  const [isLecturerImportModalOpen, setIsLecturerImportModalOpen] = useState(false);
  const [currentCourse, setCurrentCourse] = useState<Course | null>(null);
  const [currentLecturer, setCurrentLecturer] = useState<Lecturer | null>(null);

  // Filters
  const [courseFilter, setCourseFilter] = useState('all');
  const [lecturerFilter, setLecturerFilter] = useState('all');

  // View
  const [currentView, setCurrentView] = useState<'week' | 'day'>('week');
  const [viewMode, setViewMode] = useState<'week' | 'regular' | 'long'>('week');
  const [activeTab, setActiveTab] = useState<number>(2); // 0 for morning, 1 for evening, 2 for all day (default)

  // PG Canvas view
  const [showPgCanvas, setShowPgCanvas] = useState<boolean>(false);

  // Ref to track whether we need to recalculate section hours
  const needsRecalculation = useRef<boolean>(false);

  // Ref to track recalculation timeout to prevent multiple rapid recalculations
  const recalculationTimeout = useRef<NodeJS.Timeout | null>(null);

  // Save UI states to store when they change
  useEffect(() => {

    const saveUIState = async () => {
      try {
        // Get the current UI state
        const currentUIState = await window.electronAPI.store.get('uiState') as any;
        // Create a new UI state object with only AppContext-managed properties
        // Explicitly preserve App.tsx-managed properties
        const newUIState = {
          ...currentUIState,
          // Only update AppContext-managed properties
          courseFilter,
          lecturerFilter,
          currentView,
          viewMode,
          activeTab,
          showPgCanvas
          // Do NOT touch showCoursesPanel, showLecturersPanel, or darkMode
          // These are managed by App.tsx
        };
        // Set the new UI state
        await window.electronAPI.store.set('uiState', newUIState);
      } catch (error) {
        console.error('Error saving UI state:', error);
      }
    };

    saveUIState();
  }, [courseFilter, lecturerFilter, currentView, viewMode, activeTab, showPgCanvas]);

  // Function to recalculate all section hours for the current semester
  const recalculateAllSectionHours = () => {
    // Only proceed if data is loaded and we have the necessary data
    if (!isDataLoaded ||
        !courses[currentSemester] ||
        !sections[currentSemester] ||
        !sessions[currentSemester]) {
      return;
    }

    setSections(prev => {
      const updated = {...prev};

      // Reset all scheduled hours first
      updated[currentSemester] = prev[currentSemester].map(section => ({
        ...section,
        scheduledHours: 0
      }));

      // Group sessions by section ID
      const sessionsBySection: Record<string, Session[]> = {};
      sessions[currentSemester].forEach(session => {
        if (!sessionsBySection[session.sectionId]) {
          sessionsBySection[session.sectionId] = [];
        }
        sessionsBySection[session.sectionId].push(session);
      });

      // Process each section
      updated[currentSemester].forEach((section, sectionIndex) => {
        const sectionSessions = sessionsBySection[section.id] || [];
        if (sectionSessions.length === 0) return;

        // Find the course for this section
        const course = courses[currentSemester].find(c => c.id === section.courseId);
        if (!course) return;

        // No need to check if this is a postgraduate course anymore
        // We use the same calculation for all courses

        // Reset the section's scheduled hours
        let totalScheduledHours = 0;

        // Group sessions by day to handle calculation consistently
        const sessionsByDay: Record<string, Session[]> = {};
        sectionSessions.forEach(session => {
          if (!sessionsByDay[session.day]) {
            sessionsByDay[session.day] = [];
          }
          sessionsByDay[session.day].push(session);
        });

        // For each day, calculate the scheduled hours
        Object.entries(sessionsByDay).forEach(([day, daySessions]) => {
          const isLongDay = day === 'Monday' || day === 'Wednesday';

          // Use a standardized calculation logic for all courses
          daySessions.forEach(session => {
            // Calculate number of periods
            const numberOfPeriods = session.endPeriod - session.startPeriod + 1;

            // Calculate duration based on day type
            let duration = numberOfPeriods;
            if (isLongDay) {
              duration = numberOfPeriods * 1.5;
            }

            // For auto-generated sessions with 3 credit hours, use fixed values
            if (session.isAutoGenerated && course.contactHours === 3) {
              // For 3 credit hour courses on regular days (Sun, Tue, Thu), each session is 1 hour
              if (!isLongDay) {
                duration = 1;
              }
              // For 3 credit hour courses on long days (Mon, Wed), each session is 1.5 hours
              else {
                duration = 1.5;
              }
            }

            // Round to avoid floating-point precision issues
            duration = Math.round(duration * 1000) / 1000;

            totalScheduledHours += duration;
          });
        });

        // Log the calculation for debugging if this is the problematic section
        if (updated[currentSemester][sectionIndex].id === 'ef38b58f-e0b0-40dd-a3bc-5fd7ed57d97e') {
          console.log(`Recalculating problematic section ${updated[currentSemester][sectionIndex].id}:`);
          console.log(`- Sessions by day:`, sessionsByDay);
          console.log(`- Total scheduled hours: ${totalScheduledHours}`);
        }

        // Update the section with the new scheduled hours
        updated[currentSemester][sectionIndex].scheduledHours = totalScheduledHours;
      });

      // Save to store
      window.electronAPI.store.set('sections', updated);
      return updated;
    });
  };

  // Load data from store on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load current semester
        const storedSemester = await window.electronAPI.store.get('currentSemester') as Semester;
        if (storedSemester) {
          setCurrentSemester(storedSemester);
        }

        // Load courses
        const storedCourses = await window.electronAPI.store.get('courses') as Record<Semester, Course[]>;
        if (storedCourses) {
          setCourses(storedCourses);
        }

        // Load sections
        const storedSections = await window.electronAPI.store.get('sections') as Record<Semester, Section[]>;
        if (storedSections) {
          setSections(storedSections);
        }

        // Load lecturers
        const storedLecturers = await window.electronAPI.store.get('lecturers') as Lecturer[];
        if (storedLecturers) {
          setLecturers(storedLecturers);
        }

        // Load sessions
        const storedSessions = await window.electronAPI.store.get('sessions') as Record<Semester, Session[]>;
        if (storedSessions) {
          setSessions(storedSessions);
        }

        // Load UI state
        const uiState = await window.electronAPI.store.get('uiState') as any;
        if (uiState) {
          // Load filter states
          if (uiState.courseFilter) {
            setCourseFilter(uiState.courseFilter);
          }
          if (uiState.lecturerFilter) {
            setLecturerFilter(uiState.lecturerFilter);
          }
          if (uiState.currentView) {
            setCurrentView(uiState.currentView);
          }
          if (uiState.viewMode) {
            setViewMode(uiState.viewMode);
          }
          if (uiState.activeTab !== undefined) {
            setActiveTab(uiState.activeTab);
          }
          if (uiState.showPgCanvas !== undefined) {
            setShowPgCanvas(uiState.showPgCanvas);
          }
        }

        // Mark data as loaded
        setIsDataLoaded(true);

        // Trigger recalculation after all data is loaded and state is set
        // Use a small delay to ensure all state updates have been processed
        setTimeout(() => {
          recalculateAllSectionHours();
        }, 100);
      } catch (error) {
        console.error('Error loading data from store:', error);
        // Even if there's an error, mark as loaded to prevent infinite loading
        setIsDataLoaded(true);
      }
    };

    loadData();
  }, []);

  // Mark for recalculation when sessions or currentSemester changes (only after initial load)
  useEffect(() => {
    if (isDataLoaded &&
        courses[currentSemester]?.length > 0 &&
        sections[currentSemester]?.length > 0 &&
        sessions[currentSemester]?.length > 0) {
      needsRecalculation.current = true;
    }
  }, [sessions, currentSemester, courses, isDataLoaded]);

  // Perform the actual recalculation when needed (only after initial load)
  useEffect(() => {
    if (isDataLoaded &&
        needsRecalculation.current &&
        courses[currentSemester]?.length > 0 &&
        sections[currentSemester]?.length > 0 &&
        sessions[currentSemester]?.length > 0) {
      needsRecalculation.current = false;

      // Clear any existing timeout
      if (recalculationTimeout.current) {
        clearTimeout(recalculationTimeout.current);
      }

      // Debounce the recalculation to prevent multiple rapid calls
      recalculationTimeout.current = setTimeout(() => {
        recalculateAllSectionHours();
      }, 50);
    }
  }, [currentSemester, courses, sections, sessions, isDataLoaded]);

  // Add event listener for recalculating section hours
  useEffect(() => {
    const handleRecalculateSectionHours = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { sectionId, semester } = customEvent.detail as { sectionId: string, semester: Semester };

      // Find the section
      const sectionToUpdate = sections[semester]?.find((s: Section) => s.id === sectionId);
      if (!sectionToUpdate) return;

      // Get all sessions for this section
      const sectionSessions = sessions[semester]?.filter((s: Session) => s.sectionId === sectionId) || [];

      // Find the course for this section
      const course = courses[semester]?.find((c: Course) => c.id === sectionToUpdate.courseId);
      if (!course) return;

      // No need to check if this is a postgraduate course anymore
      // We use the same calculation for all courses

      // Reset the section's scheduled hours
      let totalScheduledHours = 0;

      // Group sessions by day to handle calculation consistently
      const sessionsByDay: Record<string, Session[]> = {};
      sectionSessions.forEach((session: Session) => {
        if (!sessionsByDay[session.day]) {
          sessionsByDay[session.day] = [];
        }
        sessionsByDay[session.day].push(session);
      });

      // For each day, calculate the scheduled hours
      Object.entries(sessionsByDay).forEach(([day, daySessions]) => {
        const isLongDay = day === 'Monday' || day === 'Wednesday';

        // Use a standardized calculation logic for all courses
        daySessions.forEach((session: Session) => {
          // Calculate number of periods
          const numberOfPeriods = session.endPeriod - session.startPeriod + 1;

          // Calculate duration based on day type
          let duration = numberOfPeriods;
          if (isLongDay) {
            duration = numberOfPeriods * 1.5;
          }

          // For auto-generated sessions with 3 credit hours, use fixed values
          if (session.isAutoGenerated && course.contactHours === 3) {
            // For 3 credit hour courses on regular days (Sun, Tue, Thu), each session is 1 hour
            if (!isLongDay) {
              duration = 1;
            }
            // For 3 credit hour courses on long days (Mon, Wed), each session is 1.5 hours
            else {
              duration = 1.5;
            }
          }

          // Round to avoid floating-point precision issues
          duration = Math.round(duration * 1000) / 1000;

          totalScheduledHours += duration;
        });
      });

      // Log for debugging
      if (sectionId === 'ef38b58f-e0b0-40dd-a3bc-5fd7ed57d97e') {
        console.log('Recalculating problematic section from event handler');
        console.log('Section ID:', sectionId);
        console.log('Sessions:', sectionSessions);
        console.log('Sessions by day:', sessionsByDay);
        console.log('Calculated hours:', totalScheduledHours);
      }

      // Update the section with the new scheduled hours
      const updatedSection = {
        ...sectionToUpdate,
        scheduledHours: totalScheduledHours
      };

      // Update the section
      setSections(prev => {
        const updated = {...prev};
        const sectionIndex = updated[semester].findIndex((s: Section) => s.id === sectionId);
        if (sectionIndex >= 0) {
          updated[semester][sectionIndex] = updatedSection;
          // Save to store
          window.electronAPI.store.set('sections', updated);
        }
        return updated;
      });
    };

    // Add event listener
    window.addEventListener('recalculateSectionHours', handleRecalculateSectionHours);

    // Remove event listener on cleanup
    return () => {
      window.removeEventListener('recalculateSectionHours', handleRecalculateSectionHours);
    };
  }, [sections, sessions, courses]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (recalculationTimeout.current) {
        clearTimeout(recalculationTimeout.current);
      }
    };
  }, []);

  // Course functions
  const addCourse = (course: Omit<Course, 'id'>, semester: Semester) => {
    const newCourse: Course = {
      ...course,
      id: uuidv4()
    };

    setCourses(prev => {
      const updatedCourses = {
        ...prev,
        [semester]: [...prev[semester], newCourse]
      };

      // Save to store
      window.electronAPI.store.set('courses', updatedCourses);

      return updatedCourses;
    });
  };

  const updateCourse = (course: Course, semester: Semester) => {
    try {
      setCourses(prev => {
        // Find the existing course to compare section counts
        const existingCourse = prev[semester].find(c => c.id === course.id);
        const updatedCourses = {
          ...prev,
          [semester]: prev[semester].map(c => c.id === course.id ? course : c)
        };

        // Save to store with error handling
        try {
          window.electronAPI.store.set('courses', updatedCourses);
        } catch (storeError) {
          console.error('Error saving courses to store:', storeError);
        }

        if (existingCourse) {
          // Handle male section count changes
          if (course.maleSectionsCount !== existingCourse.maleSectionsCount) {
            setSections(prevSections => {
              const currentMaleSections = prevSections[semester].filter(
                s => s.courseId === course.id && s.gender === 'M'
              );

              // If we need to add sections
              if (course.maleSectionsCount > currentMaleSections.length) {
                const newSections = [];
                // Add new male sections
                for (let i = currentMaleSections.length + 1; i <= course.maleSectionsCount; i++) {
                  newSections.push({
                    id: uuidv4(),
                    courseId: course.id,
                    sectionNumber: i,
                    gender: 'M',
                    scheduledHours: 0,
                    totalHours: course.contactHours
                  });
                }

                return {
                  ...prevSections,
                  [semester]: [...prevSections[semester], ...newSections]
                };
              }
              // If we need to remove sections
              else if (course.maleSectionsCount < currentMaleSections.length) {
                // Sort sections by section number in descending order
                const sortedSections = [...currentMaleSections].sort((a, b) => b.sectionNumber - a.sectionNumber);
                // Get the sections to remove (the ones with highest section numbers)
                const sectionsToRemove = sortedSections.slice(0, currentMaleSections.length - course.maleSectionsCount);
                const sectionIdsToRemove = sectionsToRemove.map(s => s.id);

                // Remove the sections
                return {
                  ...prevSections,
                  [semester]: prevSections[semester].filter(s => !sectionIdsToRemove.includes(s.id))
                };
              }

              return prevSections;
            });
          }

          // Handle female section count changes
          if (course.femaleSectionsCount !== existingCourse.femaleSectionsCount) {
            setSections(prevSections => {
              const currentFemaleSections = prevSections[semester].filter(
                s => s.courseId === course.id && s.gender === 'F'
              );

              // If we need to add sections
              if (course.femaleSectionsCount > currentFemaleSections.length) {
                const newSections = [];
                // Add new female sections
                for (let i = currentFemaleSections.length + 1; i <= course.femaleSectionsCount; i++) {
                  newSections.push({
                    id: uuidv4(),
                    courseId: course.id,
                    sectionNumber: i + 50,
                    gender: 'F',
                    scheduledHours: 0,
                    totalHours: course.contactHours
                  });
                }

                return {
                  ...prevSections,
                  [semester]: [...prevSections[semester], ...newSections]
                };
              }
              // If we need to remove sections
              else if (course.femaleSectionsCount < currentFemaleSections.length) {
                // Sort sections by section number in descending order
                const sortedSections = [...currentFemaleSections].sort((a, b) => b.sectionNumber - a.sectionNumber);
                // Get the sections to remove (the ones with highest section numbers)
                const sectionsToRemove = sortedSections.slice(0, currentFemaleSections.length - course.femaleSectionsCount);
                const sectionIdsToRemove = sectionsToRemove.map(s => s.id);

                // Remove the sections
                return {
                  ...prevSections,
                  [semester]: prevSections[semester].filter(s => !sectionIdsToRemove.includes(s.id))
                };
              }

              return prevSections;
            });
          }

          // Update contact hours for existing sections if they've changed
          if (course.contactHours !== existingCourse.contactHours) {
            setSections(prevSections => ({
              ...prevSections,
              [semester]: prevSections[semester].map(s =>
                s.courseId === course.id
                  ? { ...s, totalHours: course.contactHours }
                  : s
              )
            }));
          }
        }

        return updatedCourses;
      });
    } catch (error) {
      console.error('Error updating course:', error);
      // Don't update state if there's an error to prevent crashes
    }
  };

  const deleteCourse = (courseId: string, semester: Semester) => {
    setCourses(prev => {
      const updatedCourses = {
        ...prev,
        [semester]: prev[semester].filter(c => c.id !== courseId)
      };

      // Save to store
      window.electronAPI.store.set('courses', updatedCourses);

      return updatedCourses;
    });

    // Also delete associated sections
    setSections(prev => {
      const updatedSections = {
        ...prev,
        [semester]: prev[semester].filter(s => s.courseId !== courseId)
      };

      // Save to store
      window.electronAPI.store.set('sections', updatedSections);

      return updatedSections;
    });

    // Also delete associated sessions
    setSessions(prev => {
      const sectionIds = sections[semester]
        .filter(section => section.courseId === courseId)
        .map(section => section.id);

      const updatedSessions = {
        ...prev,
        [semester]: prev[semester].filter(s => !sectionIds.includes(s.sectionId))
      };

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      return updatedSessions;
    });
  };

  const reorderCourses = (courseIds: string[], semester: Semester) => {
    setCourses(prev => {
      // Create a map of courses by ID for quick lookup
      const coursesMap = new Map(prev[semester].map(course => [course.id, course]));

      // Create a new array with the courses in the specified order
      // First, add all courses that are in the courseIds array in the specified order
      const orderedCourses = courseIds
        .filter(id => coursesMap.has(id))
        .map(id => {
          // We've already filtered to ensure the ID exists in the map
          const course = coursesMap.get(id);
          // This should never happen due to the filter, but TypeScript doesn't know that
          if (!course) {
            throw new Error(`Course with ID ${id} not found in map despite filter check`);
          }
          return course;
        });

      // Then add any courses that weren't in the courseIds array
      // This ensures we don't lose any courses if the courseIds array is incomplete
      const remainingCourses = prev[semester].filter(course => !courseIds.includes(course.id));

      const updatedCourses = {
        ...prev,
        [semester]: [...orderedCourses, ...remainingCourses]
      };

      // Save to store
      window.electronAPI.store.set('courses', updatedCourses);

      return updatedCourses;
    });
  };

  const importCourses = (newCourses: Omit<Course, 'id'>[], semester: Semester) => {
    // First, normalize course codes by removing spaces
    const normalizedCourses = newCourses.map(course => ({
      ...course,
      courseCode: course.courseCode.replace(/\s+/g, '')
    }));

    // Add unique IDs to courses
    const coursesWithIds = normalizedCourses.map(course => ({
      ...course,
      id: uuidv4()
    }));

    setCourses(prev => {
      // Check for duplicate course codes before adding
      const existingCourseCodes = new Set(prev[semester].map(c => c.courseCode.toUpperCase()));
      const uniqueCoursesWithIds = coursesWithIds.filter(course =>
        !existingCourseCodes.has(course.courseCode.toUpperCase())
      );

      const updatedCourses = {
        ...prev,
        [semester]: [...prev[semester], ...uniqueCoursesWithIds]
      };

      // Save to store
      window.electronAPI.store.set('courses', updatedCourses);

      // Create sections for each imported course
      setSections(prevSections => {
        const newSections: Section[] = [];

        // Get existing courses by code for mapping
        const existingCoursesByCode: Record<string, string> = {};
        prev[semester].forEach(course => {
          existingCoursesByCode[course.courseCode.toUpperCase()] = course.id;
        });

        // Track existing section numbers for each course to avoid duplicates
        const existingSectionsByCourse: Record<string, {
          male: Set<number>,
          female: Set<number>
        }> = {};

        // Initialize tracking for each course and populate with existing sections
        uniqueCoursesWithIds.forEach(course => {
          existingSectionsByCourse[course.id] = {
            male: new Set<number>(),
            female: new Set<number>()
          };
        });

        // Track ALL existing sections in the current semester for ALL courses
        prevSections[semester].forEach(section => {
          const course = prev[semester].find(c => c.id === section.courseId);
          if (course) {
            // For each new course we're importing, check if it has the same course code
            uniqueCoursesWithIds.forEach(newCourse => {
              if (newCourse.courseCode.toUpperCase() === course.courseCode.toUpperCase()) {
                // Track this section number as existing for the new course
                if (section.gender === 'M') {
                  existingSectionsByCourse[newCourse.id].male.add(section.sectionNumber);
                } else {
                  existingSectionsByCourse[newCourse.id].female.add(section.sectionNumber);
                }
              }
            });
          }
        });

        // Also track section numbers globally across all courses with the same code
        const existingMaleSectionsByCode: Record<string, Set<number>> = {};
        const existingFemaleSectionsByCode: Record<string, Set<number>> = {};

        // Initialize tracking for each course code
        uniqueCoursesWithIds.forEach(course => {
          const upperCaseCode = course.courseCode.toUpperCase();
          if (!existingMaleSectionsByCode[upperCaseCode]) {
            existingMaleSectionsByCode[upperCaseCode] = new Set<number>();
          }
          if (!existingFemaleSectionsByCode[upperCaseCode]) {
            existingFemaleSectionsByCode[upperCaseCode] = new Set<number>();
          }
        });

        // Populate with ALL existing sections across ALL courses
        prevSections[semester].forEach(section => {
          const course = prev[semester].find(c => c.id === section.courseId);
          if (course) {
            const upperCaseCode = course.courseCode.toUpperCase();
            if (section.gender === 'M') {
              existingMaleSectionsByCode[upperCaseCode]?.add(section.sectionNumber);
            } else {
              existingFemaleSectionsByCode[upperCaseCode]?.add(section.sectionNumber);
            }
          }
        });

        // Create sections for each course
        uniqueCoursesWithIds.forEach(course => {
          const courseCode = course.courseCode.toUpperCase();

          // Make sure the tracking sets exist
          if (!existingMaleSectionsByCode[courseCode]) {
            existingMaleSectionsByCode[courseCode] = new Set<number>();
          }
          if (!existingFemaleSectionsByCode[courseCode]) {
            existingFemaleSectionsByCode[courseCode] = new Set<number>();
          }

          // Find the next available section numbers for this course code
          let nextAvailableMaleSection = 1;
          let nextAvailableFemaleSection = 51;

          // Find the highest male section number currently in use for this course code
          if (existingMaleSectionsByCode[courseCode].size > 0) {
            nextAvailableMaleSection = Math.max(...Array.from(existingMaleSectionsByCode[courseCode])) + 1;
          }

          // Find the highest female section number currently in use for this course code
          if (existingFemaleSectionsByCode[courseCode].size > 0) {
            // Ensure female sections start at 51 or higher
            nextAvailableFemaleSection = Math.max(
              Math.max(...Array.from(existingFemaleSectionsByCode[courseCode])) + 1,
              51
            );
          }

          // Create male sections
          for (let i = 0; i < course.maleSectionsCount; i++) {
            const sectionNumber = nextAvailableMaleSection + i;

            // Mark this section number as used
            existingSectionsByCourse[course.id].male.add(sectionNumber);
            existingMaleSectionsByCode[courseCode].add(sectionNumber);

            // Create the new section
            newSections.push({
              id: uuidv4(),
              courseId: course.id,
              sectionNumber: sectionNumber,
              gender: 'M',
              scheduledHours: 0,
              totalHours: course.contactHours,
              viewTypes: ['week', 'day'] // Assign to both views by default
            });
          }

          // Create female sections
          for (let i = 0; i < course.femaleSectionsCount; i++) {
            // Make sure female sections start at 51 or higher
            const sectionNumber = Math.max(nextAvailableFemaleSection + i, 51);

            // Mark this section number as used
            existingSectionsByCourse[course.id].female.add(sectionNumber);
            existingFemaleSectionsByCode[courseCode].add(sectionNumber);

            // Create the new section
            newSections.push({
              id: uuidv4(),
              courseId: course.id,
              sectionNumber: sectionNumber,
              gender: 'F',
              scheduledHours: 0,
              totalHours: course.contactHours,
              viewTypes: ['week', 'day'] // Assign to both views by default
            });
          }
        });

        // Combine existing and new sections
        const combinedSections = [...prevSections[semester], ...newSections];

        // Post-import validation: Remove duplicate sections
        // A section is considered a duplicate if it has the same courseId, gender, and sectionNumber
        const uniqueSections: Section[] = [];
        const seenSections = new Map<string, Section>();

        // Create a unique key for each section based on courseId, gender, and sectionNumber
        combinedSections.forEach(section => {
          const sectionKey = `${section.courseId}-${section.gender}-${section.sectionNumber}`;

          // If we haven't seen this section before, add it to our unique sections
          if (!seenSections.has(sectionKey)) {
            seenSections.set(sectionKey, section);
            uniqueSections.push(section);
          }
          // If we have seen this section before, keep the one we've already added
          // This ensures we keep the first occurrence and discard duplicates
        });

        return {
          ...prevSections,
          [semester]: uniqueSections
        };
      });

      return updatedCourses;
    });
  };

  // Section functions
  const addSection = (section: Omit<Section, 'id'>, semester: Semester) => {
    // Find the course to get its code for section number tracking
    let sectionNumber = section.sectionNumber;
    const course = courses[semester].find(c => c.id === section.courseId);

    if (course) {
      // Get all existing sections with the same course code
      const courseCode = course.courseCode.toUpperCase();
      const existingSections = sections[semester].filter(s => {
        const sectionCourse = courses[semester].find(c => c.id === s.courseId);
        return sectionCourse && sectionCourse.courseCode.toUpperCase() === courseCode && s.gender === section.gender;
      });

      // If section number is not specified or already exists, find the next available one
      const existingSectionNumbers = new Set(existingSections.map(s => s.sectionNumber));

      if (!sectionNumber || existingSectionNumbers.has(sectionNumber)) {
        // For male sections, start from 1
        // For female sections, start from 51
        const baseNumber = section.gender === 'M' ? 1 : 51;
        sectionNumber = baseNumber;

        // Find the next available section number
        while (existingSectionNumbers.has(sectionNumber)) {
          sectionNumber++;
        }
      }
    }

    const newSection: Section = {
      ...section,
      id: uuidv4(),
      sectionNumber: sectionNumber,
      viewTypes: ['week', 'day'] // Assign to both views by default
    };

    setSections(prev => {
      const updatedSections = {
        ...prev,
        [semester]: [...prev[semester], newSection]
      };

      // Save to store
      window.electronAPI.store.set('sections', updatedSections);

      return updatedSections;
    });

    // Update the course's section count
    setCourses(prev => {
      const course = prev[semester].find(c => c.id === section.courseId);
      if (course) {
        const updatedCourse = { ...course };
        if (section.gender === 'M') {
          updatedCourse.maleSectionsCount += 1;
        } else {
          updatedCourse.femaleSectionsCount += 1;
        }

        const updatedCourses = {
          ...prev,
          [semester]: prev[semester].map(c => c.id === section.courseId ? updatedCourse : c)
        };

        // Save to store
        window.electronAPI.store.set('courses', updatedCourses);

        return updatedCourses;
      }
      return prev;
    });
  };

  const updateSection = (section: Section, semester: Semester) => {
    setSections(prev => {
      const updatedSections = {
        ...prev,
        [semester]: prev[semester].map(s => s.id === section.id ? section : s)
      };

      // Save to store
      window.electronAPI.store.set('sections', updatedSections);

      return updatedSections;
    });
  };

  const deleteSection = (sectionId: string, semester: Semester) => {
    // Find the section to be deleted to get its courseId and gender
    const sectionToDelete = sections[semester].find(s => s.id === sectionId);

    if (sectionToDelete) {
      // Update the course's section count
      setCourses(prev => {
        const course = prev[semester].find(c => c.id === sectionToDelete.courseId);
        if (course) {
          const updatedCourse = { ...course };
          if (sectionToDelete.gender === 'M') {
            updatedCourse.maleSectionsCount = Math.max(0, updatedCourse.maleSectionsCount - 1);
          } else {
            updatedCourse.femaleSectionsCount = Math.max(0, updatedCourse.femaleSectionsCount - 1);
          }

          const updatedCourses = {
            ...prev,
            [semester]: prev[semester].map(c => c.id === sectionToDelete.courseId ? updatedCourse : c)
          };

          // Save to store
          window.electronAPI.store.set('courses', updatedCourses);

          return updatedCourses;
        }
        return prev;
      });
    }

    setSections(prev => {
      const updatedSections = {
        ...prev,
        [semester]: prev[semester].filter(s => s.id !== sectionId)
      };

      // Save to store
      window.electronAPI.store.set('sections', updatedSections);

      return updatedSections;
    });

    // Also delete associated sessions
    setSessions(prev => {
      const updatedSessions = {
        ...prev,
        [semester]: prev[semester].filter(s => s.sectionId !== sectionId)
      };

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      return updatedSessions;
    });
  };

  // Lecturer functions
  const addLecturer = (lecturer: Omit<Lecturer, 'id'>) => {
    const newLecturer: Lecturer = {
      ...lecturer,
      id: uuidv4()
    };

    setLecturers(prev => {
      const updatedLecturers = [...prev, newLecturer];

      // Save to store
      window.electronAPI.store.set('lecturers', updatedLecturers);

      return updatedLecturers;
    });
  };

  const updateLecturer = (lecturer: Lecturer) => {
    setLecturers(prev => {
      const updatedLecturers = prev.map(l => l.id === lecturer.id ? lecturer : l);

      // Save to store
      window.electronAPI.store.set('lecturers', updatedLecturers);

      return updatedLecturers;
    });
  };

  const deleteLecturer = (lecturerId: string) => {
    setLecturers(prev => {
      const updatedLecturers = prev.filter(l => l.id !== lecturerId);

      // Save to store
      window.electronAPI.store.set('lecturers', updatedLecturers);

      return updatedLecturers;
    });

    // Also update sessions to remove this lecturer
    setSessions(prev => {
      const updatedSessions = { ...prev };

      // For each semester, filter out sessions with this lecturer
      Object.keys(updatedSessions).forEach(sem => {
        const semester = sem as Semester;
        updatedSessions[semester] = updatedSessions[semester].filter(
          session => session.lecturerId !== lecturerId
        );
      });

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      return updatedSessions;
    });
  };

  const importLecturers = (newLecturers: Omit<Lecturer, 'id'>[]) => {
    const lecturersWithIds = newLecturers.map(lecturer => ({
      ...lecturer,
      id: uuidv4()
    }));

    setLecturers(prev => {
      const updatedLecturers = [...prev, ...lecturersWithIds];

      // Save to store
      window.electronAPI.store.set('lecturers', updatedLecturers);

      return updatedLecturers;
    });
  };

  const reorderLecturers = (lecturerIds: string[]) => {
    setLecturers(prev => {
      // Create a map of lecturers by ID for quick lookup
      const lecturersMap = new Map(prev.map(lecturer => [lecturer.id, lecturer]));

      // Create a new array with the lecturers in the specified order
      // First, add all lecturers that are in the lecturerIds array in the specified order
      const orderedLecturers = lecturerIds
        .filter(id => lecturersMap.has(id))
        .map(id => {
          // We've already filtered to ensure the ID exists in the map
          const lecturer = lecturersMap.get(id);
          // This should never happen due to the filter, but TypeScript doesn't know that
          if (!lecturer) {
            throw new Error(`Lecturer with ID ${id} not found in map despite filter check`);
          }
          return lecturer;
        });

      // Then add any lecturers that weren't in the lecturerIds array
      // This ensures we don't lose any lecturers if the lecturerIds array is incomplete
      const remainingLecturers = prev.filter(lecturer => !lecturerIds.includes(lecturer.id));
      const updatedLecturers = [...orderedLecturers, ...remainingLecturers];

      // Save to store
      window.electronAPI.store.set('lecturers', updatedLecturers);

      return updatedLecturers;
    });
  };

  // Session functions
  const addSession = (session: Omit<Session, 'id'> & { id?: string }, semester: Semester) => {
    // If the session already has an ID (for auto-generated sessions), use it
    // Otherwise generate a new UUID
    const newSession: Session = {
      ...session,
      id: session.id || uuidv4()
    };

    setSessions(prev => {
      const updatedSessions = {
        ...prev,
        [semester]: [...prev[semester], newSession]
      };

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      // Update section's scheduled hours
      updateSectionHoursAfterSessionChange(newSession, semester);

      return updatedSessions;
    });
  };

  // Helper function to update section hours after adding a session
  const updateSectionHoursAfterSessionChange = (session: Session, semester: Semester) => {
    // Find the section for this session
    const sectionToUpdate = sections[semester].find(s => s.id === session.sectionId);

    if (sectionToUpdate) {
      // Find the course for this section to get contact hours
      const course = courses[semester].find(c => c.id === sectionToUpdate.courseId);
      if (!course) return;

      // No need to check if this is a postgraduate course anymore
      // We use the same calculation for all courses

      // Use a standardized calculation logic for all courses
      {
        // Calculate number of periods
        const numberOfPeriods = session.endPeriod - session.startPeriod + 1;

        // Calculate duration based on day type
        // Regular days (Su, Tu, Th): 1 hour per period
        // Long days (Mo, We): 1.5 hours per period
        let duration = numberOfPeriods;
        if (session.day === 'Monday' || session.day === 'Wednesday') {
          duration = numberOfPeriods * 1.5;
        }

        // For auto-generated sessions with 3 credit hours, use fixed values
        if (session.isAutoGenerated && course.contactHours === 3) {
          // For 3 credit hour courses on regular days (Sun, Tue, Thu), each session is 1 hour
          if (session.day === 'Sunday' || session.day === 'Tuesday' || session.day === 'Thursday') {
            duration = 1;
          }
          // For 3 credit hour courses on long days (Mon, Wed), each session is 1.5 hours
          else if (session.day === 'Monday' || session.day === 'Wednesday') {
            duration = 1.5;
          }
        }

        // Round to avoid floating-point precision issues
        duration = Math.round(duration * 1000) / 1000;

        // Update the section's scheduled hours
        const updatedSection = {
          ...sectionToUpdate,
          scheduledHours: sectionToUpdate.scheduledHours + duration
        };

        // Update the section
        updateSection(updatedSection, semester);
      }
    }
  };

  const updateSession = (session: Session, semester: Semester) => {
    // Find the old session to calculate the difference in duration
    const oldSession = sessions[semester].find(s => s.id === session.id);

    setSessions(prev => {
      const updatedSessions = {
        ...prev,
        [semester]: prev[semester].map(s => s.id === session.id ? session : s)
      };

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      // If the session duration has changed, update the section's scheduled hours
      if (oldSession &&
          (oldSession.startPeriod !== session.startPeriod ||
           oldSession.endPeriod !== session.endPeriod ||
           oldSession.day !== session.day)) {

        // Find the section for this session
        const sectionToUpdate = sections[semester].find(s => s.id === session.sectionId);

        if (sectionToUpdate) {
          // Find the course for this section to get contact hours
          const course = courses[semester].find(c => c.id === sectionToUpdate.courseId);

          // Calculate old number of periods
          const oldNumberOfPeriods = oldSession.endPeriod - oldSession.startPeriod + 1;

          // Calculate old duration based on day type
          // Regular days (Su, Tu, Th): 1 hour per period
          // Long days (Mo, We): 1.5 hours per period
          let oldDuration = oldNumberOfPeriods;
          if (oldSession.day === 'Monday' || oldSession.day === 'Wednesday') {
            oldDuration = oldNumberOfPeriods * 1.5;
          }

          // For auto-generated sessions, use the course's contact hours divided by the number of sessions
          if (oldSession.isAutoGenerated && course) {
            if (course.contactHours === 3) {
              // Use a standardized calculation for all courses
              {
                // Calculate number of periods
                const numberOfPeriods = oldSession.endPeriod - oldSession.startPeriod + 1;

                // Calculate duration based on day type
                oldDuration = numberOfPeriods;
                if (oldSession.day === 'Monday' || oldSession.day === 'Wednesday') {
                  oldDuration = numberOfPeriods * 1.5;
                }

                // For auto-generated sessions with 3 credit hours, use fixed values
                if (oldSession.isAutoGenerated && course.contactHours === 3) {
                  // For 3 credit hour courses on regular days (Sun, Tue, Thu), each session is 1 hour
                  if (oldSession.day === 'Sunday' || oldSession.day === 'Tuesday' || oldSession.day === 'Thursday') {
                    oldDuration = 1;
                  }
                  // For 3 credit hour courses on long days (Mon, Wed), each session is 1.5 hours
                  else if (oldSession.day === 'Monday' || oldSession.day === 'Wednesday') {
                    oldDuration = 1.5;
                  }
                }

                // Round to avoid floating-point precision issues
                oldDuration = Math.round(oldDuration * 1000) / 1000;
              }
            }
          }

          // Calculate new number of periods
          const newNumberOfPeriods = session.endPeriod - session.startPeriod + 1;

          // Calculate new duration based on day type
          // Regular days (Su, Tu, Th): 1 hour per period
          // Long days (Mo, We): 1.5 hours per period
          let newDuration = newNumberOfPeriods;
          if (session.day === 'Monday' || session.day === 'Wednesday') {
            newDuration = newNumberOfPeriods * 1.5;
          }

          // For auto-generated sessions, use the course's contact hours divided by the number of sessions
          if (session.isAutoGenerated && course) {
            if (course.contactHours === 3) {
              // Use a standardized calculation for all courses
              {
                // Calculate number of periods
                const numberOfPeriods = session.endPeriod - session.startPeriod + 1;

                // Calculate duration based on day type
                newDuration = numberOfPeriods;
                if (session.day === 'Monday' || session.day === 'Wednesday') {
                  newDuration = numberOfPeriods * 1.5;
                }

                // For auto-generated sessions with 3 credit hours, use fixed values
                if (session.isAutoGenerated && course.contactHours === 3) {
                  // For 3 credit hour courses on regular days (Sun, Tue, Thu), each session is 1 hour
                  if (session.day === 'Sunday' || session.day === 'Tuesday' || session.day === 'Thursday') {
                    newDuration = 1;
                  }
                  // For 3 credit hour courses on long days (Mon, Wed), each session is 1.5 hours
                  else if (session.day === 'Monday' || session.day === 'Wednesday') {
                    newDuration = 1.5;
                  }
                }

                // Round to avoid floating-point precision issues
                newDuration = Math.round(newDuration * 1000) / 1000;
              }
            }
          }

          // Calculate the difference
          const durationDifference = newDuration - oldDuration;

          // Update the section's scheduled hours
          const updatedSection = {
            ...sectionToUpdate,
            scheduledHours: Math.max(0, sectionToUpdate.scheduledHours + durationDifference)
          };

          // Update the section
          updateSection(updatedSection, semester);
        }
      }

      return updatedSessions;
    });
  };

  const deleteSession = (sessionId: string, semester: Semester) => {
    // Find the session to be deleted
    const sessionToDelete = sessions[semester].find(s => s.id === sessionId);

    if (!sessionToDelete) return;

    // Store the sectionId for recalculation after deletion
    const sectionId = sessionToDelete.sectionId;

    // Delete the session first
    setSessions(prev => {
      // Create the updated sessions without the deleted one
      const updatedSessions = {
        ...prev,
        [semester]: prev[semester].filter(s => s.id !== sessionId)
      };

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      // After the session is deleted, recalculate the section's scheduled hours
      // Find the section that needs to be updated
      const sectionToUpdate = sections[semester].find(s => s.id === sectionId);

      if (sectionToUpdate) {
        // Get all remaining sessions for this section (from the updated sessions)
        const remainingSessions = updatedSessions[semester].filter(s => s.sectionId === sectionId);

        // Reset the section's scheduled hours
        let totalScheduledHours = 0;

        // Calculate scheduled hours based on remaining sessions
        remainingSessions.forEach(session => {
          // Calculate number of periods
          const numberOfPeriods = session.endPeriod - session.startPeriod + 1;

          // Calculate duration based on day type
          let duration = numberOfPeriods;
          if (session.day === 'Monday' || session.day === 'Wednesday') {
            duration = numberOfPeriods * 1.5;
          }

          // For auto-generated sessions, use fixed values for 3 credit hour courses
          const course = courses[semester].find(c => c.id === sectionToUpdate.courseId);
          if (session.isAutoGenerated && course && course.contactHours === 3) {
            // Use a standardized calculation for all courses
            {
              // Calculate number of periods
              const numberOfPeriods = session.endPeriod - session.startPeriod + 1;

              // Calculate duration based on day type
              duration = numberOfPeriods;
              if (session.day === 'Monday' || session.day === 'Wednesday') {
                duration = numberOfPeriods * 1.5;
              }

              // For auto-generated sessions with 3 credit hours, use fixed values
              if (session.isAutoGenerated && course.contactHours === 3) {
                // For 3 credit hour courses on regular days (Sun, Tue, Thu), each session is 1 hour
                if (session.day === 'Sunday' || session.day === 'Tuesday' || session.day === 'Thursday') {
                  duration = 1;
                }
                // For 3 credit hour courses on long days (Mon, Wed), each session is 1.5 hours
                else if (session.day === 'Monday' || session.day === 'Wednesday') {
                  duration = 1.5;
                }
              }

              // Round to avoid floating-point precision issues
              duration = Math.round(duration * 1000) / 1000;
            }
          }

          totalScheduledHours += duration;
        });

        // Update the section with the new scheduled hours
        const updatedSection = {
          ...sectionToUpdate,
          scheduledHours: totalScheduledHours
        };

        // Update the section
        updateSection(updatedSection, semester);
      }

      return updatedSessions;
    });
  };

  // Reset current view
  const resetCurrentView = async () => {
    try {
      // Determine current time of day based on activeTab
      const timeOfDay = activeTab === 0 ? 'morning' : 'evening';
      console.log(`Resetting ${timeOfDay} sessions for semester: ${currentSemester}`);

      setSessions(prev => {
        const updated = {...prev};
        const currentSessions = prev[currentSemester] || [];

        console.log('Current sessions before reset:', currentSessions.length);

        // Filter out sessions for the current time of day ONLY (morning/evening)
        // Ignore view type (week/regular/long)
        updated[currentSemester] = currentSessions.filter(session => {
          // If session has no timeOfDay, keep it (legacy data)
          if (!session.timeOfDay) {
            return true;
          }

          // Remove only if timeOfDay matches current setting (morning/evening)
          // Ignore viewType completely
          return session.timeOfDay !== timeOfDay;
        });

        console.log('Sessions after reset:', updated[currentSemester].length);

        // Save updated sessions to store
        window.electronAPI.store.set('sessions', updated);

        // Update section scheduled hours after removing sessions
        updateSectionScheduledHours(updated);

        return updated;
      });

      // Determine time of day text for message
      const timeText = activeTab === 0 ? 'Morning' : 'Evening';
      toast.success(`Reset ${timeText} timetable in ${currentSemester} semester`);
    } catch (error) {
      console.error('Reset error:', error);
      toast.error('Reset failed');
    }
  };

  // Helper function to recalculate scheduled hours for sections
  const updateSectionScheduledHours = (_updatedSessions: Record<Semester, Session[]>) => {
    // Mark for recalculation
    needsRecalculation.current = true;
  };

  // Export timetable data
  const exportTimetableData = async (
    semester: Semester | 'all',
    departmentName: string,
    academicYear: string
  ): Promise<void> => {
    try {
      // Prepare export data
      const exportData = prepareExportData(
        courses,
        lecturers,
        sessions,
        sections,
        departmentName,
        academicYear,
        semester
      );

      // Generate filename
      const filename = generateExportFilename(departmentName, academicYear, semester);

      // Show save dialog
      const result = await window.electronAPI.dialog.showSaveDialog({
        title: 'Export Timetable Data',
        defaultPath: filename,
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });

      if ((result as any).canceled || !(result as any).filePath) {
        return;
      }

      // Write file
      const jsonData = JSON.stringify(exportData, null, 2);
      const writeResult = await window.electronAPI.fs.writeFile((result as any).filePath, jsonData);

      if (writeResult.success) {
        toast.success(`Timetable data exported successfully to ${(result as any).filePath}`);
      } else {
        throw new Error(writeResult.error || 'Failed to write file');
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  };

  // Import timetable data
  const importTimetableData = async (
    filePath: string
  ): Promise<{ success: boolean; summary?: ImportSummary; error?: string }> => {
    try {
      // Read file
      const readResult = await window.electronAPI.fs.readFile(filePath);
      if (!readResult.success) {
        return { success: false, error: readResult.error || 'Failed to read file' };
      }

      // Parse JSON
      let jsonData;
      try {
        jsonData = JSON.parse(readResult.data!);
      } catch (parseError) {
        return { success: false, error: 'Invalid JSON format' };
      }

      // Validate data
      const validation = validateImportData(jsonData);
      if (!validation.isValid) {
        return { success: false, error: validation.errors.join(', ') };
      }

      const { targetSemester, data: importData } = validation;
      if (!targetSemester || !importData) {
        return { success: false, error: 'Invalid import data' };
      }

      // Show confirmation dialog for clearing existing sessions
      const confirmClear = window.confirm(
        `This will clear all existing timetable sessions for the ${targetSemester} semester and import new data. This action cannot be undone. Do you want to continue?`
      );

      if (!confirmClear) {
        return { success: false, error: 'Import cancelled by user' };
      }

      // Process import data
      const {
        coursesToAdd,
        lecturersToAdd,
        sessionsToImport,
        sectionsToImport,
        courseIdMap,
        lecturerIdMap,
        sectionIdMap,
        summary
      } = processImportData(importData, targetSemester, courses, lecturers);

      // Debug logging
      console.log('Processing import data:', {
        targetSemester,
        coursesToAdd: coursesToAdd.length,
        lecturersToAdd: lecturersToAdd.length,
        sectionsToImport: sectionsToImport.length,
        sessionsToImport: sessionsToImport.length,
        sectionsData: sectionsToImport,
        sessionsData: sessionsToImport
      });

      // Clear existing sessions for target semester
      setSessions(prev => {
        const updated = { ...prev };
        updated[targetSemester] = [];
        window.electronAPI.store.set('sessions', updated);
        return updated;
      });

      // Clear existing sections for target semester
      setSections(prev => {
        const updated = { ...prev };
        updated[targetSemester] = [];
        window.electronAPI.store.set('sections', updated);
        return updated;
      });

      // Add new courses and update course ID mapping
      if (coursesToAdd.length > 0) {
        setCourses(prev => {
          const updated = { ...prev };
          const newCourses = coursesToAdd.map(course => {
            const newId = uuidv4();
            courseIdMap.set(course.id, newId); // Map old ID to new ID
            return { ...course, id: newId };
          });
          updated[targetSemester] = [...prev[targetSemester], ...newCourses];
          window.electronAPI.store.set('courses', updated);
          return updated;
        });
      }

      // Add new lecturers and update lecturer ID mapping
      if (lecturersToAdd.length > 0) {
        setLecturers(prev => {
          const newLecturers = lecturersToAdd.map(lecturer => {
            const newId = uuidv4();
            lecturerIdMap.set(lecturer.id, newId); // Map old ID to new ID
            return { ...lecturer, id: newId };
          });
          const updated = [...prev, ...newLecturers];
          window.electronAPI.store.set('lecturers', updated);
          return updated;
        });
      }

      // Import sections with updated course ID references
      if (sectionsToImport.length > 0) {
        setSections(prev => {
          const updated = { ...prev };
          const newSections = sectionsToImport.map(section => {
            const newId = uuidv4();
            sectionIdMap.set(section.id, newId); // Map old section ID to new ID
            const newCourseId = courseIdMap.get(section.courseId) || section.courseId;
            return {
              ...section,
              id: newId,
              courseId: newCourseId // Update course reference
            };
          });
          updated[targetSemester] = newSections;
          window.electronAPI.store.set('sections', updated);
          return updated;
        });
      }

      // Import sessions with updated section and lecturer ID references
      if (sessionsToImport.length > 0) {
        setSessions(prev => {
          const updated = { ...prev };
          const newSessions = sessionsToImport.map(session => {
            const newId = uuidv4();
            const newSectionId = sectionIdMap.get(session.sectionId) || session.sectionId;
            const newLecturerId = session.lecturerId ? (lecturerIdMap.get(session.lecturerId) || session.lecturerId) : session.lecturerId;
            return {
              ...session,
              id: newId,
              sectionId: newSectionId, // Update section reference
              lecturerId: newLecturerId // Update lecturer reference
            };
          });
          updated[targetSemester] = newSessions;
          window.electronAPI.store.set('sessions', updated);
          return updated;
        });
      }

      // Force recalculation of section scheduled hours
      needsRecalculation.current = true;

      // Debug logging
      console.log('Import completed successfully:', {
        targetSemester,
        coursesAdded: coursesToAdd.length,
        lecturersAdded: lecturersToAdd.length,
        sectionsImported: sectionsToImport.length,
        sessionsImported: sessionsToImport.length,
        courseIdMap: Object.fromEntries(courseIdMap),
        lecturerIdMap: Object.fromEntries(lecturerIdMap),
        sectionIdMap: Object.fromEntries(sectionIdMap)
      });

      toast.success(`Successfully imported data to ${targetSemester} semester`);
      return { success: true, summary };

    } catch (error) {
      console.error('Import error:', error);
      const errorMessage = `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Context value
  const contextValue: AppContextType = {
    currentSemester,
    setCurrentSemester,
    courses,
    addCourse,
    updateCourse,
    deleteCourse,
    importCourses,
    reorderCourses,
    sections,
    addSection,
    updateSection,
    deleteSection,
    lecturers,
    addLecturer,
    updateLecturer,
    deleteLecturer,
    importLecturers,
    reorderLecturers,
    sessions,
    setSessions,
    addSession,
    updateSession,
    deleteSession,
    isCourseModalOpen,
    setIsCourseModalOpen,
    isCourseImportModalOpen,
    setIsCourseImportModalOpen,
    isLecturerModalOpen,
    setIsLecturerModalOpen,
    isLecturerImportModalOpen,
    setIsLecturerImportModalOpen,
    currentCourse,
    setCurrentCourse,
    currentLecturer,
    setCurrentLecturer,

    courseFilter,
    setCourseFilter,
    lecturerFilter,
    setLecturerFilter,
    currentView,
    setCurrentView,
    viewMode,
    setViewMode,
    activeTab,
    setActiveTab,
    showPgCanvas,
    setShowPgCanvas,
    isDataLoaded,
    resetCurrentView,
    exportTimetableData,
    importTimetableData
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

// Create a custom hook to use the context
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
