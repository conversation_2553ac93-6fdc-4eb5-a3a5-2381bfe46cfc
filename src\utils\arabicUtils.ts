/**
 * Utility functions for handling Arabic text and font application
 */

/**
 * Checks if a string contains Arabic characters
 * @param text The text to check
 * @returns true if the text contains Arabic characters, false otherwise
 */
export const containsArabic = (text: string): boolean => {
  if (!text) return false;

  // Arabic Unicode range: U+0600 to U+06FF (Arabic block)
  // Additional Arabic ranges: U+0750 to U+077F (Arabic Supplement)
  // U+08A0 to U+08FF (Arabic Extended-A)
  // U+FB50 to U+FDFF (Arabic Presentation Forms-A)
  // U+FE70 to U+FEFF (Arabic Presentation Forms-B)
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;

  return arabicRegex.test(text);
};

/**
 * Gets the appropriate CSS class for text based on whether it contains Arabic
 * @param text The text to check
 * @param isRTLContext Whether the text is in a right-to-left context
 * @returns The CSS class name to apply
 */
export const getArabicTextClass = (text: string, isRTLContext = false): string => {
  if (containsArabic(text)) {
    return isRTLContext ? 'arabic-text' : 'arabic-text-ltr';
  }
  return '';
};

/**
 * Gets the appropriate font family for text based on whether it contains Arabic
 * @param text The text to check
 * @returns The font family string
 */
export const getArabicFontFamily = (text: string): string => {
  if (containsArabic(text)) {
    return "'Tajawal', 'Arabic UI Text', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif";
  }
  return '';
};

/**
 * Applies Arabic styling to an element if the text contains Arabic characters
 * @param element The HTML element to style
 * @param text The text content to check
 */
export const applyArabicStyling = (element: HTMLElement, text: string): void => {
  if (containsArabic(text)) {
    element.style.fontFamily = "'Tajawal', 'Arabic UI Text', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif";
  }
};
