import React, { useState, useEffect, useCallback } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
  FormControlLabel,
  Checkbox,
  FormLabel,
  RadioGroup,
  Radio,
  Paper
} from '@mui/material';
import AccessibleDialog from '../common/AccessibleDialog';
import Grid from '@mui/material/Grid2';
import { useAppContext } from '../../context/AppContext';

// Define the lecturer structure based on the requirements
interface LecturerData {
  id?: string;
  title: string;
  firstName: string;
  lastName?: string; // Made optional - lecturers may only have a first name
  email: string;
  department?: string; // Department field (optional)
  supervisionHoursFall: number;
  supervisionHoursSpring: number;
  maxYearLoad: number;
  coursesAbleToTeach: string[];
  preferredTiming: 'Morning' | 'Evening' | 'Both';
  // Auto timetabling fields
  preferredCourseLevel?: 'Undergraduate' | 'Postgraduate' | 'All';
  femaleOnlyTeaching?: boolean;
  maxTeachingDaysPerWeek: number;
  maxConsecutivePeriods?: number;
  maxGapBetweenPeriods?: number;
}

// Define props for the modal component
interface LecturerAddEditModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (lecturer: LecturerData) => void;
  lecturer?: LecturerData; // If provided, we're in edit mode
  availableCourses: { code: string; name: string; color: string }[]; // Available courses with their colors
}

// Title options
const titleOptions = ['Prof.', 'Dr.', 'Mr.', 'Ms.'];

// Default empty lecturer - defined outside the component to avoid recreation on each render
const defaultEmptyLecturer: LecturerData = {
  title: 'Dr.',
  firstName: '',
  lastName: '', // Optional field, can be empty
  email: '',
  department: '',
  supervisionHoursFall: 0,
  supervisionHoursSpring: 0,
  maxYearLoad: 18,
  coursesAbleToTeach: [],
  preferredTiming: 'Both',
  // Auto timetabling fields
  preferredCourseLevel: 'All',
  femaleOnlyTeaching: false,
  maxTeachingDaysPerWeek: 5,
  maxConsecutivePeriods: 3,
  maxGapBetweenPeriods: 2
};

/**
 * LecturerAddEditModal component for adding or editing lecturer details
 * Includes validation and course selection
 */
const LecturerAddEditModal: React.FC<LecturerAddEditModalProps> = ({
  open,
  onClose,
  onSave,
  lecturer,
  availableCourses = [] // Default to empty array if not provided
}) => {
  // Get current semester from context
  const { currentSemester } = useAppContext();



  // State for lecturer data
  const [lecturerData, setLecturerData] = useState<LecturerData>(lecturer || defaultEmptyLecturer);

  // State for form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update lecturer data when the lecturer prop changes
  useEffect(() => {
    if (lecturer) {
      setLecturerData({...lecturer});
    } else {
      setLecturerData({...defaultEmptyLecturer});
    }
    setErrors({});
  }, [lecturer]);

  // Handle input changes
  const handleInputChange = (field: keyof LecturerData, value: string | number | boolean) => {
    let processedValue = value;

    // Special handling for names
    if (field === 'firstName' || field === 'lastName') {
      // Capitalize first letter
      processedValue = String(value).charAt(0).toUpperCase() + String(value).slice(1).toLowerCase();
    }

    // Handle numeric fields
    if (field === 'supervisionHoursFall' || field === 'supervisionHoursSpring' ||
        field === 'maxYearLoad' || field === 'maxTeachingDaysPerWeek' ||
        field === 'maxConsecutivePeriods' || field === 'maxGapBetweenPeriods') {

      if (value === '') {
        // For empty values, use default values
        if (field === 'maxYearLoad') {
          processedValue = 18; // Default value
        } else if (field === 'maxTeachingDaysPerWeek') {
          processedValue = 5; // Default value
        } else if (field === 'maxConsecutivePeriods') {
          processedValue = 3; // Default value
        } else if (field === 'maxGapBetweenPeriods') {
          processedValue = 2; // Default value
        } else {
          processedValue = 0; // Default for supervision hours
        }
      } else {
        const numValue = Number(value);
        if (!isNaN(numValue)) {
          processedValue = numValue;
        } else {
          // If not a valid number, keep the previous value
          return;
        }
      }
    }

    setLecturerData(prev => ({
      ...prev,
      [field]: processedValue
    }));

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle course selection
  const handleCourseToggle = (courseCode: string) => {
    setLecturerData(prev => {
      const currentCourses = [...prev.coursesAbleToTeach];
      const courseIndex = currentCourses.indexOf(courseCode);

      if (courseIndex === -1) {
        // Add course
        currentCourses.push(courseCode);
      } else {
        // Remove course
        currentCourses.splice(courseIndex, 1);
      }

      return {
        ...prev,
        coursesAbleToTeach: currentCourses
      };
    });
  };

  // Handle select all courses
  const handleSelectAllCourses = (selected: boolean) => {
    setLecturerData(prev => ({
      ...prev,
      coursesAbleToTeach: selected ? availableCourses.map(course => course.code) : []
    }));
  };

  // Validate the form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    // First name validation
    if (!lecturerData.firstName) {
      newErrors.firstName = 'First name is required';
    }

    // Last name validation - removed required validation, now optional

    // Email validation
    if (!lecturerData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(lecturerData.email)) {
      newErrors.email = 'Invalid email format';
    }

    // Supervision hours validation
    if (lecturerData.supervisionHoursFall < 0) {
      newErrors.supervisionHoursFall = 'Cannot be negative';
    }

    if (lecturerData.supervisionHoursSpring < 0) {
      newErrors.supervisionHoursSpring = 'Cannot be negative';
    }

    // Max year load validation
    if (lecturerData.maxYearLoad <= 0) {
      newErrors.maxYearLoad = 'Must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [lecturerData]);

  // Reset form to default values
  const resetForm = useCallback(() => {
    setLecturerData({...defaultEmptyLecturer});
    setErrors({});
  }, [defaultEmptyLecturer]);

  // Handle save action
  const handleSave = useCallback(() => {
    if (validateForm()) {
      onSave(lecturerData);
      resetForm();
      onClose();
    }
  }, [validateForm, lecturerData, resetForm, onSave, onClose]);

  // Calculate max semester load based on current semester
  const calculateMaxSemesterLoad = () => {
    switch (currentSemester) {
      case 'Fall':
      case 'Spring':
        return lecturerData.maxYearLoad / 2; // Half of the max year load
      case 'Summer':
        return 6; // Fixed 6 hours for Summer
      default:
        return 0;
    }
  };

  // Get current semester supervision hours
  const getCurrentSemesterSupervisionHours = () => {
    return currentSemester === 'Fall'
      ? lecturerData.supervisionHoursFall
      : lecturerData.supervisionHoursSpring;
  };

  return (
    <AccessibleDialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2
          }
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          {lecturer ? 'Edit Lecturer' : 'Add New Lecturer'} - {currentSemester} Semester
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Left column */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2}>
                <Grid size={4}>
                  <FormControl fullWidth>
                    <InputLabel>Title</InputLabel>
                    <Select
                      value={lecturerData.title}
                      label="Title"
                      onChange={(e) => handleInputChange('title', e.target.value)}
                    >
                      {titleOptions.map((title) => (
                        <MenuItem key={title} value={title}>
                          {title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid size={8}>
                  <TextField
                    label="First Name"
                    fullWidth
                    required
                    value={lecturerData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    error={!!errors.firstName}
                    helperText={errors.firstName}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Last Name"
                fullWidth
                value={lecturerData.lastName || ''}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                error={!!errors.lastName}
                helperText={errors.lastName || 'Optional: Lecturer\'s last name'}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Email Address"
                fullWidth
                required
                type="email"
                value={lecturerData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={!!errors.email}
                helperText={errors.email}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Department"
                fullWidth
                value={lecturerData.department || ''}
                onChange={(e) => handleInputChange('department', e.target.value)}
                helperText="Optional: Lecturer's department or academic unit"
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Preferred Course Timing</FormLabel>
                <RadioGroup
                  row
                  value={lecturerData.preferredTiming}
                  onChange={(e) => handleInputChange('preferredTiming', e.target.value)}
                >
                  <FormControlLabel value="Morning" control={<Radio />} label="Morning" />
                  <FormControlLabel value="Evening" control={<Radio />} label="Evening" />
                  <FormControlLabel value="Both" control={<Radio />} label="Both" />
                </RadioGroup>
              </FormControl>
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControl component="fieldset">
                <FormLabel component="legend">Preferred Course Level</FormLabel>
                <RadioGroup
                  row
                  value={lecturerData.preferredCourseLevel || 'All'}
                  onChange={(e) => handleInputChange('preferredCourseLevel', e.target.value)}
                >
                  <FormControlLabel value="Undergraduate" control={<Radio />} label="Undergraduate" />
                  <FormControlLabel value="Postgraduate" control={<Radio />} label="Postgraduate" />
                  <FormControlLabel value="All" control={<Radio />} label="All" />
                </RadioGroup>
              </FormControl>
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={lecturerData.femaleOnlyTeaching || false}
                    onChange={(e) => handleInputChange('femaleOnlyTeaching', e.target.checked)}
                  />
                }
                label="Female-only Teaching"
              />
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', ml: 4 }}>
                If checked, this lecturer will only be assigned to female sections
              </Typography>
            </Box>
          </Grid>

          {/* Right column */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Supervision Hours
              </Typography>
              <Grid container spacing={2}>
                <Grid size={6}>
                  <TextField
                    label="Supervision Hours (Fall)"
                    fullWidth
                    type="number"
                    value={lecturerData.supervisionHoursFall}
                    onChange={(e) => handleInputChange('supervisionHoursFall', e.target.value)}
                    error={!!errors.supervisionHoursFall}
                    helperText={errors.supervisionHoursFall}
                    slotProps={{
                      htmlInput: { step: "0.5", min: 0 }
                    }}
                    disabled={currentSemester !== 'Fall'}
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    label="Supervision Hours (Spring)"
                    fullWidth
                    type="number"
                    value={lecturerData.supervisionHoursSpring}
                    onChange={(e) => handleInputChange('supervisionHoursSpring', e.target.value)}
                    error={!!errors.supervisionHoursSpring}
                    helperText={errors.supervisionHoursSpring}
                    slotProps={{
                      htmlInput: { step: "0.5", min: 0 }
                    }}
                    disabled={currentSemester !== 'Spring'}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Max Year Load (Hours)"
                type="number"
                fullWidth
                value={lecturerData.maxYearLoad}
                onChange={(e) => handleInputChange('maxYearLoad', e.target.value)}
                error={!!errors.maxYearLoad}
                helperText={errors.maxYearLoad || 'Default: 18 hours'}
                slotProps={{
                  htmlInput: { min: 1 }
                }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                {currentSemester} Semester Load
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Max: {calculateMaxSemesterLoad()} hours
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Current Supervision: {getCurrentSemesterSupervisionHours()} hours
              </Typography>
            </Box>

            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle1" gutterBottom>
              Auto Timetabling Settings
            </Typography>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Max Teaching Days Per Week"
                type="number"
                fullWidth
                value={lecturerData.maxTeachingDaysPerWeek || 5}
                onChange={(e) => handleInputChange('maxTeachingDaysPerWeek', e.target.value)}
                slotProps={{ htmlInput: { min: 1, max: 5 } }}
                helperText="Maximum number of days this lecturer can teach per week"
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Max Consecutive Periods"
                type="number"
                fullWidth
                value={lecturerData.maxConsecutivePeriods || 3}
                onChange={(e) => handleInputChange('maxConsecutivePeriods', e.target.value)}
                slotProps={{ htmlInput: { min: 1, max: 5 } }}
                helperText="Maximum number of consecutive teaching periods"
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Max Empty Hours Per Day"
                type="number"
                fullWidth
                value={lecturerData.maxGapBetweenPeriods || 2}
                onChange={(e) => handleInputChange('maxGapBetweenPeriods', e.target.value)}
                slotProps={{ htmlInput: { min: 0, max: 6 } }}
                helperText="Maximum empty hours between first and last session in a day"
              />
            </Box>
          </Grid>

          {/* Courses section - full width */}
          <Grid size={12}>
            <Divider sx={{ mb: 2 }} />
            <Typography variant="subtitle1" gutterBottom>
              Courses Able to Teach ({currentSemester} Semester Courses)
            </Typography>

            <Box sx={{ mb: 2 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={lecturerData.coursesAbleToTeach.length === availableCourses.length}
                    indeterminate={
                      lecturerData.coursesAbleToTeach.length > 0 &&
                      lecturerData.coursesAbleToTeach.length < availableCourses.length
                    }
                    onChange={(e) => handleSelectAllCourses(e.target.checked)}
                  />
                }
                label="Select All Courses"
              />
            </Box>

            <Paper variant="outlined" sx={{ p: 2, maxHeight: 200, overflow: 'auto' }}>
              <Grid container spacing={1}>
                {availableCourses.map((course) => (
                  <Grid size={{ xs: 6, sm: 4, md: 3 }} key={course.code}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={lecturerData.coursesAbleToTeach.includes(course.code)}
                          onChange={() => handleCourseToggle(course.code)}
                        />
                      }
                      label={
                        <Box display="flex" alignItems="center">
                          <Chip
                            label={course.code}
                            size="small"
                            sx={{
                              bgcolor: course.color,
                              color: 'white',
                              fontWeight: 'bold',
                              mr: 1
                            }}
                          />
                          <Typography variant="body2" noWrap>
                            {course.name}
                          </Typography>
                        </Box>
                      }
                    />
                  </Grid>
                ))}

                {availableCourses.length === 0 && (
                  <Grid size={12}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      No courses available for {currentSemester} semester. Please add courses first.
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
        >
          {lecturer ? 'Save Changes' : 'Add Lecturer'}
        </Button>
      </DialogActions>
    </AccessibleDialog>
  );
};

export default LecturerAddEditModal;
