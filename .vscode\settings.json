{"files.associations": {"*.css": "tailwindcss"}, "css.validate": false, "less.validate": false, "scss.validate": false, "tailwindCSS.includeLanguages": {"typescript": "typescript", "typescriptreact": "typescriptreact", "javascript": "javascript", "javascriptreact": "javascriptreact"}, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}