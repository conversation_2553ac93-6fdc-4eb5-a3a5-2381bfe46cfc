/**
 * QU Scheduler Icon Verification Script
 *
 * This script verifies that all icons are properly standardized and
 * using the correct QU Scheduler design with maroon border, black background,
 * white content area, three golden course sections, and maroon timetable grid.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const MASTER_ICO_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.ico');
const MASTER_SVG_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.svg');
const ICONS_DIR = path.join(__dirname, '..', 'assets', 'icons');
const WEBSITE_ASSETS_DIR = path.join(__dirname, '..', 'website', 'assets');

// Required files
const REQUIRED_ICON_FILES = [
    'icon.ico',
    'icon.svg',
    'icon-16x16.png',
    'icon-24x24.png',
    'icon-32x32.png',
    'icon-48x48.png',
    'icon-64x64.png',
    'icon-128x128.png',
    'icon-256x256.png',
    'icon-512x512.png',
    'ICON_STANDARDS.md'
];

const REQUIRED_WEBSITE_FILES = [
    'favicon.ico',
    'favicon-16x16.png',
    'favicon-32x32.png',
    'favicon-48x48.png',
    'qu-scheduler-logo.svg'
];

function log(message) {
    console.log(`🔍 ${message}`);
}

function logSuccess(message) {
    console.log(`✅ ${message}`);
}

function logError(message) {
    console.log(`❌ ${message}`);
}

function logWarning(message) {
    console.log(`⚠️  ${message}`);
}

// Verify master icon files exist
function verifyMasterIcons() {
    log('Verifying master icon files...');

    let allGood = true;

    if (!fs.existsSync(MASTER_ICO_PATH)) {
        logError(`Master ICO file missing: ${MASTER_ICO_PATH}`);
        allGood = false;
    } else {
        logSuccess('Master ICO file exists');
    }

    if (!fs.existsSync(MASTER_SVG_PATH)) {
        logError(`Master SVG file missing: ${MASTER_SVG_PATH}`);
        allGood = false;
    } else {
        logSuccess('Master SVG file exists');
    }

    return allGood;
}

// Verify SVG contains correct design elements
function verifySVGDesign() {
    log('Verifying SVG design elements...');

    try {
        const svgContent = fs.readFileSync(MASTER_SVG_PATH, 'utf8');

        const designChecks = [
            { element: 'maroon border', pattern: /#8B1538|maroonGradient/, description: 'Qatar University maroon color' },
            { element: 'white background', pattern: /#FFFFFF/, description: 'White background (enhanced design)' },
            { element: 'golden course sections', pattern: /#F1C40F|goldGradient/, description: 'Golden/yellow course sections' },
            { element: 'timetable grid', pattern: /grid|rect.*fill.*#FFFFFF/, description: 'Simplified 3×3 timetable grid structure' },
            { element: 'enhanced corners', pattern: /rx="(16|12|6)"/, description: 'Enhanced corner radius for modern appearance' }
        ];

        let allChecksPass = true;

        designChecks.forEach(check => {
            if (check.pattern.test(svgContent)) {
                logSuccess(`${check.element} found - ${check.description}`);
            } else {
                logError(`${check.element} missing - ${check.description}`);
                allChecksPass = false;
            }
        });

        return allChecksPass;
    } catch (error) {
        logError(`Failed to read SVG file: ${error.message}`);
        return false;
    }
}

// Verify all required icon files exist
function verifyIconFiles() {
    log('Verifying icon files...');

    let allGood = true;

    REQUIRED_ICON_FILES.forEach(filename => {
        const filePath = path.join(ICONS_DIR, filename);
        if (fs.existsSync(filePath)) {
            logSuccess(`Icon file exists: ${filename}`);
        } else {
            logError(`Icon file missing: ${filename}`);
            allGood = false;
        }
    });

    return allGood;
}

// Verify website files exist
function verifyWebsiteFiles() {
    log('Verifying website files...');

    let allGood = true;

    REQUIRED_WEBSITE_FILES.forEach(filename => {
        const filePath = path.join(WEBSITE_ASSETS_DIR, filename);
        if (fs.existsSync(filePath)) {
            logSuccess(`Website file exists: ${filename}`);
        } else {
            logError(`Website file missing: ${filename}`);
            allGood = false;
        }
    });

    return allGood;
}

// Verify application configuration
function verifyApplicationConfig() {
    log('Verifying application configuration...');

    let allGood = true;

    // Check forge.config.ts
    const forgeConfigPath = path.join(__dirname, '..', 'forge.config.ts');
    if (fs.existsSync(forgeConfigPath)) {
        const forgeContent = fs.readFileSync(forgeConfigPath, 'utf8');
        if (forgeContent.includes('./assets/icons/icon')) {
            logSuccess('Forge configuration uses standardized icon path');
        } else {
            logError('Forge configuration does not use standardized icon path');
            allGood = false;
        }
    } else {
        logWarning('Forge configuration file not found');
    }

    // Check main.ts
    const mainTsPath = path.join(__dirname, '..', 'src', 'main.ts');
    if (fs.existsSync(mainTsPath)) {
        const mainContent = fs.readFileSync(mainTsPath, 'utf8');
        if (mainContent.includes('assets/icons/icon.ico')) {
            logSuccess('Main process uses standardized icon path');
        } else {
            logError('Main process does not use standardized icon path');
            allGood = false;
        }
    } else {
        logWarning('Main process file not found');
    }

    return allGood;
}

// Verify website HTML files
function verifyWebsiteHTML() {
    log('Verifying website HTML files...');

    const htmlFiles = ['index.html', 'features.html', 'download.html', 'support.html'];
    let allGood = true;

    htmlFiles.forEach(filename => {
        const filePath = path.join(__dirname, '..', 'website', filename);
        if (fs.existsSync(filePath)) {
            const htmlContent = fs.readFileSync(filePath, 'utf8');

            // Check for favicon references
            if (htmlContent.includes('assets/favicon.ico')) {
                logSuccess(`${filename} has correct favicon reference`);
            } else {
                logError(`${filename} missing favicon reference`);
                allGood = false;
            }

            // Check for logo references
            if (htmlContent.includes('assets/qu-scheduler-logo.svg')) {
                logSuccess(`${filename} has correct logo reference`);
            } else {
                logWarning(`${filename} may not have logo reference (could be normal)`);
            }
        } else {
            logError(`Website HTML file missing: ${filename}`);
            allGood = false;
        }
    });

    return allGood;
}

// Check file sizes to ensure they're not empty
function verifyFileSizes() {
    log('Verifying file sizes...');

    let allGood = true;

    const criticalFiles = [
        { path: MASTER_ICO_PATH, minSize: 1000, name: 'Master ICO' },
        { path: MASTER_SVG_PATH, minSize: 500, name: 'Master SVG' },
        { path: path.join(WEBSITE_ASSETS_DIR, 'favicon.ico'), minSize: 1000, name: 'Website favicon' },
        { path: path.join(WEBSITE_ASSETS_DIR, 'qu-scheduler-logo.svg'), minSize: 500, name: 'Website logo' }
    ];

    criticalFiles.forEach(file => {
        if (fs.existsSync(file.path)) {
            const stats = fs.statSync(file.path);
            if (stats.size >= file.minSize) {
                logSuccess(`${file.name} has appropriate size (${stats.size} bytes)`);
            } else {
                logWarning(`${file.name} seems small (${stats.size} bytes) - may be placeholder`);
            }
        } else {
            logError(`${file.name} file not found`);
            allGood = false;
        }
    });

    return allGood;
}

// Main verification function
function main() {
    console.log('🔍 QU Scheduler Icon Verification');
    console.log('==================================');
    console.log('');
    console.log('Verifying that all icons use the enhanced QU Scheduler design:');
    console.log('- Maroon border frame (#8B1538)');
    console.log('- White background (#FFFFFF) - simplified design');
    console.log('- Three golden course sections (#F1C40F) with maroon borders');
    console.log('- Simplified 3×3 timetable grid with consistent borders');
    console.log('- Enhanced corner radius for modern appearance');
    console.log('');

    const checks = [
        { name: 'Master Icons', func: verifyMasterIcons },
        { name: 'SVG Design', func: verifySVGDesign },
        { name: 'Icon Files', func: verifyIconFiles },
        { name: 'Website Files', func: verifyWebsiteFiles },
        { name: 'Application Config', func: verifyApplicationConfig },
        { name: 'Website HTML', func: verifyWebsiteHTML },
        { name: 'File Sizes', func: verifyFileSizes }
    ];

    let allPassed = true;

    checks.forEach(check => {
        console.log(`\n--- ${check.name} ---`);
        const result = check.func();
        if (!result) {
            allPassed = false;
        }
    });

    console.log('\n' + '='.repeat(50));

    if (allPassed) {
        console.log('🎉 All icon verification checks PASSED!');
        console.log('✅ QU Scheduler icons are properly standardized');
        console.log('✅ Correct design elements are present');
        console.log('✅ All required files exist');
        console.log('✅ Application and website configurations are correct');
    } else {
        console.log('❌ Some verification checks FAILED');
        console.log('📋 Please review the errors above and fix any issues');
        console.log('💡 Run the standardization scripts if needed:');
        console.log('   - npm run convert:icons');
        console.log('   - npm run standardize:icons');
    }

    console.log('');
    console.log('📞 For support: <EMAIL>');
}

// Run the verification
if (require.main === module) {
    main();
}

module.exports = {
    verifyMasterIcons,
    verifySVGDesign,
    verifyIconFiles,
    verifyWebsiteFiles,
    verifyApplicationConfig,
    verifyWebsiteHTML,
    verifyFileSizes
};
