import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config
export default defineConfig({
  plugins: [
    react({
      // Fix for preamble detection issues
      include: "**/*.{jsx,tsx}",
      // Use automatic JSX runtime
      jsxRuntime: 'automatic'
    })
  ],
  base: './',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    // Production optimizations
    minify: 'esbuild', // Use esbuild instead of terser for faster builds
    sourcemap: false, // Disable sourcemaps in production for security
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material', '@mui/x-data-grid'],
          utils: ['zustand', 'uuid', 'papaparse']
        }
      }
    },
    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000
  },
  // Optimize dev server for development
  server: {
    hmr: true
  }
});
