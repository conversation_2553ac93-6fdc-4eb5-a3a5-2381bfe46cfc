import{a as l}from"./vendor-AVgCjkqc.js";const n=[];for(let t=0;t<256;++t)n.push((t+256).toString(16).slice(1));function p(t,e=0){return(n[t[e+0]]+n[t[e+1]]+n[t[e+2]]+n[t[e+3]]+"-"+n[t[e+4]]+n[t[e+5]]+"-"+n[t[e+6]]+n[t[e+7]]+"-"+n[t[e+8]]+n[t[e+9]]+"-"+n[t[e+10]]+n[t[e+11]]+n[t[e+12]]+n[t[e+13]]+n[t[e+14]]+n[t[e+15]]).toLowerCase()}let i;const U=new Uint8Array(16);function I(){if(!i){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");i=crypto.getRandomValues.bind(crypto)}return i(U)}const x=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),g={randomUUID:x};function E(t,e,o){var s;if(g.randomUUID&&!t)return g.randomUUID();t=t||{};const c=t.random??((s=t.rng)==null?void 0:s.call(t))??I();if(c.length<16)throw new Error("Random bytes length must be >= 16");return c[6]=c[6]&15|64,c[8]=c[8]&63|128,p(c)}const y=t=>{let e;const o=new Set,c=(u,d)=>{const r=typeof u=="function"?u(e):u;if(!Object.is(r,e)){const b=e;e=d??(typeof r!="object"||r===null)?r:Object.assign({},e,r),o.forEach(m=>m(e,b))}},s=()=>e,a={setState:c,getState:s,getInitialState:()=>S,subscribe:u=>(o.add(u),()=>o.delete(u))},S=e=t(c,s,a);return a},h=t=>t?y(t):y,w=t=>t;function D(t,e=w){const o=l.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return l.useDebugValue(o),o}const R=t=>{const e=h(t),o=c=>D(e,c);return Object.assign(o,e),o},O=t=>R;export{O as c,E as v};
