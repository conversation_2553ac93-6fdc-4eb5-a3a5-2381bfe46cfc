import Store from 'electron-store';
import { v4 as uuidv4 } from 'uuid';
import { Course, Lecturer, Section, Session, Semester, Schema } from '../types/models';

const defaultValues: Schema = {
  courses: {
    Fall: [],
    Spring: [],
    Summer: [],
  },
  sections: {
    Fall: [],
    Spring: [],
    Summer: [],
  },
  lecturers: [],
  sessions: {
    Fall: [],
    Spring: [],
    Summer: [],
  },
  currentSemester: 'Fall',
  uiState: {
    // TimetableCanvas state
    viewMode: 'week',
    activeTab: 2, // 0 for morning, 1 for evening, 2 for all day (default)

    // Header state
    departmentName: '',
    academicYear: '',
    zoomFactor: 0.8, // Default to 80% zoom for fresh installations

    // App state
    darkMode: false,
    showCoursesPanel: true,
    showLecturersPanel: true,

    // CoursesPanel state
    expandedCourses: {},

    // LecturersPanel state
    expandedLecturers: {},

    // Filter states
    courseFilter: 'all',
    lecturerFilter: 'all'
  }
};

const store = new Store<Schema>({
  defaults: defaultValues,
  name: 'timetable-data', // Optional: specify a custom name for the store file
});

export const getCourses = (semester: Semester): Course[] => {
  return store.get('courses')[semester] || [];
};

export const addCourse = (course: Omit<Course, 'id'>, semester: Semester): Course => {
  const newCourse: Course = { ...course, id: uuidv4() };
  const courses = store.get('courses');
  courses[semester].push(newCourse);
  store.set('courses', courses);
  return newCourse;
};

export const updateCourse = (updatedCourse: Course, semester: Semester): void => {
  const courses = store.get('courses');
  const index = courses[semester].findIndex((c) => c.id === updatedCourse.id);
  if (index !== -1) {
    courses[semester][index] = updatedCourse;
    store.set('courses', courses);
  }
};

export const deleteCourse = (courseId: string, semester: Semester): void => {
  const courses = store.get('courses');
  courses[semester] = courses[semester].filter((c) => c.id !== courseId);
  store.set('courses', courses);
};

export const importCourses = (newCourses: Omit<Course, 'id'>[], semester: Semester): void => {
    const normalizedCourses = newCourses.map(course => ({
      ...course,
      courseCode: course.courseCode.replace(/\s+/g, '')
    }));

    const coursesWithIds = normalizedCourses.map(course => ({
      ...course,
      id: uuidv4()
    }));

    const existingCourses = store.get('courses');
    const existingCourseCodes = new Set(existingCourses[semester].map(c => c.courseCode.toUpperCase()));
    const uniqueCourses = coursesWithIds.filter(course => !existingCourseCodes.has(course.courseCode.toUpperCase()));

    existingCourses[semester] = [...existingCourses[semester], ...uniqueCourses];
    store.set('courses', existingCourses);
};

export const getSections = (semester: Semester): Section[] => {
  return store.get('sections')[semester] || [];
};

export const addSection = (section: Omit<Section, 'id'>, semester: Semester): Section => {
    const courses = getCourses(semester);
    let sectionNumber = section.sectionNumber;
    const course = courses.find(c => c.id === section.courseId);

    if (course) {
      const courseCode = course.courseCode.toUpperCase();
      const existingSections = getSections(semester).filter(s => {
        const sectionCourse = courses.find(c => c.id === s.courseId);
        return sectionCourse && sectionCourse.courseCode.toUpperCase() === courseCode && s.gender === section.gender;
      });

      const existingSectionNumbers = new Set(existingSections.map(s => s.sectionNumber));

      if (!sectionNumber || existingSectionNumbers.has(sectionNumber)) {
        const baseNumber = section.gender === 'M' ? 1 : 51;
        sectionNumber = baseNumber;

        while (existingSectionNumbers.has(sectionNumber)) {
          sectionNumber++;
        }
      }
    }

  const newSection: Section = { ...section, id: uuidv4(), sectionNumber: sectionNumber };
  const sections = store.get('sections');
  sections[semester].push(newSection);
  store.set('sections', sections);
  return newSection;
};

export const updateSection = (updatedSection: Section, semester: Semester): void => {
  const sections = store.get('sections');
  const index = sections[semester].findIndex((s) => s.id === updatedSection.id);
  if (index !== -1) {
    sections[semester][index] = updatedSection;
    store.set('sections', sections);
  }
};

export const deleteSection = (sectionId: string, semester: Semester): void => {
  const sections = store.get('sections');
  sections[semester] = sections[semester].filter((s) => s.id !== sectionId);
  store.set('sections', sections);
};

export const getLecturers = (): Lecturer[] => {
  return store.get('lecturers') || [];
};

export const addLecturer = (lecturer: Omit<Lecturer, 'id'>): Lecturer => {
  const newLecturer: Lecturer = { ...lecturer, id: uuidv4() };
  const lecturers = store.get('lecturers');
  lecturers.push(newLecturer);
  store.set('lecturers', lecturers);
  return newLecturer;
};

export const updateLecturer = (updatedLecturer: Lecturer): void => {
  const lecturers = store.get('lecturers');
  const index = lecturers.findIndex((l) => l.id === updatedLecturer.id);
  if (index !== -1) {
    lecturers[index] = updatedLecturer;
    store.set('lecturers', lecturers);
  }
};

export const deleteLecturer = (lecturerId: string): void => {
  const lecturers = store.get('lecturers');
  const updatedLecturers = lecturers.filter((l) => l.id !== lecturerId);
    store.set('lecturers', updatedLecturers);
};

export const importLecturers = (newLecturers: Omit<Lecturer, 'id'>[]) => {
    const lecturersWithIds = newLecturers.map(lecturer => ({
      ...lecturer,
      id: uuidv4()
    }));

    const existingLecturers = store.get('lecturers');
    const updatedLecturers = [...existingLecturers, ...lecturersWithIds];
    store.set('lecturers', updatedLecturers);
};

export const getSessions = (semester: Semester): Session[] => {
  return store.get('sessions')[semester] || [];
};

export const addSession = (session: Omit<Session, 'id'>, semester: Semester): Session => {
  const newSession: Session = { ...session, id: uuidv4() };
  const sessions = store.get('sessions');
  sessions[semester].push(newSession);
  store.set('sessions', sessions);
  return newSession;
};

export const updateSession = (updatedSession: Session, semester: Semester): void => {
  const sessions = store.get('sessions');
  const index = sessions[semester].findIndex((s) => s.id === updatedSession.id);
  if (index !== -1) {
    sessions[semester][index] = updatedSession;
    store.set('sessions', sessions);
  }
};

export const deleteSession = (sessionId: string, semester: Semester): void => {
  const sessions = store.get('sessions');
  sessions[semester] = sessions[semester].filter((s) => s.id !== sessionId);
  store.set('sessions', sessions);
};

export const getCurrentSemester = (): Semester => {
    return store.get('currentSemester');
}

export const setCurrentSemester = (semester: Semester) => {
    store.set('currentSemester', semester);
}

export const clearAllData = () => {
  store.clear();
}

// UI State functions
export const getUIState = () => {
  return store.get('uiState');
};

export const setUIState = (uiState: Partial<Schema['uiState']>) => {
  const currentUIState = store.get('uiState');
  const updatedUIState = { ...currentUIState, ...uiState };
  store.set('uiState', updatedUIState);
  return updatedUIState;
};

export default store;
