import React, { useState, useRef, ChangeEvent } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Typography,
  Box,
  LinearProgress,
  Alert,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import AccessibleDialog from '../common/AccessibleDialog';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import * as XLSX from 'xlsx';

// Define the lecturer structure based on the requirements
interface LecturerData {
  id?: string;
  title: string;
  firstName: string;
  lastName?: string; // Made optional - lecturers may only have a first name
  email: string;
  department?: string; // Department field (optional)
  supervisionHoursFall: number;
  supervisionHoursSpring: number;
  maxYearLoad: number;
  coursesAbleToTeach: string[];
  preferredTiming: 'Morning' | 'Evening' | 'Both';
}

// Define props for the modal component
interface LecturerImportModalProps {
  open: boolean;
  onClose: () => void;
  onImport: (lecturers: LecturerData[]) => void;
  availableCourses: string[]; // List of course codes available in the system
  existingLecturerEmails?: string[]; // List of existing lecturer emails in the system
}

// Update ExcelRowData
interface ExcelRowData {
  [key: string]: string | number;
}

// Type guard for ExcelRowData
const isExcelRowData = (value: unknown): value is ExcelRowData => {
  return typeof value === 'object' && value !== null && Object.values(value).every(v =>
    typeof v === 'string' || typeof v === 'number'
  );
};

/**
 * LecturerImportModal component for importing lecturers from Excel files
 * Includes validation, intelligent column mapping, and preview
 */
const LecturerImportModal: React.FC<LecturerImportModalProps> = ({
  open,
  onClose,
  onImport,
  availableCourses = [], // Default to empty array if not provided
  existingLecturerEmails = [] // Default to empty array if not provided
}) => {
  // State for file upload and processing
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [parsedData, setParsedData] = useState<LecturerData[]>([]);
  const [duplicates, setDuplicates] = useState<string[]>([]);
  const [mappedColumns, setMappedColumns] = useState<Record<string, string>>({});
  const [importError, setImportError] = useState<string | null>(null);
  const [excelHeaders, setExcelHeaders] = useState<string[]>([]);
  const [showManualMapping, setShowManualMapping] = useState(false);
  const [rawData, setRawData] = useState<ExcelRowData[]>([]);

  // State for sheet selection
  const [availableSheets, setAvailableSheets] = useState<string[]>([]);
  const [selectedSheet, setSelectedSheet] = useState<string>('');
  const [showSheetSelection, setShowSheetSelection] = useState(false);
  const [workbookData, setWorkbookData] = useState<XLSX.WorkBook | null>(null);

  // Reference to the file input element
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    if (selectedFile) {
      setFile(selectedFile);
      handleFileUpload(selectedFile);
    }
  };

  // Trigger file input click
  const handleChooseFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Process the uploaded Excel file
  const handleFileUpload = (selectedFile: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    setImportError(null);

    // Start progress simulation
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        const newProgress = prev + 10;
        if (newProgress >= 100) {
          clearInterval(interval);
          return 100;
        }
        return newProgress;
      });
    }, 100);

    // Read the Excel file
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          throw new Error('Failed to read file');
        }

        // Parse Excel data using ArrayBuffer (modern approach)
        const workbook = XLSX.read(data, { type: 'array' });

        // Store workbook for later use
        setWorkbookData(workbook);

        // Check if there are multiple sheets
        if (workbook.SheetNames.length > 1) {
          // Multiple sheets found - show sheet selection
          setAvailableSheets(workbook.SheetNames);
          setSelectedSheet(workbook.SheetNames[0]); // Default to first sheet
          setShowSheetSelection(true);

          // Clear interval
          clearInterval(interval);
          setUploadProgress(100);
          setIsUploading(false);
          return;
        }

        // Single sheet - proceed with current logic
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        const processedData = processExcelData(jsonData as unknown[]);
        setRawData(processedData as any);

        // Clear interval
        clearInterval(interval);
        setUploadProgress(100);

        // Store Excel headers for manual mapping
        if (processedData.length > 0) {
          setExcelHeaders(Object.keys(processedData[0]));
          const detectedColumns = detectColumns(processedData[0]);
          setMappedColumns(detectedColumns);

          // Check if all required fields are mapped
          const requiredFields = ['title', 'firstName', 'lastName', 'email', 'maxYearLoad'];
          const mappedFields = Object.values(detectedColumns);
          const missingFields = requiredFields.filter(field => !mappedFields.includes(field));

          // If there are missing fields, show manual mapping
          if (missingFields.length > 0) {
            setShowManualMapping(true);
            setImportError(`Some required fields could not be automatically mapped: ${missingFields.map(f => getColumnMappingDisplayName(f)).join(', ')}. Please map them manually.`);
          } else {
            // Parse the data
            const parsedLecturers = parseExcelData(processedData, detectedColumns);

            // Log lecturer titles for debugging
            console.log('Parsed lecturers with titles:', parsedLecturers.map(lecturer => ({
              name: `${lecturer.firstName} ${lecturer.lastName}`,
              title: lecturer.title
            })));

            // Process the data (validation, formatting)
            const processedLecturerData = processLecturerData(parsedLecturers);
            setParsedData(processedLecturerData.validLecturers);

            // Combine both types of duplicates for display
            const allDuplicates = [...processedLecturerData.duplicates];

            // Add existing duplicates with a prefix to distinguish them
            if (processedLecturerData.existingDuplicates.length > 0) {
              setImportError(`Found ${processedLecturerData.existingDuplicates.length} lecturer emails that already exist in the system. These lecturers will be skipped during import.`);
              allDuplicates.push(...processedLecturerData.existingDuplicates);
            }

            setDuplicates(allDuplicates);
          }
        }

      } catch (error) {
        setImportError(`Error parsing Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsUploading(false);
      }
    };

    reader.onerror = () => {
      setImportError('Error reading file');
      setIsUploading(false);
      clearInterval(interval);
    };

    // Read as ArrayBuffer (modern approach, replaces deprecated readAsBinaryString)
    reader.readAsArrayBuffer(selectedFile);
  };

  // Update Excel data processing
  const processExcelData = (data: unknown[]): ExcelRowData[] => {
    return data.filter(isExcelRowData);
  };

  // Detect columns from the Excel file and map them to our data structure
  const detectColumns = (firstRow: ExcelRowData): Record<string, string> => {
    const columnMap: Record<string, string> = {};
    const keys = Object.keys(firstRow);

    // Map columns based on header names (case insensitive)
    keys.forEach(key => {
      const lowerKey = key.toLowerCase();

      // Title/prefix detection - improved to catch more variations
      if (lowerKey.includes('title') || lowerKey.includes('prefix') ||
          lowerKey === 'dr' || lowerKey === 'dr.' || lowerKey === 'prof' || lowerKey === 'prof.' ||
          lowerKey === 'mr' || lowerKey === 'mr.' || lowerKey === 'ms' || lowerKey === 'ms.' ||
          lowerKey === 'mrs' || lowerKey === 'mrs.' || lowerKey === 'miss' || lowerKey === 'miss.' ||
          lowerKey === 'title' || lowerKey === 'prefix' || lowerKey === 'salutation' ||
          lowerKey.includes('salutation') || lowerKey.includes('honorific') || lowerKey === 'honorific') {
        columnMap[key] = 'title';
      }
      // First name detection
      else if ((lowerKey.includes('first') && lowerKey.includes('name')) ||
               lowerKey === 'firstname' || lowerKey === 'first name' ||
               lowerKey === 'fname' || lowerKey === 'f name' ||
               (lowerKey.startsWith('first') && lowerKey.includes('name'))) {
        columnMap[key] = 'firstName';
      }
      // Last name detection
      else if ((lowerKey.includes('last') && lowerKey.includes('name')) ||
               lowerKey === 'lastname' || lowerKey === 'last name' ||
               lowerKey === 'lname' || lowerKey === 'l name' ||
               (lowerKey.startsWith('last') && lowerKey.includes('name')) ||
               lowerKey === 'surname' || lowerKey === 'family name') {
        columnMap[key] = 'lastName';
      }
      // Email detection
      else if (lowerKey.includes('email') || lowerKey.includes('e-mail') ||
               lowerKey === 'mail' || lowerKey.includes('contact email')) {
        columnMap[key] = 'email';
      }
      // Fall supervision hours detection
      else if (((lowerKey.includes('supervision') || lowerKey.includes('admin') ||
                lowerKey.includes('admin hours')) &&
                (lowerKey.includes('fall') || lowerKey.includes('semester 1') ||
                lowerKey.includes('sem 1') || lowerKey.includes('first semester')))) {
        columnMap[key] = 'supervisionHoursFall';
      }
      // Spring supervision hours detection
      else if (((lowerKey.includes('supervision') || lowerKey.includes('admin') ||
                lowerKey.includes('admin hours')) &&
                (lowerKey.includes('spring') || lowerKey.includes('semester 2') ||
                lowerKey.includes('sem 2') || lowerKey.includes('second semester')))) {
        columnMap[key] = 'supervisionHoursSpring';
      }
      // Max year load detection
      else if ((lowerKey.includes('max') && lowerKey.includes('load')) ||
               lowerKey.includes('maximum load') || lowerKey.includes('max yearly load') ||
               lowerKey.includes('max year load') || lowerKey.includes('maximum yearly load') ||
               lowerKey === 'max load' || lowerKey === 'yearly load') {
        columnMap[key] = 'maxYearLoad';
      }
      // Courses able to teach detection
      else if (lowerKey.includes('courses') || lowerKey.includes('teach') ||
               lowerKey.includes('can teach') || lowerKey.includes('able to teach') ||
               lowerKey.includes('teaching courses') || lowerKey.includes('course list')) {
        columnMap[key] = 'coursesAbleToTeach';
      }
      // Preferred timing detection
      else if ((lowerKey.includes('preferred') && (lowerKey.includes('tim') || lowerKey.includes('schedule'))) ||
               lowerKey.includes('time preference') || lowerKey.includes('schedule preference') ||
               lowerKey.includes('teaching time') || lowerKey === 'timing' ||
               lowerKey === 'preferred time' || lowerKey === 'time pref') {
        columnMap[key] = 'preferredTiming';
      }
      // Department detection
      else if (lowerKey === 'department' || lowerKey === 'dept' ||
               lowerKey.includes('department') || lowerKey.includes('dept.') ||
               lowerKey === 'academic unit' || lowerKey.includes('academic unit') ||
               lowerKey === 'faculty' || lowerKey.includes('faculty') ||
               lowerKey === 'school' || lowerKey.includes('school')) {
        columnMap[key] = 'department';
      }
    });

    return columnMap;
  };

  // Parse Excel data using the mapped columns
  const parseExcelData = (jsonData: ExcelRowData[], columnMapping: Record<string, string>): LecturerData[] => {
    return jsonData.map(row => {
      const getValue = (field: string): string => {
        const value = getValueByMappedField(row, columnMapping, field);
        return value?.toString() || '';
      };

      // Normalize title to match the allowed options in the dropdown
      let title = getValue('title') || 'Dr.';
      // Normalize title to match expected values
      if (typeof title === 'string') {
        const normalizedTitle = title.trim().toLowerCase();
        if (normalizedTitle.includes('prof') || normalizedTitle.startsWith('p.') || normalizedTitle === 'p') {
          title = 'Prof.';
        } else if (normalizedTitle.includes('dr') || normalizedTitle.startsWith('d.') || normalizedTitle === 'd') {
          title = 'Dr.';
        } else if (normalizedTitle.includes('mr') || normalizedTitle.startsWith('m.') || normalizedTitle === 'm') {
          title = 'Mr.';
        } else if (normalizedTitle.includes('ms') || normalizedTitle.includes('miss') || normalizedTitle.includes('mrs') ||
                  normalizedTitle.startsWith('ms.') || normalizedTitle === 'f') {
          title = 'Ms.';
        } else {
          title = 'Dr.'; // Default to Dr. for any other value
        }
      } else {
        title = 'Dr.'; // Default to Dr. if not a string
      }

      const lecturer: LecturerData = {
        title: title,
        firstName: getValue('firstName'),
        lastName: getValue('lastName'),
        email: getValue('email'),
        department: getValue('department') || '',
        supervisionHoursFall: Number(getValue('supervisionHoursFall')) || 0,
        supervisionHoursSpring: Number(getValue('supervisionHoursSpring')) || 0,
        maxYearLoad: Number(getValue('maxYearLoad')) || 18,
        preferredTiming: (getValue('preferredTiming') || 'Both') as 'Morning' | 'Evening' | 'Both',
        coursesAbleToTeach: parseCoursesString(getValue('coursesAbleToTeach')) || []
      };

      return lecturer;
    });
  };

  // Process the parsed data to validate and format
  const processLecturerData = (lecturers: LecturerData[]): {
    validLecturers: LecturerData[];
    duplicates: string[];
    existingDuplicates: string[];
  } => {
    const validLecturers: LecturerData[] = [];
    const seenEmails = new Set<string>();
    const duplicates: string[] = [];
    const existingDuplicates: string[] = [];

    // Create a set of existing lecturer emails (case insensitive)
    // Make sure to normalize all emails by trimming and converting to lowercase
    const existingEmails = new Set<string>(existingLecturerEmails.map(email => email.toLowerCase().trim()));

    lecturers.forEach(lecturer => {
      // Skip entries with missing required fields
      if (!lecturer.email) {
        return;
      }

      const normalizedEmail = lecturer.email.toLowerCase().trim();

      // Check for duplicates within the imported data
      if (seenEmails.has(normalizedEmail)) {
        duplicates.push(lecturer.email);
        return;
      }

      // Check for duplicates with existing lecturers
      if (existingEmails.has(normalizedEmail)) {
        existingDuplicates.push(lecturer.email);
        return;
      }

      // Add to valid lecturers with normalized email
      seenEmails.add(normalizedEmail);
      validLecturers.push({
        ...lecturer,
        email: normalizedEmail // Store the normalized version
      });
    });

    return { validLecturers, duplicates, existingDuplicates };
  };

  // Apply manual mapping and parse data
  const applyManualMapping = () => {
    // Parse the data with the manual mapping
    const parsedLecturers = parseExcelData(rawData, mappedColumns);

    // Log lecturer titles for debugging
    console.log('Parsed lecturers with titles:', parsedLecturers.map(lecturer => ({
      name: `${lecturer.firstName} ${lecturer.lastName}`,
      title: lecturer.title
    })));

    // Process the data (validation, formatting, duplicate checking)
    const processedData = processLecturerData(parsedLecturers);
    setParsedData(processedData.validLecturers);

    // Combine both types of duplicates for display
    const allDuplicates = [...processedData.duplicates];

    // Set appropriate error message based on duplicates found
    if (processedData.existingDuplicates.length > 0) {
      setImportError(`Found ${processedData.existingDuplicates.length} lecturer emails that already exist in the system. These lecturers will be skipped during import.`);
      allDuplicates.push(...processedData.existingDuplicates);
    } else if (processedData.duplicates.length > 0) {
      setImportError(`Found ${processedData.duplicates.length} duplicate lecturer emails within the imported data. Only the first occurrence of each email will be imported.`);
    } else {
      setImportError(null);
    }

    setDuplicates(allDuplicates);

    // Hide manual mapping
    setShowManualMapping(false);
  };

  // Handle column mapping change
  const handleColumnMappingChange = (excelColumn: string, ourField: string) => {
    setMappedColumns(prev => {
      const updated = { ...prev };

      // Remove any existing mapping for this Excel column
      Object.keys(updated).forEach(key => {
        if (key === excelColumn) {
          delete updated[key];
        }
      });

      // Add new mapping
      if (ourField) {
        updated[excelColumn] = ourField;
      }

      return updated;
    });
  };

  // Handle import button click
  const handleImport = () => {
    // Only import lecturers that don't already exist in the system
    // This is a safeguard in case the filtering in processLecturerData didn't work
    const uniqueLecturers = parsedData.filter(lecturer => {
      const normalizedEmail = lecturer.email.toLowerCase().trim();
      return !existingLecturerEmails.some(email =>
        email.toLowerCase().trim() === normalizedEmail
      );
    });

    onImport(uniqueLecturers);
    handleReset(); // Reset the modal state before closing
    onClose();
  };

  // Handle sheet selection and process the selected sheet
  const handleSheetSelection = () => {
    if (!workbookData || !selectedSheet) {
      setImportError('Please select a sheet to import');
      return;
    }

    try {
      const worksheet = workbookData.Sheets[selectedSheet];

      if (!worksheet) {
        throw new Error(`Sheet "${selectedSheet}" not found`);
      }

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      const processedData = processExcelData(jsonData as unknown[]);

      if (processedData.length === 0) {
        throw new Error(`No data found in sheet "${selectedSheet}"`);
      }

      // Store raw data and headers for potential manual mapping
      setRawData(processedData as any);
      setExcelHeaders(Object.keys(processedData[0]));
      const detectedColumns = detectColumns(processedData[0]);
      setMappedColumns(detectedColumns);

      // Check if all required fields are mapped
      const requiredFields = ['title', 'firstName', 'lastName', 'email', 'maxYearLoad'];
      const mappedFields = Object.values(detectedColumns);
      const missingFields = requiredFields.filter(field => !mappedFields.includes(field));

      // Hide sheet selection
      setShowSheetSelection(false);

      // If there are missing fields, show manual mapping
      if (missingFields.length > 0) {
        setShowManualMapping(true);
        setImportError(`Some required fields could not be automatically mapped: ${missingFields.map(f => getColumnMappingDisplayName(f)).join(', ')}. Please map them manually.`);
      } else {
        // Parse the data
        const parsedLecturers = parseExcelData(processedData, detectedColumns);

        // Log lecturer titles for debugging
        console.log('Parsed lecturers with titles:', parsedLecturers.map(lecturer => ({
          name: `${lecturer.firstName} ${lecturer.lastName}`,
          title: lecturer.title
        })));

        // Process the data (validation, formatting)
        const processedLecturerData = processLecturerData(parsedLecturers);
        setParsedData(processedLecturerData.validLecturers);

        // Combine both types of duplicates for display
        const allDuplicates = [...processedLecturerData.duplicates];

        // Add existing duplicates with a prefix to distinguish them
        if (processedLecturerData.existingDuplicates.length > 0) {
          setImportError(`Found ${processedLecturerData.existingDuplicates.length} lecturer emails that already exist in the system. These lecturers will be skipped during import.`);
          allDuplicates.push(...processedLecturerData.existingDuplicates);
        }

        setDuplicates(allDuplicates);
      }
    } catch (error) {
      setImportError(`Error processing sheet "${selectedSheet}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Reset the modal state
  const handleReset = () => {
    setFile(null);
    setIsUploading(false);
    setUploadProgress(0);
    setParsedData([]);
    setDuplicates([]);
    setMappedColumns({});
    setImportError(null);
    setAvailableSheets([]);
    setSelectedSheet('');
    setShowSheetSelection(false);
    setWorkbookData(null);
    setRawData([]);
    setExcelHeaders([]);
    setShowManualMapping(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Get column mapping display name
  const getColumnMappingDisplayName = (key: string) => {
    switch (key) {
      case 'title': return 'Title';
      case 'firstName': return 'First Name';
      case 'lastName': return 'Last Name';
      case 'email': return 'Email';
      case 'department': return 'Department';
      case 'supervisionHoursFall': return 'Supervision (Fall)';
      case 'supervisionHoursSpring': return 'Supervision (Spring)';
      case 'maxYearLoad': return 'Max Load';
      case 'coursesAbleToTeach': return 'Courses';
      case 'preferredTiming': return 'Preferred Timing';
      default: return key;
    }
  };

  // Helper function to get value by mapped field
  const getValueByMappedField = (
    row: ExcelRowData,
    columnMapping: Record<string, string>,
    field: string
  ): string | number => {
    const mappedColumn = Object.keys(columnMapping).find(key => columnMapping[key] === field);
    return mappedColumn ? row[mappedColumn] : '';
  };

  // Helper function to parse courses string
  const parseCoursesString = (coursesString: string | undefined) => {
    if (!coursesString) {
      return [];
    }

    const courses = coursesString.split(/[,;]/).map((course: string) => course.trim()).filter(Boolean);
    return courses;
  };

  return (
    <AccessibleDialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            minHeight: '60vh'
          }
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          Import Lecturers from Excel
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Upload an Excel file with lecturer data to import
        </Typography>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        {!file ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px dashed #ccc',
              borderRadius: 2,
              p: 5,
              minHeight: '200px',
              bgcolor: 'background.paper'
            }}
          >
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              ref={fileInputRef}
              style={{ display: 'none' }}
            />
            <CloudUploadIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Drag & Drop or Select File
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
              Supported formats: .xlsx, .xls
            </Typography>
            <Button
              variant="contained"
              onClick={handleChooseFile}
              startIcon={<CloudUploadIcon />}
            >
              Choose File
            </Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {/* Left column - File info and status */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  File Information
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Name:</strong> {file.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Size:</strong> {(file.size / 1024).toFixed(2)} KB
                  </Typography>
                </Box>

                {isUploading ? (
                  <Box sx={{ width: '100%', mt: 2 }}>
                    <LinearProgress variant="determinate" value={uploadProgress} />
                    <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                      Processing file... {uploadProgress}%
                    </Typography>
                  </Box>
                ) : importError ? (
                  <Alert severity="error" icon={<ErrorIcon fontSize="inherit" />} sx={{ mt: 2 }}>
                    {importError}
                  </Alert>
                ) : (
                  <Alert severity="success" icon={<CheckCircleIcon fontSize="inherit" />} sx={{ mt: 2 }}>
                    File processed successfully
                  </Alert>
                )}

                {duplicates.length > 0 && !isUploading && (
                  <Alert
                    severity="warning"
                    icon={<InfoIcon fontSize="inherit" />}
                    sx={{ mt: 2 }}
                  >
                    Found {duplicates.length} duplicate emails. Only the first occurrence of each email will be imported.
                  </Alert>
                )}

                <Box sx={{ mt: 3 }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleReset}
                    fullWidth
                  >
                    Upload Different File
                  </Button>
                </Box>
              </Paper>
            </Grid>

            {/* Right column - Sheet selection, manual mapping, or data preview */}
            <Grid size={{ xs: 12, md: 8 }}>
              {showSheetSelection ? (
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Select Sheet to Import
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    This Excel file contains multiple sheets. Please select which sheet you want to import data from.
                  </Typography>

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Available Sheets:</strong>
                    </Typography>
                    <FormControl fullWidth>
                      <InputLabel id="sheet-select-label">Select Sheet</InputLabel>
                      <Select
                        labelId="sheet-select-label"
                        value={selectedSheet}
                        label="Select Sheet"
                        onChange={(e: SelectChangeEvent) => setSelectedSheet(e.target.value)}
                      >
                        {availableSheets.map((sheetName) => (
                          <MenuItem key={sheetName} value={sheetName}>
                            {sheetName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={handleReset}
                    >
                      Choose Different File
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSheetSelection}
                      disabled={!selectedSheet}
                    >
                      Continue with Selected Sheet
                    </Button>
                  </Box>
                </Paper>
              ) : showManualMapping ? (
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Manual Column Mapping
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Please map the Excel columns to the corresponding fields in the application.
                  </Typography>

                  <TableContainer component={Paper} variant="outlined" sx={{ mt: 2, mb: 3 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow sx={{ bgcolor: 'background.paper' }}>
                          <TableCell><strong>Excel Column</strong></TableCell>
                          <TableCell><strong>Application Field</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {excelHeaders.map((header) => (
                          <TableRow key={header}>
                            <TableCell>{header}</TableCell>
                            <TableCell>
                              <FormControl fullWidth size="small">
                                <Select
                                  value={mappedColumns[header] || ''}
                                  onChange={(e: SelectChangeEvent) =>
                                    handleColumnMappingChange(header, e.target.value)
                                  }
                                  displayEmpty
                                >
                                  <MenuItem value=""><em>Not mapped</em></MenuItem>
                                  <MenuItem value="title">Title</MenuItem>
                                  <MenuItem value="firstName">First Name</MenuItem>
                                  <MenuItem value="lastName">Last Name</MenuItem>
                                  <MenuItem value="email">Email</MenuItem>
                                  <MenuItem value="department">Department</MenuItem>
                                  <MenuItem value="supervisionHoursFall">Supervision Hours (Fall)</MenuItem>
                                  <MenuItem value="supervisionHoursSpring">Supervision Hours (Spring)</MenuItem>
                                  <MenuItem value="maxYearLoad">Max Year Load</MenuItem>
                                  <MenuItem value="coursesAbleToTeach">Courses Able to Teach</MenuItem>
                                  <MenuItem value="preferredTiming">Preferred Timing</MenuItem>
                                </Select>
                              </FormControl>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={applyManualMapping}
                    >
                      Apply Mapping
                    </Button>
                  </Box>
                </Paper>
              ) : parsedData.length > 0 ? (
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Preview ({parsedData.length} lecturers)
                  </Typography>

                  {duplicates.length > 0 && (
                    <Alert
                      severity="warning"
                      icon={<InfoIcon fontSize="inherit" />}
                      sx={{ mb: 2 }}
                    >
                      Found {duplicates.length} duplicate lecturer emails (either within the imported data or already existing in the system).
                      Only unique lecturers that don't already exist in the system will be imported.
                    </Alert>
                  )}

                  <TableContainer sx={{ maxHeight: 400 }}>
                    <Table stickyHeader size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>Name</strong></TableCell>
                          <TableCell><strong>Email</strong></TableCell>
                          <TableCell><strong>Department</strong></TableCell>
                          <TableCell><strong>Max Load</strong></TableCell>
                          <TableCell><strong>Courses</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {parsedData.map((lecturer) => (
                          <TableRow key={lecturer.email}>
                            <TableCell>
                              <strong>{lecturer.title}</strong> {lecturer.firstName} {lecturer.lastName}
                            </TableCell>
                            <TableCell>{lecturer.email}</TableCell>
                            <TableCell>{lecturer.department || '-'}</TableCell>
                            <TableCell>{lecturer.maxYearLoad}</TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {lecturer.coursesAbleToTeach.map((course, i) => (
                                  <Chip
                                    key={i}
                                    label={course}
                                    size="small"
                                    color={availableCourses.includes(course) ? "primary" : "default"}
                                    variant="outlined"
                                  />
                                ))}
                                {lecturer.coursesAbleToTeach.length === 0 && (
                                  <Typography variant="body2" color="text.secondary">
                                    None specified
                                  </Typography>
                                )}
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Column Mapping:</strong>
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      {Object.entries(mappedColumns).map(([excelCol, ourField]) => (
                        <Chip
                          key={excelCol}
                          label={`${excelCol} → ${getColumnMappingDisplayName(ourField)}`}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                    <Button
                      variant="text"
                      color="primary"
                      size="small"
                      onClick={() => setShowManualMapping(true)}
                      sx={{ mt: 1 }}
                    >
                      Edit Mapping
                    </Button>
                  </Box>
                </Paper>
              ) : null}
            </Grid>
          </Grid>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleImport}
          variant="contained"
          color="primary"
          disabled={parsedData.length === 0 || showManualMapping || showSheetSelection}
        >
          Import {parsedData.length} Lecturers
        </Button>
      </DialogActions>
    </AccessibleDialog>
  );
}

export default LecturerImportModal;
