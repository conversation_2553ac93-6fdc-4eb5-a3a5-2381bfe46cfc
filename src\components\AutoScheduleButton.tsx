import React from 'react';
import { Tooltip } from '@mui/material';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import { Section } from '../types/models';
import {
  autoScheduleSection,
  AutoSession,
  AppSession,
  convertAutoSessionsToSessions,
  startPerformanceProfiling,
  resetPerformanceMetrics
} from '../utils/autoScheduling';
import {
  Session as RuleSession,
  getAcademicLevel,
  isLecturerTeachingInDay,
  getLecturerTeachingDaysCount
} from '../utils/ruleValidation';
import { useRuleSystemStore } from '../store/ruleSystem';
import { useAppContext } from '../context/AppContext';

interface AutoScheduleButtonProps {
  section: Section;
  courseCode: string;
  courseType: 'Theory' | 'Lab';
  contactHours: number;
  gender: 'M' | 'F';
  lecturerId: string;
  existingSessions: RuleSession[];
  onScheduleSuccess: (appSessions: AppSession[]) => void;
  onScheduleError: (message: string) => void;
}

const AutoScheduleButton: React.FC<AutoScheduleButtonProps> = ({
  section,
  courseCode,
  courseType,
  contactHours,
  gender,
  lecturerId,
  existingSessions,
  onScheduleSuccess,
  onScheduleError
}) => {
  // Get lecturers from app context
  const { lecturers } = useAppContext();

  const handleAutoSchedule = () => {
    // Start performance monitoring
    resetPerformanceMetrics();
    startPerformanceProfiling();

    // Get rule system state
    const ruleSystemState = useRuleSystemStore.getState();
    const { maxSessionsPerTimeslot } = ruleSystemState;

    // Create a section object compatible with the autoScheduleSection function
    const sectionForScheduling = {
      id: section.id,
      courseCode,
      courseType,
      contactHours,
      gender,
      lecturerId,
      academicLevel: getAcademicLevel(courseCode)
    };

    // Convert existing sessions to the format expected by autoScheduleSection
    const convertedExistingSessions: AutoSession[] = existingSessions.map(session => ({
      id: session.id,
      sectionId: session.sectionId,
      courseCode: session.courseCode || '', // Include courseCode for proper validation
      courseType: session.courseType || 'Theory', // Default to 'Theory' if undefined
      academicLevel: session.academicLevel || 'unknown', // Default to 'unknown' if undefined
      gender: session.gender || 'M', // Default to 'M' if undefined
      lecturerId: session.lecturerId,
      day: session.day,
      period: session.period || session.startPeriod, // Use period if available, otherwise startPeriod
      isAutoGenerated: false
    }));

    // Get the academic level from the course code
    const academicLevel = getAcademicLevel(courseCode);

    // Determine if this is an undergraduate theory course
    const isUndergraduateTheory =
      courseType === 'Theory' &&
      !academicLevel.includes('diploma') &&
      !academicLevel.includes('masters') &&
      !academicLevel.includes('phd');

    // If this is an undergraduate theory course, check if there are any timeslots that have reached the maximum
    if (isUndergraduateTheory) {

      // Get all days and periods
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu'];
      const periods = Array.from({ length: 12 }, (_, i) => i + 1);

      // Create a map to track timeslots that have reached maximum capacity
      const fullTimeslots = new Map<string, boolean>();

      // Check each timeslot
      days.forEach(day => {
        periods.forEach(period => {
          const timeslotKey = `${day}-${period}`;
          const maxAllowed = maxSessionsPerTimeslot[timeslotKey] || 0;

          if (maxAllowed > 0) {
            const sessionsInTimeslot = convertedExistingSessions.filter(
              s => s.day === day &&
                   s.period === period &&
                   s.courseType === 'Theory' &&
                   !s.academicLevel.includes('diploma') &&
                   !s.academicLevel.includes('masters') &&
                   !s.academicLevel.includes('phd')
            );

            if (sessionsInTimeslot.length >= maxAllowed) {
              fullTimeslots.set(timeslotKey, true);
            }
          }
        });
      });



      // Filter out sessions in full timeslots from the result
      const filterFullTimeslots = (sessions: AutoSession[]): AutoSession[] => {
        return sessions.filter(session => {
          const timeslotKey = `${session.day}-${session.period}`;
          return !fullTimeslots.has(timeslotKey);
        });
      };

      // Create a modified version of the existing schedule that marks full timeslots as blocked
      const modifiedExistingSessions = [...convertedExistingSessions];

      // We're not going to block any timeslots for now, just use the existing schedule

      // Auto-schedule the section with the modified schedule, lecturers, and current semester
      const result = autoScheduleSection(sectionForScheduling, modifiedExistingSessions, lecturers);

      if (result.success) {
        // Double-check that none of the sessions are in full timeslots
        const filteredSessions = filterFullTimeslots(result.sessions);

        if (filteredSessions.length > 0) {
          // Convert filtered sessions to app sessions
          const filteredAppSessions = filteredSessions.map(session => {
            return result.appSessions.find(appSession => appSession.id === session.id);
          }).filter(Boolean) as AppSession[];

          onScheduleSuccess(filteredAppSessions);
        } else {
          onScheduleError('All available timeslots have reached maximum capacity');
        }
      } else {
        onScheduleError(result.message || 'Failed to auto-schedule section');
      }
    } else {
      // For non-undergraduate theory courses, proceed normally
      const result = autoScheduleSection(sectionForScheduling, convertedExistingSessions, lecturers);

      if (result.success) {
        // Check for any lecturer conflicts before adding to the schedule
        // IMPORTANT: Ensure a lecturer is either assigned to ALL sessions or NONE
        // First, check if any session has a lecturer assigned
        const hasLecturer = result.sessions.some(session => session.lecturerId);

        if (hasLecturer) {
          // Get the lecturer ID from the first session
          const lecturerId = result.sessions.find(s => s.lecturerId)?.lecturerId || '';

          // Check if this lecturer can be assigned to ALL sessions
          let canAssignLecturerToAll = true;

          // Check each session for conflicts
          for (const session of result.sessions) {
            // 1. Check for direct timeslot conflicts
            const lecturerConflicts = convertedExistingSessions.filter(
              s => s.lecturerId === lecturerId &&
                   s.day === session.day &&
                   s.period === session.period
            );

            if (lecturerConflicts.length > 0) {
              canAssignLecturerToAll = false;
              break;
            }

            // 2. Check for maximum teaching days
            // Convert AutoSession[] to Session[] for type compatibility
            const convertedSessions = convertAutoSessionsToSessions(convertedExistingSessions);
            if (!isLecturerTeachingInDay(lecturerId, session.day, convertedSessions)) {
              const currentDaysCount = getLecturerTeachingDaysCount(lecturerId, convertedSessions);
              const lecturer = lecturers.find(l => l.id === lecturerId);
              const maxDays = lecturer?.maxTeachingDaysPerWeek || 5;

              if (currentDaysCount >= maxDays) {
                canAssignLecturerToAll = false;
                break;
              }
            }
          }

          // If we can't assign the lecturer to all sessions, remove from all
          if (!canAssignLecturerToAll) {
            console.log(`IMPORTANT: Cannot assign lecturer ${lecturerId} to ALL sessions of this section`);
            console.log(`Removing lecturer assignment from ALL sessions`);

            // Create copies of all sessions without the lecturer
            const sessionsWithoutConflicts = result.sessions.map(session => ({
              ...session,
              lecturerId: '' // Remove lecturer assignment from all sessions
            }));

            return sessionsWithoutConflicts;
          }
        }

        // If we get here, either there's no lecturer assigned or the lecturer can be assigned to all sessions
        const sessionsWithoutConflicts = result.sessions;

        // Update the app sessions to match the modified sessions
        const appSessionsWithoutConflicts = sessionsWithoutConflicts.map(session => {
          return result.appSessions.find(appSession => appSession.id === session.id);
        }).filter(Boolean) as AppSession[];

        onScheduleSuccess(appSessionsWithoutConflicts);
      } else {
        onScheduleError(result.message || 'Failed to auto-schedule section');
      }
    }
  };

  // Determine if this is a postgraduate course
  const academicLevel = getAcademicLevel(courseCode);
  const isPostgraduate = academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma';

  // Get the rule system state to check if relevant pattern rules are enabled
  const { rules } = useRuleSystemStore.getState();

  // Determine which pattern rule applies to this section
  let patternRuleId = '';
  if (isPostgraduate && contactHours === 3) {
    patternRuleId = 'postgrad-pattern';
  } else if (!isPostgraduate) {
    // Undergraduate course patterns
    switch (contactHours) {
      case 2:
        patternRuleId = 'undergrad-2ch-pattern';
        break;
      case 3:
        patternRuleId = 'undergrad-3ch-pattern';
        break;
      case 4:
        patternRuleId = 'undergrad-4ch-pattern';
        break;
      case 5:
        patternRuleId = 'undergrad-5ch-pattern';
        break;
      default:
        // No specific pattern rule for other credit hours
        break;
    }
  }

  // Check if the relevant pattern rule is enabled
  const isPatternRuleEnabled = patternRuleId
    ? rules.find(rule => rule.id === patternRuleId)?.enabled ?? false
    : false;



  // Hide auto-schedule button for:
  // 1. Lab courses
  // 2. Postgraduate courses with contact hours != 3
  // 3. When the relevant pattern rule is disabled
  const shouldShowButton =
    courseType === 'Theory' &&
    (!isPostgraduate || (isPostgraduate && contactHours === 3)) &&
    (patternRuleId === '' || isPatternRuleEnabled);

  if (!shouldShowButton) {
    return null;
  }

  return (
    <Tooltip title="Auto-schedule this section">
      <div
        onClick={(e) => {
          e.stopPropagation();
          handleAutoSchedule();
        }}
        className="text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 cursor-pointer"
      >
        <AutoFixHighIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
      </div>
    </Tooltip>
  );
};

export default AutoScheduleButton;
