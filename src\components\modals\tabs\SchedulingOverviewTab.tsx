import React, { useMemo, useRef } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,

  LinearProgress,
  IconButton
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { Section, Session, Semester } from '../../../types/models';
import { exportCardAsPdf } from '../../../utils/exportUtils';

// Props for the SchedulingOverviewTab component
interface SchedulingOverviewTabProps {
  sections: Record<Semester, Section[]>;
  sessions: Record<Semester, Session[]>;
  currentSemester: Semester;
}

const SchedulingOverviewTab: React.FC<SchedulingOverviewTabProps> = ({
  sections,
  sessions,
  currentSemester
}) => {
  // Create refs for the cards
  const viewDistributionRef = useRef<HTMLDivElement>(null);
  const dayDistributionRef = useRef<HTMLDivElement>(null);
  const periodDistributionRef = useRef<HTMLDivElement>(null);
  const timeDistributionRef = useRef<HTMLDivElement>(null);

  // Handle export to PDF
  const handleExportToPdf = (element: HTMLElement, title: string, fileName: string) => {
    exportCardAsPdf(title, element, fileName);
  };
  // Calculate scheduling statistics
  const schedulingStats = useMemo(() => {
    const currentSections = sections[currentSemester];
    const currentSessions = sessions[currentSemester];

    // Calculate scheduled vs unscheduled sections
    const scheduledSectionIds = new Set(currentSessions.map(session => session.sectionId));
    const scheduledSections = currentSections.filter(section => scheduledSectionIds.has(section.id)).length;
    const unscheduledSections = currentSections.length - scheduledSections;

    // Calculate day distribution
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];
    const dayDistribution = days.map(day => {
      const sessionsOnDay = currentSessions.filter(session => session.day === day).length;
      return {
        day,
        count: sessionsOnDay,
        percentage: currentSessions.length > 0
          ? (sessionsOnDay / currentSessions.length) * 100
          : 0
      };
    });

    // Calculate period distribution
    const periodCounts = Array(12).fill(0);

    currentSessions.forEach(session => {
      for (let p = session.startPeriod; p <= session.endPeriod; p++) {
        if (p >= 1 && p <= 12) {
          periodCounts[p - 1]++;
        }
      }
    });

    const periodDistribution = periodCounts.map((count, index) => {
      return {
        period: index + 1,
        count,
        percentage: currentSessions.length > 0
          ? (count / currentSessions.length) * 100
          : 0
      };
    });

    // Find peak scheduling times
    const peakPeriod = [...periodDistribution].sort((a, b) => b.count - a.count)[0];
    const peakDay = [...dayDistribution].sort((a, b) => b.count - a.count)[0];

    // Calculate time of day distribution
    const morningPeriods = [1, 2, 3, 4, 5, 6];
    const eveningPeriods = [7, 8, 9, 10, 11, 12];

    const morningSessionsCount = currentSessions.filter(session =>
      morningPeriods.includes(session.startPeriod)
    ).length;

    const eveningSessionsCount = currentSessions.filter(session =>
      eveningPeriods.includes(session.startPeriod)
    ).length;

    // Calculate view type distribution
    const weekViewCount = currentSessions.filter(session => session.viewType === 'week').length;
    const regularViewCount = currentSessions.filter(session => session.viewType === 'regular').length;
    const longViewCount = currentSessions.filter(session => session.viewType === 'long').length;

    // Calculate regular days vs long days distribution
    const regularDays = ['Sunday', 'Tuesday', 'Thursday'];
    const longDays = ['Monday', 'Wednesday'];

    const regularDaySessionsCount = currentSessions.filter(session =>
      regularDays.includes(session.day)
    ).length;

    const longDaySessionsCount = currentSessions.filter(session =>
      longDays.includes(session.day)
    ).length;

    return {
      totalSections: currentSections.length,
      scheduledSections,
      unscheduledSections,
      scheduledPercentage: currentSections.length > 0
        ? (scheduledSections / currentSections.length) * 100
        : 0,
      dayDistribution,
      periodDistribution,
      peakPeriod,
      peakDay,
      morningSessionsCount,
      eveningSessionsCount,
      morningPercentage: currentSessions.length > 0
        ? (morningSessionsCount / currentSessions.length) * 100
        : 0,
      eveningPercentage: currentSessions.length > 0
        ? (eveningSessionsCount / currentSessions.length) * 100
        : 0,
      weekViewCount,
      regularViewCount,
      longViewCount,
      weekViewPercentage: currentSessions.length > 0
        ? (weekViewCount / currentSessions.length) * 100
        : 0,
      regularViewPercentage: currentSessions.length > 0
        ? (regularViewCount / currentSessions.length) * 100
        : 0,
      longViewPercentage: currentSessions.length > 0
        ? (longViewCount / currentSessions.length) * 100
        : 0,
      regularDaySessionsCount,
      longDaySessionsCount,
      regularDayPercentage: currentSessions.length > 0
        ? (regularDaySessionsCount / currentSessions.length) * 100
        : 0,
      longDayPercentage: currentSessions.length > 0
        ? (longDaySessionsCount / currentSessions.length) * 100
        : 0
    };
  }, [sections, sessions, currentSemester]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Summary Cards */}
      <Grid container spacing={1}>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Scheduling Progress
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <Typography variant="h5" color="primary" sx={{ mr: 1 }}>
                  {schedulingStats.scheduledPercentage.toFixed(1)}%
                </Typography>
                <Box sx={{ flexGrow: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={schedulingStats.scheduledPercentage}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </Box>
              <Typography variant="caption" display="block">
                {schedulingStats.scheduledSections} of {schedulingStats.totalSections} sections scheduled
              </Typography>
              <Typography variant="caption" display="block" color="error">
                {schedulingStats.unscheduledSections} sections unscheduled
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Peak Scheduling
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h6" color="primary" sx={{ mb: 0 }}>
                    {schedulingStats.peakDay?.day || 'N/A'}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Busiest Day
                  </Typography>
                  <Typography variant="caption" display="block">
                    {schedulingStats.peakDay?.count || 0} sessions
                    ({schedulingStats.peakDay?.percentage.toFixed(1) || 0}%)
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="h6" color="secondary" sx={{ mb: 0 }}>
                    Period {schedulingStats.peakPeriod?.period || 'N/A'}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Busiest Period
                  </Typography>
                  <Typography variant="caption" display="block">
                    {schedulingStats.peakPeriod?.count || 0} sessions
                    ({schedulingStats.peakPeriod?.percentage.toFixed(1) || 0}%)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card variant="outlined" sx={{ height: '100%' }} ref={viewDistributionRef}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  View Distribution
                </Typography>
                <IconButton
                  size="small"
                  title="Export as PDF"
                  sx={{ p: 0.5 }}
                  onClick={() => viewDistributionRef.current && handleExportToPdf(viewDistributionRef.current, 'View Distribution', 'view_distribution.pdf')}
                >
                  <FileDownloadIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box sx={{ mb: 0.5 }}>
                <Typography variant="caption" display="block">
                  Regular Days (Su, Tu, Th): {schedulingStats.regularDaySessionsCount}
                  ({schedulingStats.regularDayPercentage.toFixed(1)}%)
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={schedulingStats.regularDayPercentage}
                  color="primary"
                  sx={{ height: 4, borderRadius: 2, mb: 0.5 }}
                />
              </Box>
              <Box>
                <Typography variant="caption" display="block">
                  Long Days (Mo, Wed): {schedulingStats.longDaySessionsCount}
                  ({schedulingStats.longDayPercentage.toFixed(1)}%)
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={schedulingStats.longDayPercentage}
                  color="secondary"
                  sx={{ height: 4, borderRadius: 2 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Day Distribution */}
      <Card variant="outlined" ref={dayDistributionRef}>
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Day Distribution
            </Typography>
            <IconButton
              size="small"
              title="Export as PDF"
              sx={{ p: 0.5 }}
              onClick={() => dayDistributionRef.current && handleExportToPdf(dayDistributionRef.current, 'Day Distribution', 'day_distribution.pdf')}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <Grid container spacing={1}>
            {schedulingStats.dayDistribution.map(day => (
              <Grid size={{ xs: 12, sm: 2.4 }} key={day.day}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>{day.count}</Typography>
                  <Typography variant="caption" display="block">{day.day}</Typography>
                  <Typography variant="caption" display="block">
                    {day.percentage.toFixed(1)}%
                  </Typography>
                  <Box
                    sx={{
                      height: 6,
                      width: '100%',
                      backgroundColor: '#e0e0e0',
                      borderRadius: 3,
                      mt: 0.5
                    }}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        width: `${day.percentage}%`,
                        backgroundColor: 'primary.main',
                        borderRadius: 3
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Period Distribution */}
      <Card variant="outlined" ref={periodDistributionRef}>
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Period Distribution
            </Typography>
            <IconButton
              size="small"
              title="Export as PDF"
              sx={{ p: 0.5 }}
              onClick={() => periodDistributionRef.current && handleExportToPdf(periodDistributionRef.current, 'Period Distribution', 'period_distribution.pdf')}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <Grid container spacing={1}>
            {schedulingStats.periodDistribution.map(period => (
              <Grid size={{ xs: 3, sm: 2, md: 1 }} key={period.period}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold' }}>{period.count}</Typography>
                  <Typography variant="caption" display="block">P{period.period}</Typography>
                  <Typography variant="caption" display="block">
                    {period.percentage.toFixed(1)}%
                  </Typography>
                  <Box
                    sx={{
                      height: 40,
                      display: 'flex',
                      alignItems: 'flex-end',
                      justifyContent: 'center',
                      mt: 0.5
                    }}
                  >
                    <Box
                      sx={{
                        width: 12,
                        height: `${Math.max(5, period.percentage)}%`,
                        backgroundColor: period.period <= 6 ? 'primary.main' : 'secondary.main',
                        borderRadius: '2px 2px 0 0'
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Time of Day Distribution */}
      <Card variant="outlined" ref={timeDistributionRef}>
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Time of Day Distribution
            </Typography>
            <IconButton
              size="small"
              title="Export as PDF"
              sx={{ p: 0.5 }}
              onClick={() => timeDistributionRef.current && handleExportToPdf(timeDistributionRef.current, 'Time of Day Distribution', 'time_distribution.pdf')}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <Grid container spacing={1}>
            <Grid size={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary" sx={{ mb: 0 }}>
                  {schedulingStats.morningSessionsCount}
                </Typography>
                <Typography variant="caption" display="block">Morning Sessions</Typography>
                <Typography variant="caption" display="block">
                  {schedulingStats.morningPercentage.toFixed(1)}% of all sessions
                </Typography>
                <Box
                  sx={{
                    height: 8,
                    width: '100%',
                    backgroundColor: '#e0e0e0',
                    borderRadius: 4,
                    mt: 0.5
                  }}
                >
                  <Box
                    sx={{
                      height: '100%',
                      width: `${schedulingStats.morningPercentage}%`,
                      backgroundColor: 'primary.main',
                      borderRadius: 4
                    }}
                  />
                </Box>
              </Box>
            </Grid>
            <Grid size={6}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="secondary" sx={{ mb: 0 }}>
                  {schedulingStats.eveningSessionsCount}
                </Typography>
                <Typography variant="caption" display="block">Evening Sessions</Typography>
                <Typography variant="caption" display="block">
                  {schedulingStats.eveningPercentage.toFixed(1)}% of all sessions
                </Typography>
                <Box
                  sx={{
                    height: 8,
                    width: '100%',
                    backgroundColor: '#e0e0e0',
                    borderRadius: 4,
                    mt: 0.5
                  }}
                >
                  <Box
                    sx={{
                      height: '100%',
                      width: `${schedulingStats.eveningPercentage}%`,
                      backgroundColor: 'secondary.main',
                      borderRadius: 4
                    }}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default SchedulingOverviewTab;
