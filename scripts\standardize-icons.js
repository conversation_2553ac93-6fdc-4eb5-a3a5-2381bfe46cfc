/**
 * QU Scheduler Icon Standardization Script
 * 
 * This script standardizes all icons across the QU Scheduler ecosystem using
 * assets/icons/icon.ico as the single source of truth.
 * 
 * Tasks:
 * 1. Clean up the assets/icons/ folder
 * 2. Generate required icon formats and sizes
 * 3. Update application references
 * 4. Update website integration
 * 5. Organize files with clear naming convention
 */

const fs = require('fs');
const path = require('path');

// Configuration
const MASTER_ICON_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.ico');
const MASTER_SVG_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.svg');
const ICONS_DIR = path.join(__dirname, '..', 'assets', 'icons');
const WEBSITE_ASSETS_DIR = path.join(__dirname, '..', 'website', 'assets');

// Required icon sizes for different platforms
const ICON_SIZES = [16, 24, 32, 48, 64, 128, 256, 512];

// Files to keep (essential files)
const ESSENTIAL_FILES = [
    'icon.ico',
    'icon.svg',
    'CONVERSION_INSTRUCTIONS.md'
];

// Files to remove (cleanup)
const FILES_TO_REMOVE = [
    'convert-icons.js',
    'converter.html',
    'create-icons.js',
    'create-png-ico.js',
    'create-working-ico.js',
    'header.svg',
    'welcome.svg',
    'preview.html',
    'render-256.html'
];

// HTML template files to remove
const HTML_TEMPLATES_TO_REMOVE = [
    'icon-16.html',
    'icon-32.html',
    'icon-48.html',
    'icon-64.html',
    'icon-128.html',
    'icon-256.html',
    'icon-512.html'
];

// PNG files to standardize
const PNG_FILES_TO_STANDARDIZE = [
    'icon-16x16.png',
    'icon-24x24.png',
    'icon-32x32.png',
    'icon-48x48.png',
    'icon-64x64.png',
    'icon-128x128.png',
    'icon-256x256.png'
];

function log(message) {
    console.log(`🔧 ${message}`);
}

function logSuccess(message) {
    console.log(`✅ ${message}`);
}

function logWarning(message) {
    console.log(`⚠️  ${message}`);
}

function logError(message) {
    console.log(`❌ ${message}`);
}

// Step 1: Clean up the assets/icons/ folder
function cleanupIconsFolder() {
    log('Cleaning up assets/icons/ folder...');
    
    let removedCount = 0;
    
    // Remove unnecessary script files
    FILES_TO_REMOVE.forEach(filename => {
        const filePath = path.join(ICONS_DIR, filename);
        if (fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
                removedCount++;
                log(`Removed: ${filename}`);
            } catch (error) {
                logError(`Failed to remove ${filename}: ${error.message}`);
            }
        }
    });
    
    // Remove HTML template files
    HTML_TEMPLATES_TO_REMOVE.forEach(filename => {
        const filePath = path.join(ICONS_DIR, filename);
        if (fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
                removedCount++;
                log(`Removed: ${filename}`);
            } catch (error) {
                logError(`Failed to remove ${filename}: ${error.message}`);
            }
        }
    });
    
    // Remove icon.iconset directory if it exists
    const iconsetPath = path.join(ICONS_DIR, 'icon.iconset');
    if (fs.existsSync(iconsetPath)) {
        try {
            fs.rmSync(iconsetPath, { recursive: true, force: true });
            removedCount++;
            log('Removed: icon.iconset directory');
        } catch (error) {
            logError(`Failed to remove icon.iconset: ${error.message}`);
        }
    }
    
    logSuccess(`Cleanup complete. Removed ${removedCount} unnecessary files.`);
}

// Step 2: Verify master icon files exist
function verifyMasterIcons() {
    log('Verifying master icon files...');
    
    if (!fs.existsSync(MASTER_ICON_PATH)) {
        logError(`Master icon file not found: ${MASTER_ICON_PATH}`);
        return false;
    }
    
    if (!fs.existsSync(MASTER_SVG_PATH)) {
        logError(`Master SVG file not found: ${MASTER_SVG_PATH}`);
        return false;
    }
    
    logSuccess('Master icon files verified.');
    return true;
}

// Step 3: Create standardized PNG files
function createStandardizedPNGs() {
    log('Creating standardized PNG files...');
    
    // For now, we'll create placeholder PNG files with proper naming
    // In a production environment, you would use a library like sharp or jimp
    // to convert the SVG to PNG at different sizes
    
    ICON_SIZES.forEach(size => {
        const filename = `icon-${size}x${size}.png`;
        const filePath = path.join(ICONS_DIR, filename);
        
        // Create a placeholder file that indicates the required size
        const placeholderContent = `PNG placeholder for ${size}x${size} icon - Convert from icon.svg`;
        
        try {
            fs.writeFileSync(filePath, placeholderContent);
            log(`Created placeholder: ${filename}`);
        } catch (error) {
            logError(`Failed to create ${filename}: ${error.message}`);
        }
    });
    
    logSuccess('PNG placeholders created. Use image conversion tools to generate actual PNG files.');
}

// Step 4: Create website favicon
function createWebsiteFavicon() {
    log('Creating website favicon...');
    
    const faviconPath = path.join(WEBSITE_ASSETS_DIR, 'favicon.ico');
    
    try {
        // Copy the master icon as favicon
        fs.copyFileSync(MASTER_ICON_PATH, faviconPath);
        logSuccess('Website favicon created.');
    } catch (error) {
        logError(`Failed to create favicon: ${error.message}`);
    }
}

// Step 5: Update website logo
function updateWebsiteLogo() {
    log('Updating website logo...');
    
    const websiteLogoPath = path.join(WEBSITE_ASSETS_DIR, 'qu-scheduler-logo.svg');
    
    try {
        // Copy the master SVG as website logo
        fs.copyFileSync(MASTER_SVG_PATH, websiteLogoPath);
        logSuccess('Website logo updated.');
    } catch (error) {
        logError(`Failed to update website logo: ${error.message}`);
    }
}

// Step 6: Create icon documentation
function createIconDocumentation() {
    log('Creating icon documentation...');
    
    const documentation = `# QU Scheduler Icon Standards

## Master Icon Files

### Primary Source
- **File**: \`icon.ico\` - Windows ICO format (32x32, 256 colors)
- **Usage**: Windows application icon, installer icon, taskbar icon
- **Source**: Single source of truth for all QU Scheduler icons

### Vector Source
- **File**: \`icon.svg\` - Scalable Vector Graphics
- **Usage**: Website logo, high-resolution displays, print materials
- **Features**: Qatar University brand colors, academic building design

## Generated Icon Files

### PNG Files (Generated from SVG)
${ICON_SIZES.map(size => `- \`icon-${size}x${size}.png\` - ${size}×${size} pixels`).join('\n')}

### Platform-Specific Files
- **Windows**: \`icon.ico\` (multiple sizes embedded)
- **Website**: \`favicon.ico\` (copied from master icon.ico)
- **Website Logo**: \`qu-scheduler-logo.svg\` (copied from master icon.svg)

## Usage Guidelines

### Application Icons
- **Electron App**: Uses \`icon.ico\` for Windows builds
- **Installer**: Uses \`icon.ico\` for setup executable
- **Shortcuts**: Automatically use embedded icon from executable

### Website Icons
- **Favicon**: \`favicon.ico\` in website root
- **Logo**: \`qu-scheduler-logo.svg\` for navigation and branding
- **Touch Icons**: Generate from master SVG for mobile devices

## Design Specifications

### Visual Elements
- **Colors**: Qatar University maroon (#8B1538), gold (#D4AF37)
- **Design**: Academic building with columns, calendar grid, clock element
- **Typography**: "QU" prominently displayed, "SCHEDULER" subtitle
- **Background**: Transparent for flexibility

### Technical Specifications
- **Format**: ICO (Windows), SVG (web), PNG (cross-platform)
- **Sizes**: 16×16 to 512×512 pixels
- **Color Depth**: 32-bit with alpha transparency
- **Compression**: Optimized for file size while maintaining quality

## Maintenance

### When to Update Icons
- Major version releases
- Rebranding initiatives
- Platform-specific requirements
- User feedback on visibility/recognition

### Update Process
1. Modify master \`icon.svg\` file
2. Regenerate \`icon.ico\` from SVG
3. Run standardization script: \`npm run standardize:icons\`
4. Test across all platforms and contexts
5. Update documentation if needed

## File Organization

\`\`\`
assets/icons/
├── icon.ico                 # Master Windows icon (source of truth)
├── icon.svg                 # Master vector icon (design source)
├── icon-16x16.png          # Generated PNG files
├── icon-24x24.png
├── icon-32x32.png
├── icon-48x48.png
├── icon-64x64.png
├── icon-128x128.png
├── icon-256x256.png
├── icon-512x512.png
└── ICON_STANDARDS.md       # This documentation
\`\`\`

## Conversion Tools

### Recommended Tools
- **SVG to PNG**: ImageMagick, Inkscape, or online converters
- **PNG to ICO**: ImageMagick, GIMP, or ico-convert
- **Batch Processing**: Sharp (Node.js), Pillow (Python)

### Command Examples
\`\`\`bash
# Convert SVG to PNG (ImageMagick)
magick icon.svg -resize 256x256 icon-256x256.png

# Convert PNG to ICO (ImageMagick)
magick icon-256x256.png icon.ico

# Batch convert multiple sizes
for size in 16 32 48 64 128 256 512; do
  magick icon.svg -resize \${size}x\${size} icon-\${size}x\${size}.png
done
\`\`\`

---

**Last Updated**: ${new Date().toISOString().split('T')[0]}
**Version**: 1.0.0
**Maintainer**: QU Scheduler Development Team
`;

    const docPath = path.join(ICONS_DIR, 'ICON_STANDARDS.md');
    
    try {
        fs.writeFileSync(docPath, documentation);
        logSuccess('Icon documentation created.');
    } catch (error) {
        logError(`Failed to create documentation: ${error.message}`);
    }
}

// Step 7: Update package.json scripts
function updatePackageScripts() {
    log('Updating package.json scripts...');
    
    const packagePath = path.join(__dirname, '..', 'package.json');
    
    try {
        const packageContent = fs.readFileSync(packagePath, 'utf8');
        const packageJson = JSON.parse(packageContent);
        
        // Add icon standardization script
        if (!packageJson.scripts['standardize:icons']) {
            packageJson.scripts['standardize:icons'] = 'node scripts/standardize-icons.js';
            
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
            logSuccess('Added standardize:icons script to package.json');
        } else {
            log('standardize:icons script already exists in package.json');
        }
    } catch (error) {
        logError(`Failed to update package.json: ${error.message}`);
    }
}

// Main execution function
function main() {
    console.log('🎨 QU Scheduler Icon Standardization');
    console.log('=====================================');
    console.log('');
    
    // Verify master icons exist
    if (!verifyMasterIcons()) {
        logError('Cannot proceed without master icon files.');
        process.exit(1);
    }
    
    // Create website assets directory if it doesn't exist
    if (!fs.existsSync(WEBSITE_ASSETS_DIR)) {
        fs.mkdirSync(WEBSITE_ASSETS_DIR, { recursive: true });
        log('Created website assets directory.');
    }
    
    // Execute standardization steps
    cleanupIconsFolder();
    createStandardizedPNGs();
    createWebsiteFavicon();
    updateWebsiteLogo();
    createIconDocumentation();
    updatePackageScripts();
    
    console.log('');
    console.log('🎉 Icon standardization complete!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('1. Convert icon.svg to PNG files using ImageMagick or similar tools');
    console.log('2. Test the application build to ensure icons display correctly');
    console.log('3. Verify website favicon and logo are working');
    console.log('4. Update any remaining hardcoded icon references');
    console.log('');
    console.log('💡 Tip: Run "npm run standardize:icons" to execute this script');
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    cleanupIconsFolder,
    verifyMasterIcons,
    createStandardizedPNGs,
    createWebsiteFavicon,
    updateWebsiteLogo,
    createIconDocumentation,
    updatePackageScripts
};
