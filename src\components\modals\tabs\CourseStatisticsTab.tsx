import React, { useMemo } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Card,
  CardContent,
  Grid,
  IconButton
} from '@mui/material';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { Course, Section, Session, Semester } from '../../../types/models';

// Props for the CourseStatisticsTab component
interface CourseStatisticsTabProps {
  courses: Record<Semester, Course[]>;
  sections: Record<Semester, Section[]>;
  sessions: Record<Semester, Session[]>;
  currentSemester: Semester;
}

const CourseStatisticsTab: React.FC<CourseStatisticsTabProps> = ({
  courses,
  sections,
  sessions,
  currentSemester
}) => {
  // Calculate course statistics
  const courseStats = useMemo(() => {
    const currentCourses = courses[currentSemester];
    const currentSections = sections[currentSemester];
    const currentSessions = sessions[currentSemester];

    // Course type statistics
    const theoryCount = currentCourses.filter(course => course.courseType === 'Theory').length;
    const labCount = currentCourses.filter(course => course.courseType === 'Lab').length;

    // Academic level statistics (if available)
    const graduateCourses = currentCourses.filter(course =>
      course.academicLevel === 'Graduate' ||
      (course.courseCode && parseInt(course.courseCode.replace(/\D/g, '')) >= 500)
    ).length;

    const undergraduateCourses = currentCourses.length - graduateCourses;

    // Section statistics
    const totalSections = currentSections.length;
    const maleSections = currentSections.filter(section => section.gender === 'M').length;
    const femaleSections = currentSections.filter(section => section.gender === 'F').length;

    // Calculate average sections per course
    const avgSectionsPerCourse = currentCourses.length > 0
      ? totalSections / currentCourses.length
      : 0;

    // Calculate scheduled vs unscheduled sections
    const scheduledSectionIds = new Set(currentSessions.map(session => session.sectionId));
    const scheduledSections = currentSections.filter(section => scheduledSectionIds.has(section.id)).length;
    const unscheduledSections = totalSections - scheduledSections;

    // Calculate day distribution
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];
    const dayDistribution = days.map(day => {
      const sessionsOnDay = currentSessions.filter(session => session.day === day).length;
      return {
        day,
        count: sessionsOnDay,
        percentage: currentSessions.length > 0
          ? (sessionsOnDay / currentSessions.length) * 100
          : 0
      };
    });

    // Calculate time distribution (morning vs evening)
    const morningPeriods = [1, 2, 3, 4, 5, 6];
    const eveningPeriods = [7, 8, 9, 10, 11, 12];

    const morningSessionsCount = currentSessions.filter(session =>
      morningPeriods.includes(session.startPeriod)
    ).length;

    const eveningSessionsCount = currentSessions.filter(session =>
      eveningPeriods.includes(session.startPeriod)
    ).length;

    return {
      totalCourses: currentCourses.length,
      theoryCount,
      labCount,
      theoryPercentage: currentCourses.length > 0 ? (theoryCount / currentCourses.length) * 100 : 0,
      labPercentage: currentCourses.length > 0 ? (labCount / currentCourses.length) * 100 : 0,
      graduateCourses,
      undergraduateCourses,
      graduatePercentage: currentCourses.length > 0 ? (graduateCourses / currentCourses.length) * 100 : 0,
      undergraduatePercentage: currentCourses.length > 0 ? (undergraduateCourses / currentCourses.length) * 100 : 0,
      totalSections,
      maleSections,
      femaleSections,
      malePercentage: totalSections > 0 ? (maleSections / totalSections) * 100 : 0,
      femalePercentage: totalSections > 0 ? (femaleSections / totalSections) * 100 : 0,
      avgSectionsPerCourse,
      scheduledSections,
      unscheduledSections,
      scheduledPercentage: totalSections > 0 ? (scheduledSections / totalSections) * 100 : 0,
      unscheduledPercentage: totalSections > 0 ? (unscheduledSections / totalSections) * 100 : 0,
      dayDistribution,
      morningSessionsCount,
      eveningSessionsCount,
      morningPercentage: currentSessions.length > 0 ? (morningSessionsCount / currentSessions.length) * 100 : 0,
      eveningPercentage: currentSessions.length > 0 ? (eveningSessionsCount / currentSessions.length) * 100 : 0
    };
  }, [courses, sections, sessions, currentSemester]);

  // Calculate course section details
  const courseSectionDetails = useMemo(() => {
    const currentCourses = courses[currentSemester];
    const currentSections = sections[currentSemester];
    const currentSessions = sessions[currentSemester];

    return currentCourses.map(course => {
      const courseSections = currentSections.filter(section => section.courseId === course.id);
      const maleSections = courseSections.filter(section => section.gender === 'M').length;
      const femaleSections = courseSections.filter(section => section.gender === 'F').length;

      // Calculate scheduled sections
      const scheduledSectionIds = new Set(currentSessions.map(session => session.sectionId));
      const scheduledSections = courseSections.filter(section =>
        scheduledSectionIds.has(section.id)
      ).length;

      return {
        id: course.id,
        courseCode: course.courseCode,
        courseName: course.courseName,
        courseType: course.courseType,
        academicLevel: course.academicLevel ||
          (course.courseCode && parseInt(course.courseCode.replace(/\D/g, '')) >= 500
            ? 'Graduate'
            : 'Undergraduate'),
        totalSections: courseSections.length,
        maleSections,
        femaleSections,
        scheduledSections,
        unscheduledSections: courseSections.length - scheduledSections,
        scheduledPercentage: courseSections.length > 0
          ? (scheduledSections / courseSections.length) * 100
          : 0
      };
    });
  }, [courses, sections, sessions, currentSemester]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Summary Cards */}
      <Grid container spacing={1}>
        <Grid item xs={6} sm={3}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Total Courses
              </Typography>
              <Typography variant="h4" color="primary" sx={{ mb: 0.5 }}>
                {courseStats.totalCourses}
              </Typography>
              <Box>
                <Typography variant="caption" display="block">
                  Theory: {courseStats.theoryCount} ({courseStats.theoryPercentage.toFixed(1)}%)
                </Typography>
                <Typography variant="caption" display="block">
                  Lab: {courseStats.labCount} ({courseStats.labPercentage.toFixed(1)}%)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Academic Level
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h6" color="primary" sx={{ mb: 0 }}>
                    {courseStats.undergraduateCourses}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Undergraduate ({courseStats.undergraduatePercentage.toFixed(1)}%)
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="h6" color="secondary" sx={{ mb: 0 }}>
                    {courseStats.graduateCourses}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Graduate ({courseStats.graduatePercentage.toFixed(1)}%)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Sections
              </Typography>
              <Typography variant="h4" color="primary" sx={{ mb: 0.5 }}>
                {courseStats.totalSections}
              </Typography>
              <Typography variant="caption" display="block">
                Avg. {courseStats.avgSectionsPerCourse.toFixed(1)} per course
              </Typography>
              <Box>
                <Typography variant="caption" display="block">
                  Male: {courseStats.maleSections} ({courseStats.malePercentage.toFixed(1)}%)
                </Typography>
                <Typography variant="caption" display="block">
                  Female: {courseStats.femaleSections} ({courseStats.femalePercentage.toFixed(1)}%)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Time Distribution
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h6" color="primary" sx={{ mb: 0 }}>
                    {courseStats.morningSessionsCount}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Morning ({courseStats.morningPercentage.toFixed(1)}%)
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="h6" color="secondary" sx={{ mb: 0 }}>
                    {courseStats.eveningSessionsCount}
                  </Typography>
                  <Typography variant="caption" display="block">
                    Evening ({courseStats.eveningPercentage.toFixed(1)}%)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Day Distribution */}
      <Card variant="outlined">
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Day Distribution
            </Typography>
            <IconButton size="small" title="Export as PDF">
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <Grid container spacing={1}>
            {courseStats.dayDistribution.map(day => (
              <Grid item xs={12} sm={2.4} key={day.day}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>{day.count}</Typography>
                  <Typography variant="caption" display="block">{day.day}</Typography>
                  <Typography variant="caption" display="block">
                    {day.percentage.toFixed(1)}%
                  </Typography>
                  <Box
                    sx={{
                      height: 6,
                      width: '100%',
                      backgroundColor: '#e0e0e0',
                      borderRadius: 3,
                      mt: 0.5
                    }}
                  >
                    <Box
                      sx={{
                        height: '100%',
                        width: `${day.percentage}%`,
                        backgroundColor: 'primary.main',
                        borderRadius: 3
                      }}
                    />
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Course Details Table */}
      <Card variant="outlined">
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Course Details
            </Typography>
            <IconButton size="small" title="Export as PDF" sx={{ p: 0.5 }}>
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <TableContainer sx={{ flexGrow: 1, overflow: 'auto' }}>
            <Table stickyHeader size="small" sx={{ '& .MuiTableCell-root': { py: 0.75, px: 1 } }}>
              <TableHead>
                <TableRow>
                  <TableCell>Course Code</TableCell>
                  <TableCell>Course Name</TableCell>
                  <TableCell align="center">Type</TableCell>
                  <TableCell align="center">Level</TableCell>
                  <TableCell align="center">Total</TableCell>
                  <TableCell align="center">Male</TableCell>
                  <TableCell align="center">Female</TableCell>
                  <TableCell align="center">Scheduled</TableCell>
                  <TableCell align="center">Unscheduled</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {courseSectionDetails.map((course) => (
                  <TableRow key={course.id} hover>
                    <TableCell>
                      <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                        {course.courseCode}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption">
                        {course.courseName}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.courseType}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.academicLevel}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.totalSections}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.maleSections}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.femaleSections}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.scheduledSections} ({course.scheduledPercentage.toFixed(0)}%)
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.unscheduledSections}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CourseStatisticsTab;
