import React, { useState, useEffect } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  Select,
  MenuItem,
  Button,
  IconButton,
  Tooltip,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  SelectChangeEvent
} from '@mui/material';
import AccessibleDialog from './common/AccessibleDialog';
import { useAppContext } from '../context/AppContext';
import { UIState, getUIStateWithDefaults } from '../types/uiState';
import { Semester } from '../types/models';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import StatisticsModal from './modals/StatisticsModal';
import ExportImportModal from './modals/ExportImportModal';

import { RuleSystemModal } from './modals/RuleSystemModal';
import { autoScheduleAllSections } from '../utils/autoScheduling';
import { useRuleSystemStore } from '../store/ruleSystem';
import { getAcademicLevel } from '../utils/ruleValidation';

// Importing icons separately to avoid issues with missing module declarations
import DarkModeIcon from '@mui/icons-material/DarkMode';
import LightModeIcon from '@mui/icons-material/LightMode';
import InfoIcon from '@mui/icons-material/Info';
import ExportIcon from '@mui/icons-material/FileDownload';
import DeleteIcon from '@mui/icons-material/Delete';
import StatisticsIcon from '@mui/icons-material/BarChart';
import AutoGenerateIcon from '@mui/icons-material/AutoFixHigh';

import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';

/**
 * Header component that contains the app title, department name input,
 * academic year, semester selection, and action buttons
 */
interface HeaderProps {
  darkMode: boolean;
  toggleDarkMode: () => void;
}

const Header: React.FC<HeaderProps> = ({ darkMode, toggleDarkMode }) => {
  // Get current semester from context
  const { currentSemester, setCurrentSemester } = useAppContext();

  // State for academic year
  const [academicYear, setAcademicYear] = useState<string>('');

  // State for department name
  const [departmentName, setDepartmentName] = useState<string>('');

  // State for info dialog
  const [infoOpen, setInfoOpen] = useState<boolean>(false);

  // State for reset confirmation dialog
  const [resetDialogOpen, setResetDialogOpen] = useState(false);

  // State for statistics modal
  const [statisticsModalOpen, setStatisticsModalOpen] = useState(false);

  // State for export/import modal
  const [exportImportModalOpen, setExportImportModalOpen] = useState(false);

  // State for zoom factor
  const [zoomFactor, setZoomFactor] = useState<number>(1.0);

  // State for rule system modal
  const [showRuleSystemModal, setShowRuleSystemModal] = useState(false);

  // State for auto-scheduling
  const [autoScheduleDialogOpen, setAutoScheduleDialogOpen] = useState(false);
  const [isAutoScheduling, setIsAutoScheduling] = useState(false);

  // Load UI state from store and calculate default academic year
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const rawUIState = await window.electronAPI.store.get('uiState');
        const uiState = getUIStateWithDefaults(rawUIState);

        // Set department name from store if available
        if (uiState && uiState.departmentName) {
          setDepartmentName(uiState.departmentName);
        }

        // Set academic year from store if available, otherwise calculate default
        if (uiState && uiState.academicYear) {
          setAcademicYear(uiState.academicYear);
        } else {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() - 6);
          const year = currentDate.getFullYear();
          setAcademicYear(`${year}/${year + 1}`);
        }

        // Set zoom factor from store if available
        if (uiState && typeof uiState.zoomFactor === 'number') {
          const storedZoomFactor = uiState.zoomFactor;
          setZoomFactor(storedZoomFactor);
          // Apply the stored zoom factor directly
          window.electronAPI.zoom.setZoomFactor(storedZoomFactor);
        } else {
          // If no zoom factor is stored, use the default 80% and save it
          const defaultZoom = 0.8;
          setZoomFactor(defaultZoom);
          window.electronAPI.zoom.setZoomFactor(defaultZoom);
        }
      } catch (error) {
        console.error('Error loading UI state:', error);

        // Fallback to calculating default academic year
        const currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 6);
        const year = currentDate.getFullYear();
        setAcademicYear(`${year}/${year + 1}`);

        // Fallback to default zoom
        const defaultZoom = 0.8;
        setZoomFactor(defaultZoom);
        window.electronAPI.zoom.setZoomFactor(defaultZoom);
      }
    };

    loadUIState();
  }, []);

  // Save department name and academic year to store when they change
  useEffect(() => {
    const saveUIState = async () => {
      try {
        // Get the current UI state
        const rawCurrentUIState = await window.electronAPI.store.get('uiState');
        const currentUIState = getUIStateWithDefaults(rawCurrentUIState);
        // Create a new UI state object with the updated values
        const newUIState: UIState = {
          ...currentUIState,
          departmentName,
          academicYear,
          zoomFactor
        };
        // Set the new UI state
        await window.electronAPI.store.set('uiState', newUIState);
      } catch (error) {
        console.error('Error saving UI state:', error);
      }
    };

    saveUIState();
  }, [departmentName, academicYear, zoomFactor]);

  // Handler for semester change
  const handleSemesterChange = (event: SelectChangeEvent<string>) => {
    setCurrentSemester(event.target.value as Semester);
  };

  // Handler for academic year change
  const handleYearChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAcademicYear(event.target.value);
  };

  // Handler for department name change
  const handleDepartmentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;

    // Validate input: allow only English letters, numbers, spaces, ampersands, hyphens, and common abbreviations
    // This regex allows common department name patterns like "Computer Science", "Arts & Sciences", "Bio-Engineering"
    const validInput = /^[a-zA-Z0-9\s&\-().]+$/;

    // If empty or valid, update the state
    if (newValue === '' || validInput.test(newValue)) {
      setDepartmentName(newValue);

      // Immediately save the department name to store
      const saveUIState = async () => {
        try {
          const rawCurrentUIState = await window.electronAPI.store.get('uiState');
          const currentUIState = getUIStateWithDefaults(rawCurrentUIState);
          const newUIState: UIState = {
            ...currentUIState,
            departmentName: newValue,
          };
          await window.electronAPI.store.set('uiState', newUIState);
        } catch (error) {
          console.error('Error saving department name:', error);
        }
      };

      saveUIState();
    } else {
      // Optional: Show error toast for invalid input
      toast.error('Please use only English characters for department name');
    }
  };

  // Toggle info dialog
  const toggleInfoDialog = () => {
    setInfoOpen(!infoOpen);
  };

  // Zoom in handler
  const handleZoomIn = () => {
    const newZoomFactor = window.electronAPI.zoom.zoomIn();
    setZoomFactor(newZoomFactor);
    // Removed toast notification for zoom actions
  };

  // Zoom out handler
  const handleZoomOut = () => {
    const newZoomFactor = window.electronAPI.zoom.zoomOut();
    setZoomFactor(newZoomFactor);
    // Removed toast notification for zoom actions
  };

  // Reset zoom handler
  const handleResetZoom = () => {
    const newZoomFactor = window.electronAPI.zoom.resetZoom();
    setZoomFactor(newZoomFactor);
    // Removed toast notification for zoom actions
  };

  // Get additional context at the component level, not inside handlers
  const {
    // resetCurrentView is no longer used since we're implementing our own reset functionality
    sections,
    courses,
    sessions,
    lecturers,
    addSession,
    updateSection,
    deleteSession,
    setActiveTab,
    setViewMode,
    exportTimetableData,
    importTimetableData
  } = useAppContext();

  // Get rule system store
  const { retryConfiguration, assignLecturersInAutoScheduling } = useRuleSystemStore();

  // Calculate the total number of undergraduate theory sections in the current semester
  const totalSections = sections[currentSemester]?.filter(section => {
    // Find the course for this section
    const course = courses[currentSemester]?.find(c => c.id === section.courseId);
    if (!course) return false;

    // Check if it's a theory course
    const isTheory = course.courseType === 'Theory';

    // Check if it's an undergraduate course (course code doesn't start with 5 or higher)
    const numericPart = course.courseCode.match(/\d+/)?.[0] || '';
    const firstDigit = numericPart.length > 0 ? parseInt(numericPart[0]) : -1;
    const isUndergraduate = firstDigit < 5;

    return isTheory && isUndergraduate;
  }).length || 0;

  // Handle reset timetable
  const handleResetTimetable = () => {
    setResetDialogOpen(false);

    try {
      // Delete all sessions in the current semester
      const currentSessions = sessions[currentSemester];
      if (currentSessions && currentSessions.length > 0) {
        // Delete all sessions one by one
        currentSessions.forEach(session => {
          deleteSession(session.id, currentSemester);
        });

        // Show success message for this important action
        toast.success(`Timetable reset complete. All sessions deleted.`);
      } else {
        // No sessions to delete
        toast.info(`No sessions to delete in ${currentSemester} semester`);
      }

      // Switch to all day view (activeTab = 2)
      setActiveTab(2);

      // Make sure we're in week view to see all days
      setViewMode('week');

    } catch (error) {
      toast.error('Failed to reset timetable');
      console.error('Reset error:', error);
    }
  };

  // Handle auto-scheduling
  const handleAutoSchedule = async () => {
    setAutoScheduleDialogOpen(false);
    setIsAutoScheduling(true);

    try {
      // Get all sections for the current semester
      const currentSections = sections[currentSemester] || [];
      // Lecturers are shared across semesters, not organized by semester
      const currentLecturers = lecturers || [];

      if (currentSections.length === 0) {
        toast.info('No sections to schedule in the current semester');
        setIsAutoScheduling(false);
        return;
      }

      // Delete all existing sessions first
      const currentSessions = sessions[currentSemester] || [];
      currentSessions.forEach(session => {
        deleteSession(session.id, currentSemester);
      });

      // Reset scheduled hours for all sections
      currentSections.forEach(section => {
        updateSection({ ...section, scheduledHours: 0 }, currentSemester);
      });

      // Auto-scheduling setup logging removed to reduce console output

      toast.info(`Starting auto-scheduling for ${currentSections.length} sections...`);

      // Use setTimeout to allow UI to update before starting heavy computation
      setTimeout(async () => {
        try {
          // Transform sections to include courseCode and other required properties
          const transformedSections = currentSections.map(section => {
            const course = courses[currentSemester]?.find(c => c.id === section.courseId);
            if (!course) {
              return null;
            }

            return {
              id: section.id,
              courseCode: course.courseCode,
              courseType: course.courseType,
              contactHours: course.contactHours,
              gender: section.gender,
              lecturerId: section.lecturerId || '',
              academicLevel: course.academicLevel || getAcademicLevel(course.courseCode)
            };
          }).filter(Boolean); // Remove any null entries

          // Run auto-scheduling with current rule system settings
          const result = autoScheduleAllSections(
            transformedSections as any, // Type assertion since we know the structure is correct
            [], // Empty existing schedule since we deleted all sessions
            assignLecturersInAutoScheduling ? currentLecturers : [], // Use lecturers based on setting
            retryConfiguration
          );

          // Add all generated sessions to the app
          if (result.appSessions && result.appSessions.length > 0) {
            result.appSessions.forEach(appSession => {
              addSession(appSession, currentSemester);
            });
          }

          // Update scheduled hours for sections
          result.scheduledSections.forEach(sectionId => {
            const section = currentSections.find(s => s.id === sectionId);
            if (section) {
              const sectionSessions = result.appSessions.filter(s => s.sectionId === sectionId);
              const totalHours = sectionSessions.reduce((sum, s) => {
                const isLongDay = s.day === 'Monday' || s.day === 'Wednesday';
                return sum + (isLongDay ? 1.5 : 1);
              }, 0);
              updateSection({ ...section, scheduledHours: totalHours }, currentSemester);
            }
          });

          // Show results
          const successRate = (result.scheduledSections.length / currentSections.length * 100).toFixed(1);
          const message = `Auto-scheduling complete: ${result.scheduledSections.length}/${currentSections.length} sections scheduled (${successRate}%)`;

          if (result.unscheduledSections.length > 0) {
            toast.warning(`${message}. ${result.unscheduledSections.length} sections could not be scheduled.`);
          } else {
            toast.success(message);
          }

          // Switch to all day view to see results
          setActiveTab(2);
          setViewMode('week');

        } catch (error) {
          toast.error(`Auto-scheduling failed: ${error}`);
          console.error('Auto-scheduling error:', error);
        } finally {
          setIsAutoScheduling(false);
        }
      }, 100);

    } catch (error) {
      toast.error(`Failed to start auto-scheduling: ${error}`);
      console.error('Auto-scheduling setup error:', error);
      setIsAutoScheduling(false);
    }
  };

  return (
    <AppBar position="static" color="default" elevation={0} className="border-b border-gray-200 dark:border-gray-700">
      <Toolbar className="flex justify-between items-center px-3 py-1.5">
        {/* Left section: App title and department name */}
        <div className="flex items-center space-x-3">
          <Typography variant="subtitle1" component="div" className="font-extrabold">
            QU Scheduler
          </Typography>
          <Tooltip
            title="Enter your department name for timetable identification and export file naming"
            placement="bottom"
            arrow
          >
            <TextField
              placeholder="Department Name"
              variant="outlined"
              size="small"
              value={departmentName}
              onChange={handleDepartmentChange}
              className="w-40"
              error={departmentName !== '' && !/^[a-zA-Z0-9\s&\-().]+$/.test(departmentName)}
              helperText={departmentName !== '' && !/^[a-zA-Z0-9\s&\-().]+$/.test(departmentName) ? 'Invalid characters' : ''}
              slotProps={{
                htmlInput: {
                  maxLength: 40,
                  style: { textTransform: 'capitalize' }
                }
              }}
              sx={{
                '& .MuiInputBase-root': {
                  height: '28px'
                },
                '& .MuiInputBase-input': {
                  paddingTop: '2px',
                  paddingBottom: '2px'
                }
              }}
            />
          </Tooltip>
        </div>

        {/* Center section: Year, semester, and info */}
        <div className="flex items-center space-x-3">
          <Tooltip
            title="Set the academic year for your timetable (e.g., 2024/2025)"
            placement="bottom"
            arrow
          >
            <TextField
              label="Academic Year"
              variant="outlined"
              size="small"
              value={academicYear}
              onChange={handleYearChange}
              className="w-28"
              sx={{
                '& .MuiInputBase-root': {
                  height: '28px'
                },
                '& .MuiInputBase-input': {
                  paddingTop: '2px',
                  paddingBottom: '2px'
                }
              }}
            />
          </Tooltip>

          <Tooltip
            title="Select the semester for your timetable scheduling"
            placement="bottom"
            arrow
          >
            <Select
              value={currentSemester}
              onChange={handleSemesterChange}
              size="small"
              className="w-24"
              sx={{
                height: '28px',
                '& .MuiSelect-select': {
                  paddingTop: '2px',
                  paddingBottom: '2px'
                }
              }}
            >
              <MenuItem value="Fall">Fall</MenuItem>
              <MenuItem value="Spring">Spring</MenuItem>
              <MenuItem value="Summer">Summer</MenuItem>
            </Select>
          </Tooltip>

          <Tooltip
            title="About QU Scheduler - View application information and credits"
            placement="bottom"
            arrow
          >
            <IconButton
              onClick={(e) => {
                (e.target as HTMLButtonElement).blur();
                toggleInfoDialog();
              }}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 p-1"
            >
              <InfoIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </div>

        {/* Right section: Action buttons */}
        <div className="flex items-center space-x-1.5">
          <Tooltip
            title="Auto-Schedule All Sections - Automatically schedule all theory sections using rule-based algorithms and constraints"
            placement="bottom"
            arrow
          >
            <span>
              <Button
                variant="contained"
                startIcon={<AutoGenerateIcon fontSize="small" />}
                size="small"
                className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 py-1 px-2 text-xs"
                onClick={(e) => {
                  (e.target as HTMLButtonElement).blur();
                  setAutoScheduleDialogOpen(true);
                }}
                disabled={isAutoScheduling}
              >
                {isAutoScheduling ? 'Scheduling...' : 'Auto'}
              </Button>
            </span>
          </Tooltip>

          <Tooltip
            title="Export & Import - Export timetable data to JSON files or import existing timetable configurations"
            placement="bottom"
            arrow
          >
            <Button
              variant="contained"
              startIcon={<ExportIcon fontSize="small" />}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 py-1 px-2 text-xs"
              onClick={(e) => {
                (e.target as HTMLButtonElement).blur();
                setExportImportModalOpen(true);
              }}
            >
              Export
            </Button>
          </Tooltip>

          <Tooltip
            title="Delete All Sessions - Remove all scheduled sessions from the current semester and reset the timetable"
            placement="bottom"
            arrow
          >
            <Button
              variant="contained"
              startIcon={<DeleteIcon fontSize="small" />}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 py-1 px-2 text-xs"
              onClick={(e) => {
                (e.target as HTMLButtonElement).blur();
                setResetDialogOpen(true);
              }}
            >
              Delete
            </Button>
          </Tooltip>

          <Tooltip
            title="Statistics & Analytics - View detailed statistics about course scheduling, lecturer workloads, and timetable utilization"
            placement="bottom"
            arrow
          >
            <Button
              variant="contained"
              startIcon={<StatisticsIcon fontSize="small" />}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 py-1 px-2 text-xs"
              onClick={(e) => {
                (e.target as HTMLButtonElement).blur();
                setStatisticsModalOpen(true);
              }}
            >
              Stats
            </Button>
          </Tooltip>



          <Tooltip
            title="Zoom Out - Decrease the application zoom level for a wider view"
            placement="bottom"
            arrow
          >
            <IconButton
              onClick={handleZoomOut}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 p-1"
            >
              <ZoomOutIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Tooltip
            title="Zoom In - Increase the application zoom level for better readability (double-click to reset zoom)"
            placement="bottom"
            arrow
          >
            <IconButton
              onClick={handleZoomIn}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 p-1"
              onDoubleClick={handleResetZoom}
            >
              <ZoomInIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Tooltip
            title={darkMode ? "Switch to Light Mode - Change to light theme for better visibility in bright environments" : "Switch to Dark Mode - Change to dark theme for reduced eye strain in low-light environments"}
            placement="bottom"
            arrow
          >
            <IconButton
              onClick={toggleDarkMode}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 p-1"
            >
              {darkMode ? <LightModeIcon fontSize="small" /> : <DarkModeIcon fontSize="small" />}
            </IconButton>
          </Tooltip>

          <Tooltip
            title="Scheduling Rules & Configuration - Configure auto-scheduling rules, constraints, and system preferences"
            placement="bottom"
            arrow
          >
            <IconButton
              onClick={(e) => {
                // Blur the button immediately to prevent focus retention
                (e.target as HTMLButtonElement).blur();
                setShowRuleSystemModal(true);
              }}
              size="small"
              className="bg-gray-800 text-white hover:bg-gray-700 dark:bg-slate-600 dark:hover:bg-slate-500 dark:text-gray-100 p-1"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </IconButton>
          </Tooltip>
        </div>
      </Toolbar>

      {/* Info Dialog */}
      <AccessibleDialog open={infoOpen} onClose={toggleInfoDialog}>
        <DialogTitle className="text-base pb-2">About QU Timetable Scheduler</DialogTitle>
        <DialogContent className="pt-0">
          <DialogContentText className="text-sm">
            QU timetable scheduler v.1.0
            <br />
            &copy; Ayman Saleh, College of Sharia, Qatar University
          </DialogContentText>
        </DialogContent>
      </AccessibleDialog>

      {/* Auto-Schedule Confirmation Dialog */}
      <AccessibleDialog open={autoScheduleDialogOpen} onClose={() => setAutoScheduleDialogOpen(false)}>
        <DialogTitle className="text-base pb-2">Confirm Auto-Scheduling</DialogTitle>
        <DialogContent className="pt-0">
          <DialogContentText className="text-sm">
            Are you sure you want to auto-schedule all sections?
            This will:
          </DialogContentText>
          <ul className="list-disc pl-5 mt-2">
            <li>Delete ALL currently scheduled sessions</li>
            <li>Reset scheduled hours for all sections</li>
            <li>Apply rule-based auto-scheduling to all sections</li>
            <li>Use current rule system settings and constraints</li>
            <li>{assignLecturersInAutoScheduling ? 'Assign lecturers during scheduling' : 'Schedule without lecturer assignment'}</li>
          </ul>
          <DialogContentText className="text-sm mt-3 font-medium">
            {sections[currentSemester]?.length || 0} sections will be processed.
          </DialogContentText>
        </DialogContent>
        <DialogActions className="p-2">
          <Button onClick={() => setAutoScheduleDialogOpen(false)} size="small" className="text-xs py-1 px-2">Cancel</Button>
          <Button onClick={handleAutoSchedule} color="primary" size="small" className="text-xs py-1 px-2">Auto-Schedule</Button>
        </DialogActions>
      </AccessibleDialog>

      {/* Reset Confirmation Dialog */}
      <AccessibleDialog open={resetDialogOpen} onClose={() => setResetDialogOpen(false)}>
        <DialogTitle className="text-base pb-2">Confirm Reset</DialogTitle>
        <DialogContent className="pt-0">
          <DialogContentText className="text-sm">
            Are you sure you want to reset the timetable?
            This will:
          </DialogContentText>
          <ul className="list-disc pl-5 mt-2">
            <li>Delete ALL sessions in the current semester</li>
            <li>Reset scheduled hours for all sections</li>
            <li>Reset lecturer load calculations</li>
            <li>Switch to the all-day view of the timetable</li>
          </ul>
        </DialogContent>
        <DialogActions className="p-2">
          <Button onClick={() => setResetDialogOpen(false)} size="small" className="text-xs py-1 px-2">Cancel</Button>
          <Button onClick={handleResetTimetable} color="error" size="small" className="text-xs py-1 px-2">Reset</Button>
        </DialogActions>
      </AccessibleDialog>

      {/* Statistics Modal */}
      <StatisticsModal
        open={statisticsModalOpen}
        onClose={() => setStatisticsModalOpen(false)}
      />

      {/* Export/Import Modal */}
      <ExportImportModal
        open={exportImportModalOpen}
        onClose={() => setExportImportModalOpen(false)}
        onExport={exportTimetableData}
        onImport={importTimetableData}
        departmentName={departmentName}
        academicYear={academicYear}
      />

      {/* Rule System Modal */}
      <RuleSystemModal
        isOpen={showRuleSystemModal}
        onClose={() => setShowRuleSystemModal(false)}
        totalSections={totalSections}
      />
    </AppBar>
  );
};

export default Header;
