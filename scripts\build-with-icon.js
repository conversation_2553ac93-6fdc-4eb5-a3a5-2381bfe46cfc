#!/usr/bin/env node

/**
 * QU Scheduler Complete Build Script with Icon Embedding
 * 
 * This script handles the complete build process including:
 * 1. Packaging the application
 * 2. Embedding the enhanced icon
 * 3. Verifying icon embedding
 * 4. Building the NSIS installer
 * 5. Final verification
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 QU Scheduler Complete Build Process');
console.log('======================================');

const executablePath = path.resolve('out/QU Scheduler-win32-x64/qu-scheduler.exe');
const iconPath = path.resolve('assets/icons/icon.ico');
const installerPath = path.resolve('installer/QU-Scheduler-Setup.exe');

function runCommand(command, description) {
    console.log(`\n📋 ${description}...`);
    console.log(`Command: ${command}`);
    
    try {
        execSync(command, { stdio: 'inherit' });
        console.log(`✅ ${description} completed successfully`);
        return true;
    } catch (error) {
        console.error(`❌ ${description} failed:`, error.message);
        return false;
    }
}

function checkFile(filePath, description) {
    if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`✅ ${description}: Found (${(stats.size / (1024 * 1024)).toFixed(1)} MB)`);
        return true;
    } else {
        console.log(`❌ ${description}: Not found`);
        return false;
    }
}

async function embedIconWithMultipleMethods() {
    console.log('\n🎨 Embedding icon with multiple methods...');
    
    let success = false;
    
    // Method 1: rcedit
    try {
        console.log('📋 Method 1: Using rcedit...');
        execSync(`npx rcedit "${executablePath}" --set-icon "${iconPath}"`, { 
            stdio: 'inherit' 
        });
        console.log('✅ rcedit embedding successful');
        success = true;
    } catch (error) {
        console.log('⚠️  rcedit failed:', error.message);
    }
    
    // Method 2: winresourcer (if rcedit failed)
    if (!success) {
        try {
            console.log('📋 Method 2: Using winresourcer...');
            const winresourcer = require('winresourcer');
            
            await winresourcer({
                operation: 'Update',
                exeFile: executablePath,
                resourceType: 'Icongroup',
                resourceName: '1',
                resourceFile: iconPath
            });
            
            // Also try Icon resource
            await winresourcer({
                operation: 'Update',
                exeFile: executablePath,
                resourceType: 'Icon',
                resourceName: '1',
                resourceFile: iconPath
            });
            
            console.log('✅ winresourcer embedding successful');
            success = true;
        } catch (error) {
            console.log('⚠️  winresourcer failed:', error.message);
        }
    }
    
    return success;
}

async function main() {
    console.log('\n🔍 Pre-build checks...');
    
    // Check if icon file exists
    if (!checkFile(iconPath, 'Enhanced icon file')) {
        console.error('❌ Icon file not found. Build cannot continue.');
        process.exit(1);
    }
    
    // Step 1: Clean previous build
    console.log('\n🧹 Cleaning previous build...');
    try {
        if (fs.existsSync('out')) {
            execSync('rmdir /s /q out', { stdio: 'inherit' });
        }
        console.log('✅ Previous build cleaned');
    } catch (error) {
        console.log('⚠️  Clean failed (this is often normal)');
    }
    
    // Step 2: Package the application
    if (!runCommand('npm run package', 'Packaging application')) {
        console.error('❌ Packaging failed. Build cannot continue.');
        process.exit(1);
    }
    
    // Step 3: Verify executable was created
    if (!checkFile(executablePath, 'Packaged executable')) {
        console.error('❌ Executable not found after packaging. Build cannot continue.');
        process.exit(1);
    }
    
    // Step 4: Embed icon (the post-package hook should have done this, but let's ensure it)
    console.log('\n🎨 Ensuring icon is properly embedded...');
    const iconEmbedded = await embedIconWithMultipleMethods();
    
    if (!iconEmbedded) {
        console.error('❌ Icon embedding failed with all methods. Build cannot continue.');
        process.exit(1);
    }
    
    // Step 5: Verify icon embedding
    if (!runCommand('node scripts/verify-icon-embedding.js', 'Verifying icon embedding')) {
        console.warn('⚠️  Icon verification failed, but continuing with build...');
    }
    
    // Step 6: Build NSIS installer
    if (!runCommand('node installer/build-nsis.js', 'Building NSIS installer')) {
        console.error('❌ Installer build failed.');
        process.exit(1);
    }
    
    // Step 7: Verify installer was created
    if (!checkFile(installerPath, 'NSIS installer')) {
        console.error('❌ Installer not found after build.');
        process.exit(1);
    }
    
    // Step 8: Final summary
    console.log('\n🎉 BUILD COMPLETE - SUCCESS!');
    console.log('============================');
    console.log('\n📁 Generated Files:');
    console.log(`   • Executable: ${executablePath}`);
    console.log(`   • Installer: ${installerPath}`);
    
    console.log('\n🎯 Next Steps:');
    console.log('   1. Test the installer on a clean system');
    console.log('   2. Verify desktop shortcut shows enhanced icon');
    console.log('   3. Verify taskbar shows enhanced icon when running');
    console.log('   4. Clear Windows icon cache if needed: npm run clear:icon-cache');
    
    console.log('\n✅ The enhanced QU Scheduler icon should now be properly embedded!');
}

// Run the build process
main().catch(error => {
    console.error('\n❌ BUILD FAILED:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure all dependencies are installed: npm install');
    console.log('   2. Check that icon file exists: assets/icons/icon.ico');
    console.log('   3. Try running individual steps manually');
    console.log('   4. Check for permission issues (run as administrator if needed)');
    process.exit(1);
});
