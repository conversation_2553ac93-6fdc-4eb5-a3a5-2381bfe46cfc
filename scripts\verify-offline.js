#!/usr/bin/env node

/**
 * Offline Verification Script for QU Scheduler
 * Verifies that the application can run completely offline
 */

const fs = require('fs');
const path = require('path');

const EXTERNAL_DEPENDENCIES = [
  // Google Fonts
  'fonts.googleapis.com',
  'fonts.gstatic.com',
  // CDNs
  'cdn.jsdelivr.net',
  'unpkg.com',
  'cdnjs.cloudflare.com',
  // Analytics/Tracking
  'google-analytics.com',
  'googletagmanager.com',
  'analytics.google.com',
  // Update servers
  'update.electronjs.org',
  'github.com/releases',
  // Other common external services
  'api.',
  'http://',
  'https://'
];

/**
 * Recursively search for external dependencies in files
 */
function searchInFile(filePath, searchTerms) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const findings = [];

    searchTerms.forEach(term => {
      if (content.includes(term)) {
        const lines = content.split('\n');
        lines.forEach((line, index) => {
          if (line.includes(term)) {
            findings.push({
              file: filePath,
              line: index + 1,
              content: line.trim(),
              term: term
            });
          }
        });
      }
    });

    return findings;
  } catch (error) {
    return [];
  }
}

/**
 * Recursively scan directory for files
 */
function scanDirectory(dirPath, extensions = ['.js', '.ts', '.tsx', '.jsx', '.css', '.html', '.json']) {
  const findings = [];

  try {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', '.git', 'out', 'dist', '.vite'].includes(item)) {
          findings.push(...scanDirectory(itemPath, extensions));
        }
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          findings.push(...searchInFile(itemPath, EXTERNAL_DEPENDENCIES));
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning ${dirPath}:`, error.message);
  }

  return findings;
}

/**
 * Check if all required offline assets exist
 */
function checkOfflineAssets() {
  console.log('🔍 Checking offline assets...');

  const requiredAssets = [
    'src/assets/fonts/tajawal-local.css'
  ];

  const missing = [];

  requiredAssets.forEach(asset => {
    const assetPath = path.join(__dirname, '..', asset);
    if (!fs.existsSync(assetPath)) {
      missing.push(asset);
    }
  });

  if (missing.length > 0) {
    console.log('❌ Missing offline assets:');
    missing.forEach(asset => console.log(`   - ${asset}`));
    return false;
  }

  console.log('✅ All offline assets present');
  return true;
}

/**
 * Verify package.json dependencies
 */
function checkPackageDependencies() {
  console.log('📦 Checking package dependencies...');

  const packagePath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

  const problematicDeps = [];

  // Check for auto-updater or analytics dependencies
  const allDeps = {
    ...packageJson.dependencies,
    ...packageJson.devDependencies
  };

  Object.keys(allDeps).forEach(dep => {
    if (dep.includes('update') || dep.includes('analytics') || dep.includes('telemetry')) {
      problematicDeps.push(dep);
    }
  });

  if (problematicDeps.length > 0) {
    console.log('⚠️  Found potentially problematic dependencies:');
    problematicDeps.forEach(dep => console.log(`   - ${dep}`));
  } else {
    console.log('✅ No problematic dependencies found');
  }

  return problematicDeps.length === 0;
}

/**
 * Main verification function
 */
function verifyOffline() {
  console.log('🔒 QU Scheduler Offline Verification');
  console.log('=====================================\n');

  // Check offline assets
  const assetsOk = checkOfflineAssets();

  // Check package dependencies
  const depsOk = checkPackageDependencies();

  // Scan source code for external dependencies
  console.log('🔍 Scanning source code for external dependencies...');
  const rootDir = path.join(__dirname, '..');
  const findings = scanDirectory(rootDir);

  // Filter out acceptable findings
  const problematicFindings = findings.filter(finding => {
    // Allow localhost URLs in development
    if (finding.content.includes('localhost')) return false;
    // Allow data URLs
    if (finding.content.includes('data:')) return false;
    // Allow comments
    if (finding.content.trim().startsWith('//') || finding.content.trim().startsWith('*')) return false;
    // Allow our local font import
    if (finding.content.includes('./assets/fonts/tajawal-local.css')) return false;
    // Allow package-lock.json URLs (these are metadata, not runtime dependencies)
    if (finding.file.includes('package-lock.json')) return false;
    // Allow build/development script URLs (not runtime dependencies)
    if (finding.file.includes('assets\\icons\\') || finding.file.includes('installer\\') || finding.file.includes('scripts\\')) return false;
    if (finding.file.includes('assets/icons/') || finding.file.includes('installer/') || finding.file.includes('scripts/')) return false;
    // Allow forge config URLs (build-time only)
    if (finding.file.includes('forge.config.ts')) return false;
    // Allow SVG namespace URLs (standard XML namespaces)
    if (finding.content.includes('xmlns="http://www.w3.org/2000/svg"')) return false;
    // Allow verification script itself (it contains the URLs it's checking for)
    if (finding.file.includes('verify-offline.js')) return false;
    // Allow download script (used only during build)
    if (finding.file.includes('download-fonts.js')) return false;

    return true;
  });

  if (problematicFindings.length > 0) {
    console.log('❌ Found external dependencies:');
    problematicFindings.forEach(finding => {
      console.log(`   ${finding.file}:${finding.line} - ${finding.content}`);
    });
  } else {
    console.log('✅ No external dependencies found in source code');
  }

  // Summary
  console.log('\n📋 Verification Summary:');
  console.log(`   Offline Assets: ${assetsOk ? '✅' : '❌'}`);
  console.log(`   Dependencies: ${depsOk ? '✅' : '❌'}`);
  console.log(`   Source Code: ${problematicFindings.length === 0 ? '✅' : '❌'}`);

  const isOfflineReady = assetsOk && depsOk && problematicFindings.length === 0;

  if (isOfflineReady) {
    console.log('\n🎉 QU Scheduler is ready for offline use!');
    console.log('   The application can run without internet connection.');
  } else {
    console.log('\n⚠️  QU Scheduler needs attention for offline use.');
    console.log('   Please address the issues above.');
  }

  return isOfflineReady;
}

if (require.main === module) {
  const success = verifyOffline();
  process.exit(success ? 0 : 1);
}

module.exports = { verifyOffline };
