// QU Scheduler Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Mobile Navigation Toggle
    const mobileMenu = document.getElementById('mobile-menu');
    const navMenu = document.querySelector('.nav-menu');

    if (mobileMenu && navMenu) {
        mobileMenu.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }

    // Smooth Scrolling for Navigation Links
    const smoothScrollLinks = document.querySelectorAll('a[href^="#"]');
    smoothScrollLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 70; // Account for fixed navbar
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Navbar Background Change on Scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.backdropFilter = 'blur(10px)';
            } else {
                navbar.style.background = '#FFFFFF';
                navbar.style.backdropFilter = 'none';
            }
        });
    }

    // Intersection Observer for Animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Animate elements on scroll
    const animateElements = document.querySelectorAll('.feature-card, .screenshot-item, .download-card, .support-card');
    animateElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(element);
    });

    // Download Button Analytics (placeholder for future implementation)
    const downloadButtons = document.querySelectorAll('.download-btn');
    downloadButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const downloadType = 'installer';
            console.log(`Download initiated: ${downloadType}`);

            // Show download confirmation
            showDownloadConfirmation(downloadType);
        });
    });

    // Download Confirmation Modal
    function showDownloadConfirmation(type) {
        const modal = createModal(
            'Download Started',
            `Your ${type} download has started. If the download doesn't begin automatically, please check your browser's download settings.`,
            [
                {
                    text: 'OK',
                    class: 'btn btn-primary',
                    action: () => closeModal()
                }
            ]
        );
        document.body.appendChild(modal);
    }

    // Create Modal Function
    function createModal(title, message, buttons) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        const modalContent = document.createElement('div');
        modalContent.className = 'modal-content';
        modalContent.style.cssText = `
            background: white;
            padding: 2rem;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        `;

        const modalTitle = document.createElement('h3');
        modalTitle.textContent = title;
        modalTitle.style.cssText = `
            color: #8B1538;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        `;

        const modalMessage = document.createElement('p');
        modalMessage.textContent = message;
        modalMessage.style.cssText = `
            color: #6C757D;
            margin-bottom: 2rem;
            line-height: 1.6;
        `;

        const modalButtons = document.createElement('div');
        modalButtons.style.cssText = `
            display: flex;
            gap: 1rem;
            justify-content: center;
        `;

        buttons.forEach(buttonConfig => {
            const button = document.createElement('button');
            button.textContent = buttonConfig.text;
            button.className = buttonConfig.class;
            button.style.cssText = `
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-weight: 600;
                transition: all 0.3s ease;
            `;

            if (buttonConfig.class.includes('btn-primary')) {
                button.style.background = '#D4AF37';
                button.style.color = '#343A40';
            }

            button.addEventListener('click', buttonConfig.action);
            modalButtons.appendChild(button);
        });

        modalContent.appendChild(modalTitle);
        modalContent.appendChild(modalMessage);
        modalContent.appendChild(modalButtons);
        modal.appendChild(modalContent);

        // Animate modal in
        setTimeout(() => {
            modal.style.opacity = '1';
            modalContent.style.transform = 'scale(1)';
        }, 10);

        // Close modal when clicking overlay
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        return modal;
    }

    // Close Modal Function
    function closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.style.opacity = '0';
            modal.querySelector('.modal-content').style.transform = 'scale(0.9)';
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    }

    // Email Link Handler
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    emailLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Check if email client is available
            const testLink = document.createElement('a');
            testLink.href = this.href;

            // Show helpful message for users without email client
            setTimeout(() => {
                const modal = createModal(
                    'Email Support',
                    'If your email client didn\'t open automatically, please send your inquiry to: <EMAIL>',
                    [
                        {
                            text: 'Copy Email',
                            class: 'btn btn-secondary',
                            action: () => {
                                navigator.clipboard.writeText('<EMAIL>').then(() => {
                                    console.log('Email copied to clipboard');
                                });
                                closeModal();
                            }
                        },
                        {
                            text: 'OK',
                            class: 'btn btn-primary',
                            action: () => closeModal()
                        }
                    ]
                );
                document.body.appendChild(modal);
            }, 1000);
        });
    });

    // Feature Card Hover Effects
    const featureCards = document.querySelectorAll('.feature-card');
    featureCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px) scale(1)';
        });
    });

    // Screenshot Lightbox Effect
    const screenshots = document.querySelectorAll('.screenshot-img');
    screenshots.forEach(img => {
        img.addEventListener('click', function() {
            createLightbox(this.src, this.alt);
        });

        // Add cursor pointer
        img.style.cursor = 'pointer';
    });

    // Create Lightbox Function
    function createLightbox(src, alt) {
        const lightbox = document.createElement('div');
        lightbox.className = 'lightbox-overlay';
        lightbox.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
            cursor: pointer;
        `;

        const img = document.createElement('img');
        img.src = src;
        img.alt = alt;
        img.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        `;

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '×';
        closeButton.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 2rem;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.3s ease;
        `;

        closeButton.addEventListener('mouseenter', function() {
            this.style.background = 'rgba(255, 255, 255, 0.3)';
        });

        closeButton.addEventListener('mouseleave', function() {
            this.style.background = 'rgba(255, 255, 255, 0.2)';
        });

        lightbox.appendChild(img);
        lightbox.appendChild(closeButton);
        document.body.appendChild(lightbox);

        // Animate lightbox in
        setTimeout(() => {
            lightbox.style.opacity = '1';
            img.style.transform = 'scale(1)';
        }, 10);

        // Close lightbox
        function closeLightbox() {
            lightbox.style.opacity = '0';
            img.style.transform = 'scale(0.9)';
            setTimeout(() => {
                lightbox.remove();
            }, 300);
        }

        lightbox.addEventListener('click', function(e) {
            if (e.target === lightbox) {
                closeLightbox();
            }
        });

        closeButton.addEventListener('click', closeLightbox);

        // Close with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLightbox();
            }
        });
    }

    // Scroll to Top Button
    const scrollToTopBtn = document.createElement('button');
    scrollToTopBtn.innerHTML = '↑';
    scrollToTopBtn.className = 'scroll-to-top';
    scrollToTopBtn.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #8B1538;
        color: white;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3);
    `;

    document.body.appendChild(scrollToTopBtn);

    // Show/hide scroll to top button
    window.addEventListener('scroll', function() {
        if (window.scrollY > 500) {
            scrollToTopBtn.style.opacity = '1';
            scrollToTopBtn.style.visibility = 'visible';
        } else {
            scrollToTopBtn.style.opacity = '0';
            scrollToTopBtn.style.visibility = 'hidden';
        }
    });

    // Scroll to top functionality
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Add hover effect to scroll to top button
    scrollToTopBtn.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.1)';
        this.style.background = '#A61E42';
    });

    scrollToTopBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
        this.style.background = '#8B1538';
    });

    // Loading Animation for Download Links
    const downloadLinks = document.querySelectorAll('a[href*=".exe"], a[href*=".zip"]');
    downloadLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            const originalText = this.textContent;
            this.textContent = 'Preparing Download...';
            this.style.pointerEvents = 'none';

            setTimeout(() => {
                this.textContent = originalText;
                this.style.pointerEvents = 'auto';
            }, 2000);
        });
    });

    // FAQ Accordion Functionality
    const faqQuestions = document.querySelectorAll('.faq-question');
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            const answer = this.nextElementSibling;
            const toggle = this.querySelector('.faq-toggle');

            // Close all other FAQ items
            faqQuestions.forEach(otherQuestion => {
                if (otherQuestion !== this) {
                    const otherAnswer = otherQuestion.nextElementSibling;
                    const otherToggle = otherQuestion.querySelector('.faq-toggle');
                    otherAnswer.classList.remove('active');
                    otherToggle.textContent = '+';
                }
            });

            // Toggle current FAQ item
            if (answer.classList.contains('active')) {
                answer.classList.remove('active');
                toggle.textContent = '+';
            } else {
                answer.classList.add('active');
                toggle.textContent = '−';
            }
        });
    });

    // Form Enhancement
    const supportForm = document.querySelector('.support-form');
    if (supportForm) {
        supportForm.addEventListener('submit', function(e) {
            // Basic form validation
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = '#dc3545';
                } else {
                    field.style.borderColor = '#E9ECEF';
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return;
            }

            // Show submission confirmation
            setTimeout(() => {
                const modal = createModal(
                    'Message Sent',
                    'Your message has been prepared for sending. Your email client should open with the pre-filled message.',
                    [
                        {
                            text: 'OK',
                            class: 'btn btn-primary',
                            action: () => closeModal()
                        }
                    ]
                );
                document.body.appendChild(modal);
            }, 500);
        });
    }

    // Support Card Animations
    const supportCards = document.querySelectorAll('.support-card, .doc-item');
    supportCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(-5px) scale(1)';
        });
    });

    // Contact Form Auto-fill for Email
    const contactForm = document.querySelector('.support-form');
    if (contactForm) {
        const nameField = contactForm.querySelector('#name');
        const emailField = contactForm.querySelector('#email');
        const subjectField = contactForm.querySelector('#subject');
        const messageField = contactForm.querySelector('#message');

        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Construct email content
            const emailSubject = `QU Scheduler Support: ${subjectField.value}`;
            const emailBody = `Name: ${nameField.value}
Email: ${emailField.value}
Subject: ${subjectField.value}

Message:
${messageField.value}

---
Sent from QU Scheduler Website
Version: 1.0.0`;

            // Create mailto link
            const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

            // Open email client
            window.location.href = mailtoLink;

            // Reset form
            this.reset();

            // Show confirmation
            const modal = createModal(
                'Email Prepared',
                'Your email client should open with the pre-filled message. If it doesn\'t open automatically, please copy the email address: <EMAIL>',
                [
                    {
                        text: 'Copy Email',
                        class: 'btn btn-secondary',
                        action: () => {
                            navigator.clipboard.writeText('<EMAIL>').then(() => {
                                console.log('Email copied to clipboard');
                            });
                            closeModal();
                        }
                    },
                    {
                        text: 'OK',
                        class: 'btn btn-primary',
                        action: () => closeModal()
                    }
                ]
            );
            document.body.appendChild(modal);
        });
    }

    console.log('QU Scheduler website loaded successfully');
});
