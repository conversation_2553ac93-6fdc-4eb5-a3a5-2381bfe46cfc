/* Import local font configuration for offline use */
@import './assets/fonts/tajawal-local.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica,
    Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

#root {
  height: 100%;
  width: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Arabic text styling is now handled by the imported local font CSS */
/* These classes are defined in ./assets/fonts/tajawal-local.css for offline use */

/* Style for auto-generated sessions */
.auto-generated {
  border: 2px dashed #3b82f6 !important; /* Blue dashed border */
  box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
}

/* Focus management for modals - prevents focus on background content */
.modal-open #root > * {
  pointer-events: none;
}

.modal-open .MuiDialog-root {
  pointer-events: auto;
}

/* Ensure inert elements cannot receive focus */
[inert] {
  pointer-events: none;
}

[inert] * {
  pointer-events: none;
  user-select: none;
}

/* Support for browsers that don't support inert attribute natively */
.modal-open #root > *:not(.MuiDialog-root) {
  visibility: hidden;
}

.modal-open .MuiDialog-root {
  visibility: visible;
}

/* Modal content wrapper styling */
.modal-content-wrapper {
  outline: none;
}
