import type { Lecturer, Session, Semester, Section, Course } from '../types/models';

export interface PdfTemplateData {
  lecturer: Lecturer;
  currentSemester: Semester;
  departmentName: string;
  academicYear: string;
  semesterLoad: number;
  fallLoad: number;
  springLoad: number;
  totalYearLoad: number;
  overload: number;
  periods: { period: number; regularTime: string; longTime: string }[];
  days: string[];
  sessions: Record<Semester, Session[]>;
  sections: Record<Semester, Section[]>;
  courses: Record<Semester, Course[]>;
  lecturers: Lecturer[];
  findSessionsForDayAndPeriod: (day: string, period: number) => Session[];
  genderFilter?: 'M' | 'F' | null;
  isAllLecturers?: boolean;
}

/**
 * Generates HTML template for lecturer timetable PDF export
 */
export const generateLecturerPdfTemplate = (data: PdfTemplateData): string => {
  const {
    lecturer,
    currentSemester,
    departmentName,
    academicYear,
    semesterLoad,
    fallLoad,
    springLoad,
    totalYearLoad,
    overload,
    periods,
    days,
    sections,
    courses,
    lecturers,
    findSessionsForDayAndPeriod,
    isAllLecturers = false
  } = data;

  // SVG paths for gender icons (Material-UI icons)
  const MAN_ICON_PATH = "M12 7c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm2 2h-4c-1.1 0-2 .9-2 2v7h2v7h4v-7h2v-7c0-1.1-.9-2-2-2z";
  const WOMAN_ICON_PATH = "M13.94 8.31C13.62 7.52 12.85 7 12 7s-1.62.52-1.94 1.31L7 16h3v6h4v-6h3l-3.06-7.69zM12 4c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2z";

  // Helper function to convert hex color to rgba with opacity
  const hexToRgba = (hex: string, opacity: number): string => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) return `rgba(0, 0, 0, ${opacity})`;

    const r = parseInt(result[1], 16);
    const g = parseInt(result[2], 16);
    const b = parseInt(result[3], 16);

    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  };

  // Helper function to get time display for a period and day
  const getTimeDisplay = (period: number, day: string): { start: string; end: string } => {
    const isLongDay = day === 'Monday' || day === 'Wednesday';

    // Time periods mapping
    const timeMap: { [key: number]: { regular: string; long: string } } = {
      1: { regular: '8 AM', long: '8 AM' },
      2: { regular: '9 AM', long: '9:30 AM' },
      3: { regular: '10 AM', long: '11 AM' },
      4: { regular: '11 AM', long: '12:30 PM' },
      5: { regular: '12 PM', long: '2 PM' },
      6: { regular: '1 PM', long: '3:30 PM' },
      7: { regular: '2 PM', long: '2 PM' },
      8: { regular: '3 PM', long: '3:30 PM' },
      9: { regular: '4 PM', long: '5 PM' },
      10: { regular: '5 PM', long: '6:30 PM' },
      11: { regular: '6 PM', long: '8 PM' },
      12: { regular: '7 PM', long: '9:30 PM' }
    };

    // End time mapping
    const endTimeMap: { [key: number]: { regular: string; long: string } } = {
      1: { regular: '9 AM', long: '9:30 AM' },
      2: { regular: '10 AM', long: '11 AM' },
      3: { regular: '11 AM', long: '12:30 PM' },
      4: { regular: '12 PM', long: '2 PM' },
      5: { regular: '1 PM', long: '3:30 PM' },
      6: { regular: '2 PM', long: '5 PM' },
      7: { regular: '3 PM', long: '3:30 PM' },
      8: { regular: '4 PM', long: '5 PM' },
      9: { regular: '5 PM', long: '6:30 PM' },
      10: { regular: '6 PM', long: '8 PM' },
      11: { regular: '7 PM', long: '9:30 PM' },
      12: { regular: '8 PM', long: '11 PM' }
    };

    const startTime = isLongDay ? timeMap[period]?.long : timeMap[period]?.regular;
    const endTime = isLongDay ? endTimeMap[period]?.long : endTimeMap[period]?.regular;

    return { start: startTime || '', end: endTime || '' };
  };

  // Helper function to format session text with course colors, gender icons, and time pills
  const formatSessionText = (session: Session, day: string, period: number, includeLecturer = false): string => {
    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return '';
    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return '';

    const genderIcon = section.gender === 'M' ? MAN_ICON_PATH : WOMAN_ICON_PATH;
    const genderColor = section.gender === 'M' ? '#2196f3' : '#e91e63';
    const courseBackgroundColor = hexToRgba(course.color, 0.1);

    // Get time display for this period and day
    const timeDisplay = getTimeDisplay(period, day);
    const timeText = timeDisplay.start && timeDisplay.end ? `${timeDisplay.start}-${timeDisplay.end}` : '';

    let sessionText = `
      <div class="session-content" style="background-color: ${courseBackgroundColor}; border-left: 3px solid ${course.color};">
        ${timeText ? `<div class="time-pill">${timeText}</div>` : ''}
        <div class="session-header">
          <span class="course-code">${course.courseCode} (${section.sectionNumber})</span>
          <svg class="gender-icon" viewBox="0 0 24 24" style="fill: ${genderColor};">
            <path d="${genderIcon}"/>
          </svg>
        </div>
        <div class="course-name arabic">${course.courseName}</div>`;

    if (includeLecturer) {
      let lecturerName = '';
      if (session.lecturerId) {
        const sessionLecturer = lecturers.find(l => l.id === session.lecturerId);
        if (sessionLecturer) {
          lecturerName = `${sessionLecturer.title || ''} ${sessionLecturer.firstName} ${sessionLecturer.lastName}`.trim();
        }
      }
      if (lecturerName) {
        sessionText += `<div class="lecturer-name arabic">${lecturerName}</div>`;
      }
    }

    sessionText += `</div>`;
    return sessionText;
  };

  // Generate table rows with period-based grid layout
  const generateTableRows = (): string => {
    let tableRows = '';

    // Create one row per period (combining regular and long days)
    periods.forEach(period => {
      tableRows += `<tr>`;

      // Period number column
      tableRows += `<td class="period-cell">${period.period}</td>`;

      // Day columns
      days.forEach(day => {
        const sessionsForCell = findSessionsForDayAndPeriod(day, period.period);
        if (sessionsForCell.length === 0) {
          tableRows += `<td class="session-cell"></td>`;
        } else {
          const sessionTexts = sessionsForCell.map(session =>
            formatSessionText(session, day, period.period, isAllLecturers)
          );
          tableRows += `<td class="session-cell">${sessionTexts.join('<div class="session-separator"></div>')}</td>`;
        }
      });

      tableRows += `</tr>`;
    });

    return tableRows;
  };

  return `
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Using system fonts for offline capability -->
  <title>${isAllLecturers ? 'All Lecturers Timetable' : `${lecturer.title} ${lecturer.firstName} ${lecturer.lastName}'s Timetable`}</title>
  <style>
    /* Use system fonts to avoid CSP issues in preview window */

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      font-size: 12px;
      line-height: 1.4;
      color: #333;
      background: white;
      padding: 20px;
    }

    .arabic {
      font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
      direction: rtl;
      text-align: right;
    }

    .header {
      margin-bottom: 20px;
      border-bottom: 2px solid #1976d2;
      padding-bottom: 15px;
      page-break-inside: avoid;
    }

    .main-title {
      font-size: 18px;
      font-weight: 700;
      color: #1976d2;
      margin-bottom: 5px;
    }

    .subtitle {
      font-size: 11px;
      color: #666;
      margin-bottom: 10px;
    }

    .lecturer-info {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20px;
      margin-bottom: 15px;
      font-size: 10px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .info-label {
      font-weight: 600;
      color: #555;
    }

    .info-value {
      color: #333;
    }

    .overload-positive {
      color: #d32f2f;
      font-weight: 600;
    }

    .overload-negative {
      color: #388e3c;
      font-weight: 600;
    }

    .timetable {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      font-size: 9px;
    }

    .timetable th {
      background-color: #1976d2;
      color: white;
      padding: 8px 4px;
      text-align: center;
      font-weight: 600;
      border: 2px solid #1565c0;
    }

    .timetable td {
      border: 2px solid #bbb;
      padding: 4px;
      vertical-align: top;
      min-height: 40px;
    }

    .period-cell {
      background-color: #1976d2;
      color: white;
      text-align: center;
      font-weight: bold;
      width: 60px;
      font-size: 14px;
      vertical-align: middle;
    }

    .session-cell {
      width: calc((100% - 60px) / 5);
      min-height: 50px;
      padding: 2px;
      position: relative;
    }

    .session-content {
      margin: 2px 0;
      padding: 4px;
      border-radius: 4px;
      font-size: 8px;
      position: relative;
    }

    .time-pill {
      position: absolute;
      top: 2px;
      right: 4px;
      background-color: rgba(0, 0, 0, 0.04);
      color: #666;
      padding: 1px 4px;
      border-radius: 10px;
      font-size: 6px;
      opacity: 0.8;
      z-index: 1;
    }

    .session-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;
      margin-top: 12px;
      padding-right: 2px;
    }

    .course-code {
      font-weight: 600;
      font-size: 8px;
      color: #333;
      font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .gender-icon {
      width: 12px;
      height: 12px;
      flex-shrink: 0;
    }

    .course-name {
      font-size: 7px;
      color: #666;
      font-weight: 400;
      margin-bottom: 2px;
      line-height: 1.2;
      font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .lecturer-name {
      font-size: 7px;
      color: #888;
      font-style: italic;
      line-height: 1.2;
      font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .session-separator {
      margin: 4px 0;
      border-top: 1px solid #ddd;
      padding-top: 4px;
    }

    @media print {
      body {
        padding: 10px;
      }

      .header {
        margin-bottom: ${isAllLecturers ? '10px' : '15px'};
        padding-bottom: ${isAllLecturers ? '5px' : '10px'};
        page-break-after: avoid;
        page-break-inside: avoid;
      }

      .main-title {
        page-break-after: avoid;
      }

      .subtitle {
        page-break-before: avoid;
      }

      .timetable {
        page-break-inside: avoid;
      }

      .timetable tr {
        page-break-inside: avoid;
        break-inside: avoid;
      }

      @page {
        margin: 0.5in;
        @bottom-center {
          content: "Page " counter(page) " of " counter(pages);
          font-size: 10px;
          color: #666;
        }
      }
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="main-title">
      ${isAllLecturers ? 'All Lecturers Timetable' : `${lecturer.title} ${lecturer.firstName} ${lecturer.lastName}'s Timetable`}
    </div>
    <div class="subtitle">
      ${departmentName} | ${academicYear} | ${currentSemester}
    </div>

    ${!isAllLecturers ? `
    <div class="lecturer-info">
      <div>
        <div class="info-item">
          <span class="info-label">Fall Load:</span>
          <span class="info-value">${fallLoad.toFixed(1)} hrs${currentSemester === 'Fall' ? ' (Current)' : ''}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Spring Load:</span>
          <span class="info-value">${springLoad.toFixed(1)} hrs${currentSemester === 'Spring' ? ' (Current)' : ''}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Summer Load:</span>
          <span class="info-value">${currentSemester === 'Summer' ? semesterLoad.toFixed(1) : '0.0'} hrs${currentSemester === 'Summer' ? ' (Current)' : ''}</span>
        </div>
      </div>

      <div>
        <div class="info-item">
          <span class="info-label">Fall Supervision:</span>
          <span class="info-value">${lecturer.supervisionHoursFall.toFixed(1)} hrs</span>
        </div>
        <div class="info-item">
          <span class="info-label">Spring Supervision:</span>
          <span class="info-value">${lecturer.supervisionHoursSpring.toFixed(1)} hrs</span>
        </div>
        <div class="info-item">
          <span class="info-label">Email:</span>
          <span class="info-value">${lecturer.email}</span>
        </div>
      </div>

      <div>
        <div class="info-item">
          <span class="info-label">Year Load:</span>
          <span class="info-value">${totalYearLoad.toFixed(1)}/${lecturer.maxYearLoad.toFixed(1)} hrs</span>
        </div>
        <div class="info-item">
          <span class="info-label">Overload:</span>
          <span class="info-value ${overload > 0 ? 'overload-positive' : 'overload-negative'}">
            ${overload > 0 ? '+' : ''}${overload.toFixed(1)} hrs
          </span>
        </div>
      </div>
    </div>
    ` : ''}
  </div>

  <table class="timetable">
    <thead>
      <tr>
        <th>Period</th>
        ${days.map(day => `<th>${day}</th>`).join('')}
      </tr>
    </thead>
    <tbody>
      ${generateTableRows()}
    </tbody>
  </table>
</body>
</html>
  `;
};
