import React from 'react';
import { Course, Section } from '../types/models';

interface CourseCardProps {
  course: Course;
  sections: Section[];
  isFullyScheduled: boolean;
  children: React.ReactNode;
  className?: string;
  [key: string]: unknown; // For additional props like drag handlers
}

/**
 * CourseCard component that displays a course with visual indication for fully scheduled courses
 * Shows a thick green border on the top edge when all sections are fully scheduled
 */
const CourseCard: React.FC<CourseCardProps> = ({
  course: _course,
  sections: _sections,
  isFullyScheduled,
  children,
  className = '',
  ...otherProps
}) => {
  return (
    <div
      className={`mb-2 border rounded overflow-hidden shadow-md bg-amber-50 dark:bg-gray-800 ${isFullyScheduled ? 'border-t-4 border-t-green-500' : ''} ${className}`}
      {...otherProps}
    >
      {children}
    </div>
  );
};

export default CourseCard;