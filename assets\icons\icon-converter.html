<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QU Scheduler Icon Converter</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B1538;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .icon-item:hover {
            border-color: #8B1538;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 21, 56, 0.1);
        }
        .icon-container {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .icon-container svg {
            width: 100%;
            height: 100%;
        }
        .icon-size {
            font-weight: bold;
            color: #8B1538;
            margin-bottom: 5px;
        }
        .icon-usage {
            font-size: 0.9em;
            color: #666;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            margin: 30px 0;
        }
        .download-btn {
            background: #8B1538;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            transition: background 0.3s ease;
        }
        .download-btn:hover {
            background: #A61E42;
        }
        .master-icon {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #8B1538, #A61E42);
            border-radius: 8px;
            color: white;
        }
        .master-icon svg {
            width: 128px;
            height: 128px;
            background: white;
            border-radius: 8px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 QU Scheduler Icon Converter</h1>
        
        <div class="master-icon">
            <h2>Master Icon (SVG)</h2>
            <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
            <p>This is the master SVG icon that serves as the source for all other formats.</p>
        </div>
        
        <div class="instructions">
            <h3>📋 Conversion Instructions</h3>
            <p>Generate PNG files from the master SVG icon for different sizes and platforms:</p>
            <ol>
                <li><strong>Automatic (Recommended):</strong> Install Sharp and run <code>npm run convert:icons</code></li>
                <li><strong>Manual:</strong> Right-click each icon below and save as PNG</li>
                <li><strong>Batch:</strong> Use ImageMagick or similar tools for bulk conversion</li>
            </ol>
        </div>
        
        <h2>Required Icon Sizes</h2>
        <div class="icon-grid">
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 16px; height: 16px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">16×16</div>
                    <div class="icon-usage">Small icons, favicons, taskbar</div>
                    <button class="download-btn" onclick="downloadIcon(16)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 24px; height: 24px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">24×24</div>
                    <div class="icon-usage">Standard icons, toolbars, menus</div>
                    <button class="download-btn" onclick="downloadIcon(24)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 32px; height: 32px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">32×32</div>
                    <div class="icon-usage">Standard icons, toolbars, menus</div>
                    <button class="download-btn" onclick="downloadIcon(32)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 48px; height: 48px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">48×48</div>
                    <div class="icon-usage">Desktop shortcuts, file associations</div>
                    <button class="download-btn" onclick="downloadIcon(48)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 64px; height: 64px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">64×64</div>
                    <div class="icon-usage">Large icons, quick launch</div>
                    <button class="download-btn" onclick="downloadIcon(64)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 64px; height: 64px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">128×128</div>
                    <div class="icon-usage">High-DPI displays, retina screens</div>
                    <button class="download-btn" onclick="downloadIcon(128)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 64px; height: 64px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">256×256</div>
                    <div class="icon-usage">Application icons, installer graphics</div>
                    <button class="download-btn" onclick="downloadIcon(256)">
                        Download PNG
                    </button>
                </div>
            
                <div class="icon-item">
                    <div class="icon-container" style="width: 64px; height: 64px;">
                        <?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>
                    </div>
                    <div class="icon-size">512×512</div>
                    <div class="icon-usage">High-resolution displays, print materials</div>
                    <button class="download-btn" onclick="downloadIcon(512)">
                        Download PNG
                    </button>
                </div>
            
        </div>
        
        <div class="instructions">
            <h3>🔧 Command Line Tools</h3>
            <p><strong>ImageMagick:</strong></p>
            <pre>magick icon.svg -resize 256x256 icon-256x256.png</pre>
            
            <p><strong>Inkscape:</strong></p>
            <pre>inkscape icon.svg --export-png=icon-256x256.png --export-width=256 --export-height=256</pre>
            
            <p><strong>Batch conversion (ImageMagick):</strong></p>
            <pre>for size in 16 24 32 48 64 128 256 512; do
  magick icon.svg -resize ${size}x${size} icon-${size}x${size}.png
done</pre>
        </div>
    </div>
    
    <script>
        function downloadIcon(size) {
            // Create a canvas element
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            // Create an image from SVG
            const svgData = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Qatar University Maroon -->
    <linearGradient id="maroonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B1538;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6B1028;stop-opacity:1" />
    </linearGradient>

    <!-- Gold/Yellow for course sections -->
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1C40F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D4AF37;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Outer maroon border/frame with enhanced corner radius -->
  <rect x="0" y="0" width="512" height="512" fill="url(#maroonGradient)" rx="48"/>

  <!-- White background (simplified - no black background) -->
  <rect x="24" y="24" width="464" height="464" fill="#FFFFFF" rx="32"/>

  <!-- Three golden/yellow course sections at the top with maroon borders -->
  <!-- Course Section 1 -->
  <rect x="80" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="86" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 2 -->
  <rect x="206" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="212" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Course Section 3 -->
  <rect x="332" y="80" width="100" height="120" fill="url(#maroonGradient)" rx="16"/>
  <rect x="338" y="86" width="88" height="108" fill="url(#goldGradient)" rx="12"/>

  <!-- Simplified 3×3 timetable/calendar grid with perfectly consistent 16px borders -->
  <rect x="80" y="240" width="352" height="184" fill="url(#maroonGradient)" rx="16"/>

  <!-- White cells in the 3×3 grid with consistent 16px maroon borders -->
  <g fill="#FFFFFF">
    <!-- Row 1 -->
    <rect x="96" y="256" width="96" height="40" rx="6"/>
    <rect x="208" y="256" width="96" height="40" rx="6"/>
    <rect x="320" y="256" width="96" height="40" rx="6"/>

    <!-- Row 2 -->
    <rect x="96" y="312" width="96" height="40" rx="6"/>
    <rect x="208" y="312" width="96" height="40" rx="6"/>
    <rect x="320" y="312" width="96" height="40" rx="6"/>

    <!-- Row 3 -->
    <rect x="96" y="368" width="96" height="40" rx="6"/>
    <rect x="208" y="368" width="96" height="40" rx="6"/>
    <rect x="320" y="368" width="96" height="40" rx="6"/>
  </g>
</svg>`;
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // Convert to PNG and download
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = `icon-${size}x${size}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
            };
            img.src = url;
        }
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                alert('Use the Download PNG buttons to save individual icons');
            }
        });
    </script>
</body>
</html>