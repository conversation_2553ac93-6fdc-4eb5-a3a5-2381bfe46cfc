import { Course, Lecturer, Session, Section, Semester } from '../types/models';

// Export data structure
export interface ExportData {
  metadata: {
    exportDate: string;
    departmentName: string;
    academicYear: string;
    semester: Semester | 'all';
    version: string;
  };
  data: {
    courses: Record<Semester, Course[]>;
    lecturers: Lecturer[];
    sessions: Record<Semester, Session[]>;
    sections: Record<Semester, Section[]>;
  };
}

// Import validation result
export interface ImportValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  targetSemester?: Semester;
  data?: ExportData;
}

// Import summary
export interface ImportSummary {
  coursesAdded: number;
  lecturersAdded: number;
  sessionsImported: number;
  sectionsImported: number;
  targetSemester: Semester;
}

/**
 * Generate filename for export based on department, year, and semester
 */
export const generateExportFilename = (
  departmentName: string,
  academicYear: string,
  semester: Semester | 'all'
): string => {
  // Clean department name for filename
  const cleanDepartmentName = departmentName
    .trim()
    .replace(/[^a-zA-Z0-9\s&\-().]/g, '')
    .replace(/\s+/g, '-')
    .toLowerCase();

  // Clean academic year for filename (convert slashes to dashes)
  const cleanAcademicYear = academicYear
    .trim()
    .replace(/[^0-9/-]/g, '')
    .replace(/\//g, '-');

  const semesterPart = semester === 'all' ? 'all-semesters' : semester.toLowerCase();

  // Format: [department-name]-timetable-[academic-year]-[semester].json
  return `${cleanDepartmentName || 'department'}-timetable-${cleanAcademicYear || 'academic-year'}-${semesterPart}.json`;
};

/**
 * Prepare data for export
 */
export const prepareExportData = (
  courses: Record<Semester, Course[]>,
  lecturers: Lecturer[],
  sessions: Record<Semester, Session[]>,
  sections: Record<Semester, Section[]>,
  departmentName: string,
  academicYear: string,
  semester: Semester | 'all'
): ExportData => {
  const exportData: ExportData = {
    metadata: {
      exportDate: new Date().toISOString(),
      departmentName: departmentName || '',
      academicYear: academicYear || '',
      semester,
      version: '1.0'
    },
    data: {
      courses: {
        Fall: [],
        Spring: [],
        Summer: []
      },
      lecturers,
      sessions: {
        Fall: [],
        Spring: [],
        Summer: []
      },
      sections: {
        Fall: [],
        Spring: [],
        Summer: []
      }
    }
  };

  if (semester === 'all') {
    // Export all semesters
    exportData.data.courses = courses;
    exportData.data.sessions = sessions;
    exportData.data.sections = sections;
  } else {
    // Export specific semester
    exportData.data.courses = {
      Fall: semester === 'Fall' ? courses.Fall : [],
      Spring: semester === 'Spring' ? courses.Spring : [],
      Summer: semester === 'Summer' ? courses.Summer : []
    };
    exportData.data.sessions = {
      Fall: semester === 'Fall' ? sessions.Fall : [],
      Spring: semester === 'Spring' ? sessions.Spring : [],
      Summer: semester === 'Summer' ? sessions.Summer : []
    };
    exportData.data.sections = {
      Fall: semester === 'Fall' ? sections.Fall : [],
      Spring: semester === 'Spring' ? sections.Spring : [],
      Summer: semester === 'Summer' ? sections.Summer : []
    };
  }

  return exportData;
};

/**
 * Validate imported data
 */
export const validateImportData = (jsonData: unknown): ImportValidationResult => {
  const result: ImportValidationResult = {
    isValid: false,
    errors: [],
    warnings: []
  };

  try {
    // Check if data is valid JSON object
    if (!jsonData || typeof jsonData !== 'object') {
      result.errors.push('Invalid file format: Not a valid JSON object');
      return result;
    }

    // Type guard to check if jsonData has the expected structure
    const hasRequiredFields = (obj: object): obj is { metadata: unknown; data: unknown } => {
      return 'metadata' in obj && 'data' in obj;
    };

    // Check for required structure
    if (!hasRequiredFields(jsonData)) {
      result.errors.push('Invalid file format: Missing metadata or data sections');
      return result;
    }

    const { metadata, data } = jsonData;

    // Validate metadata
    if (!(metadata as any).exportDate || !(metadata as any).version) {
      result.errors.push('Invalid metadata: Missing export date or version');
      return result;
    }

    // Validate data structure
    if (!(data as any).courses || !(data as any).lecturers || !(data as any).sessions || !(data as any).sections) {
      result.errors.push('Invalid data structure: Missing required data sections');
      return result;
    }

    // Determine target semester
    let targetSemester: Semester;
    if ((metadata as any).semester === 'all') {
      // For all semesters export, we need to determine which semester has data
      const semestersWithData: Semester[] = [];
      (['Fall', 'Spring', 'Summer'] as Semester[]).forEach(sem => {
        if ((data as any).courses[sem]?.length > 0 || (data as any).sessions[sem]?.length > 0 || (data as any).sections[sem]?.length > 0) {
          semestersWithData.push(sem);
        }
      });

      if (semestersWithData.length === 0) {
        result.errors.push('No data found in any semester');
        return result;
      } else if (semestersWithData.length === 1) {
        targetSemester = semestersWithData[0];
      } else {
        // Multiple semesters have data, default to Fall
        targetSemester = 'Fall';
        result.warnings.push(`Multiple semesters contain data. Importing to ${targetSemester} semester.`);
      }
    } else {
      targetSemester = (metadata as any).semester as Semester;
    }

    // Validate semester value
    if (!['Fall', 'Spring', 'Summer'].includes(targetSemester)) {
      result.errors.push('Invalid semester in metadata');
      return result;
    }

    // Validate data arrays
    if (!Array.isArray((data as any).lecturers)) {
      result.errors.push('Invalid lecturers data: Must be an array');
      return result;
    }

    ['Fall', 'Spring', 'Summer'].forEach(sem => {
      if ((data as any).courses[sem] && !Array.isArray((data as any).courses[sem])) {
        result.errors.push(`Invalid courses data for ${sem}: Must be an array`);
      }
      if ((data as any).sessions[sem] && !Array.isArray((data as any).sessions[sem])) {
        result.errors.push(`Invalid sessions data for ${sem}: Must be an array`);
      }
      if ((data as any).sections[sem] && !Array.isArray((data as any).sections[sem])) {
        result.errors.push(`Invalid sections data for ${sem}: Must be an array`);
      }
    });

    if (result.errors.length > 0) {
      return result;
    }

    // Additional warnings
    if (!(metadata as any).departmentName) {
      result.warnings.push('No department name in export file');
    }
    if (!(metadata as any).academicYear) {
      result.warnings.push('No academic year in export file');
    }

    result.isValid = true;
    result.targetSemester = targetSemester;
    result.data = jsonData as ExportData;

  } catch (error) {
    result.errors.push(`Error parsing file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return result;
};

/**
 * Process import data and return summary with proper ID mapping
 */
export const processImportData = (
  importData: ExportData,
  targetSemester: Semester,
  existingCourses: Record<Semester, Course[]>,
  existingLecturers: Lecturer[]
): {
  coursesToAdd: Course[];
  lecturersToAdd: Lecturer[];
  sessionsToImport: Session[];
  sectionsToImport: Section[];
  courseIdMap: Map<string, string>;
  lecturerIdMap: Map<string, string>;
  sectionIdMap: Map<string, string>;
  summary: ImportSummary;
} => {
  const { data } = importData;

  // Create ID mapping objects
  const courseIdMap = new Map<string, string>();
  const lecturerIdMap = new Map<string, string>();
  const sectionIdMap = new Map<string, string>();

  // Filter courses to add (avoid duplicates) and create ID mapping
  const existingCourseCodes = new Set(
    existingCourses[targetSemester].map(c => c.courseCode.toUpperCase())
  );
  const coursesToAdd = (data.courses[targetSemester] || []).filter(
    course => !existingCourseCodes.has(course.courseCode.toUpperCase())
  );

  // Map existing courses by course code for ID mapping
  const existingCoursesByCode = new Map(
    existingCourses[targetSemester].map(c => [c.courseCode.toUpperCase(), c.id])
  );

  // Create course ID mapping (both new and existing courses)
  (data.courses[targetSemester] || []).forEach(course => {
    const existingCourseId = existingCoursesByCode.get(course.courseCode.toUpperCase());
    if (existingCourseId) {
      // Course already exists, map to existing ID
      courseIdMap.set(course.id, existingCourseId);
    } else {
      // New course, will get new ID during import
      // We'll update this mapping during the actual import
    }
  });

  // Filter lecturers to add (avoid duplicates) and create ID mapping
  const existingLecturerEmails = new Set(
    existingLecturers.map(l => l.email.toLowerCase())
  );
  const lecturersToAdd = data.lecturers.filter(
    lecturer => !existingLecturerEmails.has(lecturer.email.toLowerCase())
  );

  // Map existing lecturers by email for ID mapping
  const existingLecturersByEmail = new Map(
    existingLecturers.map(l => [l.email.toLowerCase(), l.id])
  );

  // Create lecturer ID mapping (both new and existing lecturers)
  data.lecturers.forEach(lecturer => {
    const existingLecturerId = existingLecturersByEmail.get(lecturer.email.toLowerCase());
    if (existingLecturerId) {
      // Lecturer already exists, map to existing ID
      lecturerIdMap.set(lecturer.id, existingLecturerId);
    } else {
      // New lecturer, will get new ID during import
      // We'll update this mapping during the actual import
    }
  });

  // Get sessions and sections for target semester
  const sessionsToImport = data.sessions[targetSemester] || [];
  const sectionsToImport = data.sections[targetSemester] || [];

  const summary: ImportSummary = {
    coursesAdded: coursesToAdd.length,
    lecturersAdded: lecturersToAdd.length,
    sessionsImported: sessionsToImport.length,
    sectionsImported: sectionsToImport.length,
    targetSemester
  };

  return {
    coursesToAdd,
    lecturersToAdd,
    sessionsToImport,
    sectionsToImport,
    courseIdMap,
    lecturerIdMap,
    sectionIdMap,
    summary
  };
};
