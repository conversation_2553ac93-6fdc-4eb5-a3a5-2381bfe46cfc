import React, { useMemo } from 'react';
import { Rule } from '../../types/rules';
import { Box, Typography, Paper, Divider, Chip, Grid, LinearProgress } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';

// Define types for Material-UI color props
type ChipColor = 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'default';
type ProgressColor = 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | 'inherit';

// Define interfaces for rule analysis
export interface RuleViolation {
  ruleId: string;
  message: string;
  sectionId: string;
  courseCode: string;
  day?: string;
  period?: number;
}

export interface SectionInfo {
  id: string;
  sectionNumber: number;
  courseId: string;
  courseCode: string;
  courseName: string;
  contactHours: number;
  scheduledHours: number;
  totalHours: number;
  gender: 'M' | 'F';
  academicLevel: string;
}

export interface ExcludedSectionInfo extends SectionInfo {
  reason: string;
}

export interface RuleAnalysisResult {
  scheduledSections: number;
  unscheduledSections: number;
  totalSections: number;
  allSectionsCount: number; // Total sections in the app
  ruleViolations: RuleViolation[];
  unscheduledSectionDetails: SectionInfo[]; // Detailed info about unscheduled sections
  excludedSectionDetails: ExcludedSectionInfo[]; // Detailed info about excluded sections
  ruleCompliance: {
    [ruleId: string]: {
      compliantCount: number;
      violationCount: number;
    };
  };
  cpsReport?: {
    enabled: boolean;
    triggered: boolean;
    initialViolations: number;
    finalViolations: number;
    improvementAchieved: boolean;
    refinementStrategies: string[];
    processingTime: number;
    constraintViolationDetails: {
      timeslotPercentage: number;
      lecturerOverload: number;
      patternViolations: number;
      conflictViolations: number;
    };
    lecturerAssignmentStats: {
      sectionsScheduled: number;
      sectionsWithLecturers: number;
      sectionsWithoutLecturers: number;
      lecturerAssignmentRate: number;
    };
    optimizationResults?: {
      strategiesApplied: string[];
      lecturerSwaps: number;
      sessionRepositions: number;
      intelligentReschedules: number;
      constraintRelaxations: number;
      overallOptimizationScore: number;
    };
  };
}

interface RuleAnalysisTabProps {
  rules: Rule[];
  analysisResult: RuleAnalysisResult | null;
}

const RuleAnalysisTab: React.FC<RuleAnalysisTabProps> = ({ rules, analysisResult }) => {
  // Group rules by category
  const courseRules = useMemo(() =>
    rules.filter(rule => rule.category === 'course')
      .sort((a, b) => a.priority - b.priority),
    [rules]
  );

  const lecturerRules = useMemo(() =>
    rules.filter(rule => rule.category === 'lecturer')
      .sort((a, b) => a.priority - b.priority),
    [rules]
  );

  // If no analysis result is available yet
  if (!analysisResult) {
    return (
      <Box className="p-4">
        <Paper elevation={2} className="p-6 text-center">
          <Typography variant="h6" className="mb-4">
            No timetable has been generated yet
          </Typography>
          <Typography variant="body1" className="mb-4">
            Generate a timetable using the auto-scheduling feature to see a comprehensive analysis of rule implementation and violations.
          </Typography>
          <Box className="p-4 bg-blue-50 rounded-lg text-left max-w-2xl mx-auto">
            <Typography variant="subtitle1" className="font-semibold mb-2 text-blue-800">
              What this analysis will show:
            </Typography>
            <ul className="list-disc pl-6 space-y-2">
              <li>Overall scheduling success rate and statistics</li>
              <li>Detailed breakdown of which rules were followed and which were violated</li>
              <li>Specific information about unscheduled sections and why they couldn't be scheduled</li>
              <li>Special focus on postgraduate pattern rules and other critical constraints</li>
              <li>Recommendations for improving scheduling success</li>
            </ul>
          </Box>
        </Paper>
      </Box>
    );
  }

  const {
    scheduledSections,
    unscheduledSections,
    totalSections,
    ruleViolations,
    ruleCompliance
  } = analysisResult;

  // Calculate overall compliance percentage
  const overallCompliancePercentage = Math.round(
    (scheduledSections / totalSections) * 100
  );

  // Calculate percentage of all sections that were scheduled
  const allSectionsScheduledPercentage = Math.round(
    (scheduledSections / analysisResult.allSectionsCount) * 100
  );

  return (
    <Box className="p-4 space-y-6">
      {/* Overall Statistics */}
      <Paper elevation={2} className="p-4">
        <Typography variant="h6" className="mb-2">Overall Timetable Statistics</Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box className="mb-2">
              <Typography variant="body1" className="flex justify-between">
                <span>Scheduled Sections:</span>
                <span className="font-semibold">{scheduledSections} of {totalSections} ({Math.round((scheduledSections/totalSections)*100)}%)</span>
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(scheduledSections/totalSections)*100}
                color="success"
                className="mt-1"
              />
            </Box>

            <Box className="mb-2">
              <Typography variant="body1" className="flex justify-between">
                <span>Unscheduled Sections:</span>
                <span className="font-semibold">{unscheduledSections} of {totalSections} ({Math.round((unscheduledSections/totalSections)*100)}%)</span>
              </Typography>
              <LinearProgress
                variant="determinate"
                value={(unscheduledSections/totalSections)*100}
                color="error"
                className="mt-1"
              />
            </Box>

            <Divider className="my-2" />

            <Box className="mb-2">
              <Typography variant="body1" className="flex justify-between font-semibold">
                <span>Total Sections in Scheduling Process:</span>
                <span>{totalSections}</span>
              </Typography>
            </Box>

            <Box className="mb-2">
              <Typography variant="body1" className="flex justify-between">
                <span>Total Sections in Application:</span>
                <span className="font-semibold">{analysisResult.allSectionsCount}</span>
              </Typography>
            </Box>

            <Box className="mb-2">
              <Typography variant="body1" className="flex justify-between">
                <span>Sections Not Included in Scheduling:</span>
                <span className="font-semibold">{analysisResult.allSectionsCount - totalSections}</span>
              </Typography>
              <Typography variant="caption" className="text-gray-600 italic">
                (These sections may already be fully scheduled or were filtered out)
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box className="flex flex-col items-center justify-center h-full">
              <Box className="text-center mb-4">
                <Typography variant="h4" className="text-center">
                  {overallCompliancePercentage}%
                </Typography>
                <Typography variant="body2" className="text-center">
                  Success Rate for Attempted Sections
                </Typography>
              </Box>

              <Box className="text-center">
                <Typography variant="h5" className="text-center">
                  {allSectionsScheduledPercentage}%
                </Typography>
                <Typography variant="body2" className="text-center">
                  Overall Application Coverage
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* CPS Refinement Report */}
      {analysisResult.cpsReport && analysisResult.cpsReport.enabled && (
        <Paper elevation={2} className="p-4">
          <Typography variant="h6" className="mb-2 flex items-center">
            🔧 Constraint Programming System (CPS) Refinement Report
          </Typography>

          {analysisResult.cpsReport.triggered ? (
            <Box>
              <Box className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <Box className="p-3 border rounded bg-gray-50">
                  <Typography variant="subtitle2" className="font-semibold mb-1">
                    Refinement Status
                  </Typography>
                  <Typography variant="body2" className={`font-semibold ${
                    analysisResult.cpsReport.improvementAchieved ? 'text-green-600' : 'text-orange-600'
                  }`}>
                    {analysisResult.cpsReport.improvementAchieved ? '✅ Improvement Achieved' : '⚠️ No Improvement'}
                  </Typography>
                  <Typography variant="body2" className="text-gray-600">
                    Processing Time: {(analysisResult.cpsReport.processingTime / 1000).toFixed(2)}s
                  </Typography>
                </Box>

                <Box className="p-3 border rounded bg-gray-50">
                  <Typography variant="subtitle2" className="font-semibold mb-1">
                    Constraint Violations
                  </Typography>
                  <Typography variant="body2">
                    Initial: <span className="font-semibold text-red-600">{analysisResult.cpsReport.initialViolations}</span>
                  </Typography>
                  <Typography variant="body2">
                    Final: <span className="font-semibold text-green-600">{analysisResult.cpsReport.finalViolations}</span>
                  </Typography>
                  {analysisResult.cpsReport.improvementAchieved && (
                    <Typography variant="body2" className="text-green-600 font-semibold">
                      Reduced by {analysisResult.cpsReport.initialViolations - analysisResult.cpsReport.finalViolations} violations
                    </Typography>
                  )}
                </Box>
              </Box>

              {analysisResult.cpsReport.refinementStrategies.length > 0 && (
                <Box className="mb-4">
                  <Typography variant="subtitle2" className="font-semibold mb-2">
                    Applied Refinement Strategies
                  </Typography>
                  <Box className="flex flex-wrap gap-2">
                    {analysisResult.cpsReport.refinementStrategies.map((strategy, index) => (
                      <Chip
                        key={index}
                        label={strategy.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        color="primary"
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              )}

              <Box className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Box className="text-center p-2 border rounded">
                  <Typography variant="body2" className="font-semibold text-blue-600">
                    {analysisResult.cpsReport.constraintViolationDetails.timeslotPercentage}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">
                    Timeslot %
                  </Typography>
                </Box>
                <Box className="text-center p-2 border rounded">
                  <Typography variant="body2" className="font-semibold text-orange-600">
                    {analysisResult.cpsReport.constraintViolationDetails.lecturerOverload}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">
                    Lecturer Overload
                  </Typography>
                </Box>
                <Box className="text-center p-2 border rounded">
                  <Typography variant="body2" className="font-semibold text-purple-600">
                    {analysisResult.cpsReport.constraintViolationDetails.patternViolations}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">
                    Pattern Issues
                  </Typography>
                </Box>
                <Box className="text-center p-2 border rounded">
                  <Typography variant="body2" className="font-semibold text-red-600">
                    {analysisResult.cpsReport.constraintViolationDetails.conflictViolations}
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">
                    Conflicts
                  </Typography>
                </Box>
              </Box>

              {/* Lecturer Assignment Statistics */}
              <Box className="mt-4">
                <Typography variant="subtitle2" className="font-semibold mb-2">
                  👨‍🏫 CPS Lecturer Assignment Results
                </Typography>
                <Box className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Box className="p-3 border rounded bg-blue-50">
                    <Typography variant="subtitle2" className="font-semibold mb-1">
                      Assignment Summary
                    </Typography>
                    <Typography variant="body2">
                      Sections Scheduled: <span className="font-semibold text-blue-600">{analysisResult.cpsReport.lecturerAssignmentStats.sectionsScheduled}</span>
                    </Typography>
                    <Typography variant="body2">
                      With Lecturers: <span className="font-semibold text-green-600">{analysisResult.cpsReport.lecturerAssignmentStats.sectionsWithLecturers}</span>
                    </Typography>
                    <Typography variant="body2">
                      Without Lecturers: <span className="font-semibold text-orange-600">{analysisResult.cpsReport.lecturerAssignmentStats.sectionsWithoutLecturers}</span>
                    </Typography>
                  </Box>

                  <Box className="p-3 border rounded bg-green-50">
                    <Typography variant="subtitle2" className="font-semibold mb-1">
                      Assignment Success Rate
                    </Typography>
                    <Typography variant="h4" className={`font-bold ${
                      analysisResult.cpsReport.lecturerAssignmentStats.lecturerAssignmentRate >= 70 ? 'text-green-600' :
                      analysisResult.cpsReport.lecturerAssignmentStats.lecturerAssignmentRate >= 40 ? 'text-orange-600' : 'text-red-600'
                    }`}>
                      {analysisResult.cpsReport.lecturerAssignmentStats.lecturerAssignmentRate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" className="text-gray-600">
                      {analysisResult.cpsReport.lecturerAssignmentStats.sectionsScheduled > 0 ? (
                        analysisResult.cpsReport.lecturerAssignmentStats.lecturerAssignmentRate >= 70 ?
                          'Excellent lecturer utilization' :
                        analysisResult.cpsReport.lecturerAssignmentStats.lecturerAssignmentRate >= 40 ?
                          'Good lecturer utilization' : 'Low lecturer utilization'
                      ) : 'No sections scheduled by CPS'}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* CPS Optimization Results */}
              {analysisResult.cpsReport.optimizationResults && (
                <Box className="mt-4">
                  <Typography variant="subtitle2" className="font-semibold mb-2">
                    🔧 CPS Optimization Strategies Applied
                  </Typography>
                  <Box className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Box className="p-3 border rounded bg-purple-50">
                      <Typography variant="subtitle2" className="font-semibold mb-1">
                        Optimization Actions
                      </Typography>
                      <Typography variant="body2">
                        Lecturer Swaps: <span className="font-semibold text-blue-600">{analysisResult.cpsReport.optimizationResults.lecturerSwaps}</span>
                      </Typography>
                      <Typography variant="body2">
                        Session Repositions: <span className="font-semibold text-green-600">{analysisResult.cpsReport.optimizationResults.sessionRepositions}</span>
                      </Typography>
                      <Typography variant="body2">
                        Intelligent Reschedules: <span className="font-semibold text-orange-600">{analysisResult.cpsReport.optimizationResults.intelligentReschedules}</span>
                      </Typography>
                      <Typography variant="body2">
                        Constraint Relaxations: <span className="font-semibold text-red-600">{analysisResult.cpsReport.optimizationResults.constraintRelaxations}</span>
                      </Typography>
                    </Box>

                    <Box className="p-3 border rounded bg-indigo-50">
                      <Typography variant="subtitle2" className="font-semibold mb-1">
                        Optimization Score
                      </Typography>
                      <Typography variant="h4" className={`font-bold ${
                        analysisResult.cpsReport.optimizationResults.overallOptimizationScore >= 50 ? 'text-green-600' :
                        analysisResult.cpsReport.optimizationResults.overallOptimizationScore >= 20 ? 'text-orange-600' : 'text-red-600'
                      }`}>
                        {analysisResult.cpsReport.optimizationResults.overallOptimizationScore}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600">
                        {analysisResult.cpsReport.optimizationResults.overallOptimizationScore >= 50 ?
                          'Excellent optimization achieved' :
                        analysisResult.cpsReport.optimizationResults.overallOptimizationScore >= 20 ?
                          'Good optimization achieved' : 'Limited optimization achieved'}
                      </Typography>
                      <Box className="mt-2">
                        <Typography variant="caption" className="text-gray-600">
                          Strategies: {analysisResult.cpsReport.optimizationResults.strategiesApplied.join(', ') || 'None applied'}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              )}
            </Box>
          ) : (
            <Box className="p-3 border rounded bg-green-50">
              <Typography variant="body2" className="text-green-800">
                ✅ CPS refinement was enabled but not triggered because the initial scheduling achieved the target success rate.
              </Typography>
            </Box>
          )}
        </Paper>
      )}

      {/* Rule Compliance Analysis */}
      <Paper elevation={2} className="p-4">
        <Typography variant="h6" className="mb-4">Rule Compliance Analysis</Typography>

        {/* Course Rules */}
        <Typography variant="subtitle1" className="font-semibold mb-2">Course/Section Rules</Typography>
        <Box className="space-y-3 mb-4">
          {/* Special highlight for postgraduate pattern rule */}
          {courseRules.some(rule =>
            rule.id === 'postgrad-pattern' ||
            (rule.name.toLowerCase().includes('postgraduate') && rule.name.toLowerCase().includes('pattern')) ||
            (rule.name.toLowerCase().includes('evening') && rule.name.toLowerCase().includes('postgraduate'))
          ) && (
            <Box className="border-2 border-blue-500 rounded p-3 bg-blue-50">
              <Box className="flex justify-between items-start mb-2">
                <Typography variant="body1" className="font-semibold text-blue-800">
                  Postgraduate Course Pattern Rule
                </Typography>
                <Chip
                  icon={<InfoIcon />}
                  label="Important Rule"
                  color="primary"
                  size="small"
                />
              </Box>

              <Typography variant="body2" className="text-gray-700 mb-2">
                Postgraduate courses (code numbers 5xx-9xx) should be scheduled on one day only with consecutive periods:
                either periods 10-12 on regular days (Su, Tu, Th) or periods 9-10 on long days (Mo, We).
                This rule is critical for proper scheduling.
              </Typography>

              {analysisResult.unscheduledSectionDetails.some(section =>
                section.academicLevel.toLowerCase().includes('master') ||
                section.academicLevel.toLowerCase().includes('phd') ||
                section.academicLevel.toLowerCase().includes('diploma') ||
                section.courseCode.match(/^[A-Z]+[5-9]\d+/)
              ) && (
                <Box className="p-2 bg-yellow-100 rounded mt-2">
                  <Typography variant="body2" className="text-yellow-800">
                    <WarningIcon fontSize="small" className="mr-1 align-text-bottom" />
                    There are unscheduled postgraduate sections. Check the Unscheduled Sections tab for details.
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {courseRules.map(rule => {
            // Skip the postgraduate rule as we've already highlighted it
            if (rule.id === 'postgrad-pattern' ||
                (rule.name.toLowerCase().includes('postgraduate') && rule.name.toLowerCase().includes('pattern')) ||
                (rule.name.toLowerCase().includes('evening') && rule.name.toLowerCase().includes('postgraduate'))) {
              return null;
            }

            const compliance = ruleCompliance[rule.id] || { compliantCount: 0, violationCount: 0 };
            const totalChecks = compliance.compliantCount + compliance.violationCount;
            const compliancePercentage = totalChecks > 0
              ? Math.round((compliance.compliantCount / totalChecks) * 100)
              : 100;

            // Determine status icon and color
            let StatusIcon = CheckCircleIcon;
            let statusColor = 'success';
            let statusText = 'Fully Compliant';

            if (!rule.enabled) {
              StatusIcon = InfoIcon;
              statusColor = 'info';
              statusText = 'Rule Disabled';
            } else if (compliance.violationCount > 0) {
              if (rule.priority <= 2) {
                StatusIcon = ErrorIcon;
                statusColor = 'error';
                statusText = 'Hard Rule Violated';
              } else {
                StatusIcon = WarningIcon;
                statusColor = 'warning';
                statusText = 'Soft Rule Violated';
              }
            }

            return (
              <Box key={rule.id} className="border rounded p-3">
                <Box className="flex justify-between items-start mb-2">
                  <Typography variant="body1" className="font-semibold">
                    {rule.name}
                  </Typography>
                  <Chip
                    icon={<StatusIcon />}
                    label={statusText}
                    color={statusColor as ChipColor}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" className="text-gray-600 mb-2">
                  {rule.description}
                </Typography>

                <Box className="flex items-center">
                  <Box className="flex-grow mr-2">
                    <LinearProgress
                      variant="determinate"
                      value={compliancePercentage}
                      color={statusColor as ProgressColor}
                    />
                  </Box>
                  <Typography variant="body2">
                    {compliancePercentage}% Compliant
                  </Typography>
                </Box>

                {compliance.violationCount > 0 && (
                  <Box className="mt-2">
                    <Typography variant="body2" color="error">
                      {compliance.violationCount} violation{compliance.violationCount !== 1 ? 's' : ''}
                    </Typography>
                    <Box className="mt-1 max-h-24 overflow-y-auto">
                      {ruleViolations
                        .filter(v => v.ruleId === rule.id)
                        .slice(0, 3)
                        .map((violation, index) => (
                          <Typography key={index} variant="body2" className="text-xs text-gray-600">
                            • {violation.courseCode}: {violation.message}
                            {violation.day && violation.period ? ` (${violation.day}, Period ${violation.period})` : ''}
                          </Typography>
                        ))}

                      {ruleViolations.filter(v => v.ruleId === rule.id).length > 3 && (
                        <Typography variant="body2" className="text-xs text-gray-600 italic">
                          ...and {ruleViolations.filter(v => v.ruleId === rule.id).length - 3} more violations
                        </Typography>
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            );
          }).filter(Boolean)}
        </Box>

        <Divider className="my-4" />

        {/* Lecturer Rules */}
        <Typography variant="subtitle1" className="font-semibold mb-2">Lecturer Rules</Typography>
        <Box className="space-y-3">
          {lecturerRules.map(rule => {
            const compliance = ruleCompliance[rule.id] || { compliantCount: 0, violationCount: 0 };
            const totalChecks = compliance.compliantCount + compliance.violationCount;
            const compliancePercentage = totalChecks > 0
              ? Math.round((compliance.compliantCount / totalChecks) * 100)
              : 100;

            // Determine status icon and color
            let StatusIcon = CheckCircleIcon;
            let statusColor = 'success';
            let statusText = 'Fully Compliant';

            if (!rule.enabled) {
              StatusIcon = InfoIcon;
              statusColor = 'info';
              statusText = 'Rule Disabled';
            } else if (compliance.violationCount > 0) {
              if (rule.priority <= 2) {
                StatusIcon = ErrorIcon;
                statusColor = 'error';
                statusText = 'Hard Rule Violated';
              } else {
                StatusIcon = WarningIcon;
                statusColor = 'warning';
                statusText = 'Soft Rule Violated';
              }
            }

            return (
              <Box key={rule.id} className="border rounded p-3">
                <Box className="flex justify-between items-start mb-2">
                  <Typography variant="body1" className="font-semibold">
                    {rule.name}
                  </Typography>
                  <Chip
                    icon={<StatusIcon />}
                    label={statusText}
                    color={statusColor as ChipColor}
                    size="small"
                  />
                </Box>

                <Typography variant="body2" className="text-gray-600 mb-2">
                  {rule.description}
                </Typography>

                <Box className="flex items-center">
                  <Box className="flex-grow mr-2">
                    <LinearProgress
                      variant="determinate"
                      value={compliancePercentage}
                      color={statusColor as ProgressColor}
                    />
                  </Box>
                  <Typography variant="body2">
                    {compliancePercentage}% Compliant
                  </Typography>
                </Box>

                {compliance.violationCount > 0 && (
                  <Box className="mt-2">
                    <Typography variant="body2" color="error">
                      {compliance.violationCount} violation{compliance.violationCount !== 1 ? 's' : ''}
                    </Typography>
                    <Box className="mt-1 max-h-24 overflow-y-auto">
                      {ruleViolations
                        .filter(v => v.ruleId === rule.id)
                        .slice(0, 3)
                        .map((violation, index) => (
                          <Typography key={index} variant="body2" className="text-xs text-gray-600">
                            • {violation.courseCode}: {violation.message}
                            {violation.day && violation.period ? ` (${violation.day}, Period ${violation.period})` : ''}
                          </Typography>
                        ))}

                      {ruleViolations.filter(v => v.ruleId === rule.id).length > 3 && (
                        <Typography variant="body2" className="text-xs text-gray-600 italic">
                          ...and {ruleViolations.filter(v => v.ruleId === rule.id).length - 3} more violations
                        </Typography>
                      )}
                    </Box>
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>
      </Paper>

      {/* Unscheduled Sections */}
      {unscheduledSections > 0 && (
        <Paper elevation={2} className="p-4 mb-6">
          <Typography variant="h6" className="mb-2">Unscheduled Sections</Typography>
          <Typography variant="body2" className="mb-4">
            {unscheduledSections} section(s) could not be scheduled due to rule constraints.
          </Typography>

          <Box className="overflow-auto max-h-96">
            <table className="w-full border-collapse">
              <thead className="bg-gray-100">
                <tr>
                  <th className="border p-2 text-left">Course</th>
                  <th className="border p-2 text-left">Section</th>
                  <th className="border p-2 text-left">Gender</th>
                  <th className="border p-2 text-left">Contact Hours</th>
                  <th className="border p-2 text-left">Academic Level</th>
                  <th className="border p-2 text-left">Likely Issues</th>
                </tr>
              </thead>
              <tbody>
                {analysisResult.unscheduledSectionDetails.map((section) => {
                  // Determine likely issues based on section properties
                  const likelyIssues: string[] = [];

                  // Check for postgraduate courses
                  const isPostgraduate = section.academicLevel.toLowerCase().includes('master') ||
                      section.academicLevel.toLowerCase().includes('phd') ||
                      section.academicLevel.toLowerCase().includes('diploma') ||
                      section.courseCode.match(/^[A-Z]+[5-9]\d+/);

                  // Find specific rule violations for this section
                  const sectionViolations = ruleViolations.filter(v => v.sectionId === section.id);

                  if (sectionViolations.length > 0) {
                    // Use the actual rule violations to determine likely issues
                    sectionViolations.forEach(violation => {
                      if (violation.ruleId === 'postgrad-pattern') {
                        likelyIssues.push('Postgraduate pattern constraints: Must be scheduled in periods 10-12 on regular days or 9-10 on long days');
                      } else if (violation.ruleId === 'block-break-timeslots') {
                        likelyIssues.push('System blocked periods or user-defined breaks conflict with available timeslots');
                      } else if (violation.ruleId === 'max-sessions-per-timeslot') {
                        likelyIssues.push('Maximum sessions per timeslot exceeded in available periods');
                      } else {
                        likelyIssues.push(violation.message);
                      }
                    });
                  } else {
                    // Fallback to generic issues based on section properties
                    if (isPostgraduate) {
                      // Check if the postgrad rule is enabled
                      const postGradRule = rules.find(r =>
                        r.id === 'postgrad-pattern' ||
                        (r.name.toLowerCase().includes('postgraduate') && r.name.toLowerCase().includes('pattern')) ||
                        (r.name.toLowerCase().includes('evening') && r.name.toLowerCase().includes('postgraduate'))
                      );
                      if (postGradRule?.enabled) {
                        likelyIssues.push('Postgraduate pattern constraints: Must be scheduled in periods 10-12 on regular days or 9-10 on long days');
                      } else {
                        likelyIssues.push('Postgraduate course scheduling issue (rule disabled but still constrained)');
                      }
                    }

                    // Check for high contact hours
                    if (section.contactHours >= 4) {
                      likelyIssues.push('High contact hours (4+ CH) require specific scheduling patterns');
                    }
                  }

                  // If no specific issues identified
                  if (likelyIssues.length === 0) {
                    likelyIssues.push('Timeslot constraints or conflicts with existing sessions');
                  }

                  return (
                    <tr key={section.id} className="hover:bg-gray-50">
                      <td className="border p-2">
                        <div className="font-semibold">{section.courseCode}</div>
                        <div className="text-xs text-gray-600">{section.courseName}</div>
                      </td>
                      <td className="border p-2">{section.sectionNumber}</td>
                      <td className="border p-2">{section.gender === 'M' ? 'Male' : 'Female'}</td>
                      <td className="border p-2">{section.contactHours}</td>
                      <td className="border p-2">{section.academicLevel}</td>
                      <td className="border p-2">
                        <ul className="list-disc pl-4 text-sm">
                          {likelyIssues.map((issue, index) => (
                            <li key={index}>{issue}</li>
                          ))}
                        </ul>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </Box>

          <Box className="p-3 bg-gray-100 rounded mt-4">
            <Typography variant="subtitle2" className="font-semibold mb-2">
              Recommendations to Improve Scheduling Success:
            </Typography>
            <ul className="list-disc pl-5 space-y-1">
              <li>Adjust rule priorities - consider making some soft constraints instead of hard constraints</li>
              <li>Increase maximum sessions per timeslot in periods with high demand</li>
              <li>For postgraduate courses, ensure appropriate patterns are enabled and prioritized</li>
              <li>Check for lecturer availability conflicts that may be preventing scheduling</li>
            </ul>
          </Box>
        </Paper>
      )}

      {/* Excluded Sections */}
      {analysisResult.excludedSectionDetails && analysisResult.excludedSectionDetails.length > 0 && (
        <Paper elevation={2} className="p-4">
          <Typography variant="h6" className="mb-2">Excluded Sections</Typography>
          <Typography variant="body2" className="mb-4">
            {analysisResult.excludedSectionDetails.length} section(s) were excluded from the auto-scheduling process.
            These sections were not considered for scheduling due to the reasons listed below.
          </Typography>

          <Box className="overflow-auto max-h-96">
            <table className="w-full border-collapse">
              <thead className="bg-gray-100">
                <tr>
                  <th className="border p-2 text-left">Course</th>
                  <th className="border p-2 text-left">Section</th>
                  <th className="border p-2 text-left">Gender</th>
                  <th className="border p-2 text-left">Scheduled/Total Hours</th>
                  <th className="border p-2 text-left">Academic Level</th>
                  <th className="border p-2 text-left">Reason for Exclusion</th>
                </tr>
              </thead>
              <tbody>
                {analysisResult.excludedSectionDetails.map((section) => (
                  <tr key={section.id} className="hover:bg-gray-50">
                    <td className="border p-2">
                      <div className="font-semibold">{section.courseCode}</div>
                      <div className="text-xs text-gray-600">{section.courseName}</div>
                    </td>
                    <td className="border p-2">{section.sectionNumber}</td>
                    <td className="border p-2">{section.gender === 'M' ? 'Male' : 'Female'}</td>
                    <td className="border p-2">
                      <div className="flex items-center">
                        <span className="font-semibold">{section.scheduledHours}</span>
                        <span className="mx-1">/</span>
                        <span>{section.totalHours}</span>
                        {section.scheduledHours >= section.totalHours && (
                          <span className="ml-2 text-green-600 text-xs">✓ Fully Scheduled</span>
                        )}
                      </div>
                    </td>
                    <td className="border p-2">{section.academicLevel}</td>
                    <td className="border p-2">
                      <div className="text-sm">
                        {section.reason}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </Box>

          <Box className="p-3 bg-blue-50 rounded mt-4">
            <Typography variant="subtitle2" className="font-semibold mb-2 text-blue-800">
              Understanding Excluded Sections:
            </Typography>
            <Typography variant="body2" className="mb-2">
              Sections are excluded from auto-scheduling based on your selected session handling option:
            </Typography>

            <Box className="mb-3 p-3 bg-white rounded border-l-4 border-blue-500">
              <Typography variant="subtitle2" className="font-semibold mb-1">
                Session Handling Options and Their Effects:
              </Typography>
              <ul className="list-disc pl-5 space-y-1">
                <li><span className="font-semibold">Keep all existing sessions:</span> Fully scheduled sections are excluded</li>
                <li><span className="font-semibold">Delete all existing sessions:</span> All sections are included, even fully scheduled ones</li>
                <li><span className="font-semibold">Remove only auto-generated sessions:</span> Sections fully scheduled by manual sessions are excluded</li>
              </ul>
            </Box>

            <Typography variant="subtitle2" className="font-semibold mb-1">
              Other Common Reasons for Exclusion:
            </Typography>
            <ul className="list-disc pl-5 space-y-1">
              <li><span className="font-semibold">Lab sections:</span> Laboratory sections are typically scheduled manually</li>
              <li><span className="font-semibold">Postgraduate courses:</span> May be excluded if the postgraduate pattern rule is disabled</li>
            </ul>

            <Box className="mt-3 p-2 bg-white rounded border border-gray-200">
              <Typography variant="body2" className="font-semibold mb-1">
                To include more sections in auto-scheduling:
              </Typography>
              <ul className="list-disc pl-5">
                <li>For fully scheduled sections: Choose "Delete all existing sessions" option</li>
                <li>For sections with manual sessions: Choose "Delete all existing sessions" or "Remove only auto-generated sessions"</li>
                <li>For postgraduate courses: Enable the postgraduate pattern rule</li>
              </ul>
            </Box>
          </Box>
        </Paper>
      )}
    </Box>
  );
};

export default RuleAnalysisTab;
