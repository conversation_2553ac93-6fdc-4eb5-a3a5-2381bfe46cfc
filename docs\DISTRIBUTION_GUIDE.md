# QU Scheduler Distribution Guide

## 🎯 Target Deployment Environment

**Primary Users**: Qatar University Head of Departments (~100 users)
**Platform**: Windows 10/11 Enterprise
**Distribution Method**: Internal deployment via IT department

## 📋 Pre-Distribution Checklist

### 1. Code Quality & Security
- [ ] Run `npm run security:audit` - No high/critical vulnerabilities
- [ ] Run `npm run lint` - All linting issues resolved
- [ ] Run `npm run check-ts` - TypeScript compilation successful
- [ ] Test application thoroughly in production mode
- [ ] Verify Arabic font rendering in PDF exports
- [ ] Test all core features (scheduling, PDF export, data import/export)

### 2. Build Optimization
- [ ] Run `npm run optimize:fonts` - Font optimization complete
- [ ] Verify bundle size is reasonable (<200MB total)
- [ ] Test application startup time (<5 seconds)
- [ ] Verify memory usage is acceptable (<500MB)

### 3. Security Verification
- [ ] Content Security Policy (CSP) properly configured
- [ ] Node integration disabled in renderer
- [ ] Context isolation enabled
- [ ] No sensitive data in logs
- [ ] All external URLs properly validated

## 🔐 Code Signing Process

### Step 1: Obtain Code Signing Certificate
```bash
# For Qatar University IT Department:
# 1. Purchase EV Code Signing Certificate from DigiCert/Sectigo
# 2. Complete business verification process
# 3. Receive hardware token (USB) for EV certificates
```

### Step 2: Configure Environment Variables
```bash
# Create .env file (DO NOT commit to repository)
WINDOWS_CERTIFICATE_FILE=path/to/certificate.p12
WINDOWS_CERTIFICATE_PASSWORD=your_certificate_password
```

### Step 3: Enable Code Signing in forge.config.ts
```typescript
// Uncomment the windowsSign section in forge.config.ts
windowsSign: {
  certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
  certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD,
  signingHashAlgorithms: ['sha256'],
  timestampServer: 'http://timestamp.digicert.com'
}
```

## 🚀 Build Process

### Development Build
```bash
npm run start
```

### Production Build
```bash
# Full production build with all optimizations
npm run dist:production

# Quick build for testing
npm run build:all
```

### Build Outputs
- **Executable**: `out/QU Scheduler-win32-x64/qu-scheduler.exe`
- **Installer**: `out/make/squirrel.windows/x64/QU-Scheduler-Setup.exe`
- **MSI Package**: `out/make/squirrel.windows/x64/qu-scheduler-1.0.0-full.nupkg`

## 📦 Distribution Options

### Option 1: Squirrel.Windows Installer (Recommended)
- **File**: `QU-Scheduler-Setup.exe`
- **Features**: Auto-updates, delta updates, silent installation
- **Best for**: Standard deployment to end users

### Option 2: MSI Package (Enterprise)
- **File**: Generated MSI from Squirrel
- **Features**: Group Policy deployment, enterprise management
- **Best for**: IT-managed deployment via SCCM/Intune

### Option 3: Portable Application
- **File**: Extracted executable folder
- **Features**: No installation required, USB portable
- **Best for**: Testing or restricted environments

## 🏢 Qatar University Specific Deployment

### IT Department Coordination
1. **Security Review**: Provide application for security assessment
2. **Network Requirements**: Verify internet access for font loading
3. **User Permissions**: Ensure users have local admin rights for installation
4. **Group Policy**: Configure any required policy settings

### Deployment Methods
1. **SCCM Deployment**: Use MSI package for automated deployment
2. **Manual Installation**: Distribute setup.exe via shared network drive
3. **USB Distribution**: Provide portable version for offline installation

### User Training Materials
- Create user manual with Arabic/English instructions
- Prepare video tutorials for common tasks
- Set up help desk procedures for support

## 🔧 Troubleshooting Common Issues

### Installation Issues
- **Windows SmartScreen**: Code signing resolves this
- **Antivirus False Positives**: Submit to vendors for whitelisting
- **Permission Errors**: Run installer as administrator

### Runtime Issues
- **Font Rendering**: Verify internet connection for Google Fonts
- **PDF Generation**: Check printer drivers and PDF viewers
- **Performance**: Monitor memory usage and optimize if needed

## 📊 Post-Deployment Monitoring

### Success Metrics
- Installation success rate >95%
- Application startup time <5 seconds
- User satisfaction surveys
- Support ticket volume

### Maintenance Schedule
- Monthly security updates
- Quarterly feature updates
- Annual major version releases
- Continuous monitoring of user feedback

## 🆘 Support Contacts

- **Technical Lead**: Prof Ayman Saleh (<EMAIL>)
- **IT Support**: Qatar University IT Department
- **User Training**: Department coordinators
