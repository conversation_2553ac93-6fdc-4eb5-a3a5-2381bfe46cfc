import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  ToggleButtonGroup,
  ToggleButton,
  Button,
  Menu,
  MenuItem,
  Typography,
  Box,
  Chip,
  Tooltip,
  IconButton
} from '@mui/material';
import { useRuleSystemStore } from '../store/ruleSystem';
import WarningIcon from '@mui/icons-material/Warning';

// Importing icons separately to avoid issues with missing module declarations
import FilterListIcon from '@mui/icons-material/FilterList';
import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';
import SchoolIcon from '@mui/icons-material/School';
import { useAppContext } from '../context/AppContext';
import { useDragDrop } from '../context/DragDropContext';
import DroppableSessionCard from './draggable/DroppableSessionCard';
import PgCanvas from './PgCanvas';
import { Lecturer, Course } from '../types/models';

/**
 * TimetableCanvas component that displays the central timetable grid
 * with tabs for morning and evening sessions, view toggles, and filters
 * Now semester-specific based on the selected semester in the header
 */
const TimetableCanvas: React.FC = () => {
  // Get context data and functions
  const {
    currentSemester,
    courses,
    sections,
    sessions,
    lecturers,
    updateSection,
    updateSession,
    deleteSession,
    // View state from context
    activeTab,
    setActiveTab,
    viewMode,
    setViewMode,
    setCurrentView,
    // PG Canvas state from context
    showPgCanvas,
    setShowPgCanvas
  } = useAppContext();

  // Handler to toggle PG Canvas view
  const handleTogglePgCanvas = () => {
    setShowPgCanvas(!showPgCanvas);
  };

  // Utility function to get semester-specific colors
  const getSemesterColors = (semester: string) => {
    switch (semester) {
      case 'Fall':
        return {
          headerBg: 'bg-gray-50 dark:bg-gray-800',
          headerText: 'text-gray-900 dark:text-gray-100',
          headerBorder: 'border-gray-200 dark:border-gray-700'
        };
      case 'Spring':
        return {
          headerBg: 'bg-emerald-500 dark:bg-emerald-800',
          headerText: 'text-white dark:text-emerald-100',
          headerBorder: 'border-emerald-400 dark:border-emerald-700'
        };
      case 'Summer':
        return {
          headerBg: 'bg-yellow-600 dark:bg-yellow-700',
          headerText: 'text-white dark:text-yellow-100',
          headerBorder: 'border-yellow-500 dark:border-yellow-600'
        };
      default:
        return {
          headerBg: 'bg-gray-50 dark:bg-gray-800',
          headerText: 'text-gray-900 dark:text-gray-100',
          headerBorder: 'border-gray-200 dark:border-gray-700'
        };
    }
  };

  // Utility function to get period column colors based on active tab (morning/evening/all day)
  const getPeriodColumnColors = (isActive: boolean, isMorning: boolean, period?: number) => {
    if (!isActive) return 'bg-gray-50 dark:bg-gray-800';

    // For All Day view (activeTab === 2), use morning colors for periods 1-6 and evening colors for periods 7-12
    if (activeTab === 2 && period) {
      return period <= 6
        ? 'bg-blue-200 dark:bg-blue-800'
        : 'bg-amber-200 dark:bg-amber-800'; // Using amber for evening periods with stronger colors
    }

    return isMorning
      ? 'bg-blue-200 dark:bg-blue-800'
      : 'bg-amber-200 dark:bg-amber-800'; // Using amber for evening periods with stronger colors
  };

  // Get drag and drop context
  const {
    dragItem,
    canDropSectionAt,
    canDropSessionAt,
    createSessionFromSection,
    moveSession
  } = useDragDrop();

  // Get rule system data - use separate selectors to prevent unnecessary re-renders
  const userDefinedBreaks = useRuleSystemStore(state => state.userDefinedBreaks);
  const maxSessionsPerTimeslot = useRuleSystemStore(state => state.maxSessionsPerTimeslot);

  // State for filter menu
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  // State for selected filter
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  // State for filtered lecturer
  const [filteredLecturerId, setFilteredLecturerId] = useState<string | null>(null);
  const [filteredLecturer, setFilteredLecturer] = useState<Lecturer | null>(null);

  // State for filtered course
  const [filteredCourseId, setFilteredCourseId] = useState<string | null>(null);
  const [filteredCourse, setFilteredCourse] = useState<Course | null>(null);

  // State for highlighted sessions
  const [highlightedSessions, setHighlightedSessions] = useState<string[]>([]);
  const [scrollToDay, setScrollToDay] = useState<string | null>(null);
  const [scrollToPeriod, setScrollToPeriod] = useState<number | null>(null);

  // Ref to track if we're currently updating to prevent infinite loops
  const isUpdating = useRef(false);

  // Refs for scrolling
  const dayRefs = React.useRef<Record<string, HTMLDivElement | null>>({});
  const periodRefs = React.useRef<Record<number, HTMLDivElement | null>>({});

  // Set up event listeners for component
  useEffect(() => {
    // Listen for break changes from RuleSystemModal
    const handleBreakChanges = (_event: CustomEvent) => {
      // Prevent multiple updates
      if (isUpdating.current) return;

      isUpdating.current = true;

      // Use requestAnimationFrame to prevent infinite update loops
      requestAnimationFrame(() => {
        // This will run after the current render cycle is complete
        setHighlightedSessions(prev => [...prev]);
        isUpdating.current = false;
      });
    };

    // Listen for lecturer filter changes from LecturersPanel
    const handleLecturerFilterChange = (event: CustomEvent) => {
      const { lecturerId } = event.detail;
      setFilteredLecturerId(lecturerId);

      if (lecturerId) {
        const lecturer = lecturers.find(l => l.id === lecturerId);
        setFilteredLecturer(lecturer || null);
      } else {
        setFilteredLecturer(null);
      }
    };

    // Listen for course filter changes from CoursesPanel
    const handleCourseFilterChange = (event: CustomEvent) => {
      const { courseId } = event.detail;
      setFilteredCourseId(courseId);

      if (courseId) {
        const course = courses[currentSemester].find(c => c.id === courseId);
        setFilteredCourse(course || null);
      } else {
        setFilteredCourse(null);
      }
    };

    // Listen for focus on session events
    const handleFocusOnSession = (event: CustomEvent) => {
      const { day, period, sessions } = event.detail;

      // Set the day and period to scroll to
      setScrollToDay(day);
      setScrollToPeriod(period);

      // Highlight the sessions
      setHighlightedSessions(sessions);

      // Clear the highlight after 5 seconds
      setTimeout(() => {
        setHighlightedSessions([]);
      }, 5000);
    };

    // Listen for setActiveTab events
    const handleSetActiveTab = (event: CustomEvent) => {
      const { tab } = event.detail;
      setActiveTab(tab);
    };

    // Listen for rule system changes
    const handleRuleSystemChanges = (_event: CustomEvent) => {
      // Prevent multiple updates
      if (isUpdating.current) return;

      isUpdating.current = true;

      // Use requestAnimationFrame to prevent infinite update loops
      requestAnimationFrame(() => {
        // This will run after the current render cycle is complete
        setHighlightedSessions(prev => [...prev]);
        isUpdating.current = false;
      });
    };

    // Add event listeners
    window.addEventListener('lecturerFilterChanged', handleLecturerFilterChange as EventListener);
    window.addEventListener('courseFilterChanged', handleCourseFilterChange as EventListener);
    window.addEventListener('focusOnSession', handleFocusOnSession as EventListener);
    window.addEventListener('setActiveTab', handleSetActiveTab as EventListener);
    window.addEventListener('breakChanges', handleBreakChanges as EventListener);
    window.addEventListener('rulesChanged', handleRuleSystemChanges as EventListener);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('lecturerFilterChanged', handleLecturerFilterChange as EventListener);
      window.removeEventListener('courseFilterChanged', handleCourseFilterChange as EventListener);
      window.removeEventListener('focusOnSession', handleFocusOnSession as EventListener);
      window.removeEventListener('setActiveTab', handleSetActiveTab as EventListener);
      window.removeEventListener('breakChanges', handleBreakChanges as EventListener);
      window.removeEventListener('rulesChanged', handleRuleSystemChanges as EventListener);
    };
  }, [lecturers, courses, currentSemester, selectedFilter, filteredLecturerId, filteredCourseId, activeTab, viewMode]);

  // Effect to scroll to the specified day and period
  useEffect(() => {
    if (scrollToDay && scrollToPeriod !== null) {
      // Scroll to the day
      const dayElement = dayRefs.current[scrollToDay];
      if (dayElement) {
        dayElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // Scroll to the period
      const periodElement = periodRefs.current[scrollToPeriod];
      if (periodElement) {
        periodElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // Clear the scroll targets
      setScrollToDay(null);
      setScrollToPeriod(null);
    }
  }, [scrollToDay, scrollToPeriod]);

  // We don't need to save UI state here as it's already managed by AppContext

  // Days of the week
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];

  // Time periods for Morning tab
  const morningPeriods = [
    { period: 1, regularTime: '8 AM', longTime: '8 AM' },
    { period: 2, regularTime: '9 AM', longTime: '9:30 AM' },
    { period: 3, regularTime: '10 AM', longTime: '11 AM' },
    { period: 4, regularTime: '11 AM', longTime: '12:30 PM' },
    { period: 5, regularTime: '12 PM', longTime: '2 PM' },
    { period: 6, regularTime: '1 PM', longTime: '3:30 PM' }
  ];

  // Time periods for Evening tab
  const eveningPeriods = [
    { period: 7, regularTime: '2 PM', longTime: '2 PM' },
    { period: 8, regularTime: '3 PM', longTime: '3:30 PM' },
    { period: 9, regularTime: '4 PM', longTime: '5 PM' },
    { period: 10, regularTime: '5 PM', longTime: '6:30 PM' },
    { period: 11, regularTime: '6 PM', longTime: '8 PM' },
    { period: 12, regularTime: '7 PM', longTime: '9:30 PM' }
  ];

  // All periods for All Day view
  const allDayPeriods = [
    ...morningPeriods,
    ...eveningPeriods
  ];

  // Filter options
  const filterOptions = [
    { value: 'all', label: 'All sections' },
    { value: 'male', label: 'Male sections only' },
    { value: 'female', label: 'Female sections only' },
    { value: 'theory', label: 'Theory courses only' },
    { value: 'lab', label: 'Lab courses only' },
    { value: 'year1', label: '1st year courses' },
    { value: 'year2', label: '2nd year courses' },
    { value: 'year3', label: '3rd year courses' },
    { value: 'year4', label: '4th year courses' },
    { value: 'postgrad', label: 'Postgraduate courses' },
    { value: 'assigned', label: 'Assigned lecturers\' sessions' },
    { value: 'unassigned', label: 'Non-assigned lecturers\' sessions' }
  ];

  // Memoized filtered sections
  const filteredSections = useMemo(() => {
    // Get the current semester's sections
    const currentSections = sections[currentSemester] || [];
    const currentCourses = courses[currentSemester] || [];
    const currentSessions = sessions[currentSemester] || [];

    // Apply filters
    let filtered = [...currentSections];

    // Gender filters
    if (selectedFilter === 'male') {
      filtered = filtered.filter(section => section.gender && section.gender === 'M');
    } else if (selectedFilter === 'female') {
      filtered = filtered.filter(section => section.gender && section.gender === 'F');
    }
    // Course type filters
    else if (selectedFilter === 'theory' || selectedFilter === 'lab') {
      // Filter by course type
      const courseType = selectedFilter === 'theory' ? 'Theory' : 'Lab';
      filtered = filtered.filter(section => {
        const course = currentCourses.find(c => c.id === section.courseId);
        return course && course.courseType === courseType;
      });
    }
    // Academic year filters
    else if (selectedFilter === 'year1' || selectedFilter === 'year2' ||
             selectedFilter === 'year3' || selectedFilter === 'year4' ||
             selectedFilter === 'postgrad') {
      filtered = filtered.filter(section => {
        const course = currentCourses.find(c => c.id === section.courseId);
        if (!course || !course.academicLevel) return false;

        // Match academic level based on filter
        switch (selectedFilter) {
          case 'year1':
            return course.academicLevel.includes('1st year');
          case 'year2':
            return course.academicLevel.includes('2nd year');
          case 'year3':
            return course.academicLevel.includes('3rd year');
          case 'year4':
            return course.academicLevel.includes('4th year');
          case 'postgrad':
            return course.academicLevel.includes('Master') ||
                   course.academicLevel.includes('PhD') ||
                   course.academicLevel.includes('Diploma');
          default:
            return false;
        }
      });
    }
    // Lecturer assignment filters
    else if (selectedFilter === 'assigned' || selectedFilter === 'unassigned') {
      filtered = filtered.filter(section => {
        // Get all sessions for this section
        const sectionSessions = currentSessions.filter(session => session.sectionId === section.id);

        if (sectionSessions.length === 0) {
          // If no sessions, include in unassigned filter only
          return selectedFilter === 'unassigned';
        }

        if (selectedFilter === 'assigned') {
          // Check if any session has a lecturer assigned
          return sectionSessions.some(session =>
            (session.lecturerId && session.lecturerId !== '') ||
            (session.lecturerIds && session.lecturerIds.length > 0)
          );
        } else { // unassigned
          // Check if any session has no lecturer assigned
          return sectionSessions.some(session =>
            (!session.lecturerId || session.lecturerId === '') &&
            (!session.lecturerIds || session.lecturerIds.length === 0)
          );
        }
      });
    }

    return filtered;
  }, [currentSemester, sections, courses, sessions, selectedFilter]);

  // Handler for tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handler for view mode change
  const handleViewChange = (_event: React.MouseEvent<HTMLElement>, newView: string | null) => {
    if (newView !== null) {
      // Type cast to ensure compatibility with AppContext viewMode type
      setViewMode(newView as 'week' | 'regular' | 'long');
    }
  };

  // Handler for filter menu open
  const handleFilterMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  // Handler for filter menu close
  const handleFilterMenuClose = () => {
    setFilterAnchorEl(null);
  };

  // Handler for filter selection
  const handleFilterSelect = (filter: string) => {
    setSelectedFilter(filter);
    handleFilterMenuClose();
  };

  // Check if any filters are active
  const isAnyFilterActive = (): boolean => {
    // Check if any filter is active in TimetableCanvas
    if (selectedFilter !== 'all') return true;
    if (filteredLecturerId !== null) return true;
    if (filteredCourseId !== null) return true;
    if (viewMode !== 'week') return true;
    if (activeTab !== 2) return true; // Not on "All Day" view

    return false;
  };

  // Handler for resetting all filters
  const handleResetAllFilters = () => {
    // Reset TimetableCanvas filters
    setSelectedFilter('all');
    setFilteredLecturerId(null);
    setFilteredLecturer(null);
    setFilteredCourseId(null);
    setFilteredCourse(null);

    // Reset view mode to week
    setViewMode('week');

    // Reset to week view (not day view)
    setCurrentView('week');

    // Reset to all day tab (index 2)
    setActiveTab(2);

    // Dispatch events to notify other components about filter resets

    // Reset lecturer filter in LecturersPanel
    window.dispatchEvent(new CustomEvent('lecturerFilterChanged', {
      detail: { lecturerId: null }
    }));

    // Reset course filter in CoursesPanel
    window.dispatchEvent(new CustomEvent('courseFilterChanged', {
      detail: { courseId: null }
    }));

    // Reset course panel filter
    window.dispatchEvent(new CustomEvent('resetCoursePanelFilter', {}));

    // Reset lecturer panel filter
    window.dispatchEvent(new CustomEvent('resetLecturerPanelFilter', {}));
  };

  // Get visible days based on view mode
  const getVisibleDays = () => {
    switch (viewMode) {
      case 'regular':
        return days.filter(day => ['Sunday', 'Tuesday', 'Thursday'].includes(day));
      case 'long':
        return days.filter(day => ['Monday', 'Wednesday'].includes(day));
      default:
        return days;
    }
  };

  // Get periods based on active tab
  const getPeriods = () => {
    if (activeTab === 0) return morningPeriods;
    if (activeTab === 1) return eveningPeriods;
    return allDayPeriods; // For activeTab === 2 (All Day view)
  };

  // Get course by ID
  const getCourseById = (courseId: string) => {
    return courses[currentSemester].find(course => course.id === courseId);
  };

  // Define a SessionWithLecturers interface to replace the 'any' type
  interface SessionWithLecturers {
    id: string;
    sectionId: string;
    lecturerId?: string;
    lecturerIds?: string[];
    day: string;
    startPeriod: number;
    endPeriod: number;
    isAutoGenerated?: boolean;
  }

  // Render a session card
  const renderSessionCard = (session: SessionWithLecturers) => {
    // Find the section
    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return null;

    // Find the course
    const course = getCourseById(section.courseId);
    if (!course) return null;

    // Find lecturer occurrences for this session
    const lecturerOccurrences = [];

    // Process the main lecturerId (for backward compatibility)
    if (session.lecturerId) {
      const lecturer = lecturers.find(l => l.id === session.lecturerId);
      if (lecturer) {
        lecturerOccurrences.push({
          id: session.id + '-' + lecturer.id, // Create a unique ID for the occurrence
          lecturerId: lecturer.id,
          firstName: lecturer.firstName,
          lastName: lecturer.lastName  // Add lastName
        });
      }
    }

    // Process the lecturerIds array for multiple lecturers
    if (session.lecturerIds && Array.isArray(session.lecturerIds)) {
      // Filter out the main lecturerId to avoid duplicates
      const additionalLecturerIds = session.lecturerIds.filter((id: string) => id !== session.lecturerId);

      // Add each additional lecturer
      additionalLecturerIds.forEach((lecturerId: string) => {
        const lecturer = lecturers.find(l => l.id === lecturerId);
        if (lecturer) {
          lecturerOccurrences.push({
            id: session.id + '-' + lecturer.id, // Create a unique ID for the occurrence
            lecturerId: lecturer.id,
            firstName: lecturer.firstName,
            lastName: lecturer.lastName  // Add lastName
          });
        }
      });
    }

    // Handle delete session
    const handleDeleteSession = () => {
      // Update section's scheduled hours based on day (1.5h for Monday and Wednesday, 1h for others)
      const hoursToSubtract = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Update section's scheduled hours
      const updatedSection = {
        ...section,
        scheduledHours: Math.max(0, section.scheduledHours - hoursToSubtract) // Decrement by appropriate hours
      };

      // Delete the session
      deleteSession(session.id, currentSemester);

      // Update the section
      updateSection(updatedSection, currentSemester);
    };

    // Handle remove lecturer from session
    const handleRemoveLecturer = (lecturerId: string) => {
      // Find all sessions for this section
      const sectionSessions = sessions[currentSemester].filter(
        s => s.sectionId === session.sectionId
      );

      // Update all sessions for this section to remove this lecturer
      sectionSessions.forEach(sectionSession => {
        // Handle removal from lecturerIds array
        if (sectionSession.lecturerIds && sectionSession.lecturerIds.includes(lecturerId)) {
          const updatedLecturerIds = sectionSession.lecturerIds.filter(id => id !== lecturerId);

          const updatedSession = {
            ...sectionSession,
            lecturerIds: updatedLecturerIds,
            // Update the lecturerId field if it matches the removed lecturer
            lecturerId: sectionSession.lecturerId === lecturerId ?
              (updatedLecturerIds[0] || '') : sectionSession.lecturerId
          };

          // Update session
          updateSession(updatedSession, currentSemester);
        }
        // For backward compatibility with the old lecturerId field
        else if (sectionSession.lecturerId === lecturerId) {
          const updatedSession = {
            ...sectionSession,
            lecturerId: '',
            lecturerIds: []
          };

          // Update session
          updateSession(updatedSession, currentSemester);
        }
      });
    };

    return (
      <DroppableSessionCard
        day={session.day}
        period={session.startPeriod}
        sessionId={session.id}
        sectionId={section.id}
        courseCode={course.courseCode}
        courseColor={course.color}
        sectionNumber={section.sectionNumber}
        gender={section.gender}
        scheduledHours={section.scheduledHours}
        totalHours={section.totalHours}
        lecturerOccurrences={lecturerOccurrences}
        onDelete={handleDeleteSession}
        onRemoveLecturer={handleRemoveLecturer}
        isAutoGenerated={session.isAutoGenerated}
        isHighlighted={highlightedSessions.includes(session.id)}
      />
    );
  };

  // Helper function to check if a session is for an undergraduate theory course
  const isUndergraduateTheorySession = (sessionId: string): boolean => {
    const session = sessions[currentSemester].find(s => s.id === sessionId);
    if (!session) return false;

    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return false;

    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return false;

    // Check if it's a theory course
    if (course.courseType !== 'Theory') return false;

    // Check if it's an undergraduate course (course code doesn't start with 5 or higher)
    const numericPart = course.courseCode.match(/\d+/)?.[0] || '';
    const firstDigit = numericPart.length > 0 ? parseInt(numericPart[0]) : -1;
    return firstDigit < 5;
  };

  // Count undergraduate theory sessions in a specific day and period
  const countUndergradTheorySessionsInTimeslot = (day: string, period: number): number => {
    // Get all sessions for this day and period
    return sessions[currentSemester].filter(
      session => session.day === day &&
      period >= session.startPeriod &&
      period <= session.endPeriod &&
      isUndergraduateTheorySession(session.id)
    ).length;
  };

  // Find sessions for a specific day and period
  const findSessionsForDayAndPeriod = (day: string, period: number) => {
    // Get the filtered section IDs to only show sessions for filtered sections
    const filteredSectionIds = filteredSections.map(section => section.id);

    // Determine current time of day based on activeTab
    const currentTimeOfDay = activeTab === 0 ? 'morning' : activeTab === 1 ? 'evening' : null;

    // First filter by day, period, and section filter
    let result = sessions[currentSemester].filter(
      session => session.day === day &&
      period >= session.startPeriod &&
      period <= session.endPeriod &&
      // Only include sessions for sections that match the current filter
      filteredSectionIds.includes(session.sectionId) &&
      // Filter by timeOfDay if we're in morning or evening view (not all day view)
      (activeTab === 2 || !session.timeOfDay || session.timeOfDay === currentTimeOfDay)
    );

    // Apply lecturer filter if active
    if (filteredLecturerId) {
      result = result.filter(session => {
        // Include sessions assigned to the filtered lecturer
        if (session.lecturerId === filteredLecturerId) return true;
        if (session.lecturerIds && session.lecturerIds.includes(filteredLecturerId)) return true;

        // Include sessions with no assigned lecturer
        return !session.lecturerId && (!session.lecturerIds || session.lecturerIds.length === 0);
      });
    }

    // Apply course filter if active
    if (filteredCourseId) {
      result = result.filter(session => {
        // Find the section for this session
        const section = sections[currentSemester].find(s => s.id === session.sectionId);
        if (!section) return false;

        // Check if the section belongs to the filtered course
        return section.courseId === filteredCourseId;
      });
    }

    // Apply additional filtering based on selected filter
    if (selectedFilter === 'assigned') {
      // Only show sessions with assigned lecturers
      result = result.filter(session =>
        (session.lecturerId && session.lecturerId !== '') ||
        (session.lecturerIds && session.lecturerIds.length > 0)
      );
    } else if (selectedFilter === 'unassigned') {
      // Only show sessions without assigned lecturers
      result = result.filter(session =>
        (!session.lecturerId || session.lecturerId === '') &&
        (!session.lecturerIds || session.lecturerIds.length === 0)
      );
    }

    return result;
  };

  // If PG Canvas view is active, render the PG Canvas
  if (showPgCanvas) {
    return <PgCanvas onBack={() => setShowPgCanvas(false)} />;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Controls bar with semester name - using semester-specific colors */}
      <div className={`flex justify-between items-center p-1 border-b ${getSemesterColors(currentSemester).headerBorder} ${getSemesterColors(currentSemester).headerBg}`}>
        {/* Active filters */}
        <div className="flex items-center">
          {/* Show active lecturer filter if any */}
          {filteredLecturer && (
            <Chip
              label={`${filteredLecturer.firstName} ${filteredLecturer.lastName}`}
              size="small"
              color="primary"
              onDelete={() => {
                setFilteredLecturerId(null);
                setFilteredLecturer(null);
                // Dispatch event to notify LecturersPanel
                window.dispatchEvent(new CustomEvent('lecturerFilterChanged', {
                  detail: { lecturerId: null }
                }));
              }}
              sx={{ ml: 1, height: 24 }}
            />
          )}

          {/* Show active course filter if any */}
          {filteredCourse && (
            <Chip
              label={filteredCourse.courseCode}
              size="small"
              color="primary"
              onDelete={() => {
                setFilteredCourseId(null);
                setFilteredCourse(null);
                // Dispatch event to notify CoursesPanel
                window.dispatchEvent(new CustomEvent('courseFilterChanged', {
                  detail: { courseId: null }
                }));
              }}
              sx={{ ml: 1, height: 24 }}
            />
          )}
        </div>

        {/* View mode toggle - with semester-specific styling */}
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewChange}
          size="small"
          className="mx-auto"
          sx={{
            '& .MuiToggleButton-root.Mui-selected': {
              backgroundColor: currentSemester === 'Fall' ? '#92400e' :
                              currentSemester === 'Spring' ? '#047857' :
                              currentSemester === 'Summer' ? '#b45309' : undefined,
              color: 'white'
            },
            '& .MuiToggleButton-root': {
              color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : undefined,
              borderColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.23)' : undefined,
              height: '28px'
            }
          }}
        >
          <ToggleButton value="week" aria-label="week view" className="px-2 py-0.5">
            Week
          </ToggleButton>
          <ToggleButton value="regular" aria-label="regular days" className="px-2 py-0.5">
            Su, Tu, Th
          </ToggleButton>
          <ToggleButton value="long" aria-label="long days" className="px-2 py-0.5">
            Mo, Wed
          </ToggleButton>
        </ToggleButtonGroup>

        {/* PG button */}
        <Button
          startIcon={<SchoolIcon />}
          onClick={handleTogglePgCanvas}
          variant="contained"
          size="small"
          sx={{
            backgroundColor: 'purple',
            '&:hover': {
              backgroundColor: 'darkviolet',
            },
            mx: 2,
            height: '28px',
            textTransform: 'none'
          }}
        >
          PG
        </Button>

        {/* Morning/Evening/All Day toggle - with semester-specific styling */}
        <ToggleButtonGroup
          value={activeTab}
          exclusive
          onChange={handleTabChange}
          size="small"
          className="mr-2"
          sx={{
            '& .MuiToggleButton-root.Mui-selected': {
              backgroundColor: currentSemester === 'Fall' ? '#92400e' :
                              currentSemester === 'Spring' ? '#047857' :
                              currentSemester === 'Summer' ? '#b45309' : undefined,
              color: 'white'
            },
            '& .MuiToggleButton-root': {
              color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : undefined,
              borderColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.23)' : undefined,
              height: '28px'
            }
          }}
        >
          <ToggleButton value={0} aria-label="morning" className="px-2 py-0.5">
            Morning
          </ToggleButton>
          <ToggleButton value={1} aria-label="evening" className="px-2 py-0.5">
            Evening
          </ToggleButton>
          <ToggleButton value={2} aria-label="all-day" className="px-2 py-0.5">
            All Day
          </ToggleButton>
        </ToggleButtonGroup>

        {/* Reset Filters button - changes color when filters are active */}
        <Tooltip title="Reset all filters">
          <IconButton
            onClick={handleResetAllFilters}
            size="small"
            sx={{
              backgroundColor: isAnyFilterActive() ? 'primary.main' : 'gray',
              color: 'white',
              '&:hover': {
                backgroundColor: isAnyFilterActive() ? 'primary.dark' : '#555',
                opacity: 0.9
              },
              marginRight: '8px',
              padding: '4px',
              height: '28px',
              width: '28px',
              position: 'relative'
            }}
          >
            <FilterAltOffIcon fontSize="small" />
            {isAnyFilterActive() && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: 6,
                  height: 6,
                  bgcolor: 'error.main',
                  borderRadius: '50%'
                }}
              />
            )}
          </IconButton>
        </Tooltip>

        {/* Filter button - with semester-specific styling */}
        <Button
          startIcon={<FilterListIcon />}
          onClick={handleFilterMenuOpen}
          size="small"
          sx={{
            backgroundColor: currentSemester === 'Fall' ? '#92400e' :
                           currentSemester === 'Spring' ? '#047857' :
                           currentSemester === 'Summer' ? '#b45309' : '#1976d2',
            color: 'white',
            '&:hover': {
              backgroundColor: currentSemester === 'Fall' ? '#78350f' :
                             currentSemester === 'Spring' ? '#065f46' :
                             currentSemester === 'Summer' ? '#92400e' : '#1565c0',
              opacity: 0.9
            },
            padding: '2px 12px',
            textTransform: 'none',
            height: '28px'
          }}
        >
          {filterOptions.find(option => option.value === selectedFilter)?.label || 'Filter'}
        </Button>

        {/* Filter menu */}
        <Menu
          anchorEl={filterAnchorEl}
          open={Boolean(filterAnchorEl)}
          onClose={handleFilterMenuClose}
        >
          {filterOptions.map((option) => (
            <MenuItem
              key={option.value}
              onClick={() => handleFilterSelect(option.value)}
              selected={selectedFilter === option.value}
            >
              {option.label}
            </MenuItem>
          ))}
        </Menu>
      </div>

      {/* Timetable grid - removed the tabs row to save vertical space */}
      <div className="flex-1 overflow-hidden p-0">
        {filteredSections.length === 0 && sections[currentSemester].length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              p: 3
            }}
          >
            <Typography variant="h6" color="text.secondary" align="center" gutterBottom>
              No sections available for {currentSemester} semester
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center">
              Add courses and sections to start building your timetable
            </Typography>
          </Box>
        ) : filteredSections.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              p: 3
            }}
          >
            <Typography variant="h6" color="text.secondary" align="center" gutterBottom>
              No sections match the current filter
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center">
              Try changing the filter or adding more sections
            </Typography>
          </Box>
        ) : (
          <div className="grid grid-cols-1 gap-0 bg-gray-100 dark:bg-gray-800 p-0 rounded-md h-full" style={{ minWidth: 'fit-content', overflowY: 'auto', overflowX: 'auto' }}>
            <div className="grid sticky top-0 z-30 bg-white dark:bg-gray-900 shadow-md"
              style={{
                gridTemplateColumns: `60px ${getVisibleDays().map(() => 'minmax(165px, 1fr)').join(' ')}`, // Reduced width
                width: viewMode !== 'week' ? '100%' : 'auto',
                marginBottom: 0
              }}>
              {/* Header row with days */}
              <div className="p-1 bg-white dark:bg-gray-900 sticky left-0 z-30"></div>
              {getVisibleDays().map((day) => (
                <div
                  key={day}
                  className={`py-1 text-center font-bold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 border-b-2 border-gray-300 dark:border-gray-600 shadow-sm`}
                  ref={(el) => { dayRefs.current[day] = el; }}
                >
                  {day}
                </div>
              ))}
            </div>

            {/* Time periods rows with gap between rows */}
            <div className="grid grid-cols-1 gap-1">
              {getPeriods().map((timePeriod) => (
                <div key={timePeriod.period} className="grid"
                  style={{
                    gridTemplateColumns: `60px ${getVisibleDays().map(() => 'minmax(165px, 1fr)').join(' ')}`, // Reduced width
                    width: viewMode !== 'week' ? '100%' : 'auto'
                  }}>
                  {/* Simplified period cell with white space between rows */}
                  <div
                    className={`flex items-center justify-center ${getPeriodColumnColors(true, activeTab === 0, timePeriod.period)} rounded-l border-r-2 border-gray-300 dark:border-gray-600 sticky left-0 z-20 shadow-sm font-bold`}
                    style={{ width: '60px' }} // Fixed width
                    ref={(el) => { periodRefs.current[timePeriod.period] = el; }}
                  >
                    <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                      {timePeriod.period}
                    </Typography>
                  </div>

                {/* Day cells - grow both horizontally and vertically */}
                {getVisibleDays().map((day) => {
                  const sessionsForCell = findSessionsForDayAndPeriod(day, timePeriod.period);

                  // Determine time display based on day type (regular vs long)
                  const isLongDay = day === 'Monday' || day === 'Wednesday';
                  const timeDisplay = isLongDay ? timePeriod.longTime : timePeriod.regularTime;

                  // Calculate end time based on the next period's start time
                  let endTimeDisplay = '';
                  if (isLongDay) {
                    // For long days (Monday, Wednesday)
                    let periods;
                    if (activeTab === 2) { // All Day view
                      periods = allDayPeriods;
                    } else {
                      periods = activeTab === 0 ? morningPeriods : eveningPeriods;
                    }

                    const nextPeriodIndex = periods.findIndex(p => p.period === timePeriod.period) + 1;
                    if (nextPeriodIndex < periods.length) {
                      endTimeDisplay = periods[nextPeriodIndex].longTime;
                    } else if ((activeTab === 0 || (activeTab === 2 && timePeriod.period === 6)) && timePeriod.period === 6) {
                      // Last period of morning session
                      endTimeDisplay = '3:30 PM';
                    } else if ((activeTab === 1 || (activeTab === 2 && timePeriod.period === 12)) && timePeriod.period === 12) {
                      // Last period of evening session
                      endTimeDisplay = '9:30 PM';
                    }
                  } else {
                    // For regular days (Sunday, Tuesday, Thursday)
                    let periods;
                    if (activeTab === 2) { // All Day view
                      periods = allDayPeriods;
                    } else {
                      periods = activeTab === 0 ? morningPeriods : eveningPeriods;
                    }

                    const nextPeriodIndex = periods.findIndex(p => p.period === timePeriod.period) + 1;
                    if (nextPeriodIndex < periods.length) {
                      endTimeDisplay = periods[nextPeriodIndex].regularTime;
                    } else if ((activeTab === 0 || (activeTab === 2 && timePeriod.period === 6)) && timePeriod.period === 6) {
                      // Last period of morning session
                      endTimeDisplay = '2 PM';
                    } else if ((activeTab === 1 || (activeTab === 2 && timePeriod.period === 12)) && timePeriod.period === 12) {
                      // Last period of evening session
                      endTimeDisplay = '8 PM';
                    }
                  }

                  // Check if this is a blocked time slot (periods 5-6 in morning view and periods 11-12 in evening view on Monday and Wednesday)
                  // For All Day view (activeTab === 2), we still need to block the same periods
                  const isSystemBlockedTimeSlot = (day === 'Monday' || day === 'Wednesday') &&
                    ((activeTab === 0 && (timePeriod.period === 5 || timePeriod.period === 6)) ||
                     (activeTab === 1 && (timePeriod.period === 11 || timePeriod.period === 12)) ||
                     (activeTab === 2 && (timePeriod.period === 5 || timePeriod.period === 6 || timePeriod.period === 11 || timePeriod.period === 12)));

                  // Check if this is a user-defined break
                  // Convert day format from "Monday" to "Mon" for checking against userDefinedBreaks
                  const shortDay = day.substring(0, 3);
                  const isUserDefinedBreak = userDefinedBreaks.includes(`${shortDay}-${timePeriod.period}`);

                  // Combine system blocks and user-defined breaks
                  const isBlockedTimeSlot = isSystemBlockedTimeSlot || isUserDefinedBreak;

                  return (
                    <div
                      key={`${day}-${timePeriod.period}`}
                      className={`p-1 ${
                        isSystemBlockedTimeSlot
                          ? 'bg-gray-200 dark:bg-gray-700' // System blocked timeslots
                          : isUserDefinedBreak
                            ? 'bg-red-100 dark:bg-red-900/40' // User-defined breaks with red background
                            : activeTab === 0 || (activeTab === 2 && timePeriod.period <= 6)
                              ? 'bg-blue-50 dark:bg-blue-900/40' // Morning cells with stronger blue tint
                              : 'bg-amber-50 dark:bg-amber-900/40' // Evening cells with stronger amber tint
                      } border-2 ${isUserDefinedBreak ? 'border-red-300 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-sm flex flex-col transition-all duration-200 ease-in-out relative ${isBlockedTimeSlot ? 'cursor-not-allowed' : ''} shadow-sm after:absolute after:inset-0 after:border-2 after:border-inherit after:rounded-sm after:pointer-events-none after:z-0`}
                      style={{
                        minHeight: `${Math.max(6, sessionsForCell.length * 3)}rem`,
                        height: 'auto'
                      }}

                      onDragOver={(e) => {
                        // Make the entire cell a drop zone
                        e.preventDefault();
                        // Prevent dragging onto blocked time slots
                        if (isBlockedTimeSlot) {
                          e.dataTransfer.dropEffect = 'none';
                          return;
                        }
                        if (dragItem) {
                          if (dragItem.type === 'SECTION') {
                            const canDrop = canDropSectionAt(dragItem.id, day, timePeriod.period);
                            if (canDrop) {
                              e.currentTarget.classList.add('ring-2', 'ring-blue-400', 'dark:ring-blue-500');
                              e.dataTransfer.dropEffect = 'copy';
                            } else {
                              e.dataTransfer.dropEffect = 'none';
                            }
                          } else if (dragItem.type === 'SESSION') {
                            const canDrop = canDropSessionAt(dragItem.id, day, timePeriod.period);
                            if (canDrop) {
                              e.currentTarget.classList.add('ring-2', 'ring-blue-400', 'dark:ring-blue-500');
                              e.dataTransfer.dropEffect = 'move';
                            } else {
                              e.dataTransfer.dropEffect = 'none';
                            }
                          }
                        }
                      }}
                      onDragLeave={(e) => {
                        // Remove highlight when drag leaves
                        e.currentTarget.classList.remove('ring-2', 'ring-blue-400', 'dark:ring-blue-500');
                      }}
                      onDrop={(e) => {
                        // Handle drop on the entire cell
                        e.preventDefault();
                        // Prevent dropping onto blocked time slots
                        if (isBlockedTimeSlot) {
                          return;
                        }

                        // Store the current target element to reference in the timeout
                        const currentTarget = e.currentTarget;

                        try {
                          const data = JSON.parse(e.dataTransfer.getData('application/json'));
                          if (data.type === 'SECTION') {
                            createSessionFromSection(data.id, day, timePeriod.period);
                          } else if (data.type === 'SESSION') {
                            moveSession(data.id, day, timePeriod.period);
                          }

                          // Remove the highlight ring after a short delay (500ms)
                          // This allows for multiple drops in quick succession but eventually clears the highlight
                          setTimeout(() => {
                            currentTarget.classList.remove('ring-2', 'ring-blue-400', 'dark:ring-blue-500');
                          }, 500);
                        } catch (error) {
                          console.error('Error parsing drop data:', error);
                          // Remove highlight immediately if there's an error
                          currentTarget.classList.remove('ring-2', 'ring-blue-400', 'dark:ring-blue-500');
                        }
                      }}
                    >
                      {/* Time pill box in the upper right corner - hidden for specific periods on Monday and Wednesday */}
                      {timeDisplay && endTimeDisplay && (
                        <>
                          {/* This appears to be a duplicate of the onDrop handler above, so removing the code here */}
                        </>
                      )}
                      {/* Time pill box in the upper right corner - hidden for specific periods on Monday and Wednesday */}
                      {timeDisplay && endTimeDisplay &&
                        // Hide time pill for periods 5-6 on Monday/Wednesday in morning view and periods 11-12 in evening view
                        // Also hide for the same periods in All Day view
                        !((day === 'Monday' || day === 'Wednesday') &&
                          ((activeTab === 0 && (timePeriod.period === 5 || timePeriod.period === 6)) ||
                           (activeTab === 1 && (timePeriod.period === 11 || timePeriod.period === 12)) ||
                           (activeTab === 2 && (timePeriod.period === 5 || timePeriod.period === 6 || timePeriod.period === 11 || timePeriod.period === 12)))) && (
                        <div className="absolute top-0 right-0.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-[0.6rem] px-1 py-0.5 rounded-full z-10 opacity-70 hover:opacity-100 transition-opacity">
                          {timeDisplay} - {endTimeDisplay}
                        </div>
                      )}

                      {/* Display max allowed undergraduate theory sessions in top left corner */}
                      {!isBlockedTimeSlot && (
                        (() => {
                          // Convert day format from "Monday" to "Mon" for checking against maxSessionsPerTimeslot
                          const shortDay = day.substring(0, 3);
                          const maxAllowed = maxSessionsPerTimeslot[`${shortDay}-${timePeriod.period}`] || 0;

                          // Count current undergraduate theory sessions in this timeslot
                          const currentCount = countUndergradTheorySessionsInTimeslot(day, timePeriod.period);

                          // Check if the limit is exceeded
                          const isExceeded = maxAllowed > 0 && currentCount > maxAllowed;

                          return (
                            <div className={`absolute top-0 left-0.5 ${
                              isExceeded
                                ? 'bg-red-100 dark:bg-red-900/40 text-red-700 dark:text-red-300'
                                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                            } text-[0.6rem] px-1 py-0.5 rounded-full z-10 opacity-70 hover:opacity-100 transition-opacity flex items-center`}>
                              {isExceeded && <WarningIcon fontSize="inherit" className="mr-0.5 text-red-500" />}
                              {currentCount}/{maxAllowed}
                            </div>
                          );
                        })()
                      )}
                      {/* Display BREAK text for user-defined breaks */}
                      {isUserDefinedBreak && (
                        <div className="absolute inset-0 flex items-center justify-center z-20 pointer-events-none">
                          <Typography
                            variant="h6"
                            className="font-bold text-red-700 dark:text-red-300"
                            sx={{
                              opacity: 0.8,
                              textShadow: '0px 0px 2px rgba(255,255,255,0.7)'
                            }}
                          >
                            BREAK
                          </Typography>
                        </div>
                      )}

                      {/* Session cards container with no padding */}
                      <div className="mt-4 relative z-0">
                      {sessionsForCell.length > 0 ? (
                        sessionsForCell.map((session) => (
                          <div key={session.id} className="mb-1 last:mb-0 relative z-10">
                            {renderSessionCard(session)}
                          </div>
                        ))
                      ) : (
                        <DroppableSessionCard
                          day={day}
                          period={timePeriod.period}
                        />
                      )}
                      </div>
                    </div>
                  );
                })}
              </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TimetableCanvas;
