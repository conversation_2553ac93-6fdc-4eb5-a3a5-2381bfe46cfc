{"compilerOptions": {"target": "ESNext", "module": "ESNext", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "node", "resolveJsonModule": true, "jsx": "react", "lib": ["DOM", "ESNext", "WebWorker", "ES2020"], "types": ["node"], "strict": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"]}