import { app, BrowserWindow, session, ipcMain, dialog } from 'electron';
import path from 'node:path';
import { promises as fs } from 'fs';
import started from 'electron-squirrel-startup';
import Store from 'electron-store';
import { Schema } from './types/models';

// <PERSON>le creating/removing shortcuts on Windows when installing/uninstalling.
if (started) {
  app.quit();
}

// Set application metadata
app.setName('QU Scheduler');

// Set app user model ID for Windows (for proper taskbar grouping)
if (process.platform === 'win32') {
  app.setAppUserModelId('qa.edu.qu.scheduler');
}

// Production security settings
if (!app.isPackaged) {
  // Development mode settings
  console.log('Running in development mode');
} else {
  // Production mode settings
  console.log('Running in production mode');

  // Disable hardware acceleration if needed for compatibility
  // app.disableHardwareAcceleration();

  // Set secure defaults for production
  app.commandLine.appendSwitch('--disable-web-security', 'false');
  app.commandLine.appendSwitch('--disable-features', 'VizDisplayCompositor');
}

// Initialize the store
const store = new Store<Schema>();

// Store reference to main window
let mainWindow: BrowserWindow | null = null;

// Setup IPC handlers
const setupIpcHandlers = () => {
  // IPC handlers for store operations
  ipcMain.handle('store:get', (_, key) => {
    return store.get(key);
  });

  ipcMain.handle('store:set', (_, key, value) => {
    if (key === 'uiState') {
      // Get the current UI state
      const currentUIState = store.get('uiState');
      // Merge the new UI state with the current UI state
      const mergedUIState = { ...currentUIState, ...value };
      // Set the merged UI state
      store.set(key, mergedUIState);
    } else {
      store.set(key, value);
    }
  });

  ipcMain.handle('store:delete', (_, key) => {
    store.delete(key);
  });

  ipcMain.handle('store:clear', () => {
    store.clear();
  });

  // File dialog handlers
  ipcMain.handle('dialog:showSaveDialog', async (_, options) => {
    if (!mainWindow) {
      throw new Error('Main window not available');
    }
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
  });

  ipcMain.handle('dialog:showOpenDialog', async (_, options) => {
    if (!mainWindow) {
      throw new Error('Main window not available');
    }
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
  });

  // File system handlers
  ipcMain.handle('fs:writeFile', async (_, filePath, data) => {
    try {
      await fs.writeFile(filePath, data, 'utf8');
      return { success: true };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  ipcMain.handle('fs:readFile', async (_, filePath) => {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return { success: true, data };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  // PDF generation handler
  console.log('Registering PDF generation handler...');
  ipcMain.handle('pdf:generate', async (_, htmlContent, filename) => {
    console.log('PDF generation handler called with filename:', filename);
    try {
      if (!mainWindow) {
        console.error('Main window not available');
        throw new Error('Main window not available');
      }
      console.log('Main window available, proceeding with PDF generation...');

      // Create a new hidden window for PDF generation
      const pdfWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
        },
      });

      // Load the HTML content
      await pdfWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

      // Wait for the content to load
      await new Promise<void>((resolve) => {
        pdfWindow.webContents.once('did-finish-load', () => {
          // Add a delay to ensure fonts are loaded
          setTimeout(resolve, 2000);
        });
      });

      // Generate PDF
      const pdfBuffer = await pdfWindow.webContents.printToPDF({
        pageSize: 'A4',
        printBackground: true,
        margins: {
          top: 0.5,
          bottom: 0.5,
          left: 0.5,
          right: 0.5
        }
      });

      // Close the PDF window
      pdfWindow.close();

      // Show save dialog
      const result = await dialog.showSaveDialog(mainWindow, {
        defaultPath: filename,
        filters: [
          { name: 'PDF Files', extensions: ['pdf'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        // Save the PDF file
        await fs.writeFile(result.filePath, pdfBuffer);
        return { success: true, filePath: result.filePath };
      } else {
        return { success: false, error: 'Save operation was cancelled' };
      }

    } catch (error) {
      console.error('Error in PDF generation:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });
};

const createWindow = () => {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    title: 'QU Scheduler',
    icon: app.isPackaged
      ? path.join(process.resourcesPath, 'icon.ico')
      : path.join(__dirname, '../../assets/icons/icon.ico'), // Enhanced QU Scheduler icon
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // Set enhanced Content Security Policy
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    // Different CSP for development vs production
    const isDevelopment = !app.isPackaged;

    const cspPolicy = isDevelopment
      ? // Development CSP - more permissive for Vite HMR
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-eval' 'unsafe-inline' http://localhost:* ws://localhost:*; " +
        "style-src 'self' 'unsafe-inline'; " +
        "font-src 'self' data:; " +
        "img-src 'self' data: blob:; " +
        "connect-src 'self' http://localhost:* ws://localhost:*; " +
        "object-src 'none'; " +
        "base-uri 'self'; " +
        "form-action 'self'; " +
        "frame-ancestors 'none';"
      : // Production CSP - strict security for offline use
        "default-src 'self'; " +
        "script-src 'self'; " +
        "style-src 'self' 'unsafe-inline'; " +
        "font-src 'self' data:; " +
        "img-src 'self' data: blob:; " +
        "connect-src 'self'; " +
        "object-src 'none'; " +
        "base-uri 'self'; " +
        "form-action 'self'; " +
        "frame-ancestors 'none';";

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [cspPolicy],
        // Additional security headers
        'X-Content-Type-Options': ['nosniff'],
        'X-Frame-Options': ['DENY'],
        'X-XSS-Protection': ['1; mode=block'],
        'Referrer-Policy': ['strict-origin-when-cross-origin']
      }
    });
  });

  // Initialize zoom level from stored preferences
  mainWindow.webContents.once('did-finish-load', () => {
    try {
      const uiState = store.get('uiState');
      const zoomFactor = uiState?.zoomFactor || 0.8; // Default to 80% if not set
      mainWindow?.webContents.setZoomFactor(zoomFactor);
    } catch (error) {
      console.error('Error setting initial zoom factor:', error);
      // Fallback to default 80% zoom
      mainWindow?.webContents.setZoomFactor(0.8);
    }
  });

  // and load the index.html of the app.
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(MAIN_WINDOW_VITE_DEV_SERVER_URL);
  } else {
    // In production, the renderer files are outside the asar file
    // We need to go up from the asar file to the app root, then to renderer directory
    // __dirname in packaged app: /path/to/app/resources/app.asar/.vite/build
    // We need to get to: /path/to/app/renderer/main_window/index.html
    const appPath = app.getAppPath(); // Gets the asar path
    const appRoot = path.dirname(path.dirname(appPath)); // Go up from resources/app.asar to app root
    const rendererPath = path.join(appRoot, 'renderer', MAIN_WINDOW_VITE_NAME, 'index.html');

    console.log('Loading renderer from:', rendererPath);
    console.log('MAIN_WINDOW_VITE_NAME:', MAIN_WINDOW_VITE_NAME);
    console.log('__dirname:', __dirname);
    console.log('app.getAppPath():', appPath);
    console.log('appRoot:', appRoot);

    mainWindow.loadFile(rendererPath);
  }

  // Open the DevTools only in development
  if (MAIN_WINDOW_VITE_DEV_SERVER_URL) {
    mainWindow.webContents.openDevTools();
  }
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', () => {
  setupIpcHandlers();
  createWindow();
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and import them here.
