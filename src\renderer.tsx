/**
 * This file will automatically be loaded by vite and run in the "renderer" context.
 * To learn more about the differences between the "main" and the "renderer" context in
 * Electron, visit:
 *
 * https://electronjs.org/docs/tutorial/process-model
 */

import './index.css';
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Import inert polyfill for browsers that don't support it natively
import 'wicg-inert';

// Create root element for React
const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

// Render the React application
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
