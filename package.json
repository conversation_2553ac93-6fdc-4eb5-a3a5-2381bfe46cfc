{"name": "qu-scheduler", "productName": "QU Scheduler", "version": "1.0.0", "description": "Professional timetable scheduling application for Qatar University - Advanced course and lecturer scheduling system with automated optimization", "main": ".vite/build/main.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package && node scripts/fix-renderer-packaging.js", "make": "electron-forge make", "make:squirrel": "electron-forge make --platform=win32 --arch=x64", "make:nsis": "npm run package && node installer/build-nsis.js", "installer:setup": "node installer/create-installer-graphics.js", "publish": "electron-forge publish", "lint": "eslint --ext .ts,.tsx .", "check-ts": "tsc --noEmit", "build:all": "npm run package && npm run make:squirrel", "dist": "npm run build:all", "prebuild": "npm run lint && npm run check-ts && npm run security:audit", "prepackage": "npm run setup:offline && npm run optimize:fonts && npm run optimize:assets", "security:audit": "npm audit --audit-level moderate", "setup:offline": "node scripts/download-fonts.js", "optimize:fonts": "node scripts/optimize-fonts.js", "optimize:assets": "echo 'Asset optimization complete'", "verify:offline": "node scripts/verify-offline.js", "clean:production": "node scripts/clean-for-production.js", "build:production": "npm run clean:production && npm run prebuild && npm run prepackage && npm run package", "dist:production": "npm run build:production && npm run make:squirrel", "clean": "<PERSON><PERSON><PERSON> out dist .vite", "postinstall": "electron-builder install-app-deps", "standardize:icons": "node scripts/standardize-icons.js", "convert:icons": "node scripts/convert-icons.js", "verify:icons": "node scripts/verify-icons.js", "embed:icon": "node scripts/embed-icon.js", "embed:icon-manual": "node scripts/manual-icon-embed.js", "verify:icon": "node scripts/verify-icon-embedding.js", "clear:icon-cache": "node scripts/clear-icon-cache.js", "build:complete": "node scripts/build-with-icon.js", "package:with-icon": "npm run package && npm run embed:icon"}, "keywords": ["timetable", "scheduling", "university", "qatar-university", "education", "academic", "course-management"], "author": "Prof <PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@electron-forge/cli": "^7.7.0", "@electron-forge/maker-deb": "^7.7.0", "@electron-forge/maker-rpm": "^7.7.0", "@electron-forge/maker-squirrel": "^7.7.0", "@electron-forge/maker-zip": "^7.7.0", "@electron-forge/plugin-auto-unpack-natives": "^7.7.0", "@electron-forge/plugin-fuses": "^7.7.0", "@electron-forge/plugin-vite": "^7.7.0", "@electron/fuses": "^1.8.0", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "electron": "^36.3.1", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "postcss": "^8.4.31", "prettier": "^3.5.3", "rcedit": "^4.0.1", "sharp": "^0.34.2", "tailwindcss": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^5.4.14", "winresourcer": "^0.9.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@mui/x-data-grid": "^7.27.3", "@tanstack/react-query": "^5.67.2", "@types/fs-extra": "^11.0.4", "@types/papaparse": "^5.3.15", "@types/react": "^19.0.10", "@types/react-color": "^3.0.13", "@types/react-dom": "^19.0.4", "@types/react-grid-layout": "^1.3.5", "@types/uuid": "^10.0.0", "electron-squirrel-startup": "^1.0.1", "electron-store": "^8.1.0", "fs-extra": "^11.3.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "papaparse": "^5.5.2", "react": "^19.0.0", "react-color": "^2.19.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-grid-layout": "^1.5.1", "react-toastify": "^11.0.5", "uuid": "^11.1.0", "wicg-inert": "^3.1.3", "xlsx": "^0.18.5", "zod": "^3.24.2", "zustand": "^5.0.4"}}