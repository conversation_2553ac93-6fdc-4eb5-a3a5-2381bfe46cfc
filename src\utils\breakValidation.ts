import { useRuleSystemStore } from '../store/ruleSystem';

/**
 * Checks if a timeslot is a user-defined break
 * @param day The day in short format (e.g., 'Mon', 'Tue')
 * @param period The period number
 * @param ignoreRuleStatus If true, checks only if the timeslot is in the userDefinedBreaks array without considering if the rule is enabled
 * @returns An object indicating if the timeslot is blocked and the reason
 */
export const isUserDefinedBreak = (
  day: string,
  period: number,
  _ignoreRuleStatus = false
): { blocked: boolean; reason: string } => {
  const { userDefinedBreaks } = useRuleSystemStore.getState();

  // Convert day format if it's in long format (e.g., "Monday" to "Mon")
  const shortDay = day.length > 3 ? day.substring(0, 3) : day;

  // Check if this timeslot is in the user-defined breaks
  const breakKey = `${shortDay}-${period}`;
  const isBreak = userDefinedBreaks.includes(breakKey);

  // Silent mode - no break validation logging

  if (!isBreak) {
    return { blocked: false, reason: '' };
  }

  // IMPORTANT: Always respect user-defined breaks regardless of rule status
  // User-defined breaks should have top priority and be respected at all times
  return { blocked: true, reason: 'user-defined-break' };
};

/**
 * Checks if a timeslot is system-blocked (e.g., periods 5-6 and 11-12 on long days)
 * @param day The day in short format (e.g., 'Mon', 'Tue')
 * @param period The period number
 * @returns An object indicating if the timeslot is blocked and the reason
 */
export const isSystemBlockedTimeslot = (
  day: string,
  period: number
): { blocked: boolean; reason: string } => {
  // Convert day format if it's in long format (e.g., "Monday" to "Mon")
  const shortDay = day.length > 3 ? day.substring(0, 3) : day;

  // Check if this is a long day (Monday or Wednesday)
  const isLongDay = ['Mon', 'Wed'].includes(shortDay);

  // System blocked periods - only 5, 6, 11, 12 on long days
  if (isLongDay && (period === 5 || period === 6 || period === 11 || period === 12)) {
    return { blocked: true, reason: 'system-blocked' };
  }

  return { blocked: false, reason: '' };
};

/**
 * Checks if a timeslot is blocked (either system-blocked or user-defined break)
 * @param day The day in short format (e.g., 'Mon', 'Tue')
 * @param period The period number
 * @param ignoreRuleStatus If true, checks only if the timeslot is in the userDefinedBreaks array without considering if the rule is enabled
 * @returns An object indicating if the timeslot is blocked and the reason
 */
export const isBlockedTimeslot = (
  day: string,
  period: number,
  _ignoreRuleStatus = false
): { blocked: boolean; reason: string } => {
  // Convert day format to short format for consistent logging
  const _shortDay = day.length > 3 ? day.substring(0, 3) : day;

  // First check if it's system-blocked
  const systemBlocked = isSystemBlockedTimeslot(day, period);
  if (systemBlocked.blocked) {
    return systemBlocked;
  }

  // Then check if it's a user-defined break
  // IMPORTANT: Always pass true to ignoreRuleStatus to ensure breaks are always respected
  const userBreakResult = isUserDefinedBreak(day, period, true);

  return userBreakResult;
};
