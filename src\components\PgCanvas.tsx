import React, { useState, useEffect, useMemo } from 'react';
import {
  Button,
  Typography,
  Chip
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useAppContext, isPostgraduateCourse } from '../context/AppContext';
import { useDragDrop } from '../context/DragDropContext';
import PgSessionCard from './draggable/PgSessionCard';
import { Lecturer, Course, Session } from '../types/models';

// Define interface for grouped PG session data
interface PgSessionGroup {
  day: string;
  sectionId: string;
  courseId: string;
  courseCode: string;
  courseName: string;
  courseColor: string;
  sectionNumber: number;
  gender: 'M' | 'F';
  scheduledHours: number;
  totalHours: number;
  academicLevel: string;
  sessions: Session[];
}

/**
 * PgCanvas component that displays a dedicated view for postgraduate courses
 * Shows a unified representation of postgraduate sessions (5 PM - 8 PM)
 */
const PgCanvas: React.FC<{ onBack: () => void }> = ({ onBack }) => {
  // Get context data and functions
  const {
    currentSemester,
    courses,
    sections,
    sessions,
    lecturers,
    updateSection,
    updateSession,
    deleteSession
  } = useAppContext();

  // Get drag and drop context
  const {
    dragItem,
    canDropSectionAt,
    createSessionFromSection
  } = useDragDrop();

  // State for highlighted sessions
  const [highlightedSessions, setHighlightedSessions] = useState<string[]>([]);

  // Days of the week
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];

  // State for filtered lecturer
  const [filteredLecturerId, setFilteredLecturerId] = useState<string | null>(null);
  const [filteredLecturer, setFilteredLecturer] = useState<Lecturer | null>(null);

  // State for filtered course
  const [filteredCourseId, setFilteredCourseId] = useState<string | null>(null);
  const [filteredCourse, setFilteredCourse] = useState<Course | null>(null);

  // Set up event listeners for component
  useEffect(() => {
    // Listen for lecturer filter changes from LecturersPanel
    const handleLecturerFilterChange = (event: CustomEvent) => {
      const { lecturerId } = event.detail;
      setFilteredLecturerId(lecturerId);

      if (lecturerId) {
        const lecturer = lecturers.find(l => l.id === lecturerId);
        setFilteredLecturer(lecturer || null);
      } else {
        setFilteredLecturer(null);
      }
    };

    // Listen for course filter changes from CoursesPanel
    const handleCourseFilterChange = (event: CustomEvent) => {
      const { courseId } = event.detail;
      setFilteredCourseId(courseId);

      if (courseId) {
        const course = courses[currentSemester].find(c => c.id === courseId);
        setFilteredCourse(course || null);
      } else {
        setFilteredCourse(null);
      }
    };

    // Listen for focus on session events
    const handleFocusOnSession = (event: CustomEvent) => {
      const { sessions } = event.detail;

      // Highlight the sessions
      setHighlightedSessions(sessions);

      // Clear the highlight after 5 seconds
      setTimeout(() => {
        setHighlightedSessions([]);
      }, 5000);
    };

    // Add event listeners
    window.addEventListener('lecturerFilterChanged', handleLecturerFilterChange as EventListener);
    window.addEventListener('courseFilterChanged', handleCourseFilterChange as EventListener);
    window.addEventListener('focusOnSession', handleFocusOnSession as EventListener);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('lecturerFilterChanged', handleLecturerFilterChange as EventListener);
      window.removeEventListener('courseFilterChanged', handleCourseFilterChange as EventListener);
      window.removeEventListener('focusOnSession', handleFocusOnSession as EventListener);
    };
  }, [lecturers, courses, currentSemester]);

  // Helper function to get the academic level description
  const getAcademicLevelFromCourseCode = (code: string): string => {
    const numericPart = code.match(/\d+/)?.[0] || '';

    if (numericPart.length >= 3) {
      const firstTwoDigits = parseInt(numericPart.substring(0, 2));

      if (firstTwoDigits >= 50 && firstTwoDigits <= 59) return 'diploma';
      if (firstTwoDigits >= 60 && firstTwoDigits <= 69) return 'masters';
      if (firstTwoDigits >= 70 && firstTwoDigits <= 89) return 'phd';
    }

    return 'unknown';
  };

  // Group postgraduate sessions by day, section, and timeframe (5-8 PM)
  const pgSessionGroups = useMemo(() => {
    const groups: Record<string, PgSessionGroup> = {};

    // Get all sections
    const allSections = sections[currentSemester];

    // Get all courses for the current semester
    const allCourses = courses[currentSemester];

    // Apply filtering if active
    let filteredSessions = sessions[currentSemester];

    // Apply lecturer filter if active
    if (filteredLecturerId) {
      filteredSessions = filteredSessions.filter(session => {
        if (session.lecturerId === filteredLecturerId) return true;
        if (session.lecturerIds && session.lecturerIds.includes(filteredLecturerId)) return true;
        return false;
      });
    }

    // Apply course filter if active
    if (filteredCourseId) {
      filteredSessions = filteredSessions.filter(session => {
        const section = allSections.find(s => s.id === session.sectionId);
        return section && section.courseId === filteredCourseId;
      });
    }

    // Process sessions
    filteredSessions.forEach(session => {
      const section = allSections.find(s => s.id === session.sectionId);
      if (!section) return;

      const course = allCourses.find(c => c.id === section.courseId);
      if (!course) return;

      // Only include postgraduate courses
      if (!isPostgraduateCourse(course.courseCode)) return;

      const { day, sectionId } = session;
      const groupKey = `${day}-${sectionId}`;

      // Check if session is in evening hours (periods 9-12)
      if (session.startPeriod >= 9 && session.startPeriod <= 12) {
        if (!groups[groupKey]) {
          groups[groupKey] = {
            day,
            sectionId,
            courseId: course.id,
            courseCode: course.courseCode,
            courseName: course.courseName,
            courseColor: course.color,
            sectionNumber: section.sectionNumber,
            gender: section.gender,
            scheduledHours: section.scheduledHours,
            totalHours: section.totalHours,
            academicLevel: getAcademicLevelFromCourseCode(course.courseCode),
            sessions: []
          };
        }
        groups[groupKey].sessions.push(session);
      }
    });

    return groups;
  }, [currentSemester, sections, courses, sessions, filteredLecturerId, filteredCourseId]);

  // Get course by ID
  const getCourseById = (courseId: string) => {
    return courses[currentSemester].find(course => course.id === courseId);
  };

  // Render a PG session card
  const renderPgSessionCard = (pgSession: PgSessionGroup) => {
    // Skip if no sessions
    if (!pgSession.sessions || pgSession.sessions.length === 0) return null;

    // Sort sessions by period to ensure consistent order
    const sortedSessions = [...pgSession.sessions].sort((a, b) => a.startPeriod - b.startPeriod);

    // Get session IDs for unified operations
    const sessionIds = sortedSessions.map(s => s.id);

    // Find lecturer occurrences across all sessions
    const lecturerOccurrences: Record<string, {
      id: string;
      lecturerId: string;
      firstName: string;
      lastName?: string;
    }> = {};

    // Process each session to collect all lecturers
    sortedSessions.forEach(session => {
      // Process main lecturer (for backward compatibility)
      if (session.lecturerId) {
        const lecturer = lecturers.find(l => l.id === session.lecturerId);
        if (lecturer) {
          const occurrenceId = `${session.id}-${lecturer.id}`;
          lecturerOccurrences[lecturer.id] = {
            id: occurrenceId,
            lecturerId: lecturer.id,
            firstName: lecturer.firstName,
            lastName: lecturer.lastName
          };
        }
      }

      // Process additional lecturers
      if (session.lecturerIds && Array.isArray(session.lecturerIds)) {
        session.lecturerIds.forEach((lecturerId: string) => {
          if (lecturerId === session.lecturerId) return; // Skip if already added from lecturerId

          const lecturer = lecturers.find(l => l.id === lecturerId);
          if (lecturer) {
            const occurrenceId = `${session.id}-${lecturer.id}`;
            lecturerOccurrences[lecturer.id] = {
              id: occurrenceId,
              lecturerId: lecturer.id,
              firstName: lecturer.firstName,
              lastName: lecturer.lastName
            };
          }
        });
      }
    });

    // Handle delete session operation
    const handleDeleteSession = () => {
      // Delete all sessions in this group
      sessionIds.forEach(sessionId => {
        deleteSession(sessionId, currentSemester);
      });

      // Update section's scheduled hours
      const section = sections[currentSemester].find(s => s.id === pgSession.sectionId);
      if (section) {
        // For postgrad courses, we subtract 3 hours (one unified 3-hour block)
        const updatedSection = {
          ...section,
          scheduledHours: Math.max(0, section.scheduledHours - 3)
        };

        updateSection(updatedSection, currentSemester);
      }
    };

    // Handle remove lecturer operation
    const handleRemoveLecturer = (lecturerId: string) => {
      // Remove lecturer from all sessions in this group
      sessionIds.forEach(sessionId => {
        const session = sessions[currentSemester].find(s => s.id === sessionId);
        if (session) {
          // Handle removal from lecturerIds array
          if (session.lecturerIds && session.lecturerIds.includes(lecturerId)) {
            const updatedLecturerIds = session.lecturerIds.filter(id => id !== lecturerId);

            const updatedSession = {
              ...session,
              lecturerIds: updatedLecturerIds,
              // Update the lecturerId field if it matches the removed lecturer
              lecturerId: session.lecturerId === lecturerId ?
                (updatedLecturerIds[0] || '') : session.lecturerId
            };

            // Update session
            updateSession(updatedSession, currentSemester);
          }
          // For backward compatibility with the old lecturerId field
          else if (session.lecturerId === lecturerId) {
            const updatedSession = {
              ...session,
              lecturerId: '',
              lecturerIds: []
            };

            // Update session
            updateSession(updatedSession, currentSemester);
          }
        }
      });
    };

    return (
      <PgSessionCard
        day={pgSession.day}
        sessionIds={sessionIds}
        sectionId={pgSession.sectionId}
        courseCode={pgSession.courseCode}
        courseName={pgSession.courseName}
        courseColor={pgSession.courseColor}
        sectionNumber={pgSession.sectionNumber}
        gender={pgSession.gender}
        scheduledHours={pgSession.scheduledHours}
        totalHours={pgSession.totalHours}
        lecturerOccurrences={Object.values(lecturerOccurrences)}
        onDelete={handleDeleteSession}
        onRemoveLecturer={handleRemoveLecturer}
        isHighlighted={sessionIds.some(id => highlightedSessions.includes(id))}
      />
    );
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-850">
      {/* Header with Back button */}
      <div className="flex justify-between items-center p-2 border-b border-gray-200 dark:border-gray-700 bg-purple-600 dark:bg-purple-900 text-white">
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={onBack}
          variant="outlined"
          color="inherit"
          size="small"
          sx={{ fontSize: '0.85rem', textTransform: 'none', borderColor: 'rgba(255,255,255,0.5)', mr: 2 }}
        >
          Back to Main Canvas
        </Button>

        <Typography variant="h6" className="font-bold">
          Postgraduate Courses (5:00 PM - 8:00 PM)
        </Typography>

        <div className="flex items-center">
          {/* Show active filters */}
          {filteredLecturer && (
            <Chip
              label={`${filteredLecturer.firstName} ${filteredLecturer.lastName}`}
              size="small"
              color="primary"
              onDelete={() => {
                setFilteredLecturerId(null);
                setFilteredLecturer(null);
                // Dispatch event to notify LecturersPanel
                window.dispatchEvent(new CustomEvent('lecturerFilterChanged', {
                  detail: { lecturerId: null }
                }));
              }}
              sx={{ ml: 1, height: 24 }}
            />
          )}

          {filteredCourse && (
            <Chip
              label={filteredCourse.courseCode}
              size="small"
              color="primary"
              onDelete={() => {
                setFilteredCourseId(null);
                setFilteredCourse(null);
                // Dispatch event to notify CoursesPanel
                window.dispatchEvent(new CustomEvent('courseFilterChanged', {
                  detail: { courseId: null }
                }));
              }}
              sx={{ ml: 1, height: 24 }}
            />
          )}
        </div>
      </div>

      {/* PG Timetable grid */}
      <div className="flex-1 overflow-auto p-4">
        <div className="grid grid-cols-1 gap-4 overflow-x-auto bg-gray-100 dark:bg-gray-800 p-3 rounded-md" style={{ minWidth: 'fit-content' }}>
          {/* Header row with days */}
          <div className="grid sticky top-0 z-10 bg-white dark:bg-gray-900 shadow-sm"
            style={{
              gridTemplateColumns: `60px ${days.map(() => 'minmax(190px, 1fr)').join(' ')}`,
              width: '100%'
            }}>
            <div className="p-2"></div>
            {days.map((day) => (
              <div
                key={day}
                className="p-2 text-center font-bold bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-t border-b-2 border-gray-300 dark:border-gray-600 shadow-sm"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Time row (only one row for PG canvas - 5:00 PM to 8:00 PM) */}
          <div className="grid"
            style={{
              gridTemplateColumns: `60px ${days.map(() => 'minmax(190px, 1fr)').join(' ')}`,
              width: '100%'
            }}>
            {/* Time cell */}
            <div
              className="flex items-center justify-center bg-purple-200 dark:bg-purple-900 rounded-l border-r-2 border-gray-300 dark:border-gray-600 sticky left-0 z-10 shadow-sm font-medium text-purple-900 dark:text-purple-200"
              style={{ width: '60px' }} // Fixed width
            >
              <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                5-8 PM
              </Typography>
            </div>

            {/* Day cells */}
            {days.map((day) => {
              // Get PG sessions for this day
              const daySessions = Object.values(pgSessionGroups).filter(
                (group: PgSessionGroup) => group.day === day
              );

              return (
                <div
                  key={day}
                  className="p-2 bg-purple-50 dark:bg-purple-900/20 border-2 border-gray-300 dark:border-gray-600 rounded-sm flex flex-col relative transition-all duration-200 ease-in-out"
                  style={{ minHeight: '6rem', height: 'auto' }}
                  onDragOver={(e) => {
                    // Make the cell a drop zone for postgrad sections
                    e.preventDefault();
                    if (dragItem && dragItem.type === 'SECTION') {
                      // Check if this is a postgraduate section
                      const sectionId = dragItem.id;
                      const section = sections[currentSemester].find(s => s.id === sectionId);
                      if (!section) {
                        e.dataTransfer.dropEffect = 'none';
                        return;
                      }

                      const course = getCourseById(section.courseId);
                      if (!course || !isPostgraduateCourse(course.courseCode)) {
                        e.dataTransfer.dropEffect = 'none';
                        return;
                      }

                      const canDrop = canDropSectionAt(dragItem.id, day, 10); // Use period 10 (5 PM) as reference
                      if (canDrop) {
                        e.currentTarget.classList.add('ring-2', 'ring-purple-400', 'dark:ring-purple-500');
                        e.dataTransfer.dropEffect = 'copy';
                      } else {
                        e.dataTransfer.dropEffect = 'none';
                      }
                    }
                  }}
                  onDragLeave={(e) => {
                    // Remove highlight when drag leaves
                    e.currentTarget.classList.remove('ring-2', 'ring-purple-400', 'dark:ring-purple-500');
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    e.currentTarget.classList.remove('ring-2', 'ring-purple-400', 'dark:ring-purple-500');

                    try {
                      const data = JSON.parse(e.dataTransfer.getData('application/json'));
                      if (data.type === 'SECTION') {
                        // Create session at period 10 (5 PM)
                        createSessionFromSection(data.id, day, 10);
                      }
                    } catch (error) {
                      console.error('Error parsing drop data:', error);
                    }
                  }}
                >
                  {/* Time pill box */}
                  <div className="absolute top-0 right-0.5 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-[0.6rem] px-1 py-0.5 rounded-full z-10 opacity-70 hover:opacity-100 transition-opacity">
                    5:00 PM - 8:00 PM
                  </div>

                  {/* PG Sessions container */}
                  <div className="mt-4 relative z-0">
                    {daySessions.length > 0 ? (
                      daySessions.map((pgSession: PgSessionGroup) => (
                        <div key={`${pgSession.day}-${pgSession.sectionId}`} className="mb-1 last:mb-0 relative z-10">
                          {renderPgSessionCard(pgSession)}
                        </div>
                      ))
                    ) : (
                      <PgSessionCard
                        day={day}
                        sessionIds={[]}
                        isEmpty={true}
                      />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PgCanvas;