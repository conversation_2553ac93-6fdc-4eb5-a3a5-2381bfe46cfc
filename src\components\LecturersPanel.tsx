import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { Lecturer, Semester, Session, Section, Course } from '../types/models';
import { Typography, IconButton, Tooltip, Menu, MenuItem } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import LecturerImportModal from './modals/LecturerImportModal';
import LecturerAddEditModal from './modals/LecturerAddEditModal';
import LecturerTimetableModal from './modals/LecturerTimetableModal';
import DraggableLecturerCard from './draggable/DraggableLecturerCard';

// Define the lecturer data structure for the modal
interface LecturerData {
  title: string;
  firstName: string;
  lastName?: string; // Made optional - lecturers may only have a first name
  email: string;
  department?: string; // Department field (optional)
  supervisionHoursFall: number;
  supervisionHoursSpring: number;
  maxYearLoad: number;
  coursesAbleToTeach: string[];
  preferredTiming: 'Morning' | 'Evening' | 'Both';
}

/**
 * LecturersPanel component that displays the right panel for lecturers
 * Shows a list of lecturers with their workload and assigned courses
 */
const LecturersPanel: React.FC = () => {
  // Get context data
  const {
    currentSemester,
    courses,
    lecturers,
    addLecturer,
    updateLecturer,
    deleteLecturer,
    importLecturers,
    lecturerFilter,
    setLecturerFilter,
    isLecturerModalOpen,
    setIsLecturerModalOpen,
    isLecturerImportModalOpen,
    setIsLecturerImportModalOpen,
    currentLecturer,
    setCurrentLecturer,
    sessions,
    sections
  } = useAppContext();

  // State for filter menu
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  // State for expanded lecturer cards - default to expanded
  const [expandedLecturers, setExpandedLecturers] = useState<Record<string, boolean>>({});

  // Load UI state from store on component mount
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const uiState = await window.electronAPI.store.get('uiState') as any;
        if (uiState && uiState.expandedLecturers) {
          setExpandedLecturers(uiState.expandedLecturers);
        } else {
          // Initialize all lecturers as expanded by default if not in store
          const expanded: Record<string, boolean> = {};
          lecturers.forEach(lecturer => {
            expanded[lecturer.id] = true; // Set all to expanded by default
          });
          setExpandedLecturers(expanded);
        }
      } catch (error) {
        console.error('Error loading UI state:', error);

        // Fallback to initializing all lecturers as expanded
        const expanded: Record<string, boolean> = {};
        lecturers.forEach(lecturer => {
          expanded[lecturer.id] = true; // Set all to expanded by default
        });
        setExpandedLecturers(expanded);
      }
    };

    loadUIState();
  }, [lecturers]);

  // Save expanded lecturers state to store when it changes
  useEffect(() => {
    const saveUIState = async () => {
      try {
        // Get the current UI state
        const currentUIState = await window.electronAPI.store.get('uiState') as any;
        // Create a new UI state object with only the expandedLecturers updated
        // Preserve all other properties, especially panel visibility managed by App.tsx
        const newUIState = {
          ...currentUIState,
          expandedLecturers
          // Do NOT touch showCoursesPanel, showLecturersPanel, or darkMode
          // These are managed by App.tsx
        };
        // Set the new UI state
        await window.electronAPI.store.set('uiState', newUIState);
      } catch (error) {
        console.error('Error saving UI state:', error);
      }
    };

    saveUIState();
  }, [expandedLecturers]);

  // Filter options
  const filterOptions = [
    { value: 'all', label: 'All lecturers' },
    { value: 'fully', label: 'Fully loaded' },
    { value: 'partially', label: 'Not fully loaded' },
    { value: 'overloaded', label: 'Overloaded' }
  ];

  // Handler for filter menu open
  const handleFilterMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  // Handler for filter menu close
  const handleFilterMenuClose = () => {
    setFilterAnchorEl(null);
  };

  // Handler for filter selection
  const handleFilterSelect = (filter: string) => {
    setLecturerFilter(filter);
    handleFilterMenuClose();
  };

  // Toggle lecturer expansion
  const toggleLecturerExpansion = (lecturerId: string) => {
    setExpandedLecturers(prev => ({
      ...prev,
      [lecturerId]: !prev[lecturerId]
    }));
  };

  // Get teaching hours for a lecturer in the current semester
  const getTeachingHours = (lecturer: Lecturer) => {
    // Find all sessions assigned to this lecturer in the current semester
    // Check both lecturerId and lecturerIds array
    const lecturerSessions = sessions[currentSemester].filter(
      session => session.lecturerId === lecturer.id ||
                (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach(session => {
      // Find the section and course for this session
      const section = sections[currentSemester].find(s => s.id === session.sectionId);
      if (!section) return;

      const course = courses[currentSemester].find(c => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  // Get teaching hours for a lecturer in a specific semester
  // This function is used by calculateYearlyLoad to avoid modifying the currentSemester variable
  const getTeachingHoursForSemester = (lecturer: Lecturer, semester: Semester) => {
    // Find all sessions assigned to this lecturer in the specified semester
    // Check both lecturerId and lecturerIds array
    const lecturerSessions = sessions[semester as keyof typeof sessions].filter(
      (session: Session) => session.lecturerId === lecturer.id ||
                (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach((session: Session) => {
      // Find the section and course for this session
      const section = sections[semester as keyof typeof sections].find((s: Section) => s.id === session.sectionId);
      if (!section) return;

      const course = courses[semester as keyof typeof courses].find((c: Course) => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  // Calculate semester load based on current semester
  const calculateSemesterLoad = (lecturer: Lecturer) => {
    // Get supervision hours for current semester
    // Don't include supervision hours for Summer semester
    const supervisionHours = currentSemester === 'Summer' ? 0 :
                            currentSemester === 'Fall' ?
                            lecturer.supervisionHoursFall :
                            lecturer.supervisionHoursSpring;

    // Get teaching hours for current semester
    const teachingHours = getTeachingHours(lecturer);

    return supervisionHours + teachingHours;
  };

  // Calculate max semester load based on current semester
  const calculateMaxSemesterLoad = (lecturer: Lecturer) => {
    switch (currentSemester) {
      case 'Fall':
        return lecturer.maxYearLoad / 2; // Half of the max year load
      case 'Spring': {
        // Calculate remaining load after Fall semester
        const fallLoad = lecturer.supervisionHoursFall + getTeachingHoursForSemester(lecturer, 'Fall');
        return Math.max(0, lecturer.maxYearLoad - fallLoad);
      }
      case 'Summer':
        return 6; // Fixed 6 hours for Summer
      default:
        return 0;
    }
  };

  // Calculate yearly load
  const calculateYearlyLoad = (lecturer: Lecturer) => {
    // Supervision hours from both semesters
    const supervisionHours = lecturer.supervisionHoursFall + lecturer.supervisionHoursSpring;

    // Store current semester to restore it later
    const originalSemester = currentSemester;

    // Create a mutable copy of the semester for temporary calculations
    let tempSemester = originalSemester;

    // Calculate Fall teaching load
    let fallTeachingLoad = 0;
    if (originalSemester !== 'Fall') {
      // Temporarily switch to Fall to calculate load
      tempSemester = 'Fall';
      // Use a modified version of getTeachingHours that accepts a semester parameter
      fallTeachingLoad = getTeachingHoursForSemester(lecturer, tempSemester);
    } else {
      fallTeachingLoad = getTeachingHours(lecturer);
    }

    // Calculate Spring teaching load
    let springTeachingLoad = 0;
    if (originalSemester !== 'Spring') {
      // Temporarily switch to Spring to calculate load
      tempSemester = 'Spring';
      // Use a modified version of getTeachingHours that accepts a semester parameter
      springTeachingLoad = getTeachingHoursForSemester(lecturer, tempSemester);
    } else {
      springTeachingLoad = getTeachingHours(lecturer);
    }

    // Summer is not included in yearly load as per requirements

    return supervisionHours + fallTeachingLoad + springTeachingLoad;
  };

  // Get filtered lecturers based on selected filter
  const getFilteredLecturers = () => {
    // Sort lecturers alphabetically by last name, then first name
    const sortedLecturers = [...lecturers].sort((a, b) => {
      const aName = `${a.lastName || ''} ${a.firstName}`.trim();
      const bName = `${b.lastName || ''} ${b.firstName}`.trim();
      return aName.localeCompare(bName);
    });

    return sortedLecturers.filter(lecturer => {
      const semLoad = calculateSemesterLoad(lecturer);
      const maxSemLoad = calculateMaxSemesterLoad(lecturer);
      const percentage = (semLoad / maxSemLoad) * 100;

      switch (lecturerFilter) {
        case 'fully':
          return percentage >= 90 && percentage <= 100;
        case 'partially':
          return percentage < 90;
        case 'overloaded':
          return percentage > 100;
        default:
          return true;
      }
    });
  };

  // Get assigned courses for a lecturer in the current semester
  const getAssignedCourses = (lecturer: Lecturer) => {
    // Find all sessions assigned to this lecturer in the current semester
    const lecturerSessions = sessions[currentSemester].filter(
      session => session.lecturerId === lecturer.id
    );

    // Extract unique section IDs from these sessions
    const sectionIds = [...new Set(lecturerSessions.map(session => session.sectionId))];

    // Get course IDs from sections
    const courseIds = sectionIds.map(sectionId => {
      const section = sections[currentSemester].find(s => s.id === sectionId);
      return section ? section.courseId : null;
    }).filter(Boolean) as string[];

    // Map course IDs to course codes and colors
    const coursesMap = new Map<string, { code: string; color: string }>();

    courseIds.forEach(courseId => {
      const course = courses[currentSemester].find(c => c.id === courseId);
      if (course) {
        // Only add if this course code isn't already in the map
        if (!coursesMap.has(course.courseCode)) {
          coursesMap.set(course.courseCode, {
            code: course.courseCode,
            color: course.color
          });
        }
      }
    });

    // Convert map values to array
    return Array.from(coursesMap.values());
  };

  // Get the teaching days count for a lecturer
  const getTeachingDaysCount = (lecturer: Lecturer) => {
    try {
      // Get all sessions for this lecturer, including those where the lecturer is in lecturerIds array
      const lecturerSessions = sessions[currentSemester].filter(
        session => session.lecturerId === lecturer.id || (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
      );



      // Get unique days
      const teachingDays = new Set<string>();

      lecturerSessions.forEach(session => {
        teachingDays.add(session.day);
      });


      return teachingDays.size;
    } catch (error) {
      console.error('Error calculating teaching days count:', error);
      return 0;
    }
  };

  // Get the total empty hours between the first and last session in a day for a lecturer
  // Note: On long days (Monday, Wednesday), periods 5 and 6 are not considered as empty hours
  // because they are not real time slots that could be filled
  const getMaxGapBetweenSessions = (lecturer: Lecturer) => {
    try {
      // Get all days the lecturer is teaching
      const lecturerSessions = sessions[currentSemester].filter(
        session => session.lecturerId === lecturer.id || (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
      );




      // If there are no sessions, return 0
      if (lecturerSessions.length === 0) {
        return 0;
      }

      // Get unique days
      const teachingDays = [...new Set(lecturerSessions.map(session => session.day))];

      // If there are no teaching days, return 0
      if (teachingDays.length === 0) {
        return 0;
      }

      // Calculate total empty hours for each day and find the maximum
      let maxEmptyHours = 0;

      // Process each teaching day
      for (const day of teachingDays) {
        // Get all sessions for this lecturer on this day
        const daySessions = lecturerSessions.filter(session => session.day === day);



        // If there's only one session on this day, there's no empty hours
        if (daySessions.length <= 1) {

          continue; // Skip to the next day
        }

        // Sort sessions by start period
        const sortedSessions = [...daySessions].sort((a, b) => a.startPeriod - b.startPeriod);

        // Get first and last session
        const firstSession = sortedSessions[0];
        const lastSession = sortedSessions[sortedSessions.length - 1];

        // Check if this is a long day (Monday or Wednesday)
        const isLongDay = ['Monday', 'Wednesday'].includes(day);

        // Create an array of all periods between first and last session
        const allPeriods: number[] = [];
        for (let p = firstSession.startPeriod; p <= lastSession.endPeriod; p++) {
          allPeriods.push(p);
        }

        // Create an array of all periods occupied by sessions
        const occupiedPeriods: number[] = [];
        for (const session of sortedSessions) {
          for (let p = session.startPeriod; p <= session.endPeriod; p++) {
            occupiedPeriods.push(p);
          }
        }

        // Find empty periods (periods that are in allPeriods but not in occupiedPeriods)
        const emptyPeriods = allPeriods.filter(p => !occupiedPeriods.includes(p));

        // For long days, remove periods 5 and 6 from empty periods if they exist
        let adjustedEmptyPeriods = [...emptyPeriods];
        if (isLongDay) {
          adjustedEmptyPeriods = emptyPeriods.filter(p => p !== 5 && p !== 6);
        }

        // Calculate empty hours based on day type
        let emptyHours = 0;
        if (isLongDay) {
          // On long days, each period is 1.5 hours
          emptyHours = adjustedEmptyPeriods.length * 1.5;
        } else {
          // On regular days, each period is 1 hour
          emptyHours = adjustedEmptyPeriods.length;
        }



        // Update max empty hours
        if (emptyHours > maxEmptyHours) {
          maxEmptyHours = emptyHours;
        }
      }


      return maxEmptyHours;
    } catch (error) {
      console.error('Error calculating max gap between sessions:', error);
      return 0;
    }
  };

  // Handle add lecturer button click
  const handleAddLecturer = () => {
    setCurrentLecturer(null);
    setIsLecturerModalOpen(true);
  };

  // Handle import lecturers button click
  const handleImportLecturers = () => {
    setIsLecturerImportModalOpen(true);
  };

  // Handle edit lecturer button click
  const handleEditLecturer = (lecturer: Lecturer, event: React.MouseEvent) => {
    event.stopPropagation();
    setCurrentLecturer(lecturer);
    setIsLecturerModalOpen(true);
  };

  // Handle delete lecturer button click
  const handleDeleteLecturer = (lecturerId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (window.confirm('Are you sure you want to delete this lecturer?')) {
      deleteLecturer(lecturerId);
    }
  };

  // State for lecturer timetable modal
  const [isLecturerTimetableModalOpen, setIsLecturerTimetableModalOpen] = useState(false);
  const [selectedLecturer, setSelectedLecturer] = useState<Lecturer | null>(null);

  // State for filtered lecturer in timetable
  const [filteredLecturerId, setFilteredLecturerId] = useState<string | null>(null);

  // State for filtered lecturer in timetable (original state)

  // Listen for filter changes from TimetableCanvas
  useEffect(() => {
    const handleLecturerFilterChange = (event: CustomEvent) => {
      const { lecturerId } = event.detail;
      setFilteredLecturerId(lecturerId);
    };

    // Listen for reset filter events
    const handleResetLecturerPanelFilter = () => {
      setLecturerFilter('all');
    };

    // Add event listeners
    window.addEventListener('lecturerFilterChanged', handleLecturerFilterChange as EventListener);
    window.addEventListener('resetLecturerPanelFilter', handleResetLecturerPanelFilter as EventListener);

    // Clean up event listeners on unmount
    return () => {
      window.removeEventListener('lecturerFilterChanged', handleLecturerFilterChange as EventListener);
      window.removeEventListener('resetLecturerPanelFilter', handleResetLecturerPanelFilter as EventListener);
    };
  }, []);

  // Handle show lecturer timetable
  const handleShowLecturerTimetable = (lecturer: Lecturer, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedLecturer(lecturer);
    setIsLecturerTimetableModalOpen(true);
  };

  // Handle filter timetable by lecturer
  const handleFilterTimetable = (lecturer: Lecturer, event: React.MouseEvent) => {
    event.stopPropagation();
    // If the lecturer is already filtered, clear the filter
    if (filteredLecturerId === lecturer.id) {
      setFilteredLecturerId(null);

      // Dispatch a custom event to notify TimetableCanvas about the filter change
      window.dispatchEvent(new CustomEvent('lecturerFilterChanged', {
        detail: {
          lecturerId: null
        }
      }));
    } else {
      setFilteredLecturerId(lecturer.id);

      // Dispatch a custom event to notify TimetableCanvas about the filter change
      window.dispatchEvent(new CustomEvent('lecturerFilterChanged', {
        detail: {
          lecturerId: lecturer.id
        }
      }));
    }
  };

  // Handle save lecturer (from modal)
  const handleSaveLecturer = (lecturerData: LecturerData) => {
    // Ensure department is included (even if empty)
    const lecturerWithDepartment = {
      ...lecturerData,
      department: lecturerData.department || ''
    };

    if (currentLecturer) {
      // Update existing lecturer
      updateLecturer({
        ...lecturerWithDepartment,
        id: currentLecturer.id
      });
    } else {
      // Add new lecturer
      addLecturer(lecturerWithDepartment);
      setLecturerFilter('all'); // Reset filter after adding new lecturer
    }
    setIsLecturerModalOpen(false);
  };

  // Handle import lecturers (from modal)
  const handleImportLecturersSubmit = (lecturers: LecturerData[]) => {
    // Ensure department is included for all imported lecturers
    const lecturersWithDepartment = lecturers.map(lecturer => ({
      ...lecturer,
      department: lecturer.department || ''
    }));

    importLecturers(lecturersWithDepartment);
    setIsLecturerImportModalOpen(false); // Close the modal after import
    setLecturerFilter('all'); // Reset filter after import
  };

  // Get filtered lecturers
  const filteredLecturers = getFilteredLecturers();

  // Get all available courses for the lecturer modal
  const availableCourses = courses[currentSemester].map(course => ({
    code: course.courseCode,
    name: course.courseName,
    color: course.color
  }));

  return (
    <div className="h-full flex flex-col">
      {/* Panel header */}
      <div className="p-3 pl-10 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800">
        <Typography variant="subtitle1" className="font-semibold">
          Lecturers
        </Typography>

        <div className="flex space-x-1">
          <Tooltip title="Add new lecturer">
            <IconButton
              size="small"
              onClick={handleAddLecturer}
              className="bg-blue-50 hover:bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-200 dark:hover:bg-blue-800"
            >
              <AddIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Import lecturers from Excel">
            <IconButton
              size="small"
              onClick={handleImportLecturers}
              className="bg-green-50 hover:bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-200 dark:hover:bg-green-800"
            >
              <UploadFileIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Filter lecturers">
            <IconButton
              size="small"
              onClick={handleFilterMenuOpen}
              sx={{
                backgroundColor: lecturerFilter && lecturerFilter !== 'all' ? 'primary.main' : 'inherit',
                color: lecturerFilter && lecturerFilter !== 'all' ? 'common.white' : 'inherit',
                '&:hover': {
                  backgroundColor: lecturerFilter && lecturerFilter !== 'all' ? 'primary.dark' : 'action.hover'
                }
              }}
            >
              <FilterListIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
              {lecturerFilter !== 'all' && (
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
              )}
            </IconButton>
          </Tooltip>

          {/* Filter menu */}
          <Menu
            anchorEl={filterAnchorEl}
            open={Boolean(filterAnchorEl)}
            onClose={handleFilterMenuClose}
          >
            {filterOptions.map((option) => (
              <MenuItem
                key={option.value}
                onClick={() => handleFilterSelect(option.value)}
                selected={lecturerFilter === option.value}
              >
                {option.label}
              </MenuItem>
            ))}
          </Menu>
        </div>
      </div>

      {/* Lecturers list */}
      <div className="flex-1 overflow-y-auto p-2 space-y-2 bg-gray-100 dark:bg-gray-900">
        {filteredLecturers.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400 p-4">
            <Typography variant="body2" align="center">
              No lecturers found for the selected filter.
            </Typography>
            <Typography variant="body2" align="center">
              Try a different filter or add a new lecturer.
            </Typography>
          </div>
        ) : (
          filteredLecturers.map((lecturer) => {
            const semesterLoad = calculateSemesterLoad(lecturer);
            const maxSemesterLoad = calculateMaxSemesterLoad(lecturer);
            const yearlyLoad = calculateYearlyLoad(lecturer);
            // These percentages are calculated but not used in the component
            // const semesterPercentage = getLoadPercentage(semesterLoad, maxSemesterLoad);
            // const yearlyPercentage = getLoadPercentage(yearlyLoad, lecturer.maxYearLoad);
            const assignedCourses = getAssignedCourses(lecturer);
            const supervisionHours = currentSemester === 'Fall'
              ? lecturer.supervisionHoursFall
              : lecturer.supervisionHoursSpring;
            const teachingDaysCount = getTeachingDaysCount(lecturer);
            const maxGapBetweenSessions = getMaxGapBetweenSessions(lecturer);

            return (
              <div
                key={lecturer.id}
                className="mb-2 shadow-md"
              >
                <DraggableLecturerCard
                  lecturer={lecturer}
                  expanded={expandedLecturers[lecturer.id] || false}
                  onToggleExpand={() => toggleLecturerExpansion(lecturer.id)}
                  onEdit={handleEditLecturer}
                  onDelete={handleDeleteLecturer}
                  onShowTimetable={handleShowLecturerTimetable}
                  onFilter={handleFilterTimetable}
                  isFiltered={filteredLecturerId === lecturer.id}
                  semesterLoad={semesterLoad}
                  maxSemesterLoad={maxSemesterLoad}
                  yearlyLoad={yearlyLoad}
                  assignedCourses={assignedCourses}
                  supervisionHours={supervisionHours}
                  teachingDaysCount={teachingDaysCount}
                  maxGapBetweenSessions={maxGapBetweenSessions}
                />
              </div>
            );
          })
        )}
      </div>

      {/* Lecturer Add/Edit Modal */}
      <LecturerAddEditModal
        open={isLecturerModalOpen}
        onClose={() => setIsLecturerModalOpen(false)}
        onSave={handleSaveLecturer}
        lecturer={currentLecturer ? {
          id: currentLecturer.id,
          title: currentLecturer.title,
          firstName: currentLecturer.firstName,
          lastName: currentLecturer.lastName,
          email: currentLecturer.email,
          department: currentLecturer.department || '',
          supervisionHoursFall: currentLecturer.supervisionHoursFall,
          supervisionHoursSpring: currentLecturer.supervisionHoursSpring,
          maxYearLoad: currentLecturer.maxYearLoad,
          coursesAbleToTeach: currentLecturer.coursesAbleToTeach,
          preferredTiming: currentLecturer.preferredTiming,
          preferredCourseLevel: currentLecturer.preferredCourseLevel,
          femaleOnlyTeaching: currentLecturer.femaleOnlyTeaching,
          maxTeachingDaysPerWeek: currentLecturer.maxTeachingDaysPerWeek || 5,
          maxConsecutivePeriods: currentLecturer.maxConsecutivePeriods,
          maxGapBetweenPeriods: currentLecturer.maxGapBetweenPeriods
        } : undefined}
        availableCourses={availableCourses}
      />

      {/* Lecturer Import Modal */}
      <LecturerImportModal
        open={isLecturerImportModalOpen}
        onClose={() => setIsLecturerImportModalOpen(false)}
        onImport={handleImportLecturersSubmit}
        availableCourses={availableCourses.map(course => course.code)}
        existingLecturerEmails={lecturers.map(lecturer => lecturer.email)}
      />

      {/* Lecturer Timetable Modal */}
      <LecturerTimetableModal
        open={isLecturerTimetableModalOpen}
        onClose={() => setIsLecturerTimetableModalOpen(false)}
        lecturer={selectedLecturer}
      />
    </div>
  );
};

export default LecturersPanel;
