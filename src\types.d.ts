// Declaration file for global variables and modules

// Declare electron-forge environment variables
declare const MAIN_WINDOW_VITE_DEV_SERVER_URL: string | undefined;
declare const MAIN_WINDOW_VITE_NAME: string;

// Declare module for electron-squirrel-startup
declare module 'electron-squirrel-startup' {
  const started: boolean;
  export default started;
}

// Declare the electronAPI interface for the window object
interface Window {
  electronAPI: {
    store: {
      get: (key: string) => Promise<unknown>;
      set: (key: string, value: unknown) => Promise<void>;
      delete: (key: string) => Promise<void>;
      clear: () => Promise<void>;
    },
    dialog: {
      showSaveDialog: (options: unknown) => Promise<unknown>;
      showOpenDialog: (options: unknown) => Promise<unknown>;
    },
    fs: {
      writeFile: (filePath: string, data: string) => Promise<{ success: boolean; error?: string }>;
      readFile: (filePath: string) => Promise<{ success: boolean; data?: string; error?: string }>;
    },
    zoom: {
      zoomIn: () => number;
      zoomOut: () => number;
      resetZoom: () => number;
      setZoomFactor: (zoomFactor: number) => number;
      getZoomFactor: () => number;
    },
    pdf: {
      generate: (htmlContent: string, filename: string) => Promise<{ success: boolean; filePath?: string; error?: string }>;
    },
    shell: {
      openExternal: (url: string) => Promise<void>;
    }
  }
}
