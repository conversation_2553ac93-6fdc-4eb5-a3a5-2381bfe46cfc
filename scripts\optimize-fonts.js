#!/usr/bin/env node

/**
 * Font Optimization Script for QU Scheduler
 * Optimizes Tajawal font for Arabic text support in production builds
 */

const fs = require('fs');
const path = require('path');

const FONT_CONFIG = {
  // Tajawal font subsets for Arabic support
  arabic: {
    unicodeRange: 'U+0600-06FF,U+200C-200E,U+2010-2011,U+204F,U+2E80-2EFF',
    description: 'Arabic script support'
  },
  latin: {
    unicodeRange: 'U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD',
    description: 'Latin script support'
  }
};

function optimizeFonts() {
  console.log('🔤 Optimizing fonts for offline production...');

  const fontDir = path.join(__dirname, '../src/assets/fonts');

  if (!fs.existsSync(fontDir)) {
    console.log('📁 Creating fonts directory...');
    fs.mkdirSync(fontDir, { recursive: true });
  }

  // Check if local font CSS already exists
  const localCssPath = path.join(fontDir, 'tajawal-local.css');
  if (fs.existsSync(localCssPath)) {
    console.log('✅ Local font configuration already exists!');
    console.log(`📄 Using: ${localCssPath}`);
    return;
  }

  // Create offline font CSS with system font fallbacks
  const fontCSS = `/* Offline-Ready Arabic Font Configuration for QU Scheduler */

/* Use system fonts that support Arabic text for complete offline capability */
.arabic-text {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
  direction: rtl;
  text-align: right;
  font-feature-settings: 'liga' 1, 'kern' 1;
}

/* Arabic text styling for left-to-right contexts (mixed content) */
.arabic-text-ltr {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1;
}

/* Optimize text rendering for all fonts */
.text-optimize {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Font weight classes for consistent styling */
.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

/* Ensure proper Arabic text display in all contexts */
[lang="ar"], .arabic {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
  direction: rtl;
}

/* Mixed content (Arabic + English) styling */
.mixed-content {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;
}`;

  fs.writeFileSync(localCssPath, fontCSS);

  console.log('✅ Offline font optimization complete!');
  console.log(`📄 Generated: ${localCssPath}`);
  console.log('🔒 Fonts are now configured for complete offline use!');
}

if (require.main === module) {
  optimizeFonts();
}

module.exports = { optimizeFonts, FONT_CONFIG };
