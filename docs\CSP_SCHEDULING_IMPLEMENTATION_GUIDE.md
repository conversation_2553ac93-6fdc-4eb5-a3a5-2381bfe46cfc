# CSP-Based Auto-Scheduling Implementation Guide

## Overview

This guide outlines the implementation of a Constraint Satisfaction Problem (CSP) approach for the auto-scheduling system, which will provide significant improvements over the current greedy algorithm.

## CSP Framework Design

### Core Data Structures

```typescript
interface TimeSlot {
  day: string;
  period: number;
  isLongDay: boolean;
  timeOfDay: 'morning' | 'evening';
}

interface CSPVariable {
  sectionId: string;
  courseCode: string;
  contactHours: number;
  academicLevel: string;
  gender: string;
  lecturerId?: string;
  domain: TimeSlot[][];  // Array of possible patterns
  currentAssignment?: TimeSlot[];
}

interface CSPConstraint {
  id: string;
  type: 'unary' | 'binary' | 'global';
  priority: number;
  variables: string[];  // Variable IDs this constraint affects
  validate: (assignment: Map<string, TimeSlot[]>) => ConstraintResult;
}

interface ConstraintResult {
  satisfied: boolean;
  violationScore: number;
  message?: string;
}
```

### Constraint Categories

#### 1. Hard Constraints (Must be satisfied)
- **System Blocked Timeslots**: Periods 5-6 on long days, period 12 on regular days
- **User-Defined Breaks**: User-specified break periods
- **Contact Hours**: Exact match of scheduled hours to required hours
- **No Duplicate Sessions**: Same section cannot be in multiple timeslots simultaneously
- **Maximum Sessions per Timeslot**: Undergraduate theory course 
limits
- **Pattern Preferences**: Follow standard scheduling patterns

#### 2. Soft Constraints (Preferably satisfied)
- **Lecturer Preferences**: Morning/evening teaching preferences


#### 3. Global Constraints (System-wide optimization)

- **Academic Level Separation**: Minimize overlaps between year levels
- **Lecturer Utilization**: Optimize lecturer schedule efficiency

## Algorithm Implementation

### 1. Variable Ordering Heuristics

```typescript
function orderVariables(variables: CSPVariable[]): CSPVariable[] {
  return variables.sort((a, b) => {
    // Most Constrained Variable (MCV) - fewest remaining values
    const aDomainSize = a.domain.length;
    const bDomainSize = b.domain.length;
    
    if (aDomainSize !== bDomainSize) {
      return aDomainSize - bDomainSize;
    }
    
    // Most Constraining Variable - affects most other variables
    const aConstrainingScore = calculateConstrainingScore(a);
    const bConstrainingScore = calculateConstrainingScore(b);
    
    if (aConstrainingScore !== bConstrainingScore) {
      return bConstrainingScore - aConstrainingScore;
    }
    
    // Academic level priority (PhD → Masters → Diploma → 4th → 3rd → 2nd → 1st)
    const levelPriority = {
      'phd': 0, 'masters': 1, 'diploma': 2,
      '4th-year': 3, '3rd-year': 4, '2nd-year': 5, '1st-year': 6
    };
    
    return (levelPriority[a.academicLevel] || 7) - (levelPriority[b.academicLevel] || 7);
  });
}
```

### 2. Value Ordering Heuristics

```typescript
function orderValues(variable: CSPVariable, assignment: Map<string, TimeSlot[]>): TimeSlot[][] {
  return variable.domain.sort((a, b) => {
    // Least Constraining Value (LCV) - preserves most options for other variables
    const aConstrainingScore = calculateValueConstrainingScore(a, variable, assignment);
    const bConstrainingScore = calculateValueConstrainingScore(b, variable, assignment);
    
    if (aConstrainingScore !== bConstrainingScore) {
      return aConstrainingScore - bConstrainingScore;
    }
    
    // Prefer patterns that match lecturer preferences
    const aLecturerScore = calculateLecturerPreferenceScore(a, variable);
    const bLecturerScore = calculateLecturerPreferenceScore(b, variable);
    
    return bLecturerScore - aLecturerScore;
  });
}
```

### 3. Constraint Propagation

```typescript
function propagateConstraints(
  variables: Map<string, CSPVariable>,
  constraints: CSPConstraint[],
  assignment: Map<string, TimeSlot[]>
): boolean {
  let changed = true;
  
  while (changed) {
    changed = false;
    
    for (const constraint of constraints) {
      const result = applyConstraint(constraint, variables, assignment);
      
      if (result.domainReduced) {
        changed = true;
      }
      
      if (result.inconsistent) {
        return false; // Inconsistency detected
      }
    }
  }
  
  return true; // Consistent state achieved
}
```

### 4. Backtracking Search

```typescript
function backtrackingSearch(
  variables: CSPVariable[],
  constraints: CSPConstraint[],
  assignment: Map<string, TimeSlot[]> = new Map()
): Map<string, TimeSlot[]> | null {
  
  // Check if assignment is complete
  if (assignment.size === variables.length) {
    return assignment;
  }
  
  // Select next variable using heuristics
  const unassignedVars = variables.filter(v => !assignment.has(v.sectionId));
  const nextVariable = selectNextVariable(unassignedVars, assignment);
  
  // Try each value in the domain
  const orderedValues = orderValues(nextVariable, assignment);
  
  for (const value of orderedValues) {
    // Check if value is consistent with current assignment
    if (isConsistent(nextVariable, value, assignment, constraints)) {
      // Make assignment
      assignment.set(nextVariable.sectionId, value);
      
      // Apply constraint propagation
      const variablesCopy = new Map(variables.map(v => [v.sectionId, { ...v }]));
      
      if (propagateConstraints(variablesCopy, constraints, assignment)) {
        // Recursively search
        const result = backtrackingSearch(
          Array.from(variablesCopy.values()),
          constraints,
          assignment
        );
        
        if (result !== null) {
          return result;
        }
      }
      
      // Backtrack
      assignment.delete(nextVariable.sectionId);
    }
  }
  
  return null; // No solution found
}
```

## Advanced Optimization Techniques

### 1. Forward Checking

```typescript
function forwardCheck(
  variable: CSPVariable,
  value: TimeSlot[],
  variables: Map<string, CSPVariable>,
  constraints: CSPConstraint[]
): boolean {
  
  for (const otherVar of variables.values()) {
    if (otherVar.sectionId === variable.sectionId) continue;
    
    // Remove inconsistent values from other variables' domains
    const originalDomain = [...otherVar.domain];
    otherVar.domain = otherVar.domain.filter(otherValue => 
      isConsistentPair(variable, value, otherVar, otherValue, constraints)
    );
    
    // If domain becomes empty, backtrack
    if (otherVar.domain.length === 0) {
      // Restore domain
      otherVar.domain = originalDomain;
      return false;
    }
  }
  
  return true;
}
```

### 2. Arc Consistency (AC-3)

```typescript
function maintainArcConsistency(
  variables: Map<string, CSPVariable>,
  constraints: CSPConstraint[]
): boolean {
  
  const queue: [string, string][] = [];
  
  // Initialize queue with all arcs
  for (const constraint of constraints) {
    if (constraint.type === 'binary') {
      const [var1, var2] = constraint.variables;
      queue.push([var1, var2]);
      queue.push([var2, var1]);
    }
  }
  
  while (queue.length > 0) {
    const [xi, xj] = queue.shift()!;
    
    if (revise(variables.get(xi)!, variables.get(xj)!, constraints)) {
      if (variables.get(xi)!.domain.length === 0) {
        return false; // Inconsistent
      }
      
      // Add all arcs (xk, xi) where xk is a neighbor of xi
      for (const constraint of constraints) {
        if (constraint.variables.includes(xi)) {
          for (const xk of constraint.variables) {
            if (xk !== xi && xk !== xj) {
              queue.push([xk, xi]);
            }
          }
        }
      }
    }
  }
  
  return true;
}
```

### 3. Intelligent Backtracking

```typescript
function conflictDirectedBackjumping(
  variables: CSPVariable[],
  constraints: CSPConstraint[],
  assignment: Map<string, TimeSlot[]>,
  conflictSet: Set<string>
): Map<string, TimeSlot[]> | null {
  
  if (assignment.size === variables.length) {
    return assignment;
  }
  
  const nextVariable = selectNextVariable(
    variables.filter(v => !assignment.has(v.sectionId)),
    assignment
  );
  
  for (const value of orderValues(nextVariable, assignment)) {
    const conflicts = findConflicts(nextVariable, value, assignment, constraints);
    
    if (conflicts.length === 0) {
      assignment.set(nextVariable.sectionId, value);
      
      const result = conflictDirectedBackjumping(
        variables,
        constraints,
        assignment,
        new Set()
      );
      
      if (result !== null) {
        return result;
      }
      
      assignment.delete(nextVariable.sectionId);
    } else {
      // Add conflicts to conflict set
      conflicts.forEach(conflict => conflictSet.add(conflict));
    }
  }
  
  // Backjump to the most recent variable in conflict set
  const backjumpTarget = findBackjumpTarget(assignment, conflictSet);
  return backjumpTarget;
}
```

## Integration Strategy

### 1. Gradual Migration
- Implement CSP as alternative scheduling engine
- A/B test against current greedy algorithm
- Gradually increase CSP usage based on performance

### 2. Hybrid Approach
- Use CSP for complex scheduling scenarios
- Fall back to greedy algorithm for simple cases
- Combine both approaches for optimal results

### 3. Performance Monitoring
- Track scheduling time and quality metrics
- Compare rule violation rates
- Monitor user satisfaction scores

## Expected Benefits

### Performance Improvements
- **Solution Quality**: 50-70% reduction in rule violations
- **Global Optimization**: Better overall timetable structure
- **Conflict Resolution**: Systematic handling of constraint conflicts

### Scalability
- **Large Datasets**: Better performance with many sections
- **Complex Constraints**: Handles intricate rule combinations
- **Extensibility**: Easy to add new constraint types

### Maintainability
- **Modular Design**: Clear separation of concerns
- **Testability**: Individual constraint testing
- **Debuggability**: Clear constraint violation reporting

## Implementation Timeline

### Phase 1 (2-3 weeks): Core Framework
- Implement basic CSP data structures
- Create constraint validation framework
- Build simple backtracking algorithm

### Phase 2 (2-3 weeks): Optimization
- Add variable/value ordering heuristics
- Implement constraint propagation
- Add forward checking

### Phase 3 (1-2 weeks): Advanced Features
- Implement arc consistency
- Add intelligent backtracking
- Performance optimization

### Phase 4 (1 week): Integration
- Integrate with existing system
- Add configuration options
- Performance testing and tuning

This CSP implementation will provide a robust, scalable foundation for the auto-scheduling system with significantly improved solution quality and maintainability.
