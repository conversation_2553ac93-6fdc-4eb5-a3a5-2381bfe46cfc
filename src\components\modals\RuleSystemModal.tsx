import React, { useState, useEffect } from 'react';
import { useRuleSystemStore } from '../../store/ruleSystem';
import { Rule, defaultRules } from '../../types/rules';
import { useAppContext } from '../../context/AppContext';
import { autoScheduleAllSections, AutoSession } from '../../utils/autoScheduling';
import { getAcademicLevel } from '../../utils/ruleValidation';
import { Section, Course, Lecturer, Session } from '../../types/models';
import {
  isUserDefinedBreak,
  isSystemBlockedTimeslot as checkSystemBlockedTimeslot,
  isUserDefinedBreak as checkUserDefinedBreak
} from '../../utils/breakValidation';
import MinimizeIcon from '@mui/icons-material/Minimize';
import CloseIcon from '@mui/icons-material/Close';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SaveIcon from '@mui/icons-material/Save';

import RestartAltIcon from '@mui/icons-material/RestartAlt';
import RefreshIcon from '@mui/icons-material/Refresh';
import CalculateIcon from '@mui/icons-material/Calculate';
import { DialogTitle, DialogContent, DialogActions, Button, Radio, RadioGroup, FormControlLabel, FormControl, FormLabel, Switch, Typography, IconButton, Tooltip } from '@mui/material';
import AccessibleDialog from '../common/AccessibleDialog';
import RuleAnalysisTab, { RuleAnalysisResult, RuleViolation, SectionInfo, ExcludedSectionInfo } from '../tabs/RuleAnalysisTab';
import LecturerAnalysisTab from '../tabs/LecturerAnalysisTab';
import { CPSRefinementReport } from '../../utils/cpsRefinement';

interface RuleSystemModalProps {
  isOpen: boolean;
  onClose: () => void;
  totalSections: number;
}

export const RuleSystemModal: React.FC<RuleSystemModalProps> = ({
  isOpen,
  onClose,
  totalSections
}) => {
  const {
    rules,
    maxSessionsPerTimeslot,
    maxSessionsPerDay,
    userDefinedBreaks,
    maxPostgradLevelPerDay,
    maxPostgradCoursePerDay,
    maxPostgradCoursesPerDay,
    maxGap4thYear,
    maxGap3rdYear,
    assignLecturersInAutoScheduling,
    retryConfiguration,
    setRule,
    setMaxSessionsPerTimeslot,
    setMaxSessionsPerDay,
    setMaxPostgradLevelPerDay,
    setMaxPostgradCoursePerDay,
    setMaxPostgradCoursesPerDay,
    setMaxGap4thYear,
    setMaxGap3rdYear,
    setAssignLecturersInAutoScheduling,
    setRetryConfiguration,
    calculateDefaultMaxSessions
  } = useRuleSystemStore();

  // Get app context for sessions, sections, courses, and adding/deleting sessions
  const {
    sessions,
    sections,
    courses,
    lecturers,
    addSession,
    updateSession,
    deleteSession,
    currentSemester
  } = useAppContext();

  const [activeTab, setActiveTab] = useState<'course-rules' | 'lecturer-rules' | 'timeslots' | 'days' | 'breaks' | 'postgrad' | 'advanced' | 'analysis' | 'lecturer-analysis'>('course-rules');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingMessage, setLoadingMessage] = useState<string>('');
  const [isMinimized, setIsMinimized] = useState<boolean>(false);
  const [showSessionOptions, setShowSessionOptions] = useState<boolean>(false);
  const [sessionOption, setSessionOption] = useState<'keep-all' | 'delete-all' | 'delete-auto'>('keep-all');
  const [lecturerAssignmentOption, setLecturerAssignmentOption] = useState<'no-assignment' | 'simultaneous' | 'post-scheduling'>('simultaneous');
  const [analysisResult, setAnalysisResult] = useState<RuleAnalysisResult | null>(null);
  const [lastCpsReport, setLastCpsReport] = useState<CPSRefinementReport | null>(null);

  // Function to generate current timetable analysis independent of auto-scheduling
  const generateCurrentTimetableAnalysis = (): RuleAnalysisResult => {
    const allSections = sections[currentSemester];
    const currentSessions = sessions[currentSemester];

    // Get sections that have sessions (are scheduled)
    const sectionsWithSessions = new Set(currentSessions.map(session => session.sectionId));

    // Calculate scheduled and unscheduled sections based on actual timetable state
    const actualScheduledSections: string[] = [];
    const actualUnscheduledSections: string[] = [];

    allSections.forEach(section => {
      const course = courses[currentSemester].find(c => c.id === section.courseId);
      if (!course) return;

      // Skip lab sections as they are typically not auto-scheduled
      if (course.courseType === 'Lab') return;

      // Check if section has any sessions
      if (sectionsWithSessions.has(section.id)) {
        // Calculate total scheduled hours for this section
        let totalScheduledHours = 0;
        const sectionSessions = currentSessions.filter(s => s.sectionId === section.id);

        sectionSessions.forEach(session => {
          const isLongDay = session.day === 'Mon' || session.day === 'Wed';
          const sessionHours = (session.endPeriod - session.startPeriod + 1) * (isLongDay ? 1.5 : 1.0);
          totalScheduledHours += sessionHours;
        });

        // Consider section scheduled if it has at least some sessions
        if (totalScheduledHours >= 0.1) {
          actualScheduledSections.push(section.id);
        } else {
          actualUnscheduledSections.push(section.id);
        }
      } else {
        // No sessions for this section
        actualUnscheduledSections.push(section.id);
      }
    });

    // Create basic analysis result
    const analysisData: RuleAnalysisResult = {
      scheduledSections: actualScheduledSections.length,
      unscheduledSections: actualUnscheduledSections.length,
      totalSections: actualScheduledSections.length + actualUnscheduledSections.length,
      allSectionsCount: allSections.length,
      ruleViolations: [],
      unscheduledSectionDetails: [],
      excludedSectionDetails: [],
      ruleCompliance: {},
      // Preserve existing CPS report if available
      cpsReport: analysisResult?.cpsReport || lastCpsReport || undefined
    };

    return analysisData;
  };
  const [forceUpdate, setForceUpdate] = useState<number>(0); // Add this state to force re-renders

  // Initialize analysis when modal opens or when switching to analysis tab
  useEffect(() => {
    if (isOpen && (activeTab === 'analysis' || activeTab === 'lecturer-analysis')) {
      const currentAnalysis = generateCurrentTimetableAnalysis();
      setAnalysisResult(currentAnalysis);
    }
  }, [isOpen, activeTab, currentSemester, sections, sessions, courses]);

  // Clear CPS report when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setLastCpsReport(null);
    }
  }, [isOpen]);

  // Helper function to calculate average contact hours for undergraduate theory courses only
  const calculateAverageContactHours = (): number => {
    let totalContactHours = 0;
    let sectionCount = 0;

    // Check if sections exist for the current semester
    if (!sections[currentSemester] || sections[currentSemester].length === 0) {
      return 3; // Default value if no sections
    }

    sections[currentSemester].forEach(section => {
      const course = courses[currentSemester].find(c => c.id === section.courseId);
      if (course) {
        // Check if it's a theory course
        const isTheory = course.courseType === 'Theory';

        // Check if it's an undergraduate course (course code doesn't start with 5 or higher)
        const numericPart = course.courseCode.match(/\d+/)?.[0] || '';
        const firstDigit = numericPart.length > 0 ? parseInt(numericPart[0]) : -1;
        const isUndergraduate = firstDigit < 5;

        // Only include undergraduate theory courses
        if (isTheory && isUndergraduate) {
          totalContactHours += course.contactHours;
          sectionCount++;
        }
      }
    });

    const avgContactHours = sectionCount > 0 ? Math.round(totalContactHours / sectionCount) : 3;
    return avgContactHours;
  };

  // Calculate default values when the modal is opened
  useEffect(() => {
    if (isOpen && totalSections > 0 && Object.keys(maxSessionsPerTimeslot).length === 0) {
      // Calculate average contact hours and set default max sessions
      const avgContactHours = calculateAverageContactHours();

      // Calculate default max sessions
      calculateDefaultMaxSessions(totalSections, avgContactHours);
    }

    // Ensure the postgraduate rules are enabled when the modal is opened
    if (isOpen) {
      // Check if the postgraduate rules are enabled
      const postgradLevelRule = rules.find(r => r.id === 'limit-postgrad-level-per-day');
      const postgradCourseRule = rules.find(r => r.id === 'limit-postgrad-course-per-day');
      const distributePostgradRule = rules.find(r => r.id === 'distribute-postgrad-courses');

      // If the rules exist but are not enabled, enable them
      if (postgradLevelRule && !postgradLevelRule.enabled) {
        handleRuleToggle('limit-postgrad-level-per-day', true);
      }

      if (postgradCourseRule && !postgradCourseRule.enabled) {
        handleRuleToggle('limit-postgrad-course-per-day', true);
      }

      if (distributePostgradRule && !distributePostgradRule.enabled) {
        handleRuleToggle('distribute-postgrad-courses', true);
      }
    }
  }, [isOpen, totalSections, calculateDefaultMaxSessions, maxSessionsPerTimeslot, calculateAverageContactHours, sections, courses, currentSemester, rules, forceUpdate]);

  // Ensure periods 5 and 6 on long days are always set to 0
  useEffect(() => {
    if (isOpen) {
      // Ensure periods 5 and 6 on long days are set to 0
      ['Mon', 'Wed'].forEach(day => {
        [5, 6].forEach(period => {
          if (maxSessionsPerTimeslot[`${day}-${period}`] !== 0) {
            setMaxSessionsPerTimeslot(`${day}-${period}`, 0);
          }
        });
      });
    }
  }, [isOpen, maxSessionsPerTimeslot, setMaxSessionsPerTimeslot]);

  // Group rules by category for easier rendering
  const courseRules = rules.filter((rule: Rule) => rule.category === 'course')
    .sort((a: Rule, b: Rule) => a.priority - b.priority);

  const lecturerRules = rules.filter((rule: Rule) => rule.category === 'lecturer')
    .sort((a: Rule, b: Rule) => a.priority - b.priority);

  const handleRuleToggle = (ruleId: string, enabled: boolean) => {
    // Update the rule
    setRule(ruleId, { enabled });

    // Force update the rule in localStorage to ensure it's saved
    try {
      const storageData = localStorage.getItem('rule-system-storage');
      if (storageData) {
        const data = JSON.parse(storageData);
        if (data.state && data.state.rules) {
          // Find the rule by ID
          const ruleIndex = data.state.rules.findIndex(
            (r: { id: string }) => r.id === ruleId
          );

          if (ruleIndex !== -1) {
            // Update the rule's enabled state
            data.state.rules[ruleIndex] = {
              ...data.state.rules[ruleIndex],
              enabled: enabled
            };

            // Save back to local storage
            localStorage.setItem('rule-system-storage', JSON.stringify(data));
          }
        }
      }
    } catch (error) {
      console.error(`Error updating rule ${ruleId} in localStorage:`, error);
    }

    // Dispatch an event to notify components that rules have changed
    window.dispatchEvent(new CustomEvent('rulesChanged', {
      detail: { ruleId, enabled }
    }));
  };

  const handlePriorityChange = (ruleId: string, priority: number) => {
    setRule(ruleId, { priority });
  };

  // Restore default rule priorities
  const handleRestoreDefaultRules = () => {
    // Apply updates one by one, but in a single batch
    defaultRules.forEach(defaultRule => {
      // For the postgrad-pattern rule, also update the name and description
      if (defaultRule.id === 'postgrad-pattern') {
        setRule(defaultRule.id, {
          name: 'Follow specific periods for postgraduate courses',
          description: 'Schedule postgraduate courses in periods 10-12 on regular days or 9-10 on long days',
          priority: 2, // Set priority to 2 as it's a critical rule
          enabled: true
        });
      }
      // Make sure the new postgraduate rules are enabled
      else if (defaultRule.id === 'limit-postgrad-level-per-day' ||
               defaultRule.id === 'limit-postgrad-course-per-day' ||
               defaultRule.id === 'distribute-postgrad-courses') {
        setRule(defaultRule.id, {
          priority: defaultRule.priority,
          enabled: true // Force enable these rules
        });
      }
      else {
        setRule(defaultRule.id, {
          priority: defaultRule.priority,
          enabled: defaultRule.enabled
        });
      }
    });

    // Reset the postgraduate rule limits to default values
    setMaxPostgradLevelPerDay(1);
    setMaxPostgradCoursePerDay(1);

    // Reset the gap values to default values
    setMaxGap4thYear(5);
    setMaxGap3rdYear(5);

    // Dispatch an event to notify components that all rules have been reset
    window.dispatchEvent(new CustomEvent('rulesChanged', {
      detail: { allRulesReset: true }
    }));
  };

  // Force reset of rule system storage and reload default rules
  const handleForceResetRules = () => {
    // Clear the rule system storage
    localStorage.removeItem('rule-system-storage');

    // Dispatch an event to notify components that all rules have been reset
    window.dispatchEvent(new CustomEvent('rulesChanged', {
      detail: { allRulesReset: true }
    }));

    // Reload the page to reset the store
    if (confirm('This will reset all rules to their default values and refresh the page. Continue?')) {
      // Before reloading, ensure the new rules are enabled in localStorage
      try {
        const initialState = {
          rules: defaultRules.map(rule => {
            if (rule.id === 'limit-postgrad-level-per-day' ||
                rule.id === 'limit-postgrad-course-per-day' ||
                rule.id === 'distribute-postgrad-courses') {
              return { ...rule, enabled: true };
            }
            return rule;
          }),
          maxSessionsPerTimeslot: {},
          maxSessionsPerDay: {},
          userDefinedBreaks: [],
          maxPostgradLevelPerDay: 1,
          maxPostgradCoursePerDay: 1,
          maxPostgradCoursesPerDay: 2,
          maxGap4thYear: 5,
          maxGap3rdYear: 5
        };

        localStorage.setItem('rule-system-storage', JSON.stringify({
          state: initialState,
          version: 0
        }));
      } catch (error) {
        console.error('Error setting initial state:', error);
      }

      window.location.reload();
    }
  };

  // Show session options dialog
  const handleShowSessionOptions = () => {
    setShowSessionOptions(true);
  };

  // Helper function to calculate lecturer workload with day-type distribution analysis
  const calculateLecturerWorkload = (lecturerId: string, updatedSessions?: Session[]) => {
    const currentSessions = updatedSessions || sessions[currentSemester];
    const lecturerSessions = currentSessions.filter(s => s.lecturerId === lecturerId);

    let totalHours = 0;
    let regularDayHours = 0;
    let longDayHours = 0;
    let regularDaySessions = 0;
    let longDaySessions = 0;
    const dayTypes = new Set<string>();

    lecturerSessions.forEach(session => {
      // Calculate session duration based on day type
      const isLongDay = session.day === 'Mon' || session.day === 'Wed';
      const duration = isLongDay
        ? (session.endPeriod - session.startPeriod + 1) * 1.5  // Long days
        : (session.endPeriod - session.startPeriod + 1);       // Regular days

      totalHours += duration;

      if (isLongDay) {
        longDayHours += duration;
        longDaySessions++;
        dayTypes.add('long');
      } else {
        regularDayHours += duration;
        regularDaySessions++;
        dayTypes.add('regular');
      }
    });

    return {
      sessionCount: lecturerSessions.length,
      totalHours,
      regularDayHours,
      longDayHours,
      regularDaySessions,
      longDaySessions,
      dayTypes,
      sessions: lecturerSessions
    };
  };

  // Helper function to check for schedule conflicts with updated sessions
  const hasScheduleConflict = (lecturerId: string, newSessions: Session[], updatedSessions?: Session[]) => {
    const lecturerWorkload = calculateLecturerWorkload(lecturerId, updatedSessions);

    for (const newSession of newSessions) {
      for (const existingSession of lecturerWorkload.sessions) {
        // Check if sessions overlap
        if (existingSession.day === newSession.day) {
          const existingStart = existingSession.startPeriod;
          const existingEnd = existingSession.endPeriod;
          const newStart = newSession.startPeriod;
          const newEnd = newSession.endPeriod;

          // Check for overlap
          if (!(newEnd < existingStart || newStart > existingEnd)) {
            return true; // Conflict found
          }
        }
      }
    }

    return false; // No conflicts
  };

  // Helper function to analyze day-type distribution for a section's sessions
  const analyzeSectionDayTypes = (sectionSessions: Session[]) => {
    let regularDaySessions = 0;
    let longDaySessions = 0;
    const days = new Set<string>();

    sectionSessions.forEach(session => {
      const isLongDay = session.day === 'Mon' || session.day === 'Wed';
      if (isLongDay) {
        longDaySessions++;
      } else {
        regularDaySessions++;
      }
      days.add(session.day);
    });

    return {
      regularDaySessions,
      longDaySessions,
      totalSessions: sectionSessions.length,
      days: Array.from(days),
      isRegularDayOnly: longDaySessions === 0,
      isLongDayOnly: regularDaySessions === 0,
      isMixed: regularDaySessions > 0 && longDaySessions > 0
    };
  };

  // Helper function to calculate day-type distribution balance score
  const calculateDayTypeBalanceScore = (
    lecturerWorkload: {
      sessionCount: number;
      regularDaySessions: number;
      longDaySessions: number;
    },
    sectionAnalysis: {
      totalSessions: number;
      regularDaySessions: number;
      longDaySessions: number;
    }
  ) => {
    // If lecturer has no existing sessions, any assignment is balanced
    if (lecturerWorkload.sessionCount === 0) {
      return 50; // Neutral bonus for new lecturers
    }

    // Calculate current distribution ratio
    const currentRegularRatio = lecturerWorkload.regularDaySessions / lecturerWorkload.sessionCount;
    const currentLongRatio = lecturerWorkload.longDaySessions / lecturerWorkload.sessionCount;

    // Calculate what the ratio would be after adding this section
    const newTotalSessions = lecturerWorkload.sessionCount + sectionAnalysis.totalSessions;
    const newRegularSessions = lecturerWorkload.regularDaySessions + sectionAnalysis.regularDaySessions;
    const newLongSessions = lecturerWorkload.longDaySessions + sectionAnalysis.longDaySessions;

    const newRegularRatio = newRegularSessions / newTotalSessions;
    const newLongRatio = newLongSessions / newTotalSessions;

    // Ideal ratio is around 0.5 for each (balanced distribution)
    const idealRatio = 0.5;

    // Calculate how close the new distribution would be to ideal
    const regularBalance = 1 - Math.abs(newRegularRatio - idealRatio);
    const longBalance = 1 - Math.abs(newLongRatio - idealRatio);
    const overallBalance = (regularBalance + longBalance) / 2;

    // Convert to score (0-100 range)
    const balanceScore = overallBalance * 100;

    // Bonus for improving balance vs penalty for worsening it
    const currentBalance = (1 - Math.abs(currentRegularRatio - idealRatio) + 1 - Math.abs(currentLongRatio - idealRatio)) / 2;
    const balanceImprovement = overallBalance - currentBalance;

    return balanceScore + (balanceImprovement * 50); // Extra bonus/penalty for improvement/degradation
  };

  // Enhanced lecturer selection algorithm with flexible validation
  const selectBestLecturer = (
    eligibleLecturers: Lecturer[],
    sectionSessions: Session[],
    _course: Course,
    updatedSessions: Session[]
  ) => {
    if (eligibleLecturers.length === 0) return null;

    // Analyze the day-type distribution of the section being assigned
    const sectionAnalysis = analyzeSectionDayTypes(sectionSessions);

    // Score all eligible lecturers with flexible validation
    const lecturerScores = eligibleLecturers.map(lecturer => {
      const workload = calculateLecturerWorkload(lecturer.id, updatedSessions);
      const hasConflict = hasScheduleConflict(lecturer.id, sectionSessions, updatedSessions);

      let score = 100; // Base score
      const validationIssues = [];

      // Critical checks that must pass
      if (hasConflict) {
        score -= 1000; // Major penalty for conflicts
        validationIssues.push('schedule conflict');
      }

      // Check basic lecturer constraints (more flexible approach)
      try {
        // Check maximum semester load
        const maxSemesterLoad = lecturer.maxYearLoad ? lecturer.maxYearLoad / 2 : 12; // Default to 12 if not set
        const sessionHours = sectionSessions.reduce((total, session) => {
          const isLongDay = session.day === 'Mon' || session.day === 'Wed';
          return total + (isLongDay ? 1.5 : 1.0);
        }, 0);

        if (workload.totalHours + sessionHours > maxSemesterLoad) {
          score -= 200; // Penalty for exceeding load, but not disqualifying
          validationIssues.push('would exceed semester load');
        }

        // Check teaching days constraint
        const lecturerDays = new Set(workload.sessions.map(s => s.day));
        const newDays = new Set(sectionSessions.map(s => s.day));
        const combinedDays = new Set([...lecturerDays, ...newDays]);
        const maxDays = lecturer.maxTeachingDaysPerWeek || 5;

        if (combinedDays.size > maxDays) {
          score -= 150; // Penalty for exceeding days, but not disqualifying
          validationIssues.push('would exceed max teaching days');
        }

        // Check for reasonable gap constraints (flexible)
        const maxGap = lecturer.maxGapBetweenPeriods || 4; // More flexible default
        // Only apply gap penalty if it's extremely excessive
        if (maxGap < 2) {
          score -= 50; // Minor penalty for very restrictive gap requirements
          validationIssues.push('restrictive gap requirements');
        }

      } catch (error) {
        // Minor penalty for validation errors
        score -= 50;
      }

      // Prefer lecturers with lower workload (distribute load evenly)
      score -= workload.totalHours * 1; // Reduced penalty per hour
      score -= workload.sessionCount * 2; // Reduced penalty per session

      // Bonus for lecturers who can teach multiple courses (versatility)
      const courseCount = lecturer.coursesAbleToTeach ? lecturer.coursesAbleToTeach.length : 0;
      score += Math.min(courseCount * 5, 25); // Increased bonus for versatility

      // Bonus for underutilized lecturers
      if (lecturer.maxYearLoad) {
        const maxSemesterLoad = lecturer.maxYearLoad / 2;
        const loadPercentage = (workload.totalHours / maxSemesterLoad) * 100;
        if (loadPercentage < 30) {
          score += 50; // Significant bonus for very underutilized lecturers
        } else if (loadPercentage < 60) {
          score += 25; // Moderate bonus for underutilized lecturers
        } else if (loadPercentage > 90) {
          score -= 100; // Penalty for nearly overloaded lecturers
        }
      }

      // Preferred timing bonus
      if (lecturer.preferredTiming === 'Both') {
        score += 10; // Bonus for flexible timing
      }

      // Day-type distribution balance bonus/penalty (NEW - addresses the bias issue)
      const dayTypeBalanceScore = calculateDayTypeBalanceScore(workload, sectionAnalysis);
      score += dayTypeBalanceScore * 0.5; // Scale the balance score appropriately

      // Log day-type distribution for debugging
      const dayTypeInfo = {
        currentRegular: workload.regularDaySessions,
        currentLong: workload.longDaySessions,
        sectionRegular: sectionAnalysis.regularDaySessions,
        sectionLong: sectionAnalysis.longDaySessions,
        balanceScore: dayTypeBalanceScore
      };

      return {
        lecturer,
        score,
        workload,
        hasConflict,
        validationIssues,
        dayTypeInfo
      };
    });

    // Sort by score (highest first)
    lecturerScores.sort((a, b) => b.score - a.score);

    // Return the best lecturer (even if score is negative, unless there's a critical conflict)
    const bestLecturer = lecturerScores[0];
    if (bestLecturer.hasConflict) {
      const nonConflictLecturers = lecturerScores.filter(ls => !ls.hasConflict);
      if (nonConflictLecturers.length > 0) {
        return nonConflictLecturers[0].lecturer;
      }
      return null;
    }

    return bestLecturer.lecturer;
  };

  // Function to assign lecturers to already scheduled sessions
  const assignLecturersToScheduledSessions = async (scheduledSectionIds: string[]) => {

    const results = {
      successful: [] as { sectionId: string, courseCode: string, lecturerName: string, sessionCount: number }[],
      failed: [] as { sectionId: string, courseCode: string, reason: string }[],
      conflicts: [] as { sectionId: string, courseCode: string, lecturerName: string, reason: string }[]
    };

    try {
      // Get all sessions for the current semester and create a working copy
      const updatedSessions = [...sessions[currentSemester]];

      // Randomize section processing order to prevent systematic bias
      const shuffledSectionIds = [...scheduledSectionIds].sort(() => Math.random() - 0.5);

      // Process each scheduled section individually
      for (const sectionId of shuffledSectionIds) {
        const section = sections[currentSemester].find(s => s.id === sectionId);
        if (!section) {
          results.failed.push({
            sectionId,
            courseCode: 'Unknown',
            reason: 'Section not found'
          });
          continue;
        }

        const course = courses[currentSemester].find(c => c.id === section.courseId);
        if (!course) {
          results.failed.push({
            sectionId,
            courseCode: 'Unknown',
            reason: 'Course not found'
          });
          continue;
        }

        // Find sessions for this section that don't already have a lecturer
        const sectionSessions = updatedSessions.filter(s =>
          s.sectionId === sectionId && (!s.lecturerId || s.lecturerId === '')
        );

        if (sectionSessions.length === 0) {
          continue;
        }

        // Find eligible lecturers for this course with more flexible matching
        let eligibleLecturers = lecturers.filter(lecturer =>
          lecturer.coursesAbleToTeach &&
          lecturer.coursesAbleToTeach.includes(course.courseCode)
        );

        // If no exact matches, try partial matching for similar course codes
        if (eligibleLecturers.length === 0) {
          const coursePrefix = course.courseCode.replace(/\d+$/, ''); // Remove numbers from end
          eligibleLecturers = lecturers.filter(lecturer =>
            lecturer.coursesAbleToTeach &&
            lecturer.coursesAbleToTeach.some(courseCode =>
              courseCode.startsWith(coursePrefix) || courseCode.includes(coursePrefix)
            )
          );


        }

        // If still no matches, check for lecturers who can teach "All" courses or have broad capabilities
        if (eligibleLecturers.length === 0) {
          eligibleLecturers = lecturers.filter(lecturer =>
            lecturer.coursesAbleToTeach &&
            (lecturer.coursesAbleToTeach.includes('ALL') ||
             lecturer.coursesAbleToTeach.includes('All') ||
             lecturer.coursesAbleToTeach.length > 10) // Lecturers with many courses might be flexible
          );


        }

        if (eligibleLecturers.length === 0) {
          results.failed.push({
            sectionId,
            courseCode: course.courseCode,
            reason: 'No eligible lecturers found (tried exact match, similar courses, and flexible lecturers)'
          });
          continue;
        }

        // Select the best lecturer using enhanced algorithm with rule validation
        let selectedLecturer = selectBestLecturer(
          eligibleLecturers,
          sectionSessions,
          course,
          updatedSessions
        );

        if (!selectedLecturer) {
          // Fallback: try with the most flexible lecturer (ignore some constraints)

          // Sort eligible lecturers by flexibility (most courses they can teach)
          const fallbackLecturers = eligibleLecturers.sort((a, b) => {
            const aFlexibility = (a.coursesAbleToTeach?.length || 0) + (a.maxYearLoad || 0) / 10;
            const bFlexibility = (b.coursesAbleToTeach?.length || 0) + (b.maxYearLoad || 0) / 10;
            return bFlexibility - aFlexibility;
          });

          // Try the most flexible lecturer, ignoring some constraints
          for (const fallbackLecturer of fallbackLecturers.slice(0, 3)) { // Try top 3 most flexible
            const hasBasicConflict = hasScheduleConflict(fallbackLecturer.id, sectionSessions, updatedSessions);
            if (!hasBasicConflict) {
              selectedLecturer = fallbackLecturer;
              break;
            }
          }

          if (!selectedLecturer) {
            results.failed.push({
              sectionId,
              courseCode: course.courseCode,
              reason: 'No valid lecturer found (even with fallback approach)'
            });
            continue;
          }
        }

        // Update all sessions for this section with the selected lecturer
        let sessionUpdateCount = 0;
        const sessionsToUpdate = [];

        for (const session of sectionSessions) {
          try {
            const updatedSession = {
              ...session,
              lecturerId: selectedLecturer.id
            };

            // Update the session in our working copy
            const sessionIndex = updatedSessions.findIndex(s => s.id === session.id);
            if (sessionIndex >= 0) {
              updatedSessions[sessionIndex] = updatedSession;
              sessionsToUpdate.push(updatedSession);
              sessionUpdateCount++;
            }

          } catch (error) {
            console.error(`Failed to update session ${session.id}:`, error);
          }
        }

        // Apply the updates to the actual session store
        for (const sessionToUpdate of sessionsToUpdate) {
          updateSession(sessionToUpdate, currentSemester);
        }

        if (sessionUpdateCount > 0) {
          results.successful.push({
            sectionId,
            courseCode: course.courseCode,
            lecturerName: `${selectedLecturer.firstName} ${selectedLecturer.lastName}`,
            sessionCount: sessionUpdateCount
          });
        }
      }

      // Provide comprehensive user feedback
      const totalAssigned = results.successful.reduce((sum, r) => sum + r.sessionCount, 0);
      const totalFailed = results.failed.length + results.conflicts.length;

      let message = `Post-scheduling lecturer assignment completed!\n\n`;

      if (results.successful.length > 0) {
        message += `✅ Successfully assigned:\n`;
        results.successful.forEach(r => {
          message += `  • ${r.courseCode}: ${r.lecturerName} (${r.sessionCount} sessions)\n`;
        });
        message += `\nTotal: ${totalAssigned} sessions assigned to ${results.successful.length} sections\n`;
      }

      if (results.failed.length > 0) {
        message += `\n❌ Failed assignments:\n`;
        results.failed.forEach(r => {
          message += `  • ${r.courseCode}: ${r.reason}\n`;
        });
      }

      if (results.conflicts.length > 0) {
        message += `\n⚠️ Schedule conflicts:\n`;
        results.conflicts.forEach(r => {
          message += `  • ${r.courseCode}: ${r.lecturerName} - ${r.reason}\n`;
        });
      }

      if (totalFailed > 0) {
        message += `\n💡 Tip: Check lecturer course assignments and schedules to resolve issues.`;
      }

      alert(message);

    } catch (error) {
      console.error('Error during post-scheduling lecturer assignment:', error);
      alert(`Error during lecturer assignment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle auto-scheduling with the selected session option
  const handleRunAutoSchedulingAll = () => {
    // If options dialog is not shown yet, show it first
    if (!showSessionOptions) {
      setShowSessionOptions(true);
      return;
    }

    // Get all sections
    const allSections = sections[currentSemester];

    // Filter sections based on the selected session handling option
    let sectionsToSchedule: typeof allSections = [];

    if (sessionOption === 'keep-all') {
      // If keeping all sessions, only schedule sections that are not fully scheduled
      sectionsToSchedule = allSections.filter(section => {
        return section.scheduledHours < section.totalHours;
      });
    } else if (sessionOption === 'delete-all') {
      // If deleting all sessions, schedule all sections including fully scheduled ones
      sectionsToSchedule = [...allSections];
    } else if (sessionOption === 'delete-auto') {
      // If deleting only auto-generated sessions, include:
      // 1. Sections that are not fully scheduled
      // 2. Sections that are fully scheduled but only by auto-generated sessions

      // First, get all sessions for the current semester
      const currentSessions = sessions[currentSemester];

      // Create a map to track which sections have manual sessions
      const sectionsWithManualSessions = new Map();

      // Identify sections with manually created sessions
      currentSessions.forEach(session => {
        if (!session.isAutoGenerated) {
          // This is a manually created session
          const sectionId = session.sectionId;

          // Calculate hours for this manual session
          const hoursForSession = session.day === 'Mon' || session.day === 'Wed'
            ? (session.endPeriod - session.startPeriod + 1) * 1.5
            : (session.endPeriod - session.startPeriod + 1);

          // Add or update the hours for this section
          if (sectionsWithManualSessions.has(sectionId)) {
            sectionsWithManualSessions.set(
              sectionId,
              sectionsWithManualSessions.get(sectionId) + hoursForSession
            );
          } else {
            sectionsWithManualSessions.set(sectionId, hoursForSession);
          }
        }
      });

      // Filter sections based on manual scheduling status
      sectionsToSchedule = allSections.filter(section => {
        // Get manual hours for this section (if any)
        const manualHours = sectionsWithManualSessions.get(section.id) || 0;

        // Include if:
        // 1. Not fully scheduled, OR
        // 2. Fully scheduled but only by auto-generated sessions (manual hours < total hours)
        return section.scheduledHours < section.totalHours || manualHours < section.totalHours;
      });
    }

    // Handle existing sessions based on the selected option
    if (sessionOption === 'delete-all') {
      // Delete all existing sessions
      sessions[currentSemester].forEach(session => {
        deleteSession(session.id, currentSemester);
      });

      // Reset existingSessions to empty array since we deleted all
      const existingSessions: AutoSession[] = [];

      // Prepare sections for auto-scheduling
      const sectionsForScheduling = sectionsToSchedule.map(section => {
        const course = courses[currentSemester].find(c => c.id === section.courseId);
        const academicLevel = course ? getAcademicLevel(course.courseCode) : 'unknown';

        return {
          id: section.id,
          courseCode: course?.courseCode || '',
          courseType: course?.courseType || 'Theory',
          contactHours: course?.contactHours || 3,
          gender: section.gender,
          lecturerId: section.lecturerId || '',
          academicLevel
        };
      });

      // Clear previous CPS report since we're starting new auto-scheduling
      setLastCpsReport(null);

      // Show loading state
      setIsLoading(true);
      const lecturerMessage = lecturerAssignmentOption === 'no-assignment'
        ? ' (without lecturer assignment)'
        : lecturerAssignmentOption === 'simultaneous'
        ? ' (with simultaneous lecturer assignment)'
        : ' (with post-scheduling lecturer assignment)';
      setLoadingMessage(`Deleting existing sessions and auto-scheduling all sections${lecturerMessage}...`);

      // Use setTimeout to allow the UI to update before starting the heavy computation
      setTimeout(async () => {
        try {
          // Determine lecturer assignment strategy
          const lecturerData = lecturerAssignmentOption === 'no-assignment' ? [] : lecturers;
          const enableLecturerAssignment = lecturerAssignmentOption === 'simultaneous';

          // Temporarily set the lecturer assignment flag based on the selected option
          const originalAssignLecturers = assignLecturersInAutoScheduling;
          setAssignLecturersInAutoScheduling(enableLecturerAssignment);

          // Run auto-scheduling with empty existing sessions
          const result = autoScheduleAllSections(sectionsForScheduling, existingSessions, lecturerData, retryConfiguration);

          // Restore original lecturer assignment setting
          setAssignLecturersInAutoScheduling(originalAssignLecturers);

          // Add all generated sessions to the app first
          addNewSessions(result);

          // Handle post-scheduling lecturer assignment if selected
          if (lecturerAssignmentOption === 'post-scheduling') {
            await assignLecturersToScheduledSessions(result.scheduledSections);
          }
        } catch (error) {
          setIsLoading(false);
          alert(`Error during auto-scheduling: ${error}`);
        }
      }, 100);
    } else if (sessionOption === 'delete-auto') {
      // Delete only auto-generated sessions
      const autoGeneratedSessions = sessions[currentSemester].filter(session => session.isAutoGenerated);
      autoGeneratedSessions.forEach(session => {
        deleteSession(session.id, currentSemester);
      });

      // Get remaining sessions for validation (manually created ones)
      const existingSessions = sessions[currentSemester].map(session => {
        // Find the section for this session
        const section = sections[currentSemester].find(s => s.id === session.sectionId);

        // Find the course for this section
        const course = section
          ? courses[currentSemester].find(c => c.id === section.courseId)
          : null;

        // Get academic level from course code
        const academicLevel = course
          ? getAcademicLevel(course.courseCode)
          : '';

        return {
          id: session.id,
          sectionId: session.sectionId,
          courseCode: course?.courseCode || '',
          courseType: course?.courseType || 'Theory',
          academicLevel,
          gender: section?.gender || 'M',
          lecturerId: session.lecturerId || '',
          day: session.day,
          period: session.startPeriod,
          isAutoGenerated: session.isAutoGenerated || false
        };
      });

      // Prepare sections for auto-scheduling
      const sectionsForScheduling = sectionsToSchedule.map(section => {
        const course = courses[currentSemester].find(c => c.id === section.courseId);
        const academicLevel = course ? getAcademicLevel(course.courseCode) : 'unknown';

        return {
          id: section.id,
          courseCode: course?.courseCode || '',
          courseType: course?.courseType || 'Theory',
          contactHours: course?.contactHours || 3,
          gender: section.gender,
          lecturerId: section.lecturerId || '',
          academicLevel
        };
      });

      // Clear previous CPS report since we're starting new auto-scheduling
      setLastCpsReport(null);

      // Show loading state
      setIsLoading(true);
      const lecturerMessage = lecturerAssignmentOption === 'no-assignment'
        ? ' (without lecturer assignment)'
        : lecturerAssignmentOption === 'simultaneous'
        ? ' (with simultaneous lecturer assignment)'
        : ' (with post-scheduling lecturer assignment)';
      setLoadingMessage(`Deleting auto-generated sessions and auto-scheduling all sections${lecturerMessage}...`);

      // Use setTimeout to allow the UI to update before starting the heavy computation
      setTimeout(async () => {
        try {
          // Determine lecturer assignment strategy
          const lecturerData = lecturerAssignmentOption === 'no-assignment' ? [] : lecturers;
          const enableLecturerAssignment = lecturerAssignmentOption === 'simultaneous';

          // Temporarily set the lecturer assignment flag based on the selected option
          const originalAssignLecturers = assignLecturersInAutoScheduling;
          setAssignLecturersInAutoScheduling(enableLecturerAssignment);

          // Run auto-scheduling with remaining manual sessions
          const result = autoScheduleAllSections(sectionsForScheduling, existingSessions, lecturerData, retryConfiguration);

          // Restore original lecturer assignment setting
          setAssignLecturersInAutoScheduling(originalAssignLecturers);

          // Add all generated sessions to the app first
          addNewSessions(result);

          // Handle post-scheduling lecturer assignment if selected
          if (lecturerAssignmentOption === 'post-scheduling') {
            await assignLecturersToScheduledSessions(result.scheduledSections);
          }
        } catch (error) {
          setIsLoading(false);
          alert(`Error during auto-scheduling: ${error}`);
        }
      }, 100);
    } else {
      // Keep all existing sessions (default)
      // Get all existing sessions for validation
      const existingSessions = sessions[currentSemester].map(session => {
        // Find the section for this session
        const section = sections[currentSemester].find(s => s.id === session.sectionId);

        // Find the course for this section
        const course = section
          ? courses[currentSemester].find(c => c.id === section.courseId)
          : null;

        // Get academic level from course code
        const academicLevel = course
          ? getAcademicLevel(course.courseCode)
          : '';

        return {
          id: session.id,
          sectionId: session.sectionId,
          courseCode: course?.courseCode || '',
          courseType: course?.courseType || 'Theory',
          academicLevel,
          gender: section?.gender || 'M',
          lecturerId: session.lecturerId || '',
          day: session.day,
          period: session.startPeriod,
          isAutoGenerated: session.isAutoGenerated || false
        };
      });

      // Prepare sections for auto-scheduling
      const sectionsForScheduling = sectionsToSchedule.map(section => {
        const course = courses[currentSemester].find(c => c.id === section.courseId);
        const academicLevel = course ? getAcademicLevel(course.courseCode) : 'unknown';

        return {
          id: section.id,
          courseCode: course?.courseCode || '',
          courseType: course?.courseType || 'Theory',
          contactHours: course?.contactHours || 3,
          gender: section.gender,
          lecturerId: section.lecturerId || '',
          academicLevel
        };
      });

      // Clear previous CPS report since we're starting new auto-scheduling
      setLastCpsReport(null);

      // Show loading state
      setIsLoading(true);
      const lecturerMessage = lecturerAssignmentOption === 'no-assignment'
        ? ' (without lecturer assignment)'
        : lecturerAssignmentOption === 'simultaneous'
        ? ' (with simultaneous lecturer assignment)'
        : ' (with post-scheduling lecturer assignment)';
      setLoadingMessage(`Auto-scheduling all sections${lecturerMessage}...`);

      // Use setTimeout to allow the UI to update before starting the heavy computation
      setTimeout(async () => {
        try {
          // Determine lecturer assignment strategy
          const lecturerData = lecturerAssignmentOption === 'no-assignment' ? [] : lecturers;
          const enableLecturerAssignment = lecturerAssignmentOption === 'simultaneous';

          // Temporarily set the lecturer assignment flag based on the selected option
          const originalAssignLecturers = assignLecturersInAutoScheduling;
          setAssignLecturersInAutoScheduling(enableLecturerAssignment);

          // Run auto-scheduling
          const result = autoScheduleAllSections(sectionsForScheduling, existingSessions, lecturerData, retryConfiguration);

          // Restore original lecturer assignment setting
          setAssignLecturersInAutoScheduling(originalAssignLecturers);

          // Add all generated sessions to the app first
          addNewSessions(result);

          // Handle post-scheduling lecturer assignment if selected
          if (lecturerAssignmentOption === 'post-scheduling') {
            await assignLecturersToScheduledSessions(result.scheduledSections);
          }
        } catch (error) {
          setIsLoading(false);
          alert(`Error during auto-scheduling: ${error}`);
        }
      }, 100);
    }

    // Reset the dialog state
    setShowSessionOptions(false);
  };

  // Helper function to add new sessions and handle UI updates
  const addNewSessions = (result: {
    appSessions: Array<{
      id: string;
      sectionId: string;
      lecturerId: string;
      lecturerIds?: string[];
      day: string;
      startPeriod: number;
      endPeriod: number;
      viewType: 'week' | 'regular' | 'long';
      timeOfDay?: 'morning' | 'evening';
      isAutoGenerated?: boolean;
    }>,
    scheduledSections: string[],
    unscheduledSections: string[],
    cpsReport?: {
      enabled: boolean;
      triggered: boolean;
      initialViolations: number;
      finalViolations: number;
      improvementAchieved: boolean;
      refinementStrategies: string[];
      processingTime: number;
      constraintViolationDetails: {
        timeslotPercentage: number;
        lecturerOverload: number;
        patternViolations: number;
        conflictViolations: number;
      };
    }
  }) => {
    // Add all generated sessions to the app first
    result.appSessions.forEach((session) => {
      addSession({
        id: session.id, // Pass the ID from the auto-generated session
        sectionId: session.sectionId,
        lecturerId: session.lecturerId,
        lecturerIds: session.lecturerIds,
        day: session.day,
        startPeriod: session.startPeriod,
        endPeriod: session.endPeriod,
        viewType: session.viewType,
        timeOfDay: session.timeOfDay,
        isAutoGenerated: true
      }, currentSemester);
    });

    // Calculate actual scheduled vs unscheduled sections based on current timetable state
    // This is more accurate than using the auto-scheduling result because it accounts for:
    // 1. Sections that were already scheduled before auto-scheduling
    // 2. Sections scheduled by CPS system
    // 3. Sections with manual sessions
    const allSections = sections[currentSemester];
    const currentSessions = sessions[currentSemester];

    // Get sections that have sessions (are scheduled)
    const sectionsWithSessions = new Set(currentSessions.map(session => session.sectionId));

    // Calculate scheduled and unscheduled sections based on actual timetable state
    const actualScheduledSections: string[] = [];
    const actualUnscheduledSections: string[] = [];

    allSections.forEach(section => {
      const course = courses[currentSemester].find(c => c.id === section.courseId);
      if (!course) return;

      // Skip lab sections as they are typically not auto-scheduled
      if (course.courseType === 'Lab') return;

      // Check if section has any sessions
      if (sectionsWithSessions.has(section.id)) {
        // Calculate total scheduled hours for this section
        let totalScheduledHours = 0;
        const sectionSessions = currentSessions.filter(s => s.sectionId === section.id);

        sectionSessions.forEach(session => {
          const isLongDay = session.day === 'Mon' || session.day === 'Wed';
          const sessionHours = (session.endPeriod - session.startPeriod + 1) * (isLongDay ? 1.5 : 1.0);
          totalScheduledHours += sessionHours;
        });

        // Consider section scheduled if it has at least some sessions
        // We use a small threshold to account for floating point precision
        if (totalScheduledHours >= 0.1) {
          actualScheduledSections.push(section.id);
        } else {
          actualUnscheduledSections.push(section.id);
        }
      } else {
        // No sessions for this section
        actualUnscheduledSections.push(section.id);
      }
    });

    // Use the actual calculated sections instead of auto-scheduling result
    const scheduledSections = actualScheduledSections;
    const unscheduledSections = actualUnscheduledSections;

    // Debug logging to verify the fix
    console.log(`📊 Rule Analysis Calculation Fix:`);
    console.log(`   Total sections (excluding labs): ${scheduledSections.length + unscheduledSections.length}`);
    console.log(`   ✅ Scheduled sections: ${scheduledSections.length}`);
    console.log(`   🔴 Unscheduled sections: ${unscheduledSections.length}`);
    console.log(`   📈 Success rate: ${((scheduledSections.length / (scheduledSections.length + unscheduledSections.length)) * 100).toFixed(1)}%`);

    // Collect rule violations and compliance data
    const ruleViolations: RuleViolation[] = [];
    const ruleCompliance: Record<string, { compliantCount: number, violationCount: number }> = {};

    // Initialize rule compliance counters for all rules
    rules.forEach(rule => {
      ruleCompliance[rule.id] = { compliantCount: 0, violationCount: 0 };
    });

    // Ensure the block-break-timeslots rule is always initialized, even if it's not in the rules list
    if (!ruleCompliance['block-break-timeslots']) {
      ruleCompliance['block-break-timeslots'] = { compliantCount: 0, violationCount: 0 };
      console.log('Added block-break-timeslots rule to compliance tracking');
    }

    // Log user-defined breaks for debugging
    console.log(`User-defined breaks for analysis: ${userDefinedBreaks.join(', ') || 'None'}`);
    if (userDefinedBreaks.length > 0) {
      console.log('User-defined breaks are present, should be tracked in rule violations');
    }

    // For each unscheduled section, add specific rule violations based on section properties
    unscheduledSections.forEach((sectionId: string) => {
      const section = sections[currentSemester].find(s => s.id === sectionId);
      if (section) {
        const course = courses[currentSemester].find(c => c.id === section.courseId);
        if (course) {
          const academicLevel = course.academicLevel || getAcademicLevel(course.courseCode);
          const isPostgraduate = academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma' ||
                                course.courseCode.match(/^[A-Z]+[5-9]\d+/);

          // Check if postgraduate pattern rule is enabled
          const postGradRuleEnabled = rules.find(r =>
            r.id === 'postgrad-pattern' ||
            (r.name.toLowerCase().includes('postgraduate') && r.name.toLowerCase().includes('pattern')) ||
            (r.name.toLowerCase().includes('evening') && r.name.toLowerCase().includes('postgraduate'))
          )?.enabled || false;

          // Add specific violations based on section properties
          if (isPostgraduate && course.courseType === 'Theory' && course.contactHours === 3) {
            if (postGradRuleEnabled) {
              // If the rule is enabled, it's likely a constraint violation
              ruleViolations.push({
                ruleId: 'postgrad-pattern',
                message: 'Postgraduate course must be scheduled in specific periods (10-12 on regular days or 9-10 on long days)',
                sectionId: section.id,
                courseCode: course.courseCode
              });

              // Update rule compliance counter
              if (ruleCompliance['postgrad-pattern']) {
                ruleCompliance['postgrad-pattern'].violationCount += 1;
              }
            } else {
              // If the rule is disabled, it might be another constraint
              ruleViolations.push({
                ruleId: 'block-break-timeslots',
                message: 'Could not find available timeslots that satisfy system constraints',
                sectionId: section.id,
                courseCode: course.courseCode
              });

              // Update rule compliance counter
              if (ruleCompliance['block-break-timeslots']) {
                ruleCompliance['block-break-timeslots'].violationCount += 1;
              }
            }
          } else if (course.contactHours >= 4) {
            // For high contact hour courses
            ruleViolations.push({
              ruleId: 'max-sessions-per-timeslot',
              message: 'High contact hours course requires specific scheduling patterns',
              sectionId: section.id,
              courseCode: course.courseCode
            });

            // Update rule compliance counter
            if (ruleCompliance['max-sessions-per-timeslot']) {
              ruleCompliance['max-sessions-per-timeslot'].violationCount += 1;
            }
          } else {
            // Check if this section might be affected by user-defined breaks
            // This is a critical check to ensure user-defined breaks are properly tracked
            if (userDefinedBreaks.length > 0) {
              console.log(`Section ${section.id} (${course.courseCode}) could not be scheduled, checking if user-defined breaks are involved`);
              console.log(`User-defined breaks: ${userDefinedBreaks.join(', ')}`);

              // Attribute this to the block-break-timeslots rule
              ruleViolations.push({
                ruleId: 'block-break-timeslots',
                message: 'Could not find available timeslots due to user-defined breaks or system constraints',
                sectionId: section.id,
                courseCode: course.courseCode
              });

              // Update rule compliance counter for block-break-timeslots
              if (ruleCompliance['block-break-timeslots']) {
                ruleCompliance['block-break-timeslots'].violationCount += 1;
              }
            } else {
              // Default case - likely a timeslot constraint
              ruleViolations.push({
                ruleId: 'max-sessions-per-timeslot',
                message: 'Could not find a suitable timeslot that satisfies all constraints',
                sectionId: section.id,
                courseCode: course.courseCode
              });

              // Update rule compliance counter
              if (ruleCompliance['max-sessions-per-timeslot']) {
                ruleCompliance['max-sessions-per-timeslot'].violationCount += 1;
              }
            }
          }
        }
      }
    });

    // For each scheduled section, check if it violates any rules
    // Specifically, check if any sessions are scheduled in user-defined breaks

    // Get all sessions for the current semester
    const allSessions = sessions[currentSemester];

    // Check each scheduled section for violations
    scheduledSections.forEach((sectionId: string) => {
      // Get all sessions for this section
      const sectionSessions = allSessions.filter(s => s.sectionId === sectionId);
      let hasViolation = false;

      // Check each session to see if it's in a user-defined break
      sectionSessions.forEach(session => {
        // Check each period in the session
        for (let period = session.startPeriod; period <= session.endPeriod; period++) {
          // Use our centralized function to check if this is a user-defined break
          // We use ignoreRuleStatus=true to check all breaks regardless of rule status
          const userBreakCheck = isUserDefinedBreak(session.day, period, true);

          if (userBreakCheck.blocked) {
            // Get the short day format for display
            const shortDay = session.day.length > 3 ? session.day.substring(0, 3) : session.day;

            console.log(`VIOLATION: Session ${session.id} for section ${sectionId} is scheduled in user-defined break at ${shortDay}-${period}`);

            // Find the section and course
            const section = sections[currentSemester].find(s => s.id === sectionId);
            const course = section ? courses[currentSemester].find(c => c.id === section.courseId) : null;
            const courseCode = course ? course.courseCode : 'Unknown';

            // Add a rule violation
            ruleViolations.push({
              ruleId: 'block-break-timeslots',
              message: `Session scheduled in user-defined break at ${shortDay} period ${period}`,
              sectionId: sectionId,
              courseCode: courseCode
            });

            // Update rule compliance counter
            if (ruleCompliance['block-break-timeslots']) {
              ruleCompliance['block-break-timeslots'].violationCount += 1;
            }

            hasViolation = true;
            break;
          }
        }

        if (hasViolation) {
          return; // Skip checking other sessions for this section
        }
      });

      // If no violations were found, mark rules as compliant
      if (!hasViolation) {
        // Mark all enabled rules as compliant for this section
        rules.filter(r => r.enabled).forEach(rule => {
          if (ruleCompliance[rule.id]) {
            ruleCompliance[rule.id].compliantCount += 1;
          }
        });
      }
    });

    // Collect detailed information about unscheduled sections
    const unscheduledSectionDetails: SectionInfo[] = [];

    unscheduledSections.forEach((sectionId: string) => {
      const section = sections[currentSemester].find(s => s.id === sectionId);
      if (section) {
        const course = courses[currentSemester].find(c => c.id === section.courseId);
        if (course) {
          unscheduledSectionDetails.push({
            id: section.id,
            sectionNumber: section.sectionNumber,
            courseId: section.courseId,
            courseCode: course.courseCode,
            courseName: course.courseName,
            contactHours: course.contactHours,
            scheduledHours: section.scheduledHours,
            totalHours: section.totalHours,
            gender: section.gender,
            academicLevel: course.academicLevel || getAcademicLevel(course.courseCode)
          });
        }
      }
    });

    // Collect detailed information about excluded sections
    const excludedSectionDetails: ExcludedSectionInfo[] = [];

    // Get all sections that were excluded from scheduling
    const excludedSections = allSections.filter(section => {
      // Check if this section was included in either scheduled or unscheduled sections
      const wasIncluded = [...scheduledSections, ...unscheduledSections].includes(section.id);
      return !wasIncluded;
    });

    excludedSections.forEach((section) => {
      const course = courses[currentSemester].find(c => c.id === section.courseId);
      if (course) {
        // Determine the reason for exclusion
        let reason = "Unknown reason";

        if (section.scheduledHours >= section.totalHours) {
          // Different reasons based on the session option selected
          if (sessionOption === 'keep-all') {
            reason = "Already fully scheduled (excluded when keeping existing sessions)";
          } else if (sessionOption === 'delete-auto') {
            // Check if this section is fully scheduled by manual sessions
            const currentSessions = sessions[currentSemester];
            let manualHours = 0;

            currentSessions.forEach(session => {
              if (!session.isAutoGenerated && session.sectionId === section.id) {
                // Calculate hours for this manual session
                const hoursForSession = session.day === 'Mon' || session.day === 'Wed'
                  ? (session.endPeriod - session.startPeriod + 1) * 1.5
                  : (session.endPeriod - session.startPeriod + 1);

                manualHours += hoursForSession;
              }
            });

            if (manualHours >= section.totalHours) {
              reason = "Fully scheduled by manually created sessions (excluded when keeping manual sessions)";
            } else {
              reason = "Excluded for unknown reasons";
            }
          } else {
            reason = "Already fully scheduled";
          }
        } else if (course.courseType === "Lab") {
          reason = "Lab sections are not auto-scheduled";
        }

        excludedSectionDetails.push({
          id: section.id,
          sectionNumber: section.sectionNumber,
          courseId: section.courseId,
          courseCode: course.courseCode,
          courseName: course.courseName,
          contactHours: course.contactHours,
          scheduledHours: section.scheduledHours,
          totalHours: section.totalHours,
          gender: section.gender,
          academicLevel: course.academicLevel || getAcademicLevel(course.courseCode),
          reason: reason
        });
      }
    });

    // Get the total number of sections in the app for the current semester
    const allSectionsCount = sections[currentSemester].length;

    // Create the analysis result
    const analysisData: RuleAnalysisResult = {
      scheduledSections: scheduledSections.length,
      unscheduledSections: unscheduledSections.length,
      totalSections: scheduledSections.length + unscheduledSections.length,
      allSectionsCount: allSectionsCount,
      ruleViolations,
      unscheduledSectionDetails,
      excludedSectionDetails,
      ruleCompliance,
      cpsReport: result.cpsReport ? {
        ...result.cpsReport,
        lecturerAssignmentStats: (result.cpsReport as CPSRefinementReport & { lecturerAssignmentStats?: unknown }).lecturerAssignmentStats || {
          sectionsScheduled: 0,
          sectionsWithLecturers: 0,
          sectionsWithoutLecturers: 0,
          lecturerAssignmentRate: 0
        },
        optimizationResults: (result.cpsReport as CPSRefinementReport & { optimizationResults?: unknown }).optimizationResults
      } : undefined // Add CPS report to analysis data with proper structure
    };

    // Display CPS report if available
    if (result.cpsReport && result.cpsReport.enabled) {
      console.log('🔧 CPS Refinement Report:', result.cpsReport);

      if (result.cpsReport.triggered) {
        const improvementText = result.cpsReport.improvementAchieved ?
          `✅ Improved (${result.cpsReport.initialViolations} → ${result.cpsReport.finalViolations} violations)` :
          `⚠️ No improvement (${result.cpsReport.initialViolations} violations remain)`;

        const strategiesText = result.cpsReport.refinementStrategies.length > 0 ?
          `Applied strategies: ${result.cpsReport.refinementStrategies.join(', ')}` :
          'No refinement strategies were applied';

        console.log(`🔧 CPS Results: ${improvementText}`);
        console.log(`🔧 Processing time: ${(result.cpsReport.processingTime / 1000).toFixed(2)}s`);
        console.log(`🔧 ${strategiesText}`);
      } else {
        console.log('🔧 CPS refinement was enabled but not triggered (success rate was sufficient)');
      }
    }

    // Save CPS report to persistent state if available
    if (result.cpsReport) {
      // Ensure the CPS report has the new lecturerAssignmentStats and optimizationResults fields
      const cpsReportWithStats: CPSRefinementReport = {
        ...result.cpsReport,
        lecturerAssignmentStats: (result.cpsReport as CPSRefinementReport & { lecturerAssignmentStats?: unknown }).lecturerAssignmentStats || {
          sectionsScheduled: 0,
          sectionsWithLecturers: 0,
          sectionsWithoutLecturers: 0,
          lecturerAssignmentRate: 0
        },
        optimizationResults: (result.cpsReport as CPSRefinementReport & { optimizationResults?: unknown }).optimizationResults || {
          strategiesApplied: [],
          lecturerSwaps: 0,
          sessionRepositions: 0,
          intelligentReschedules: 0,
          constraintRelaxations: 0,
          overallOptimizationScore: 0
        }
      };
      setLastCpsReport(cpsReportWithStats);
    }

    // Update the analysis result state
    setAnalysisResult(analysisData);

    // Show results
    setIsLoading(false);

    // If sessions were created successfully, focus on the first one
    if (result.appSessions.length > 0) {
      // Switch to all day view to ensure all sessions are visible
      // This is done by dispatching a custom event that will be caught by TimetableCanvas
      window.dispatchEvent(new CustomEvent('setActiveTab', { detail: { tab: 2 } }));

      // Focus on the first session
      const firstSession = result.appSessions[0];
      window.dispatchEvent(new CustomEvent('focusOnSession', {
        detail: {
          day: firstSession.day,
          period: firstSession.startPeriod,
          sessions: result.appSessions.map(s => s.id)
        }
      }));
    }

    // Switch to the analysis tab
    if (lecturerAssignmentOption === 'simultaneous' || lecturerAssignmentOption === 'post-scheduling') {
      // If lecturer assignment is enabled, switch to lecturer analysis tab
      setActiveTab('lecturer-analysis');
    } else {
      // Otherwise, switch to regular analysis tab
      setActiveTab('analysis');
    }

    // Note: We don't close the modal here so the user can see the analysis
  };

  if (!isOpen) return null;

  // Render minimized version if minimized
  if (isMinimized) {
    return (
      <div
        className="fixed bottom-0 right-0 z-[1000] m-4"
        role="dialog"
        aria-modal="true"
        aria-labelledby="minimized-modal-title"
      >
        <div className="bg-white rounded-lg shadow-lg p-4 flex items-center">
          <h3 id="minimized-modal-title" className="text-lg font-semibold mr-4">Auto-Scheduling Rules</h3>
          <Tooltip title="Restore dialog to full view">
            <button
              onClick={() => setIsMinimized(false)}
              className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
              style={{
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                color: '#fff',
                boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.4)';
                e.currentTarget.style.transform = 'translateY(-1px) scale(1.05)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.3)';
                e.currentTarget.style.transform = 'translateY(0) scale(1)';
              }}
              aria-label="Restore dialog"
            >
              <RefreshIcon sx={{ fontSize: '1rem' }} />
              Restore
            </button>
          </Tooltip>
        </div>
      </div>
    );
  }

  return (
    <AccessibleDialog
      open={isOpen}
      onClose={onClose}
      maxWidth={false}
      fullScreen
      slotProps={{
        paper: {
          style: {
            margin: 0,
            maxHeight: '100vh',
            height: '100vh',
            width: '100vw',
            maxWidth: '100vw',
            borderRadius: 0
          }
        }
      }}
    >
      {isLoading && (
        <div className="fixed inset-0 z-[1010] flex items-center justify-center bg-black bg-opacity-70">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-lg font-semibold">{loadingMessage}</p>
          </div>
        </div>
      )}
      <div className="bg-white rounded-lg shadow-lg w-full h-full overflow-hidden flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 id="modal-title" className="text-xl font-bold">Auto-Scheduling Rules System</h2>
          <div className="flex items-center space-x-3">
            {/* Auto-scheduling button */}
            <Tooltip title="Configure and run auto-scheduling for all courses">
              <button
                onClick={(e) => {
                  // Blur the button immediately to prevent focus retention
                  (e.target as HTMLButtonElement).blur();
                  handleShowSessionOptions();
                }}
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                style={{
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  color: '#fff',
                  boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';
                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.4)';
                  e.currentTarget.style.transform = 'translateY(-2px) scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                }}
              >
                <PlayArrowIcon sx={{ fontSize: '1.1rem' }} />
                Run Auto-Scheduling for All Courses
              </button>
            </Tooltip>

            {/* Save and Close button */}
            <Tooltip title="Save current settings and close modal">
              <button
                onClick={(e) => {
                  // Blur the button immediately to prevent focus retention
                  (e.target as HTMLButtonElement).blur();
                  onClose();
                }}
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                  color: '#fff',
                  boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'linear-gradient(135deg, #059669 0%, #047857 100%)';
                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(16, 185, 129, 0.4)';
                  e.currentTarget.style.transform = 'translateY(-2px) scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.3)';
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                }}
              >
                <SaveIcon sx={{ fontSize: '1.1rem' }} />
                Save and Close
              </button>
            </Tooltip>

            <Tooltip title="Minimize dialog">
              <IconButton
                onClick={() => setIsMinimized(!isMinimized)}
                size="small"
                sx={{
                  p: 1,
                  borderRadius: 2,
                  background: theme => theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
                    : 'linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%)',
                  color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
                  border: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                  boxShadow: theme => theme.palette.mode === 'dark'
                    ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                    : '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: theme => theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, rgba(255, 152, 0, 0.2) 0%, rgba(255, 152, 0, 0.1) 100%)'
                      : 'linear-gradient(135deg, rgba(255, 152, 0, 0.1) 0%, rgba(255, 152, 0, 0.05) 100%)',
                    color: '#ff9800',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 16px rgba(255, 152, 0, 0.2)',
                    borderColor: 'rgba(255, 152, 0, 0.3)'
                  },
                  '&:active': {
                    transform: 'translateY(0) scale(0.98)'
                  }
                }}
              >
                <MinimizeIcon sx={{ fontSize: '1.1rem' }} />
              </IconButton>
            </Tooltip>

            <Tooltip title="Close dialog">
              <IconButton
                onClick={onClose}
                size="small"
                sx={{
                  p: 1,
                  borderRadius: 2,
                  background: theme => theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
                    : 'linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%)',
                  color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
                  border: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                  boxShadow: theme => theme.palette.mode === 'dark'
                    ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                    : '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: theme => theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(244, 67, 54, 0.1) 100%)'
                      : 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(244, 67, 54, 0.05) 100%)',
                    color: '#f44336',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 16px rgba(244, 67, 54, 0.2)',
                    borderColor: 'rgba(244, 67, 54, 0.3)'
                  },
                  '&:active': {
                    transform: 'translateY(0) scale(0.98)'
                  }
                }}
              >
                <CloseIcon sx={{ fontSize: '1.1rem' }} />
              </IconButton>
            </Tooltip>
          </div>
        </div>

        <div className="flex border-b overflow-x-auto">
          <button
            className={`px-4 py-2 ${activeTab === 'course-rules' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('course-rules')}
          >
            Course Rules
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'lecturer-rules' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('lecturer-rules')}
          >
            Lecturer Rules
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'timeslots' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('timeslots')}
          >
            Max Sessions Per Timeslot
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'days' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('days')}
          >
            Max Sessions Per Day
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'breaks' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('breaks')}
          >
            User Defined Breaks
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'postgrad' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('postgrad')}
          >
            Postgraduate Rules
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'advanced' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('advanced')}
          >
            Advanced
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'analysis' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('analysis')}
          >
            Rule Analysis
          </button>
          <button
            className={`px-4 py-2 ${activeTab === 'lecturer-analysis' ? 'border-b-2 border-blue-500' : ''}`}
            onClick={() => setActiveTab('lecturer-analysis')}
          >
            Lecturer Analysis
          </button>
        </div>

        <div className="flex-1 overflow-y-auto p-4">
          {activeTab === 'course-rules' && (
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-semibold">Course/Section Rules</h3>
                  <div className="flex space-x-3">
                    <Tooltip title="Reset all rules to default values and refresh the page">
                      <button
                        onClick={(e) => {
                          (e.target as HTMLButtonElement).blur();
                          handleForceResetRules();
                        }}
                        className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                        style={{
                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                          color: '#fff',
                          boxShadow: '0 2px 8px rgba(239, 68, 68, 0.3)',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(239, 68, 68, 0.4)';
                          e.currentTarget.style.transform = 'translateY(-1px) scale(1.05)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)';
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(239, 68, 68, 0.3)';
                          e.currentTarget.style.transform = 'translateY(0) scale(1)';
                        }}
                      >
                        <RestartAltIcon sx={{ fontSize: '1rem' }} />
                        Force Reset
                      </button>
                    </Tooltip>

                    <Tooltip title="Restore all rules to their default values">
                      <button
                        onClick={(e) => {
                          (e.target as HTMLButtonElement).blur();
                          handleRestoreDefaultRules();
                        }}
                        className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                        style={{
                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                          color: '#fff',
                          boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.4)';
                          e.currentTarget.style.transform = 'translateY(-1px) scale(1.05)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                          e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.3)';
                          e.currentTarget.style.transform = 'translateY(0) scale(1)';
                        }}
                      >
                        <RefreshIcon sx={{ fontSize: '1rem' }} />
                        Restore Defaults
                      </button>
                    </Tooltip>
                  </div>
                </div>
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border p-2 text-left">Enabled</th>
                      <th className="border p-2 text-left">Rule</th>
                      <th className="border p-2 text-left">Priority</th>
                      <th className="border p-2 text-left">Value</th>
                      <th className="border p-2 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {courseRules.map((rule: Rule) => (
                      <tr key={rule.id} className="hover:bg-gray-50">
                        <td className="border p-2">
                          <input
                            type="checkbox"
                            checked={rule.enabled}
                            onChange={(e) => handleRuleToggle(rule.id, e.target.checked)}
                          />
                        </td>
                        <td className="border p-2">
                          {rule.name}

                        </td>
                        <td className="border p-2">
                          <input
                            type="number"
                            min="1"
                            value={rule.priority}
                            onChange={(e) => handlePriorityChange(rule.id, parseInt(e.target.value))}
                            className="w-16 p-1 border rounded"
                          />
                        </td>
                        <td className="border p-2">
                          {rule.id === 'max-gap-4th-year' ? (
                            <input
                              type="number"
                              min="1"
                              max="10"
                              value={maxGap4thYear}
                              onChange={(e) => {
                                const value = parseInt(e.target.value);
                                if (value > 0) {
                                  setMaxGap4thYear(value);

                                  // Dispatch event to notify components that rules have changed
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('rulesChanged', {
                                      detail: { type: 'maxGap4thYear', value }
                                    }));
                                  }, 0);
                                }
                              }}
                              className="w-16 p-1 border rounded"
                              title="Maximum gap in periods between 4th-year sessions"
                            />
                          ) : rule.id === 'max-gap-3rd-year' ? (
                            <input
                              type="number"
                              min="1"
                              max="10"
                              value={maxGap3rdYear}
                              onChange={(e) => {
                                const value = parseInt(e.target.value);
                                if (value > 0) {
                                  setMaxGap3rdYear(value);

                                  // Dispatch event to notify components that rules have changed
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('rulesChanged', {
                                      detail: { type: 'maxGap3rdYear', value }
                                    }));
                                  }, 0);
                                }
                              }}
                              className="w-16 p-1 border rounded"
                              title="Maximum gap in periods between 3rd-year sessions"
                            />
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </td>
                        <td className="border p-2">{rule.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'lecturer-rules' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Lecturer Rules</h3>

                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border p-2 text-left">Enabled</th>
                      <th className="border p-2 text-left">Rule</th>
                      <th className="border p-2 text-left">Priority</th>
                      <th className="border p-2 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {lecturerRules.map((rule: Rule) => (
                      <tr key={rule.id} className="hover:bg-gray-50">
                        <td className="border p-2">
                          <input
                            type="checkbox"
                            checked={rule.enabled}
                            onChange={(e) => handleRuleToggle(rule.id, e.target.checked)}
                          />
                        </td>
                        <td className="border p-2">{rule.name}</td>
                        <td className="border p-2">
                          <input
                            type="number"
                            min="1"
                            value={rule.priority}
                            onChange={(e) => handlePriorityChange(rule.id, parseInt(e.target.value))}
                            className="w-16 p-1 border rounded"
                          />
                        </td>
                        <td className="border p-2">{rule.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'timeslots' && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold">Maximum Sessions Per Timeslot</h3>
                <Tooltip title="Recalculate default maximum sessions based on current course data">
                  <button
                    onClick={(e) => {
                      // Blur the button immediately to prevent focus retention
                      (e.target as HTMLButtonElement).blur();

                      // Calculate average contact hours and reset to default values
                      const avgContactHours = calculateAverageContactHours();
                      calculateDefaultMaxSessions(totalSections, avgContactHours);

                      // Force a re-render to update the summary section
                      setForceUpdate(prev => prev + 1);

                      // After recalculating, ensure user-defined breaks have max sessions set to 0
                      userDefinedBreaks.forEach(breakKey => {
                        // We don't need to extract day and period here, just set max sessions to 0
                        setMaxSessionsPerTimeslot(breakKey, 0);
                      });

                      // Ensure periods 5 and 6 on long days are set to 0
                      ['Mon', 'Wed'].forEach(day => {
                        [5, 6].forEach(period => {
                          setMaxSessionsPerTimeslot(`${day}-${period}`, 0);
                        });
                      });

                      // Dispatch event to notify components that max sessions have been recalculated
                      // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                      setTimeout(() => {
                        window.dispatchEvent(new CustomEvent('rulesChanged', {
                          detail: { type: 'maxSessionsRecalculated' }
                        }));
                      }, 0);
                    }}
                    className="flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                    style={{
                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                      color: '#fff',
                      boxShadow: '0 2px 8px rgba(59, 130, 246, 0.3)',
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.4)';
                      e.currentTarget.style.transform = 'translateY(-1px) scale(1.05)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.3)';
                      e.currentTarget.style.transform = 'translateY(0) scale(1)';
                    }}
                  >
                    <CalculateIcon sx={{ fontSize: '1rem' }} />
                    Recalculate Defaults
                  </button>
                </Tooltip>
              </div>
              <p className="mb-4 text-sm text-gray-600">
                Configure the maximum number of undergraduate theory sessions allowed per timeslot.
              </p>

              {/* Summary section for session distribution */}
              {(() => {
                // Get undergraduate theory sections with their courses
                const undergradTheorySectionsWithCourses = sections[currentSemester]
                  .map(section => {
                    const course = courses[currentSemester].find(c => c.id === section.courseId);
                    if (!course) return null;

                    // Check if it's a theory course (not a lab)
                    const isTheory = course.courseType === 'Theory';

                    // Check if it's an undergraduate course (course code doesn't start with 5 or higher)
                    const numericPart = course.courseCode.match(/\d+/)?.[0] || '';
                    const firstDigit = numericPart.length > 0 ? parseInt(numericPart[0]) : -1;
                    const isUndergraduate = firstDigit < 5;

                    // Only include undergraduate theory sections
                    if (isTheory && isUndergraduate) {
                      return { section, course };
                    }
                    return null;
                  })
                  .filter(item => item !== null) as { section: Section, course: Course }[];

                // Log the number of undergraduate theory sections for debugging
                console.log(`Number of undergraduate theory sections: ${undergradTheorySectionsWithCourses.length}`);

                // Calculate total sessions needed for regular days (Sun, Tue, Thu)
                // Using formula: max_sessions = Math.ceil((∑(UG theory sections × CHrs) / 5) * 3)
                const totalContactHoursRegular = undergradTheorySectionsWithCourses.reduce(
                  (sum, { course }) => sum + course.contactHours, 0
                );
                const regularDaysIntermediateValue = (totalContactHoursRegular / 5) * 3;
                const totalSessionsNeededRegular = Math.ceil(regularDaysIntermediateValue);

                // Log the calculation details for regular days
                console.log(`Regular days calculation: ${totalContactHoursRegular} contact hours / 5 * 3 = ${regularDaysIntermediateValue}, ceiling = ${totalSessionsNeededRegular}`);

                // Calculate total sessions needed for long days (Mon, Wed)
                // Using formula: max_sessions = Math.ceil((∑(UG theory sections × CHrs) / 1.5 / 5) * 2)
                const totalContactHoursLong = undergradTheorySectionsWithCourses.reduce(
                  (sum, { course }) => sum + course.contactHours, 0
                );
                const longDaysIntermediateValue = (totalContactHoursLong / 1.5 / 5) * 2;
                const totalSessionsNeededLong = Math.ceil(longDaysIntermediateValue);

                // Log the calculation details for long days
                console.log(`Long days calculation: ${totalContactHoursLong} contact hours / 1.5 / 5 * 2 = ${longDaysIntermediateValue}, ceiling = ${totalSessionsNeededLong}`);

                // Calculate total sessions needed overall
                const totalSessionsNeeded = totalSessionsNeededRegular + totalSessionsNeededLong;

                // Calculate sum of max sessions per timeslot for regular days (Sun, Tue, Thu)
                const regularDays = ['Sun', 'Tue', 'Thu'];
                const totalMaxesRegularDays = Object.entries(maxSessionsPerTimeslot)
                  .filter(([key]) => {
                    const day = key.split('-')[0];
                    return regularDays.includes(day);
                  })
                  .reduce((sum, [_, value]) => sum + value, 0);

                // Calculate sum of max sessions per timeslot for long days (Mon, Wed)
                const longDays = ['Mon', 'Wed'];
                const totalMaxesLongDays = Object.entries(maxSessionsPerTimeslot)
                  .filter(([key]) => {
                    const day = key.split('-')[0];
                    return longDays.includes(day);
                  })
                  .reduce((sum, [_, value]) => sum + value, 0);

                // Calculate total max sessions available
                const totalMaxesAvailable = totalMaxesRegularDays + totalMaxesLongDays;

                // Calculate differences for regular and long days
                const regularDaysDifference = totalSessionsNeededRegular - totalMaxesRegularDays;
                const longDaysDifference = totalSessionsNeededLong - totalMaxesLongDays;

                // Calculate overall difference
                const overallDifference = totalSessionsNeeded - totalMaxesAvailable;

                return (
                  <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800 border rounded-md">
                    <h4 className="font-semibold mb-2">Session Distribution Summary</h4>

                    {/* Regular Days Summary */}
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Regular Days (Sun, Tue, Thu)</h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-3 bg-white dark:bg-gray-700 rounded-md shadow-sm">
                          <div className="text-sm text-gray-600 dark:text-gray-300">Sessions Needed:</div>
                          <div className="text-xl font-bold">{totalSessionsNeededRegular}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            ceil((∑(UG theory sections × CHrs) / 5) × 3) = {totalSessionsNeededRegular}
                          </div>
                        </div>
                        <div className="p-3 bg-white dark:bg-gray-700 rounded-md shadow-sm">
                          <div className="text-sm text-gray-600 dark:text-gray-300">Slots Available:</div>
                          <div className="text-xl font-bold">{totalMaxesRegularDays}</div>
                        </div>
                        <div className={`p-3 rounded-md shadow-sm ${
                          regularDaysDifference > 0
                            ? 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                            : 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        }`}>
                          <div className="text-sm">Sessions Without Space:</div>
                          <div className="text-xl font-bold">{regularDaysDifference > 0 ? regularDaysDifference : 0}</div>
                        </div>
                      </div>
                    </div>

                    {/* Long Days Summary */}
                    <div className="mb-4">
                      <h5 className="font-medium mb-2">Long Days (Mon, Wed)</h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-3 bg-white dark:bg-gray-700 rounded-md shadow-sm">
                          <div className="text-sm text-gray-600 dark:text-gray-300">Sessions Needed:</div>
                          <div className="text-xl font-bold">{totalSessionsNeededLong}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            ceil((∑(UG theory sections × CHrs) / 1.5 / 5) × 2) = {totalSessionsNeededLong}
                          </div>
                        </div>
                        <div className="p-3 bg-white dark:bg-gray-700 rounded-md shadow-sm">
                          <div className="text-sm text-gray-600 dark:text-gray-300">Slots Available:</div>
                          <div className="text-xl font-bold">{totalMaxesLongDays}</div>
                        </div>
                        <div className={`p-3 rounded-md shadow-sm ${
                          longDaysDifference > 0
                            ? 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                            : 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        }`}>
                          <div className="text-sm">Sessions Without Space:</div>
                          <div className="text-xl font-bold">{longDaysDifference > 0 ? longDaysDifference : 0}</div>
                        </div>
                      </div>
                    </div>

                    {/* Overall Summary */}
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <h5 className="font-medium mb-2">Overall Summary</h5>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-3 bg-white dark:bg-gray-700 rounded-md shadow-sm">
                          <div className="text-sm text-gray-600 dark:text-gray-300">Total Sessions Needed:</div>
                          <div className="text-xl font-bold">{totalSessionsNeeded}</div>
                        </div>
                        <div className="p-3 bg-white dark:bg-gray-700 rounded-md shadow-sm">
                          <div className="text-sm text-gray-600 dark:text-gray-300">Total Slots Available:</div>
                          <div className="text-xl font-bold">{totalMaxesAvailable}</div>
                        </div>
                        <div className={`p-3 rounded-md shadow-sm ${
                          overallDifference > 0
                            ? 'bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                            : 'bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        }`}>
                          <div className="text-sm">Total Sessions Without Space:</div>
                          <div className="text-xl font-bold">{overallDifference > 0 ? overallDifference : 0}</div>
                        </div>
                      </div>

                      {overallDifference > 0 && (
                        <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                          Warning: You need to increase the maximum sessions per timeslot to accommodate all undergraduate theory sessions.
                        </div>
                      )}
                      {overallDifference < 0 && (
                        <div className="mt-2 text-sm text-green-600 dark:text-green-400">
                          You have {Math.abs(overallDifference)} extra slots available beyond what's needed for all undergraduate theory sessions.
                        </div>
                      )}
                    </div>
                  </div>
                );
              })()}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Regular Days (Su, Tu, Th)</h4>
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border p-2">Period</th>
                        <th className="border p-2">Sunday</th>
                        <th className="border p-2">Tuesday</th>
                        <th className="border p-2">Thursday</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.from({ length: 12 }, (_, i) => i + 1).map((period) => (
                        <tr key={`regular-${period}`}>
                          <td className="border p-2 font-semibold">{period}</td>
                          {['Sun', 'Tue', 'Thu'].map(day => (
                            <td key={`${day}-${period}`} className="border p-2">
                              <input
                                type="number"
                                min="0"
                                value={maxSessionsPerTimeslot[`${day}-${period}`] || 0}
                                onChange={(e) => {
                                  const value = parseInt(e.target.value);
                                  setMaxSessionsPerTimeslot(`${day}-${period}`, value);

                                  // Force a re-render to update the summary section
                                  setForceUpdate(prev => prev + 1);

                                  // Check if this is a system-blocked timeslot
                                  const { blocked: isSystemBlocked } = checkSystemBlockedTimeslot(day, period);

                                  if (isSystemBlocked) {
                                    // Don't modify breaks for system-blocked timeslots
                                    return;
                                  }

                                  // If value is greater than 0, remove from user-defined breaks
                                  if (value > 0) {
                                    const isUserBreak = userDefinedBreaks.includes(`${day}-${period}`);
                                    if (isUserBreak) {
                                      // Remove from breaks
                                      const updatedBreaks = userDefinedBreaks.filter(b => b !== `${day}-${period}`);
                                      useRuleSystemStore.setState({ userDefinedBreaks: updatedBreaks });

                                      // Dispatch event to notify components that breaks have changed
                                      setTimeout(() => {
                                        window.dispatchEvent(new CustomEvent('breakChanges', {
                                          detail: { action: 'remove', dayPeriod: `${day}-${period}` }
                                        }));
                                      }, 0);
                                    }
                                  }
                                  // If value is 0, add to user-defined breaks
                                  else if (value === 0) {
                                    const isUserBreak = userDefinedBreaks.includes(`${day}-${period}`);
                                    if (!isUserBreak) {
                                      // Add to breaks
                                      useRuleSystemStore.setState({
                                        userDefinedBreaks: [...userDefinedBreaks, `${day}-${period}`]
                                      });

                                      // Dispatch event to notify components that breaks have changed
                                      setTimeout(() => {
                                        window.dispatchEvent(new CustomEvent('breakChanges', {
                                          detail: { action: 'add', dayPeriod: `${day}-${period}` }
                                        }));
                                      }, 0);
                                    }
                                  }

                                  // Dispatch event to notify components that max sessions have changed
                                  // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('rulesChanged', {
                                      detail: { type: 'maxSessionsPerTimeslot', dayPeriod: `${day}-${period}`, value }
                                    }));
                                  }, 0);
                                }}
                                className="w-16 p-1 border rounded"
                              />
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Long Days (Mo, We)</h4>
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100">
                        <th className="border p-2">Period</th>
                        <th className="border p-2">Monday</th>
                        <th className="border p-2">Wednesday</th>
                      </tr>
                    </thead>
                    <tbody>
                      {Array.from({ length: 10 }, (_, i) => i + 1).map((period) => {
                        // Check if this is period 5 or 6 on long days (should be disabled and set to 0)
                        const isDisabledPeriod = period === 5 || period === 6;

                        // If it's period 5 or 6, ensure max sessions is set to 0
                        if (isDisabledPeriod) {
                          ['Mon', 'Wed'].forEach(day => {
                            if (maxSessionsPerTimeslot[`${day}-${period}`] !== 0) {
                              setMaxSessionsPerTimeslot(`${day}-${period}`, 0);
                            }
                          });
                        }

                        return (
                          <tr key={`long-${period}`}>
                            <td className="border p-2 font-semibold">{period}</td>
                            {['Mon', 'Wed'].map(day => (
                              <td key={`${day}-${period}`} className={`border p-2 ${isDisabledPeriod ? 'bg-gray-200' : ''}`}>
                                <input
                                  type="number"
                                  min="0"
                                  value={maxSessionsPerTimeslot[`${day}-${period}`] || 0}
                                  onChange={(e) => {
                                    // Don't allow changes for periods 5 and 6
                                    if (isDisabledPeriod) return;

                                    const value = parseInt(e.target.value);
                                    setMaxSessionsPerTimeslot(`${day}-${period}`, value);

                                    // Force a re-render to update the summary section
                                    setForceUpdate(prev => prev + 1);

                                    // Check if this is a system-blocked timeslot
                                    const { blocked: isSystemBlocked } = checkSystemBlockedTimeslot(day, period);

                                    if (isSystemBlocked) {
                                      // Don't modify breaks for system-blocked timeslots
                                      return;
                                    }

                                    // If value is greater than 0, remove from user-defined breaks
                                    if (value > 0) {
                                      const isUserBreak = userDefinedBreaks.includes(`${day}-${period}`);
                                      if (isUserBreak) {
                                        // Remove from breaks
                                        const updatedBreaks = userDefinedBreaks.filter(b => b !== `${day}-${period}`);
                                        useRuleSystemStore.setState({ userDefinedBreaks: updatedBreaks });

                                        // Dispatch event to notify components that breaks have changed
                                        setTimeout(() => {
                                          window.dispatchEvent(new CustomEvent('breakChanges', {
                                            detail: { action: 'remove', dayPeriod: `${day}-${period}` }
                                          }));
                                        }, 0);
                                      }
                                    }
                                    // If value is 0, add to user-defined breaks
                                    else if (value === 0) {
                                      const isUserBreak = userDefinedBreaks.includes(`${day}-${period}`);
                                      if (!isUserBreak) {
                                        // Add to breaks
                                        useRuleSystemStore.setState({
                                          userDefinedBreaks: [...userDefinedBreaks, `${day}-${period}`]
                                        });

                                        // Dispatch event to notify components that breaks have changed
                                        setTimeout(() => {
                                          window.dispatchEvent(new CustomEvent('breakChanges', {
                                            detail: { action: 'add', dayPeriod: `${day}-${period}` }
                                          }));
                                        }, 0);
                                      }
                                    }

                                    // Dispatch event to notify components that max sessions have changed
                                    // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                                    setTimeout(() => {
                                      window.dispatchEvent(new CustomEvent('rulesChanged', {
                                        detail: { type: 'maxSessionsPerTimeslot', dayPeriod: `${day}-${period}`, value }
                                      }));
                                    }, 0);
                                  }}
                                  className="w-16 p-1 border rounded"
                                  disabled={isDisabledPeriod}
                                  title={isDisabledPeriod ? "Periods 5 and 6 are not available on long days" : ""}
                                />
                              </td>
                            ))}
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'days' && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold">Maximum Sessions Per Day</h3>
                <button
                  onClick={() => {
                    // Calculate average contact hours and reset to default values
                    const avgContactHours = calculateAverageContactHours();
                    calculateDefaultMaxSessions(totalSections, avgContactHours);

                    // Dispatch event to notify components that max sessions have been recalculated
                    // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                    setTimeout(() => {
                      window.dispatchEvent(new CustomEvent('rulesChanged', {
                        detail: { type: 'maxSessionsRecalculated' }
                      }));
                    }, 0);
                  }}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  Recalculate Defaults
                </button>
              </div>
              <p className="mb-4 text-sm text-gray-600">
                Configure the maximum number of undergraduate theory sessions allowed per day.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border p-4 rounded">
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">
                      Regular Days (Su, Tu, Th)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={maxSessionsPerDay['regular'] || 0}
                      onChange={(e) => setMaxSessionsPerDay('regular', parseInt(e.target.value))}
                      className="w-full p-2 border rounded"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Long Days (Mo, We)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={maxSessionsPerDay['long'] || 0}
                      onChange={(e) => setMaxSessionsPerDay('long', parseInt(e.target.value))}
                      className="w-full p-2 border rounded"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'breaks' && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold">User Defined Breaks</h3>
                <button
                  onClick={() => {
                    // Get current user-defined breaks before clearing
                    const currentBreaks = [...userDefinedBreaks];

                    // Clear all user-defined breaks
                    useRuleSystemStore.setState({ userDefinedBreaks: [] });

                    // For each previously defined break, update max sessions if it's not a system-blocked timeslot
                    currentBreaks.forEach(breakKey => {
                      const [day, periodStr] = breakKey.split('-');
                      const period = parseInt(periodStr);

                      // Check if this is a system-blocked timeslot
                      const { blocked: isSystemBlocked } = checkSystemBlockedTimeslot(day, period);

                      // Only update max sessions if it's not a system-blocked timeslot
                      if (!isSystemBlocked) {
                        // Calculate a default value based on the current average
                        const avgContactHours = calculateAverageContactHours();
                        const defaultValue = Math.ceil(totalSections * avgContactHours / 15);

                        // Update max sessions for this timeslot
                        setMaxSessionsPerTimeslot(breakKey, defaultValue);
                      }
                    });

                    // Dispatch event to notify components that all breaks have been cleared
                    // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                    setTimeout(() => {
                      window.dispatchEvent(new CustomEvent('breakChanges', {
                        detail: { action: 'clear-all' }
                      }));

                      // Also dispatch event for max sessions change
                      window.dispatchEvent(new CustomEvent('rulesChanged', {
                        detail: { type: 'maxSessionsRecalculated' }
                      }));
                    }, 0);
                  }}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  Restore Defaults
                </button>
              </div>
              <p className="mb-4 text-sm text-gray-600">
                Define specific timeslots as breaks where no sessions should be scheduled.
                Click on a timeslot to toggle it as a break.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Regular Days (Su, Tu, Th)</h4>
                  <div className="grid grid-cols-4 gap-1">
                    <div className="font-semibold text-center">Period</div>
                    <div className="font-semibold text-center">Sunday</div>
                    <div className="font-semibold text-center">Tuesday</div>
                    <div className="font-semibold text-center">Thursday</div>

                    {Array.from({ length: 12 }, (_, i) => i + 1).map((period) => (
                      <React.Fragment key={`regular-${period}`}>
                        <div className="text-center font-semibold">{period}</div>
                        {['Sun', 'Tue', 'Thu'].map(day => {
                          // Use our centralized functions to check if this is a system-blocked timeslot or user-defined break
                          const { blocked: isSystemBlocked } = checkSystemBlockedTimeslot(day, period);
                          const { blocked: isUserBreak } = checkUserDefinedBreak(day, period, true); // true means ignore rule status

                          return (
                            <div
                              key={`${day}-${period}`}
                              className={`
                                p-2 text-center rounded cursor-pointer
                                ${isSystemBlocked ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : ''}
                                ${isUserBreak && !isSystemBlocked ? 'bg-red-200 hover:bg-red-300' : ''}
                                ${!isUserBreak && !isSystemBlocked ? 'bg-green-100 hover:bg-green-200' : ''}
                              `}
                              onClick={() => {
                                if (isSystemBlocked) return;

                                if (isUserBreak) {
                                  // Remove from breaks
                                  const updatedBreaks = userDefinedBreaks.filter(b => b !== `${day}-${period}`);
                                  useRuleSystemStore.setState({ userDefinedBreaks: updatedBreaks });

                                  // Dispatch event to notify components that breaks have changed
                                  // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('breakChanges', {
                                      detail: { action: 'remove', dayPeriod: `${day}-${period}` }
                                    }));
                                  }, 0);
                                } else {
                                  // Add to breaks
                                  useRuleSystemStore.setState({
                                    userDefinedBreaks: [...userDefinedBreaks, `${day}-${period}`]
                                  });

                                  // Set max sessions to 0 for this timeslot
                                  setMaxSessionsPerTimeslot(`${day}-${period}`, 0);

                                  // Dispatch event to notify components that breaks have changed
                                  // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('breakChanges', {
                                      detail: { action: 'add', dayPeriod: `${day}-${period}` }
                                    }));

                                    // Also dispatch event for max sessions change
                                    window.dispatchEvent(new CustomEvent('rulesChanged', {
                                      detail: { type: 'maxSessionsPerTimeslot', dayPeriod: `${day}-${period}`, value: 0 }
                                    }));
                                  }, 0);
                                }
                              }}
                            >
                              {isSystemBlocked ? 'Blocked' : isUserBreak ? 'Break' : 'Available'}
                            </div>
                          );
                        })}
                      </React.Fragment>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Long Days (Mo, We)</h4>
                  <div className="grid grid-cols-3 gap-1">
                    <div className="font-semibold text-center">Period</div>
                    <div className="font-semibold text-center">Monday</div>
                    <div className="font-semibold text-center">Wednesday</div>

                    {Array.from({ length: 12 }, (_, i) => i + 1).map((period) => (
                      <React.Fragment key={`long-${period}`}>
                        <div className="text-center font-semibold">{period}</div>
                        {['Mon', 'Wed'].map(day => {
                          // Use our centralized functions to check if this is a system-blocked timeslot or user-defined break
                          const { blocked: isSystemBlocked } = checkSystemBlockedTimeslot(day, period);
                          const { blocked: isUserBreak } = checkUserDefinedBreak(day, period, true); // true means ignore rule status

                          return (
                            <div
                              key={`${day}-${period}`}
                              className={`
                                p-2 text-center rounded cursor-pointer
                                ${isSystemBlocked ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : ''}
                                ${isUserBreak && !isSystemBlocked ? 'bg-red-200 hover:bg-red-300' : ''}
                                ${!isUserBreak && !isSystemBlocked ? 'bg-green-100 hover:bg-green-200' : ''}
                              `}
                              onClick={() => {
                                if (isSystemBlocked) return;

                                if (isUserBreak) {
                                  // Remove from breaks
                                  const updatedBreaks = userDefinedBreaks.filter(b => b !== `${day}-${period}`);
                                  useRuleSystemStore.setState({ userDefinedBreaks: updatedBreaks });

                                  // Dispatch event to notify components that breaks have changed
                                  // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('breakChanges', {
                                      detail: { action: 'remove', dayPeriod: `${day}-${period}` }
                                    }));
                                  }, 0);
                                } else {
                                  // Add to breaks
                                  useRuleSystemStore.setState({
                                    userDefinedBreaks: [...userDefinedBreaks, `${day}-${period}`]
                                  });

                                  // Set max sessions to 0 for this timeslot
                                  setMaxSessionsPerTimeslot(`${day}-${period}`, 0);

                                  // Dispatch event to notify components that breaks have changed
                                  // Use setTimeout to prevent immediate dispatch which could cause infinite loops
                                  setTimeout(() => {
                                    window.dispatchEvent(new CustomEvent('breakChanges', {
                                      detail: { action: 'add', dayPeriod: `${day}-${period}` }
                                    }));

                                    // Also dispatch event for max sessions change
                                    window.dispatchEvent(new CustomEvent('rulesChanged', {
                                      detail: { type: 'maxSessionsPerTimeslot', dayPeriod: `${day}-${period}`, value: 0 }
                                    }));
                                  }, 0);
                                }
                              }}
                            >
                              {isSystemBlocked ? 'Blocked' : isUserBreak ? 'Break' : 'Available'}
                            </div>
                          );
                        })}
                      </React.Fragment>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Postgraduate Rules Tab */}
          {activeTab === 'postgrad' && (
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold">Postgraduate Course Rules</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      // Reset to default values
                      setMaxPostgradLevelPerDay(1);
                      setMaxPostgradCoursePerDay(1);
                      setMaxPostgradCoursesPerDay(2);

                      // Dispatch event to notify components that rules have changed
                      setTimeout(() => {
                        window.dispatchEvent(new CustomEvent('rulesChanged', {
                          detail: { type: 'postgraduateRulesReset' }
                        }));
                      }, 0);
                    }}
                    className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                  >
                    Reset to Default Values
                  </button>

                </div>
              </div>
              <p className="mb-4 text-sm text-gray-600">
                Configure rules for scheduling postgraduate courses (Diploma, Master, PhD).
              </p>

              <div className="grid grid-cols-1 gap-4">
                <div className="border p-4 rounded">
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        checked={rules.find(r => r.id === 'limit-postgrad-level-per-day')?.enabled || false}
                        onChange={(e) => handleRuleToggle('limit-postgrad-level-per-day', e.target.checked)}
                        className="mr-2"
                      />
                      <label className="font-medium">
                        Limit same postgraduate level sections per day
                      </label>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      Don't allow more than the specified number of the same postgraduate level (Diploma, Master, PhD) of the same gender to be scheduled on the same day.
                    </p>
                    <div className="flex items-center">
                      <label className="text-sm mr-2">Maximum sections:</label>
                      <input
                        type="number"
                        min="1"
                        value={maxPostgradLevelPerDay}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (value > 0) {
                            setMaxPostgradLevelPerDay(value);

                            // Dispatch event to notify components that rules have changed
                            setTimeout(() => {
                              window.dispatchEvent(new CustomEvent('rulesChanged', {
                                detail: { type: 'maxPostgradLevelPerDay', value }
                              }));
                            }, 0);
                          }
                        }}
                        className="w-16 p-1 border rounded"
                      />
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        checked={rules.find(r => r.id === 'limit-postgrad-course-per-day')?.enabled || false}
                        onChange={(e) => handleRuleToggle('limit-postgrad-course-per-day', e.target.checked)}
                        className="mr-2"
                      />
                      <label className="font-medium">
                        Limit same postgraduate course sections per day
                      </label>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      Don't allow more than the specified number of the same postgraduate course sections to be scheduled on the same day.
                    </p>
                    <div className="flex items-center">
                      <label className="text-sm mr-2">Maximum sections:</label>
                      <input
                        type="number"
                        min="1"
                        value={maxPostgradCoursePerDay}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (value > 0) {
                            setMaxPostgradCoursePerDay(value);

                            // Dispatch event to notify components that rules have changed
                            setTimeout(() => {
                              window.dispatchEvent(new CustomEvent('rulesChanged', {
                                detail: { type: 'maxPostgradCoursePerDay', value }
                              }));
                            }, 0);
                          }
                        }}
                        className="w-16 p-1 border rounded"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex items-center mb-2">
                      <input
                        type="checkbox"
                        checked={rules.find(r => r.id === 'distribute-postgrad-courses')?.enabled || false}
                        onChange={(e) => handleRuleToggle('distribute-postgrad-courses', e.target.checked)}
                        className="mr-2"
                      />
                      <label className="font-medium">
                        Distribute postgraduate courses across days
                      </label>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      Distribute postgraduate courses of the same level evenly across the week with no more than the specified number of courses of the same level per day.
                    </p>
                    <div className="flex items-center">
                      <label className="text-sm mr-2">Maximum courses per day:</label>
                      <input
                        type="number"
                        min="1"
                        value={maxPostgradCoursesPerDay}
                        onChange={(e) => {
                          const value = parseInt(e.target.value);
                          if (value > 0) {
                            setMaxPostgradCoursesPerDay(value);

                            // Dispatch event to notify components that rules have changed
                            setTimeout(() => {
                              window.dispatchEvent(new CustomEvent('rulesChanged', {
                                detail: { type: 'maxPostgradCoursesPerDay', value }
                              }));
                            }, 0);
                          }
                        }}
                        className="w-16 p-1 border rounded"
                      />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      <strong>Note:</strong> If you have more than 5 postgraduate courses of the same level, set this value to at least 2 to allow scheduling all courses.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Advanced Scheduling Features</h3>
                <p className="mb-4 text-sm text-gray-600">
                  Configure advanced auto-scheduling features including retry mechanisms and constraint programming system (CPS) refinement.
                </p>

                {/* Retry Configuration Section */}
                <div className="mb-6 p-4 border rounded bg-gray-50">
                  <h4 className="font-semibold mb-3">Auto-Scheduling Retry Mechanism</h4>

                  <div className="mb-3">
                    <div className="flex items-center justify-between">
                      <Typography variant="subtitle1" className="font-semibold">
                        Enable Retry Mechanism
                      </Typography>
                      <Switch
                        checked={retryConfiguration.enabled}
                        onChange={(e) => setRetryConfiguration({ enabled: e.target.checked })}
                        color="primary"
                      />
                    </div>
                    <Typography variant="body2" className="text-gray-600 mt-1">
                      When enabled, the system will automatically retry scheduling with different strategies if the initial attempt doesn't achieve the target success rate.
                    </Typography>
                  </div>

                  {retryConfiguration.enabled && (
                    <>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Maximum Retries
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="10"
                            value={retryConfiguration.maxRetries}
                            onChange={(e) => setRetryConfiguration({ maxRetries: parseInt(e.target.value) })}
                            className="w-full p-2 border rounded"
                          />
                          <Typography variant="body2" className="text-gray-500 mt-1">
                            Number of retry attempts (1-10)
                          </Typography>
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Target Success Rate (%)
                          </label>
                          <input
                            type="number"
                            min="50"
                            max="100"
                            value={Math.round(retryConfiguration.minSuccessRate * 100)}
                            onChange={(e) => setRetryConfiguration({ minSuccessRate: parseInt(e.target.value) / 100 })}
                            className="w-full p-2 border rounded"
                          />
                          <Typography variant="body2" className="text-gray-500 mt-1">
                            Stop retrying when this success rate is achieved
                          </Typography>
                        </div>

                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Timeout (minutes)
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="30"
                            value={Math.round(retryConfiguration.timeoutMs / 60000)}
                            onChange={(e) => setRetryConfiguration({ timeoutMs: parseInt(e.target.value) * 60000 })}
                            className="w-full p-2 border rounded"
                          />
                          <Typography variant="body2" className="text-gray-500 mt-1">
                            Maximum time to spend on retries
                          </Typography>
                        </div>
                      </div>

                      {/* CPS Refinement Section */}
                      <div className="mt-4 p-3 border rounded bg-blue-50">
                        <div className="flex items-center justify-between mb-2">
                          <Typography variant="subtitle1" className="font-semibold text-blue-800">
                            🔧 Constraint Programming System (CPS) Refinement
                          </Typography>
                          <Switch
                            checked={retryConfiguration.enableCPSRefinement || false}
                            onChange={(e) => setRetryConfiguration({ enableCPSRefinement: e.target.checked })}
                            color="primary"
                          />
                        </div>
                        <Typography variant="body2" className="text-blue-700 mb-2">
                          <strong>Enhanced Scheduling:</strong> When enabled, the CPS system schedules unscheduled sections without lecturer constraints while respecting academic pattern rules.
                        </Typography>
                        <div className="text-sm text-blue-600 space-y-1">
                          <div>• <strong>Academic Pattern Compliance:</strong> Ensures 2CH, 3CH, 5CH undergraduate and 3CH postgraduate patterns are followed</div>
                          <div>• <strong>Rule System Integration:</strong> Respects all enabled rules from the Rule System Modal</div>
                          <div>• <strong>Lecturer-Independent Scheduling:</strong> Allows sections to be scheduled without lecturer assignment for later manual assignment</div>
                          <div>• <strong>Comprehensive Validation:</strong> Each timeslot is validated against all constraints before scheduling</div>
                        </div>
                        {retryConfiguration.enableCPSRefinement && (
                          <div className="mt-2 p-2 bg-green-100 rounded text-sm text-green-800">
                            ✅ CPS refinement will automatically activate when success rate is below {Math.round((retryConfiguration.minSuccessRate || 0.85) * 100)}%
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Rule Analysis Tab */}
          {activeTab === 'analysis' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Rule Analysis</h3>
                <button
                  onClick={(e) => {
                    (e.target as HTMLButtonElement).blur();
                    const currentAnalysis = generateCurrentTimetableAnalysis();
                    setAnalysisResult(currentAnalysis);
                  }}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                  title="Refresh analysis based on current timetable state"
                >
                  Refresh Analysis
                </button>
              </div>
              <RuleAnalysisTab
                rules={rules}
                analysisResult={analysisResult}
              />
            </div>
          )}

          {/* Lecturer Analysis Tab */}
          {activeTab === 'lecturer-analysis' && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Lecturer Analysis</h3>
                <button
                  onClick={(e) => {
                    (e.target as HTMLButtonElement).blur();
                    // Force re-render by updating a state that triggers useEffect in LecturerAnalysisTab
                    setForceUpdate(prev => prev + 1);
                  }}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                  title="Refresh lecturer analysis based on current timetable state"
                >
                  Refresh Analysis
                </button>
              </div>
              <LecturerAnalysisTab
                currentSemester={currentSemester}
                key={forceUpdate} // Force re-mount when forceUpdate changes
              />
            </div>
          )}
        </div>

        {/* Enhanced Auto-Scheduling Options Dialog */}
        <AccessibleDialog
          open={showSessionOptions}
          onClose={() => setShowSessionOptions(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Auto-Scheduling Options</DialogTitle>
          <DialogContent>
            <div className="space-y-6 mt-4">
              {/* Session Handling Options */}
              <FormControl component="fieldset">
                <FormLabel component="legend" className="text-base font-semibold mb-3">
                  Session Handling
                </FormLabel>
                <Typography variant="body2" className="text-gray-600 mb-3">
                  Choose how to handle existing sessions before auto-scheduling:
                </Typography>
                <RadioGroup
                  value={sessionOption}
                  onChange={(e) => setSessionOption(e.target.value as 'keep-all' | 'delete-all' | 'delete-auto')}
                  className="space-y-2"
                >
                  <FormControlLabel
                    value="keep-all"
                    control={<Radio />}
                    label={
                      <div>
                        <div className="font-medium">Keep all existing sessions</div>
                        <div className="text-sm text-gray-600">Add new sessions while respecting existing ones and all rules</div>
                      </div>
                    }
                  />
                  <FormControlLabel
                    value="delete-all"
                    control={<Radio />}
                    label={
                      <div>
                        <div className="font-medium">Delete all existing sessions</div>
                        <div className="text-sm text-gray-600">Remove all sessions before auto-scheduling</div>
                      </div>
                    }
                  />
                  <FormControlLabel
                    value="delete-auto"
                    control={<Radio />}
                    label={
                      <div>
                        <div className="font-medium">Remove only auto-generated sessions</div>
                        <div className="text-sm text-gray-600">Keep manually created sessions, remove auto-generated ones</div>
                      </div>
                    }
                  />
                </RadioGroup>
              </FormControl>

              {/* Lecturer Assignment Options */}
              <FormControl component="fieldset">
                <FormLabel component="legend" className="text-base font-semibold mb-3">
                  Lecturer Assignment Strategy
                </FormLabel>
                <Typography variant="body2" className="text-gray-600 mb-3">
                  Choose how to handle lecturer assignments during auto-scheduling:
                </Typography>
                <RadioGroup
                  value={lecturerAssignmentOption}
                  onChange={(e) => setLecturerAssignmentOption(e.target.value as 'no-assignment' | 'simultaneous' | 'post-scheduling')}
                  className="space-y-2"
                >
                  <FormControlLabel
                    value="no-assignment"
                    control={<Radio />}
                    label={
                      <div>
                        <div className="font-medium">Schedule without lecturer assignment</div>
                        <div className="text-sm text-gray-600">Schedule sections to time slots but leave lecturerId empty for all sessions</div>
                      </div>
                    }
                  />
                  <FormControlLabel
                    value="simultaneous"
                    control={<Radio />}
                    label={
                      <div>
                        <div className="font-medium">Schedule with simultaneous lecturer assignment</div>
                        <div className="text-sm text-gray-600">Assign eligible lecturers during scheduling (if no eligible lecturer found, schedule without lecturer)</div>
                      </div>
                    }
                  />
                  <FormControlLabel
                    value="post-scheduling"
                    control={<Radio />}
                    label={
                      <div>
                        <div className="font-medium">Schedule first, then assign lecturers</div>
                        <div className="text-sm text-gray-600">First schedule all sections without lecturers, then run separate lecturer assignment process</div>
                      </div>
                    }
                  />
                </RadioGroup>
              </FormControl>
            </div>
          </DialogContent>
          <DialogActions sx={{ p: 3, gap: 2 }}>
            <Button
              onClick={(e) => {
                (e.target as HTMLButtonElement).blur();
                setShowSessionOptions(false);
              }}
              sx={{
                px: 3,
                py: 1.5,
                borderRadius: 2,
                background: theme => theme.palette.mode === 'dark'
                  ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
                  : 'linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%)',
                color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
                border: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                boxShadow: theme => theme.palette.mode === 'dark'
                  ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                  : '0 2px 8px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  background: theme => theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(156, 163, 175, 0.2) 0%, rgba(156, 163, 175, 0.1) 100%)'
                    : 'linear-gradient(135deg, rgba(156, 163, 175, 0.1) 0%, rgba(156, 163, 175, 0.05) 100%)',
                  color: '#9ca3af',
                  transform: 'translateY(-1px) scale(1.02)',
                  boxShadow: '0 4px 12px rgba(156, 163, 175, 0.2)',
                  borderColor: 'rgba(156, 163, 175, 0.3)'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={(e) => {
                (e.target as HTMLButtonElement).blur();
                handleRunAutoSchedulingAll();
              }}
              variant="contained"
              sx={{
                px: 3,
                py: 1.5,
                borderRadius: 2,
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                color: '#fff',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)',
                  transform: 'translateY(-2px) scale(1.05)',
                  boxShadow: '0 6px 20px rgba(59, 130, 246, 0.4)'
                },
                '&:active': {
                  transform: 'translateY(0) scale(0.98)'
                }
              }}
            >
              Proceed
            </Button>
          </DialogActions>
        </AccessibleDialog>
      </div>
    </AccessibleDialog>
  );
};