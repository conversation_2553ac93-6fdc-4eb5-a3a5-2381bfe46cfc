/**
 * Node.js script to build NSIS installer for QU Scheduler
 * Checks prerequisites and builds the installer with proper error handling
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔨 QU Scheduler NSIS Installer Builder');
console.log('=====================================');
console.log('');

// Check if NSIS is installed
function checkNSIS() {
  const nsisCommands = [
    'makensis',
    '"C:\\Program Files\\NSIS\\makensis.exe"',
    '"C:\\Program Files (x86)\\NSIS\\makensis.exe"'
  ];

  for (const cmd of nsisCommands) {
    try {
      execSync(`${cmd} /VERSION`, { stdio: 'pipe' });
      console.log('✅ NSIS is installed and available');
      global.nsisCommand = cmd;
      return true;
    } catch (error) {
      // Continue to next command
    }
  }

  console.log('❌ NSIS is not installed or not in PATH');
  console.log('');
  console.log('📥 Please install NSIS:');
  console.log('   1. Download from: https://nsis.sourceforge.io/');
  console.log('   2. Install and add to system PATH');
  console.log('   3. Restart your terminal/command prompt');
  console.log('   4. Verify with: makensis /VERSION');
  return false;
}

// Check if application is packaged
function checkPackage() {
  const appPath = path.join(__dirname, '../out/QU Scheduler-win32-x64/qu-scheduler.exe');
  if (fs.existsSync(appPath)) {
    console.log('✅ Application package found');
    return true;
  } else {
    console.log('❌ Application package not found');
    console.log('');
    console.log('📦 Please package the application first:');
    console.log('   Run: npm run package');
    return false;
  }
}

// Check and convert graphics
function checkGraphics() {
  const headerSVG = path.join(__dirname, '../assets/icons/header.svg');
  const welcomeSVG = path.join(__dirname, '../assets/icons/welcome.svg');
  const headerBMP = path.join(__dirname, '../assets/icons/header.bmp');
  const welcomeBMP = path.join(__dirname, '../assets/icons/welcome.bmp');

  let hasGraphics = true;

  if (!fs.existsSync(headerBMP)) {
    console.log('⚠️  Header BMP not found (header.bmp)');
    hasGraphics = false;
  }

  if (!fs.existsSync(welcomeBMP)) {
    console.log('⚠️  Welcome BMP not found (welcome.bmp)');
    hasGraphics = false;
  }

  if (!hasGraphics) {
    console.log('');
    console.log('🎨 Graphics Conversion Needed:');
    console.log('   Convert these files to BMP format:');
    console.log('   • assets/icons/header.svg → assets/icons/header.bmp (150x57)');
    console.log('   • assets/icons/welcome.svg → assets/icons/welcome.bmp (164x314)');
    console.log('');
    console.log('   Online converters:');
    console.log('   • https://convertio.co/svg-bmp/');
    console.log('   • https://cloudconvert.com/svg-to-bmp');
    console.log('');
    console.log('   Or use ImageMagick:');
    console.log('   • convert header.svg -resize 150x57 header.bmp');
    console.log('   • convert welcome.svg -resize 164x314 welcome.bmp');
    console.log('');
    console.log('   ℹ️  Installer will work without graphics but won\'t look as professional');
  } else {
    console.log('✅ Installer graphics found');
  }

  return hasGraphics;
}

// Build the installer
function buildInstaller() {
  const nsiScript = path.join(__dirname, 'qu-scheduler-installer.nsi');

  if (!fs.existsSync(nsiScript)) {
    console.log('❌ NSIS script not found:', nsiScript);
    return false;
  }

  console.log('');
  console.log('🔨 Building NSIS installer...');

  try {
    // Run makensis with the script using detected command
    const nsisCmd = global.nsisCommand || 'makensis';
    const result = execSync(`${nsisCmd} "${nsiScript}"`, {
      stdio: 'pipe',
      encoding: 'utf8',
      cwd: __dirname
    });

    console.log('✅ Installer built successfully!');
    console.log('');

    // Check if installer was created
    const installerPath = path.join(__dirname, 'QU-Scheduler-Setup.exe');
    if (fs.existsSync(installerPath)) {
      const stats = fs.statSync(installerPath);
      const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);

      console.log('📦 Installer Details:');
      console.log(`   📁 Location: ${installerPath}`);
      console.log(`   📏 Size: ${sizeInMB} MB`);
      console.log('');
      console.log('🎯 Installer Features:');
      console.log('   ✅ Custom installation location');
      console.log('   ✅ Desktop shortcut option');
      console.log('   ✅ Start menu shortcuts');
      console.log('   ✅ Quick launch shortcut option');
      console.log('   ✅ Professional uninstaller');
      console.log('   ✅ License agreement');
      console.log('   ✅ System requirements check');
      console.log('');
      console.log('🚀 Ready for distribution!');
    }

    return true;
  } catch (error) {
    console.log('❌ Failed to build installer');
    console.log('');
    console.log('Error details:');
    console.log(error.message);

    if (error.stdout) {
      console.log('NSIS Output:');
      console.log(error.stdout);
    }

    if (error.stderr) {
      console.log('NSIS Errors:');
      console.log(error.stderr);
    }

    return false;
  }
}

// Main execution
async function main() {
  let canBuild = true;

  // Check prerequisites
  if (!checkNSIS()) canBuild = false;
  if (!checkPackage()) canBuild = false;

  // Check graphics (warning only)
  const hasGraphics = checkGraphics();
  if (!hasGraphics) {
    console.log('⚠️  Proceeding without custom graphics - installer will use default NSIS graphics');
  }

  if (!canBuild) {
    console.log('');
    console.log('❌ Cannot build installer due to missing prerequisites');
    console.log('   Please resolve the issues above and try again');
    process.exit(1);
  }

  // Build the installer
  const success = buildInstaller();

  if (success) {
    console.log('');
    console.log('🎉 NSIS Installer Build Complete!');
    console.log('');
    console.log('📋 Testing Recommendations:');
    console.log('   1. Test installer on a clean Windows system');
    console.log('   2. Verify all installation options work');
    console.log('   3. Test uninstallation process');
    console.log('   4. Check shortcuts and file associations');
    console.log('');
    console.log('📤 Distribution:');
    console.log('   • Share installer/QU-Scheduler-Setup.exe');
    console.log('   • Consider code signing for production');
    console.log('   • Document installation instructions');
    process.exit(0);
  } else {
    console.log('');
    console.log('❌ Installer build failed');
    console.log('   Check the error messages above for details');
    process.exit(1);
  }
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.log('');
  console.log('❌ Unexpected error:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.log('');
  console.log('❌ Unhandled promise rejection:', error.message);
  process.exit(1);
});

// Run the main function
main().catch(error => {
  console.log('');
  console.log('❌ Build script error:', error.message);
  process.exit(1);
});
