import React, { createContext, useContext, useState, ReactNode, useRef } from 'react';
import { useAppContext, isPostgraduateCourse } from './AppContext';
import { Section, Lecturer, Session } from '../types/models';
import { useRuleSystemStore } from '../store/ruleSystem';
import { getAcademicLevel } from '../utils/ruleValidation';
import { getSchedulePatterns } from '../utils/autoScheduling';
import { isSystemBlockedTimeslot, isUserDefinedBreak } from '../utils/breakValidation';

// Define types for drag items
export interface DragItem {
  type: 'SECTION' | 'LECTURER' | 'SESSION';
  id: string;
  data: Section | Lecturer | Session; // Union type to replace any
}

// Define lecturer occurrence type
export interface LecturerOccurrence {
  id: string;
  lecturerId: string;
  firstName: string;
}

// Define the shape of our drag-drop context
interface DragDropContextType {
  // Dragging state
  isDragging: boolean;
  dragItem: DragItem | null;
  setDragItem: (item: DragItem | null) => void;

  // Session management
  createSessionFromSection: (sectionId: string, day: string, period: number) => void;
  assignLecturerToSession: (lecturerId: string, sessionId: string) => void;
  removeLecturerFromSession: (lecturerId: string, sessionId: string) => void;
  moveSession: (sessionId: string, newDay: string, newPeriod: number) => void;

  // Validation
  canDropSectionAt: (sectionId: string, day: string, period: number) => boolean;
  canDropLecturerAt: (lecturerId: string, sessionId: string) => boolean;
  canDropSessionAt: (sessionId: string, day: string, period: number) => boolean;
  isSectionFullyScheduled: (sectionId: string) => boolean;
}

// Create the context with a default value
const DragDropContext = createContext<DragDropContextType | undefined>(undefined);

// Create a provider component
interface DragDropProviderProps {
  children: ReactNode;
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({ children }) => {
  // Get app context
  const {
    currentSemester,
    sections,
    sessions,
    lecturers,
    courses,
    addSession,
    updateSession,
    updateSection,
    updateLecturer,
    viewMode
  } = useAppContext();

  // Dragging state
  const [isDragging, setIsDragging] = useState(false);
  const [dragItem, setDragItem] = useState<DragItem | null>(null);

  // Handle setting drag item and dragging state
  const handleSetDragItem = (item: DragItem | null) => {
    setDragItem(item);
    setIsDragging(!!item);
  };

  // Check if a section is fully scheduled
  const isSectionFullyScheduled = (sectionId: string): boolean => {
    const section = sections[currentSemester].find(s => s.id === sectionId);
    if (!section) return false;

    // Use a small epsilon for floating point comparison to ensure exact match
    return Math.abs(section.scheduledHours - section.totalHours) < 0.01;
  };

  // Get user-defined breaks from the rule system store
  // const userDefinedBreaks = useRuleSystemStore(state => state.userDefinedBreaks);

  // Validate if a section can be dropped at a specific day and period
  const canDropSectionAt = (sectionId: string, day: string, period: number): boolean => {
    const section = sections[currentSemester].find(s => s.id === sectionId);
    if (!section) return false;

    // Don't allow dropping if section is fully scheduled
    if (isSectionFullyScheduled(sectionId)) return false;

    // Get the course to determine if it's a postgraduate course
    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return false;

    // Calculate what would be added by this new session
    const isLongDay = ['Monday', 'Wednesday'].includes(day);
    const hoursToAdd = isLongDay ? 1.5 : 1.0;

    // Check if adding this session would exceed contact hours
    if (section.scheduledHours + hoursToAdd > section.totalHours) {
      return false;
    }

    // Check if this is a system-blocked timeslot
    const systemBlockedCheck = isSystemBlockedTimeslot(day, period);
    if (systemBlockedCheck.blocked) {
      return false;
    }

    // Check if this is a user-defined break
    const userBreakCheck = isUserDefinedBreak(day, period, false); // false means respect rule status
    if (userBreakCheck.blocked) {
      return false;
    }

    // Check if this section is already scheduled at this day and period
    const existingSession = sessions[currentSemester].find(
      session => session.sectionId === sectionId &&
                session.day === day &&
                period >= session.startPeriod &&
                period <= session.endPeriod
    );

    return !existingSession;
  };

  // Validate if a lecturer can be dropped on a specific session
  const canDropLecturerAt = (lecturerId: string, sessionId: string): boolean => {
    const session = sessions[currentSemester].find(s => s.id === sessionId);
    if (!session) return false;

    // Check if this lecturer is already assigned to this session
    if (session.lecturerIds && session.lecturerIds.includes(lecturerId)) return false;
    if (session.lecturerId === lecturerId) return false;

    // Check if lecturer is available at this time
    const lecturer = lecturers.find(l => l.id === lecturerId);
    if (!lecturer) return false;

    // Check if lecturer can teach this course
    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return false;

    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return false;

    // Check if lecturer can teach this course
    const canTeach = lecturer.coursesAbleToTeach.includes(course.courseCode);
    if (!canTeach) return false;

    // We're allowing multiple lecturers to be assigned to the same session,
    // even if they're already assigned to other sessions at the same time
    return true;
  };

  // Create a new session from a section
  // Use a ref to track recent session creation attempts to prevent duplicates
  const recentSessionCreations = useRef<Map<string, number>>(new Map());

  const createSessionFromSection = (sectionId: string, day: string, period: number) => {
    // Prevent duplicate/rapid creation
    const key = `${sectionId}-${day}-${period}`;
    const now = Date.now();
    if (recentSessionCreations.current.has(key)) {
      const lastCreation = recentSessionCreations.current.get(key);
      if (lastCreation && now - lastCreation < 1000) {
        return; // Prevent if less than 1 second since last creation
      }
    }
    recentSessionCreations.current.set(key, now);

    // Get the section
    const section = sections[currentSemester].find(s => s.id === sectionId);
    if (!section) {
      console.error(`Section ${sectionId} not found`);
      return;
    }

    // Get the course
    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) {
      console.error(`Course ${section.courseId} not found for section ${sectionId}`);
      return;
    }

    console.log(`Creating session for section ${section.id} (${course.courseCode}-${section.sectionNumber}) on ${day} at period ${period}`);

    // Check if the section is already partially scheduled
    const isPartiallyScheduled = section.scheduledHours > 0 && section.scheduledHours < section.totalHours;

    // Special handling for postgraduate courses
    if (isPostgraduateCourse(course.courseCode) && course.courseType === 'Theory' && section.totalHours === 3) {
      console.log(`Creating postgraduate session pattern for course ${course.courseCode} on ${day}`);

      // Check if adding 3 hours would exceed contact hours
      if (section.scheduledHours + 3 > section.totalHours) {
        console.log(`Adding 3 hours would exceed the ${section.totalHours} contact hours for this section`);
        return;
      }

      // Create sessions based on day type
      const isLongDay = ['Monday', 'Wednesday'].includes(day);
      const timeOfDay = 'evening'; // Postgrad courses are always in the evening

      if (isLongDay) {
        // For long days (Mon, Wed), create 2 consecutive sessions (periods 9, 10)
        // Create session at period 9
        const session1: Omit<Session, 'id'> = {
          sectionId,
          lecturerId: '',
          lecturerIds: [],
          day,
          startPeriod: 9,
          endPeriod: 9,
          viewType: viewMode,
          timeOfDay,
          isAutoGenerated: false
        };
        addSession(session1, currentSemester);

        // Create session at period 10
        const session2: Omit<Session, 'id'> = {
          sectionId,
          lecturerId: '',
          lecturerIds: [],
          day,
          startPeriod: 10,
          endPeriod: 10,
          viewType: viewMode,
          timeOfDay,
          isAutoGenerated: false
        };
        addSession(session2, currentSemester);
      } else {
        // For regular days (Sun, Tue, Thu), create 3 consecutive sessions (periods 10, 11, 12)
        // Create session at period 10
        const session1: Omit<Session, 'id'> = {
          sectionId,
          lecturerId: '',
          lecturerIds: [],
          day,
          startPeriod: 10,
          endPeriod: 10,
          viewType: viewMode,
          timeOfDay,
          isAutoGenerated: false
        };
        addSession(session1, currentSemester);

        // Create session at period 11
        const session2: Omit<Session, 'id'> = {
          sectionId,
          lecturerId: '',
          lecturerIds: [],
          day,
          startPeriod: 11,
          endPeriod: 11,
          viewType: viewMode,
          timeOfDay,
          isAutoGenerated: false
        };
        addSession(session2, currentSemester);

        // Create session at period 12
        const session3: Omit<Session, 'id'> = {
          sectionId,
          lecturerId: '',
          lecturerIds: [],
          day,
          startPeriod: 12,
          endPeriod: 12,
          viewType: viewMode,
          timeOfDay,
          isAutoGenerated: false
        };
        addSession(session3, currentSemester);
      }

      // Update section's scheduled hours (always add 3 hours for postgrad courses)
      section.scheduledHours += 3;
      updateSection(section, currentSemester);
      return;
    }

    // Function to create a single session
    const createSingleSession = (sessionDay: string, sessionPeriod: number) => {
      // More robust check for existing sessions at this day and period for this section
      // First check if any session exists for this section on this day and period
      const existingSession = sessions[currentSemester].find(
        session => session.sectionId === sectionId &&
                  session.day === sessionDay &&
                  ((sessionPeriod >= session.startPeriod && sessionPeriod <= session.endPeriod) ||
                   (session.startPeriod === sessionPeriod && session.endPeriod === sessionPeriod))
      );

      // Removed session checking logs to reduce console output

      // If a session already exists, don't create a duplicate
      if (existingSession) {
        // Removed duplicate session logs to reduce console output
        return;
      }

      // Check if this is a system-blocked timeslot
      const systemBlockedCheck = isSystemBlockedTimeslot(sessionDay, sessionPeriod);
      if (systemBlockedCheck.blocked) {
        // Removed system-blocked logs to reduce console output
        return;
      }

      // Check if this is a user-defined break
      const userBreakCheck = isUserDefinedBreak(sessionDay, sessionPeriod, false); // false means respect rule status
      if (userBreakCheck.blocked) {
        console.log(`Cannot create session in user-defined break period ${sessionPeriod} on ${sessionDay}`);
        return;
      }

      // Determine time of day based on period (1-6 morning, 7-12 evening)
      const isMorning = sessionPeriod <= 6;
      const timeOfDay = isMorning ? 'morning' : 'evening';

      console.log(`Creating session for course ${course.courseCode} - Time of Day: ${timeOfDay}`);

      const newSession: Omit<Session, 'id'> = {
        sectionId,
        lecturerId: '', // Empty initially
        lecturerIds: [], // Initialize empty array for multiple lecturers
        day: sessionDay,
        startPeriod: sessionPeriod,
        endPeriod: sessionPeriod,
        viewType: viewMode, // Using viewMode from parent context
        timeOfDay: timeOfDay // Add time of day (morning/evening)
      };

      // Add the session
      addSession(newSession, currentSemester);

      // Update section's scheduled hours based on day (1.5h for Monday and Wednesday, 1h for others)
      const hoursToAdd = ['Monday', 'Wednesday'].includes(sessionDay) ? 1.5 : 1.0;

      // Update the section's scheduled hours
      section.scheduledHours += hoursToAdd;
    };

    // If section is partially scheduled, check if adding this session would complete it
    if (isPartiallyScheduled) {
      // Calculate what would be added by this new session
      const isLongDay = ['Monday', 'Wednesday'].includes(day);
      const hoursToAdd = isLongDay ? 1.5 : 1.0;

      // Check if adding this session would exceed contact hours
      if (section.scheduledHours + hoursToAdd > section.totalHours) {
        console.log(`Adding this session would exceed the ${section.totalHours} contact hours for this section`);
        return;
      }

      createSingleSession(day, period);

      // Update the section
      updateSection({
        ...section
      }, currentSemester);
      return;
    }

    // If section is fully unscheduled, apply scheduling patterns based on course type, level, and contact hours
    const isFullyUnscheduled = section.scheduledHours === 0;

    if (isFullyUnscheduled) {
      // Get academic level from course code
      const academicLevel = getAcademicLevel(course.courseCode);
      const isPostgraduate = academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma';
      const contactHours = section.totalHours;
      const courseType = course.courseType as 'Theory' | 'Lab';

      // Determine which pattern rule applies to this section
      let patternRuleId = '';
      if (isPostgraduate && courseType === 'Theory' && contactHours === 3) {
        patternRuleId = 'postgrad-pattern';
      } else if (!isPostgraduate && courseType === 'Theory') {
        // Undergraduate course patterns
        switch (contactHours) {
          case 2:
            patternRuleId = 'undergrad-2ch-pattern';
            break;
          case 3:
            patternRuleId = 'undergrad-3ch-pattern';
            break;
          case 4:
            patternRuleId = 'undergrad-4ch-pattern';
            break;
          case 5:
            patternRuleId = 'undergrad-5ch-pattern';
            break;
          default:
            // No specific pattern rule for other credit hours
            break;
        }
      }

      // Check if the relevant pattern rule is enabled
      const isPatternRuleEnabled = patternRuleId
        ? useRuleSystemStore.getState().rules.find(rule => rule.id === patternRuleId)?.enabled ?? false
        : false;

      console.log(`Pattern rule ${patternRuleId} enabled: ${isPatternRuleEnabled}`);

      // If pattern rule is enabled, apply the appropriate scheduling pattern
      if (isPatternRuleEnabled) {
        // Get matching schedule patterns
        const schedulePatterns = getSchedulePatterns(contactHours, courseType, academicLevel);

        if (schedulePatterns.length > 0) {
          console.log(`Found ${schedulePatterns.length} matching patterns for ${course.courseCode}`);

          // For postgraduate courses with 3 contact hours
          if (isPostgraduate && courseType === 'Theory' && contactHours === 3) {
            if (day === 'Sunday' || day === 'Tuesday' || day === 'Thursday') {
              // On regular days, schedule in periods 10-12
              if (period >= 10 && period <= 12) {
                // Create 3 consecutive sessions
                createSingleSession(day, 10);
                createSingleSession(day, 11);
                createSingleSession(day, 12);
              } else {
                // If dropped outside the correct periods, move to period 10
                createSingleSession(day, 10);
                createSingleSession(day, 11);
                createSingleSession(day, 12);
              }
            } else if (day === 'Monday' || day === 'Wednesday') {
              // On long days, schedule in periods 9-10
              if (period >= 9 && period <= 10) {
                // Create 2 consecutive sessions
                createSingleSession(day, 9);
                createSingleSession(day, 10);
              } else {
                // If dropped outside the correct periods, move to period 9
                createSingleSession(day, 9);
                createSingleSession(day, 10);
              }
            }
          }
          // For undergraduate courses with 2 contact hours
          else if (!isPostgraduate && courseType === 'Theory' && contactHours === 2) {
            if (day === 'Sunday' || day === 'Tuesday' || day === 'Thursday') {
              // Schedule on two regular days at the same time
              const regularDays = ['Sunday', 'Tuesday', 'Thursday'];
              const otherRegularDays = regularDays.filter(d => d !== day);

              // Create session on the dropped day
              createSingleSession(day, period);

              // Create session on another regular day at the same period
              createSingleSession(otherRegularDays[0], period);
            } else {
              // If dropped on a long day, schedule on two regular days instead
              createSingleSession('Sunday', period);
              createSingleSession('Tuesday', period);
            }
          }
          // For undergraduate courses with 3 contact hours
          else if (!isPostgraduate && courseType === 'Theory' && contactHours === 3) {
            if (day === 'Sunday' || day === 'Tuesday' || day === 'Thursday') {
              // Schedule on three regular days at the same time
              createSingleSession('Sunday', period);
              createSingleSession('Tuesday', period);
              createSingleSession('Thursday', period);
            } else if (day === 'Monday' || day === 'Wednesday') {
              // Schedule on two long days at the same time
              createSingleSession('Monday', period);
              createSingleSession('Wednesday', period);
            }
          }
          // For undergraduate courses with 4 contact hours
          else if (!isPostgraduate && courseType === 'Theory' && contactHours === 4) {
            if (day === 'Sunday' || day === 'Tuesday' || day === 'Thursday') {
              // Schedule 2 consecutive periods on two regular days
              const regularDays = ['Sunday', 'Tuesday', 'Thursday'];
              const dayIndex = regularDays.indexOf(day);
              const firstDay = regularDays[dayIndex];
              const secondDay = regularDays[(dayIndex + 1) % 3];

              createSingleSession(firstDay, period);
              createSingleSession(firstDay, period + 1);
              createSingleSession(secondDay, period);
              createSingleSession(secondDay, period + 1);
            } else if (day === 'Monday' || day === 'Wednesday') {
              // Schedule on two long days and one regular day
              createSingleSession('Monday', period);
              createSingleSession('Wednesday', period);
              createSingleSession('Thursday', period);
            }
          }
          // For undergraduate courses with 5 contact hours
          else if (!isPostgraduate && courseType === 'Theory' && contactHours === 5) {
            if (day === 'Sunday' || day === 'Tuesday' || day === 'Thursday') {
              // Pattern 2-2-1: 2 consecutive periods on Sun, 2 on Tue, 1 on Thu
              createSingleSession('Sunday', period);
              createSingleSession('Sunday', period + 1);
              createSingleSession('Tuesday', period);
              createSingleSession('Tuesday', period + 1);
              createSingleSession('Thursday', period);
            } else if (day === 'Monday' || day === 'Wednesday') {
              // Two long days and one regular day with 2 consecutive periods
              createSingleSession('Monday', period);
              createSingleSession('Wednesday', period);
              createSingleSession('Thursday', period);
              createSingleSession('Thursday', period + 1);
            }
          } else {
            // For other cases, just create a single session
            createSingleSession(day, period);
          }
        } else {
          // No matching patterns found, create a single session
          console.log(`No matching patterns found for ${course.courseCode}`);
          createSingleSession(day, period);
        }
      } else {
        // If pattern rule is not enabled, fall back to the original behavior
        if ((day === 'Sunday' || day === 'Monday') && contactHours === 3) {
          if (day === 'Sunday') {
            // Create 3 session cards: one each on Sunday, Tuesday, and Thursday
            createSingleSession('Sunday', period);
            createSingleSession('Tuesday', period);
            createSingleSession('Thursday', period);
          } else if (day === 'Monday') {
            // Create 2 session cards: one on Monday and one on Wednesday
            createSingleSession('Monday', period);
            createSingleSession('Wednesday', period);
          }
        } else if ((day === 'Sunday' || day === 'Monday') && contactHours === 4) {
          if (day === 'Sunday') {
            // Create 4 session cards: 2 consecutive on Sunday, 2 consecutive on Tuesday
            createSingleSession('Sunday', period);
            createSingleSession('Sunday', period + 1);
            createSingleSession('Tuesday', period);
            createSingleSession('Tuesday', period + 1);
          } else if (day === 'Monday') {
            // Create 3 session cards: one on Monday, one on Wednesday, and one on Thursday
            createSingleSession('Monday', period);
            createSingleSession('Wednesday', period);
            createSingleSession('Thursday', period);
          }
        } else if ((day === 'Sunday' || day === 'Monday') && contactHours === 5) {
          if (day === 'Sunday') {
            // Create 5 session cards: 2 consecutive on Sunday, 2 consecutive on Monday, 1 on Thursday
            createSingleSession('Sunday', period);
            createSingleSession('Sunday', period + 1);
            createSingleSession('Monday', period);
            createSingleSession('Monday', period + 1);
            createSingleSession('Thursday', period);
          } else if (day === 'Monday') {
            // Create 4 session cards: one on Monday, one on Wednesday, 2 consecutive on Thursday
            createSingleSession('Monday', period);
            createSingleSession('Wednesday', period);
            createSingleSession('Thursday', period);
            createSingleSession('Thursday', period + 1);
          }
        } else {
          // For other cases, just create a single session
          const isLongDay = ['Monday', 'Wednesday'].includes(day);
          const hoursToAdd = isLongDay ? 1.5 : 1.0;

          if (hoursToAdd <= contactHours) {
            createSingleSession(day, period);
          } else {
            console.log(`Cannot schedule ${hoursToAdd} hours for a course with ${contactHours} contact hours`);
          }
        }
      }
    } else {
      // For other cases (not fully unscheduled), check if adding this session would exceed contact hours
      const isLongDay = ['Monday', 'Wednesday'].includes(day);
      const hoursToAdd = isLongDay ? 1.5 : 1.0;

      if (section.scheduledHours + hoursToAdd <= section.totalHours) {
        createSingleSession(day, period);
      } else {
        console.log(`Adding this session would exceed the ${section.totalHours} contact hours for this section`);
      }
    }

    // Update the section
    updateSection({
      ...section
    }, currentSemester);
  };

  // Assign a lecturer to a session
  const assignLecturerToSession = (lecturerId: string, sessionId: string) => {
    const session = sessions[currentSemester].find(s => s.id === sessionId);
    if (!session) return;

    // Find the section and course
    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return;

    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return;

    // Find the lecturer
    const lecturer = lecturers.find(l => l.id === lecturerId);
    if (!lecturer) return;

    // Check if this lecturer is already assigned to this session
    if (session.lecturerIds && session.lecturerIds.includes(lecturerId)) return;

    // Update the session with the lecturer ID
    const updatedSession = {
      ...session,
      // If lecturerIds exists, add the new lecturer, otherwise create a new array
      lecturerIds: session.lecturerIds ? [...session.lecturerIds, lecturerId] : [lecturerId],
      // Keep the old lecturerId field for backward compatibility
      lecturerId: session.lecturerId || lecturerId
    };

    // Update the session in context
    updateSession(updatedSession, currentSemester);

    // Find all sessions for this section
    const sectionSessions = sessions[currentSemester].filter(
      s => s.sectionId === session.sectionId
    );

    // Update all sessions for this section with this lecturer
    sectionSessions.forEach(sectionSession => {
      if (sectionSession.id !== sessionId) { // Skip the session we already updated
        const updatedLecturerIds = sectionSession.lecturerIds
          ? [...sectionSession.lecturerIds, lecturerId]
          : [lecturerId];

        // Remove duplicates
        const uniqueLecturerIds = [...new Set(updatedLecturerIds)];

        const updatedSectionSession = {
          ...sectionSession,
          lecturerIds: uniqueLecturerIds,
          lecturerId: sectionSession.lecturerId || lecturerId
        };

        // Update session in context
        updateSession(updatedSectionSession, currentSemester);
      }
    });

    // Update lecturer's courses able to teach if not already included
    if (!lecturer.coursesAbleToTeach.includes(course.courseCode)) {
      const updatedLecturer = {
        ...lecturer,
        coursesAbleToTeach: [...lecturer.coursesAbleToTeach, course.courseCode]
      };

      updateLecturer(updatedLecturer);
    }
  };

  // Remove a lecturer from a session
  const removeLecturerFromSession = (lecturerId: string, sessionId: string) => {
    const session = sessions[currentSemester].find(s => s.id === sessionId);
    if (!session) return;

    // If using the new lecturerIds array
    if (session.lecturerIds && session.lecturerIds.includes(lecturerId)) {
      // Update the session to remove the lecturer
      const updatedSession = {
        ...session,
        lecturerIds: session.lecturerIds.filter(id => id !== lecturerId),
        // Update the lecturerId field if it matches the removed lecturer
        lecturerId: session.lecturerId === lecturerId ?
          (session.lecturerIds.filter(id => id !== lecturerId)[0] || '') :
          session.lecturerId
      };

      // Update session in context
      updateSession(updatedSession, currentSemester);
    }
    // For backward compatibility with the old lecturerId field
    else if (session.lecturerId === lecturerId) {
      // Update the session to remove the lecturer
      const updatedSession = {
        ...session,
        lecturerId: '',
        lecturerIds: []
      };

      // Update session in context
      updateSession(updatedSession, currentSemester);
    }
  };

  // Check if a session can be dropped at a specific day and period
  const canDropSessionAt = (sessionId: string, day: string, period: number): boolean => {
    const session = sessions[currentSemester].find(s => s.id === sessionId);
    if (!session) return false;

    // Don't allow dropping on the same position
    if (session.day === day && session.startPeriod === period) return false;

    // Find the section
    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return false;

    // Find the course
    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return false;

    // Check if this is a system-blocked timeslot
    const systemBlockedCheck = isSystemBlockedTimeslot(day, period);
    if (systemBlockedCheck.blocked) {
      return false;
    }

    // Check if this is a user-defined break
    const userBreakCheck = isUserDefinedBreak(day, period, false); // false means respect rule status
    if (userBreakCheck.blocked) {
      return false;
    }

    // Check if there's already a session for this section at this day and period
    const existingSession = sessions[currentSemester].find(
      s => s.id !== sessionId && // Not the same session
          s.sectionId === session.sectionId && // Same section
          s.day === day && // Same day
          period >= s.startPeriod && period <= s.endPeriod // Period overlap
    );

    return !existingSession;
  };

  // Move a session to a new day and period
  const moveSession = (sessionId: string, newDay: string, newPeriod: number) => {
    const session = sessions[currentSemester].find(s => s.id === sessionId);
    if (!session) return;

    // Find the section
    const section = sections[currentSemester].find(s => s.id === session.sectionId);
    if (!section) return;

    // Find the course
    const course = courses[currentSemester].find(c => c.id === section.courseId);
    if (!course) return;

    // Check if the new position is a system-blocked timeslot
    const systemBlockedCheck = isSystemBlockedTimeslot(newDay, newPeriod);
    if (systemBlockedCheck.blocked) {
      console.log(`Cannot move session to system-blocked period ${newPeriod} on ${newDay}`);
      return;
    }

    // Check if the new position is a user-defined break
    const userBreakCheck = isUserDefinedBreak(newDay, newPeriod, false); // false means respect rule status
    if (userBreakCheck.blocked) {
      console.log(`Cannot move session to user-defined break period ${newPeriod} on ${newDay}`);
      return;
    }


    // Calculate hours to subtract from the old position
    const oldHoursValue = ['Monday', 'Wednesday'].includes(session.day) ? 1.5 : 1.0;

    // Calculate hours to add at the new position
    const newHoursValue = ['Monday', 'Wednesday'].includes(newDay) ? 1.5 : 1.0;

    // Calculate the net change in hours
    const hoursDifference = newHoursValue - oldHoursValue;

    // Update the session with the new day and period
    // Calculate timeOfDay based on the period (1-6 morning, 7-12 evening)
    const isMorning = newPeriod <= 6;
    const timeOfDay = isMorning ? 'morning' as const : 'evening' as const;

    const updatedSession = {
      ...session,
      day: newDay,
      startPeriod: newPeriod,
      endPeriod: newPeriod,
      timeOfDay: timeOfDay
    };

    // Update the session
    updateSession(updatedSession, currentSemester);

    // Update the section's scheduled hours
    const updatedSection = {
      ...section,
      scheduledHours: Math.max(0, section.scheduledHours + hoursDifference)
    };

    // Update the section
    updateSection(updatedSection, currentSemester);
  };

  // Context value
  const contextValue: DragDropContextType = {
    isDragging,
    dragItem,
    setDragItem: handleSetDragItem,
    createSessionFromSection,
    assignLecturerToSession,
    removeLecturerFromSession,
    moveSession,
    canDropSectionAt,
    canDropLecturerAt,
    canDropSessionAt,
    isSectionFullyScheduled
  };

  return (
    <DragDropContext.Provider value={contextValue}>
      {children}
    </DragDropContext.Provider>
  );
};

// Custom hook to use the drag-drop context
export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (context === undefined) {
    throw new Error('useDragDrop must be used within a DragDropProvider');
  }
  return context;
};
