export interface Rule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  category: 'course' | 'lecturer';
}

// Retry mechanism configuration
export interface RetryConfiguration {
  enabled: boolean; // whether to enable retry mechanism
  maxRetries: number; // maximum number of retry attempts
  minSuccessRate: number; // minimum acceptable success rate (0-1)
  timeoutMs: number; // maximum time to spend on retries
  enableCPSRefinement?: boolean; // whether to enable CPS refinement system
}

export interface RuleSystemSettings {
  rules: Rule[];
  maxSessionsPerTimeslot: Record<string, number>; // day-period -> max sessions
  maxSessionsPerDay: Record<string, number>; // dayType -> max sessions
  userDefinedBreaks: string[]; // day-period identifiers
  maxPostgradLevelPerDay: number; // max same postgrad level sections per day
  maxPostgradCoursePerDay: number; // max same postgrad course sections per day
  maxPostgradCoursesPerDay: number; // max different postgrad courses of same level per day
  maxGap4thYear: number; // max gap between 4th-year sessions
  maxGap3rdYear: number; // max gap between 3rd-year sessions
  assignLecturersInAutoScheduling: boolean; // whether to assign lecturers during auto-scheduling
  retryConfiguration: RetryConfiguration; // retry mechanism settings
}

export const defaultRules: Rule[] = [
  // Course/sections/timeslot rules
  {
    id: 'block-break-timeslots',
    name: 'Don\'t schedule in blocked/break time slots',
    description: 'Prevents scheduling in system blocked or user-defined break slots',
    enabled: true,
    priority: 1,
    category: 'course'
  },
  {
    id: 'limit-postgrad-level-per-day',
    name: 'Limit same postgraduate level sections per day',
    description: 'Don\'t allow more than a user-defined number of the same postgraduate level (Diploma, Master, PhD) of the same gender to be scheduled on the same day',
    enabled: true,
    priority: 2,
    category: 'course'
  },
  {
    id: 'limit-postgrad-course-per-day',
    name: 'Limit same postgraduate course sections per day',
    description: 'Don\'t allow more than a user-defined number of the same postgraduate course sections to be scheduled on the same day',
    enabled: true,
    priority: 2,
    category: 'course'
  },
  {
    id: 'distribute-postgrad-courses',
    name: 'Distribute postgraduate courses across days',
    description: 'Distribute postgraduate courses of the same level evenly across the week with no more than 2 courses of the same level per day',
    enabled: true,
    priority: 2,
    category: 'course'
  },
  {
    id: 'no-duplicate-sessions',
    name: 'Don\'t duplicate sessions in same time slot',
    description: 'Prevents scheduling multiple sessions of the same section in one time slot',
    enabled: true,
    priority: 2,
    category: 'course'
  },
  {
    id: 'no-same-course-gender-overlap',
    name: 'No same course-gender overlap per timeslot',
    description: 'Prevent two or more sections of the same course and gender from being scheduled in the same timeslot',
    enabled: true,
    priority: 2,
    category: 'course'
  },
  {
    id: 'undergrad-theory-max-per-slot',
    name: 'Respect max undergrad theory sessions per slot',
    description: 'Don\'t exceed maximum allowed undergraduate theory sessions in a time slot',
    enabled: true,
    priority: 2,
    category: 'course'
  },

  {
    id: 'undergrad-2ch-pattern',
    name: 'Follow pattern for 2CH undergrad courses',
    description: 'Schedule 2CH courses over two regular days at the same time',
    enabled: true,
    priority: 3,
    category: 'course'
  },
  {
    id: 'undergrad-3ch-pattern',
    name: 'Follow pattern for 3CH undergrad courses',
    description: 'Schedule 3CH courses over three regular days or two long days',
    enabled: true,
    priority: 3,
    category: 'course'
  },
  {
    id: 'undergrad-4ch-pattern',
    name: 'Follow pattern for 4CH undergrad courses',
    description: 'Schedule 4CH courses according to the defined patterns',
    enabled: true,
    priority: 3,
    category: 'course'
  },
  {
    id: 'undergrad-5ch-pattern',
    name: 'Follow pattern for 5CH undergrad courses',
    description: 'Schedule 5CH courses over at least three days in specified patterns',
    enabled: true,
    priority: 3,
    category: 'course'
  },
  {
    id: 'postgrad-pattern',
    name: 'Follow specific periods for postgraduate courses',
    description: 'Schedule postgraduate courses in periods 10-12 on regular days or 9-10 on long days',
    enabled: true,
    priority: 2,
    category: 'course'
  },
  {
    id: 'max-sessions-per-day',
    name: 'Respect max sessions per day',
    description: 'Don\'t exceed maximum undergraduate theory sessions per day',
    enabled: false,
    priority: 4,
    category: 'course'
  },
  {
    id: 'no-overlap-4th-year',
    name: 'No overlap for 4th-year courses',
    description: 'Sessions of 4th-year academic level theory courses should not overlap',
    enabled: true,
    priority: 5,
    category: 'course'
  },
  {
    id: 'no-overlap-3rd-year',
    name: 'No overlap for 3rd-year courses',
    description: 'Sessions of 3rd-year academic level theory courses should not overlap',
    enabled: true,
    priority: 5,
    category: 'course'
  },
  {
    id: 'no-overlap-2nd-year',
    name: 'No overlap for 2nd-year courses',
    description: 'Sessions of 2nd-year academic level theory courses should not overlap',
    enabled: true,
    priority: 5,
    category: 'course'
  },
  {
    id: 'no-overlap-1st-year',
    name: 'No overlap for 1st-year courses',
    description: 'Sessions of 1st-year academic level theory courses should not overlap',
    enabled: false,
    priority: 5,
    category: 'course'
  },
  {
    id: 'max-gap-4th-year',
    name: 'Maximum gap for 4th-year courses',
    description: 'Maximum period gap between sessions of 4th-year courses of the same gender',
    enabled: true,
    priority: 6,
    category: 'course'
  },
  {
    id: 'max-gap-3rd-year',
    name: 'Maximum gap for 3rd-year courses',
    description: 'Maximum period gap between sessions of 3rd-year courses of the same gender',
    enabled: true,
    priority: 6,
    category: 'course'
  },

  // Lecturer rules
  {
    id: 'no-lecturer-duplicate',
    name: 'No duplicate lecturer per timeslot',
    description: 'Prevent scheduling the same lecturer in multiple sessions at the same time',
    enabled: true,
    priority: 1,
    category: 'lecturer'
  },
  {
    id: 'lecturer-capability',
    name: 'Respect lecturer capabilities',
    description: 'Don\'t assign lecturer to courses they are unable to teach',
    enabled: true,
    priority: 2,
    category: 'lecturer'
  },
  {
    id: 'lecturer-preferred-time',
    name: 'Consider lecturer preferred teaching time',
    description: 'Schedule according to lecturer preferred time of day (morning/evening)',
    enabled: true,
    priority: 3,
    category: 'lecturer'
  },
  {
    id: 'lecturer-max-days',
    name: 'Maximum teaching days per week',
    description: 'Respect maximum number of teaching days per week for each lecturer',
    enabled: true,
    priority: 4,
    category: 'lecturer'
  },
  {
    id: 'lecturer-max-consecutive',
    name: 'Maximum consecutive periods',
    description: 'Respect maximum consecutive teaching periods for each lecturer',
    enabled: true,
    priority: 5,
    category: 'lecturer'
  },
  {
    id: 'lecturer-max-gap',
    name: 'Maximum empty hours per day',
    description: 'Respect maximum empty hours between first and last session in a day for each lecturer',
    enabled: true,
    priority: 6,
    category: 'lecturer'
  },
  {
    id: 'lecturer-max-load',
    name: 'Maximum semester load',
    description: 'Respect maximum semester teaching load for each lecturer',
    enabled: true,
    priority: 5,
    category: 'lecturer'
  }
];