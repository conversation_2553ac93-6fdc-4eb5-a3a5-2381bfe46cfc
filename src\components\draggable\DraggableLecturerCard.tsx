import React, { useRef } from 'react';
import { useDragDrop } from '../../context/DragDropContext';
import { Lecturer } from '../../types/models';
import { Tooltip, IconButton, Box, LinearProgress, Typography, Collapse } from '@mui/material';
import { getArabicTextClass, getArabicFontFamily } from '../../utils/arabicUtils';

// Import icons
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import FilterListIcon from '@mui/icons-material/FilterList';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';

interface DraggableLecturerCardProps {
  lecturer: Lecturer;
  assignedCourses: Array<{ code: string; color: string }>;
  onEdit?: (lecturer: Lecturer, event: React.MouseEvent) => void;
  onDelete?: (lecturerId: string, event: React.MouseEvent) => void;
  onShowTimetable?: (lecturer: Lecturer, event: React.MouseEvent) => void;
  onFilter?: (lecturer: Lecturer, event: React.MouseEvent) => void;
  isFiltered?: boolean;
  expanded?: boolean;
  onToggleExpand?: () => void;
  supervisionHours: number;
  semesterLoad: number;
  maxSemesterLoad: number;
  yearlyLoad: number;
  teachingDaysCount?: number;
  maxGapBetweenSessions?: number;
}

/**
 * DraggableLecturerCard component that makes a lecturer card draggable
 * Used in the LecturersPanel to allow dragging lecturers to session cards
 * Includes all lecturer information and action buttons
 */
const DraggableLecturerCard: React.FC<DraggableLecturerCardProps> = ({
  lecturer,
  assignedCourses,
  onEdit,
  onDelete,
  onShowTimetable,
  onFilter,
  isFiltered = false,
  expanded = true,
  onToggleExpand,
  supervisionHours,
  semesterLoad,
  maxSemesterLoad,
  yearlyLoad,
  teachingDaysCount = 0,
  maxGapBetweenSessions = 0
}) => {
  // Get drag-drop context
  const { setDragItem } = useDragDrop();

  // Create a ref for the drag handle
  const dragHandleRef = useRef<HTMLDivElement>(null);

  // Handle drag start
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'LECTURER',
      id: lecturer.id
    }));

    // Set drag image (optional)
    const dragImage = document.createElement('div');
    dragImage.className = 'p-2 rounded-md shadow-md bg-white dark:bg-gray-800';
    dragImage.style.width = '120px';
    dragImage.style.height = '40px';
    dragImage.textContent = `${lecturer.firstName} ${lecturer.lastName}`;
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 60, 20);
    setTimeout(() => document.body.removeChild(dragImage), 0);

    // Update drag context
    setDragItem({
      type: 'LECTURER',
      id: lecturer.id,
      data: lecturer
    });
  };

  // Handle drag end
  const handleDragEnd = () => {
    // Clear drag context
    setDragItem(null);
  };

  // Get load percentage for progress bar
  const getLoadPercentage = (current: number, max: number) => {
    return Math.round((current / max) * 100);
  };

  // Define color constants for different load statuses
  const LOAD_COLORS = {
    // Overloaded (>100%)
    overloaded: {
      main: '#ef4444', // red
      light: 'rgba(239, 68, 68, 0.2)', // light red background - more distinct
      text: 'rgb(153, 27, 27)', // dark red text
      muiColor: 'error'
    },
    // Fully loaded (>=90%)
    full: {
      main: '#10b981', // green
      light: 'rgba(16, 185, 129, 0.2)', // light green background - more distinct
      text: 'rgb(6, 95, 70)', // dark green text
      muiColor: 'success'
    },
    // Under-loaded (<90%)
    under: {
      main: '#f97316', // orange
      light: 'rgba(249, 115, 22, 0.2)', // light orange background - more distinct
      text: 'rgb(154, 52, 18)', // dark orange text
      muiColor: 'warning'
    }
  };

  // Get load status colors based on percentage
  const getLoadStatusColors = (percentage: number) => {
    if (percentage > 100) return LOAD_COLORS.overloaded;
    if (percentage >= 90) return LOAD_COLORS.full;
    return LOAD_COLORS.under;
  };

  // Get Tailwind classes for load status
  const getLoadStatusClasses = (percentage: number) => {
    if (percentage > 100) return 'bg-red-100 dark:bg-red-900/40 text-red-800 dark:text-red-200';
    if (percentage >= 90) return 'bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-200';
    return 'bg-orange-100 dark:bg-orange-900/40 text-orange-800 dark:text-orange-200';
  };

  // Get Tailwind classes for yearly load pill in header
  const getYearlyLoadPillClasses = (percentage: number) => {
    if (percentage > 100) return 'bg-red-600 text-white border border-red-600'; // Full red with white text
    if (percentage >= 90) return 'bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-200';
    return 'bg-orange-100 dark:bg-orange-900/40 text-orange-800 dark:text-orange-200';
  };

  // Get Tailwind classes for semester load pill
  const getSemesterLoadPillClasses = (percentage: number) => {
    if (percentage > 100) return 'bg-transparent text-red-600 border border-red-600'; // Transparent with red border and text
    if (percentage >= 90) return 'bg-green-100 dark:bg-green-900/40 text-green-800 dark:text-green-200';
    return 'bg-orange-100 dark:bg-orange-900/40 text-orange-800 dark:text-orange-200';
  };

  // Calculate percentages
  const semesterPercentage = getLoadPercentage(semesterLoad, maxSemesterLoad);
  const yearlyPercentage = getLoadPercentage(yearlyLoad, lecturer.maxYearLoad);

  // Determine if supervision hours should be shown (hide for Summer semester)
  const showSupervisionHours = supervisionHours > 0 && lecturer.supervisionHoursFall + lecturer.supervisionHoursSpring > 0;

  return (
    <div
      className="rounded border border-gray-300 dark:border-gray-600 bg-amber-50 dark:bg-gray-800 overflow-hidden cursor-grab active:cursor-grabbing shadow-md"
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {/* Upper part: Name and action buttons */}
      <div
        className={`p-1 flex flex-col cursor-pointer py-1 ${getLoadStatusClasses(semesterPercentage)}`}
        onClick={onToggleExpand}
      >
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <div
              ref={dragHandleRef}
              className="drag-handle mr-1 cursor-grab text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
            >
              <DragIndicatorIcon fontSize="small" sx={{ fontSize: '1rem' }} />
            </div>
            <Typography
              variant="subtitle2"
              className={`font-medium ${getArabicTextClass(`${lecturer.firstName} ${lecturer.lastName || ''}`)}`}
              sx={{
                fontFamily: getArabicFontFamily(`${lecturer.firstName} ${lecturer.lastName || ''}`) || undefined
              }}
            >
              {lecturer.firstName} {lecturer.lastName}
            </Typography>
            {expanded ?
              <ExpandLessIcon fontSize="small" className="ml-1 text-gray-500" sx={{ fontSize: '0.85rem' }} /> :
              <ExpandMoreIcon fontSize="small" className="ml-1 text-gray-500" sx={{ fontSize: '0.85rem' }} />
            }
          </div>

          {/* First row of action buttons */}
          <div className="flex items-center space-x-0.5">
            {onEdit && (
              <Tooltip title="Edit lecturer">
                <IconButton
                  size="small"
                  className="text-gray-600 dark:text-gray-300"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit(lecturer, e);
                  }}
                >
                  <EditIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                </IconButton>
              </Tooltip>
            )}

            {onDelete && (
              <Tooltip title="Delete lecturer">
                <IconButton
                  size="small"
                  className="text-gray-600 dark:text-gray-300"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(lecturer.id, e);
                  }}
                >
                  <DeleteIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                </IconButton>
              </Tooltip>
            )}
          </div>
        </div>

        {/* Second row with yearly load pill and action icons */}
        <div className="flex justify-between items-center mt-0.5">
          <span className={`inline-flex px-2 py-0.5 rounded text-xs font-medium ${getYearlyLoadPillClasses(yearlyPercentage)} ${semesterPercentage > 100 ? 'border border-red-600' : ''}`}>
            {`${yearlyLoad % 1 === 0 ? yearlyLoad.toFixed(0) : yearlyLoad}/${lecturer.maxYearLoad}`}
          </span>

          <div className="flex items-center space-x-0.5">
            {onFilter && (
              <Tooltip title={isFiltered ? "Clear filter" : "Filter timetable to show only this lecturer's sessions"}>
                <IconButton
                  size="small"
                  sx={{
                    color: isFiltered ? 'primary.main' : 'text.secondary',
                    position: 'relative'
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onFilter(lecturer, e);
                  }}
                >
                  <FilterListIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                  {isFiltered && (
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: 6,
                        height: 6,
                        bgcolor: 'error.main',
                        borderRadius: '50%'
                      }}
                    />
                  )}
                </IconButton>
              </Tooltip>
            )}

            {onShowTimetable && (
              <Tooltip title="Show lecturer timetable">
                <IconButton
                  size="small"
                  className="text-gray-600 dark:text-gray-300"
                  onClick={(e) => {
                    e.stopPropagation();
                    onShowTimetable(lecturer, e);
                  }}
                >
                  <CalendarMonthIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
                </IconButton>
              </Tooltip>
            )}
          </div>
        </div>
      </div>

      {/* Lower part: Collapsible info section */}
      <Collapse in={expanded}>
        <div className="p-2 bg-amber-50 dark:bg-gray-900 space-y-2">
          {/* Department - only show if there is a department */}
          {lecturer.department && (
            <div>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.3 }}>
                <Typography variant="body2" className="font-medium">
                  Department:
                </Typography>
                <span className="min-w-[65px] px-2 py-0.5 rounded text-xs font-medium flex justify-center items-center bg-purple-100 text-purple-800 dark:bg-purple-900/40 dark:text-purple-200">
                  {lecturer.department}
                </span>
              </Box>
            </div>
          )}

          {/* Supervision load - only show if there are supervision hours */}
          {showSupervisionHours && (
            <div>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.3 }}>
                <Typography variant="body2" className="font-medium">
                  Supervision:
                </Typography>
                <span className="min-w-[65px] px-2 py-0.5 rounded text-xs font-medium flex justify-center items-center bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200">
                  {`${supervisionHours % 1 === 0 ? supervisionHours.toFixed(0) : supervisionHours}`} hrs
                </span>
              </Box>
            </div>
          )}

          {/* Semester workload with progress bar */}
          <div>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.3 }}>
              <Typography variant="body2" className="font-medium">
                Sem Load:
              </Typography>
              <span className={`min-w-[65px] px-2 py-0.5 rounded text-xs font-medium flex justify-center items-center ${getSemesterLoadPillClasses(semesterPercentage)}`}>
                {semesterLoad}/{maxSemesterLoad}
              </span>
            </Box>
            <Box sx={{ width: '100%', mb: 1.5 }}>
              <LinearProgress
                variant="determinate"
                value={semesterPercentage > 100 ? 100 : semesterPercentage}
                color={getLoadStatusColors(semesterPercentage).muiColor as 'success' | 'warning' | 'error'}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)'
                }}
              />
            </Box>
          </div>

          {/* Yearly workload with progress bar */}
          <div>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.3 }}>
              <Typography variant="body2" className="font-medium">
                Yearly Load:
              </Typography>
              <span className={`min-w-[65px] px-2 py-0.5 rounded text-xs font-medium flex justify-center items-center ${getYearlyLoadPillClasses(yearlyPercentage)}`}>
                {yearlyLoad}/{lecturer.maxYearLoad}
              </span>
            </Box>
            <Box sx={{ width: '100%', mb: 1.5 }}>
              <LinearProgress
                variant="determinate"
                value={yearlyPercentage > 100 ? 100 : yearlyPercentage}
                color={getLoadStatusColors(yearlyPercentage).muiColor as 'success' | 'warning' | 'error'}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(0, 0, 0, 0.1)'
                }}
              />
            </Box>
          </div>

          {/* Assigned courses - removed label but kept functionality */}
          {assignedCourses.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {assignedCourses.map((course, index) => (
                <span
                  key={index}
                  className="px-1 py-0.5 rounded-full text-xs font-medium text-white"
                  style={{
                    backgroundColor: course.color,
                    fontSize: '0.65rem',
                    height: '18px',
                    display: 'inline-flex',
                    alignItems: 'center'
                  }}
                >
                  {course.code}
                </span>
              ))}
            </div>
          )}

          {/* Teaching days and max gap information */}
          <div className="flex justify-between items-center mt-2">
            {/* Teaching days count */}
            <Tooltip title="Teaching days out of the maximum days set for the lecturer in the lecturer add/edit modal">
              <span
                className={`px-2 py-0.5 rounded text-xs font-medium flex justify-center items-center ${
                  (teachingDaysCount || 0) > (lecturer.maxTeachingDaysPerWeek || 5)
                    ? 'bg-red-600 text-white'
                    : (teachingDaysCount || 0) > 0
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                }`}
              >
                Days: {isNaN(teachingDaysCount) ? 0 : teachingDaysCount}/{lecturer.maxTeachingDaysPerWeek || 5}
              </span>
            </Tooltip>

            {/* Maximum empty hours per day */}
            <Tooltip title="Maximum empty hours between first and last session in a day out of the maximum empty hours set in the lecturer add/edit modal">
              <span
                className={`px-2 py-0.5 rounded text-xs font-medium flex justify-center items-center ${
                  (maxGapBetweenSessions || 0) > (lecturer.maxGapBetweenPeriods || 2)
                    ? 'bg-red-600 text-white'
                    : 'bg-green-600 text-white'
                }`}
              >
                Hrs: {isNaN(maxGapBetweenSessions) || maxGapBetweenSessions === null ? 0 : maxGapBetweenSessions.toFixed(1)}/{lecturer.maxGapBetweenPeriods || 2}
              </span>
            </Tooltip>
          </div>
        </div>
      </Collapse>
    </div>
  );
};

export default DraggableLecturerCard;