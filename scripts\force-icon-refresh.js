#!/usr/bin/env node

/**
 * QU Scheduler Force Icon Refresh Script
 * 
 * This script forces Windows to refresh all icon caches and recreate shortcuts
 * to ensure the correct embedded icon is displayed everywhere.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

console.log('🔄 QU Scheduler Force Icon Refresh');
console.log('==================================');

function clearWindowsIconCache() {
    console.log('\n🧹 Clearing Windows icon cache...');
    
    const commands = [
        // Stop Windows Explorer
        'taskkill /f /im explorer.exe',
        
        // Clear icon cache files
        'del /a /q "%localappdata%\\IconCache.db"',
        'del /a /f /q "%localappdata%\\Microsoft\\Windows\\Explorer\\iconcache*"',
        'del /a /f /q "%localappdata%\\Microsoft\\Windows\\Explorer\\thumbcache*"',
        
        // Clear additional cache locations
        'del /a /f /q "%appdata%\\Microsoft\\Windows\\Recent\\*"',
        'del /a /f /q "%temp%\\*" 2>nul',
        
        // Restart Windows Explorer
        'start explorer.exe'
    ];
    
    for (const command of commands) {
        try {
            console.log(`📋 Running: ${command}`);
            execSync(command, { stdio: 'pipe' });
            console.log('✅ Success');
        } catch (error) {
            console.log(`⚠️  Warning: ${error.message.split('\n')[0]}`);
        }
    }
}

function forceRegistryRefresh() {
    console.log('\n🔧 Forcing registry icon refresh...');
    
    const commands = [
        // Refresh shell icon cache
        'ie4uinit.exe -show',
        'ie4uinit.exe -ClearIconCache',
        
        // Refresh file associations
        'sfc /scannow',
        
        // Refresh desktop
        'rundll32.exe user32.dll,UpdatePerUserSystemParameters'
    ];
    
    for (const command of commands) {
        try {
            console.log(`📋 Running: ${command}`);
            execSync(command, { stdio: 'pipe', timeout: 10000 });
            console.log('✅ Success');
        } catch (error) {
            console.log(`⚠️  Warning: ${error.message.split('\n')[0]}`);
        }
    }
}

function recreateDesktopShortcut() {
    console.log('\n🔗 Recreating desktop shortcut...');
    
    try {
        const desktopPath = path.join(os.homedir(), 'Desktop');
        const shortcutPath = path.join(desktopPath, 'QU Scheduler.lnk');
        const executablePath = path.resolve('out/QU Scheduler-win32-x64/qu-scheduler.exe');
        
        // Delete existing shortcut
        if (fs.existsSync(shortcutPath)) {
            fs.unlinkSync(shortcutPath);
            console.log('✅ Deleted old desktop shortcut');
        }
        
        // Create new shortcut using PowerShell
        const powershellScript = `
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("${shortcutPath.replace(/\\/g, '\\\\')}")
            $Shortcut.TargetPath = "${executablePath.replace(/\\/g, '\\\\')}"
            $Shortcut.WorkingDirectory = "${path.dirname(executablePath).replace(/\\/g, '\\\\')}"
            $Shortcut.IconLocation = "${executablePath.replace(/\\/g, '\\\\')},0"
            $Shortcut.Description = "QU Scheduler - University Course Scheduling Application"
            $Shortcut.Save()
            Write-Host "Desktop shortcut created successfully"
        `;
        
        execSync(`powershell -Command "${powershellScript}"`, { stdio: 'inherit' });
        console.log('✅ New desktop shortcut created with correct icon reference');
        
    } catch (error) {
        console.error('❌ Failed to recreate desktop shortcut:', error.message);
    }
}

function updateStartMenuShortcuts() {
    console.log('\n📋 Updating Start Menu shortcuts...');
    
    try {
        const startMenuPath = path.join(os.homedir(), 'AppData', 'Roaming', 'Microsoft', 'Windows', 'Start Menu', 'Programs');
        const quSchedulerPath = path.join(startMenuPath, 'QU Scheduler');
        const executablePath = path.resolve('out/QU Scheduler-win32-x64/qu-scheduler.exe');
        
        // Create QU Scheduler folder if it doesn't exist
        if (!fs.existsSync(quSchedulerPath)) {
            fs.mkdirSync(quSchedulerPath, { recursive: true });
            console.log('✅ Created Start Menu folder');
        }
        
        // Remove old shortcuts
        const oldShortcuts = [
            path.join(quSchedulerPath, 'QU Scheduler.lnk'),
            path.join(startMenuPath, 'QU Scheduler.lnk')
        ];
        
        for (const shortcut of oldShortcuts) {
            if (fs.existsSync(shortcut)) {
                fs.unlinkSync(shortcut);
                console.log(`✅ Deleted old shortcut: ${path.basename(shortcut)}`);
            }
        }
        
        // Create new Start Menu shortcut
        const newShortcutPath = path.join(quSchedulerPath, 'QU Scheduler.lnk');
        
        const powershellScript = `
            $WshShell = New-Object -comObject WScript.Shell
            $Shortcut = $WshShell.CreateShortcut("${newShortcutPath.replace(/\\/g, '\\\\')}")
            $Shortcut.TargetPath = "${executablePath.replace(/\\/g, '\\\\')}"
            $Shortcut.WorkingDirectory = "${path.dirname(executablePath).replace(/\\/g, '\\\\')}"
            $Shortcut.IconLocation = "${executablePath.replace(/\\/g, '\\\\')},0"
            $Shortcut.Description = "QU Scheduler - University Course Scheduling Application"
            $Shortcut.Save()
            Write-Host "Start Menu shortcut created successfully"
        `;
        
        execSync(`powershell -Command "${powershellScript}"`, { stdio: 'inherit' });
        console.log('✅ New Start Menu shortcut created with correct icon reference');
        
    } catch (error) {
        console.error('❌ Failed to update Start Menu shortcuts:', error.message);
    }
}

function forceSystemRefresh() {
    console.log('\n🔄 Forcing system-wide refresh...');
    
    const commands = [
        // Refresh desktop
        'rundll32.exe shell32.dll,SHChangeNotify',
        
        // Refresh system image lists
        'rundll32.exe shell32.dll,Control_RunDLL desk.cpl,,0',
        
        // Force icon refresh
        'taskkill /f /im dwm.exe 2>nul || echo DWM restart not needed'
    ];
    
    for (const command of commands) {
        try {
            console.log(`📋 Running: ${command}`);
            execSync(command, { stdio: 'pipe', timeout: 5000 });
            console.log('✅ Success');
        } catch (error) {
            console.log(`⚠️  Warning: ${error.message.split('\n')[0]}`);
        }
    }
}

async function main() {
    console.log('\n🚀 Starting force icon refresh process...');
    console.log('\n⚠️  IMPORTANT: This script will temporarily close Windows Explorer');
    console.log('⚠️  Save any open work before continuing!');
    
    // Wait a moment for user to read the warning
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    try {
        // Step 1: Clear Windows icon cache
        clearWindowsIconCache();
        
        // Wait for Explorer to restart
        console.log('\n⏳ Waiting for Windows Explorer to restart...');
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Step 2: Force registry refresh
        forceRegistryRefresh();
        
        // Step 3: Recreate desktop shortcut
        recreateDesktopShortcut();
        
        // Step 4: Update Start Menu shortcuts
        updateStartMenuShortcuts();
        
        // Step 5: Force system refresh
        forceSystemRefresh();
        
        // Final wait
        console.log('\n⏳ Waiting for changes to take effect...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        console.log('\n🎉 FORCE ICON REFRESH COMPLETE!');
        console.log('===============================');
        console.log('\n📋 What was done:');
        console.log('   ✅ Cleared Windows icon cache files');
        console.log('   ✅ Restarted Windows Explorer');
        console.log('   ✅ Forced registry icon refresh');
        console.log('   ✅ Recreated desktop shortcut with correct icon reference');
        console.log('   ✅ Updated Start Menu shortcuts with correct icon reference');
        console.log('   ✅ Forced system-wide refresh');
        
        console.log('\n📋 Next Steps:');
        console.log('   1. Check desktop shortcut - should show correct QU Scheduler icon');
        console.log('   2. Check Start Menu - should show correct QU Scheduler icon');
        console.log('   3. If still not updated, restart the computer');
        console.log('   4. Test the application to ensure it still works correctly');
        
        console.log('\n✅ The correct QU Scheduler icon should now be visible everywhere!');
        
    } catch (error) {
        console.error('\n❌ FORCE REFRESH FAILED:', error.message);
        console.log('\n🔧 Manual Steps:');
        console.log('   1. Restart the computer');
        console.log('   2. Delete desktop shortcut and recreate it manually');
        console.log('   3. Clear Start Menu cache manually');
        process.exit(1);
    }
}

// Run the force icon refresh
main();
