import type { ForgeConfig } from '@electron-forge/shared-types';
import { MakerSquirrel } from '@electron-forge/maker-squirrel';
import { MakerZIP } from '@electron-forge/maker-zip';
import { MakerDeb } from '@electron-forge/maker-deb';
import { MakerRpm } from '@electron-forge/maker-rpm';
import { VitePlugin } from '@electron-forge/plugin-vite';
import { FusesPlugin } from '@electron-forge/plugin-fuses';
import { FuseV1Options, FuseVersion } from '@electron/fuses';

const config: ForgeConfig = {
  packagerConfig: {
    asar: {
      unpack: '**/assets/icons/**' // Unpack icon assets for proper access
    },
    name: 'QU Scheduler',
    executableName: 'qu-scheduler',
    appBundleId: 'qa.edu.qu.scheduler',
    appCategoryType: 'public.app-category.education',
    appCopyright: 'Copyright © 2025 Qatar University. All rights reserved.',
    appVersion: '1.0.0',
    buildVersion: '1.0.0',
    icon: './assets/icons/icon.ico', // Standardized enhanced QU Scheduler icon
    extraResource: [
      './assets/icons/icon.ico'
    ],
    // Code signing configuration (uncomment when certificate is available)
    // osxSign: {
    //   identity: 'Developer ID Application: Qatar University',
    //   'hardened-runtime': true,
    //   entitlements: 'entitlements.plist',
    //   'entitlements-inherit': 'entitlements.plist',
    //   'signature-flags': 'library'
    // },
    // osxNotarize: {
    //   tool: 'notarytool',
    //   appleId: process.env.APPLE_ID,
    //   appleIdPassword: process.env.APPLE_PASSWORD,
    //   teamId: process.env.APPLE_TEAM_ID,
    // },
    win32metadata: {
      CompanyName: 'Qatar University',
      FileDescription: 'QU Scheduler - Professional Timetable Scheduling Application',
      OriginalFilename: 'qu-scheduler.exe',
      ProductName: 'QU Scheduler',
      InternalName: 'qu-scheduler'
    },
    // Windows code signing (uncomment when certificate is available)
    // windowsSign: {
    //   certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
    //   certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD,
    //   signingHashAlgorithms: ['sha256'],
    //   timestampServer: 'http://timestamp.digicert.com'
    // }
  },
  rebuildConfig: {},
  makers: [
    new MakerSquirrel({
      name: 'qu-scheduler',
      authors: 'Qatar University',
      description: 'Professional timetable scheduling application for Qatar University',
      setupExe: 'QU-Scheduler-Setup.exe',
      setupIcon: './assets/icons/icon.ico',
      iconUrl: './assets/icons/icon.ico',
      noMsi: false, // Allow MSI generation for enterprise deployment
      title: 'QU Scheduler Setup',
      version: '1.0.0',
      copyright: 'Copyright © 2025 Qatar University. All rights reserved.'
    }),
    new MakerZIP({}, ['darwin']),
    new MakerRpm({
      options: {
        name: 'qu-scheduler',
        productName: 'QU Scheduler',
        genericName: 'Timetable Scheduler',
        description: 'Professional timetable scheduling application for Qatar University',
        categories: ['Education', 'Office'],
        homepage: 'https://qu.edu.qa'
      }
    }),
    new MakerDeb({
      options: {
        name: 'qu-scheduler',
        productName: 'QU Scheduler',
        genericName: 'Timetable Scheduler',
        description: 'Professional timetable scheduling application for Qatar University',
        categories: ['Education', 'Office'],
        homepage: 'https://qu.edu.qa'
      }
    })
  ],
  plugins: [
    new VitePlugin({
      // `build` can specify multiple entry builds, which can be Main process, Preload scripts, Worker process, etc.
      // If you are familiar with Vite configuration, it will look really familiar.
      build: [
        {
          // `entry` is just an alias for `build.lib.entry` in the corresponding file of `config`.
          entry: 'src/main.ts',
          config: 'vite.main.config.ts',
          target: 'main',
        },
        {
          entry: 'src/preload.ts',
          config: 'vite.preload.config.ts',
          target: 'preload',
        },
      ],
      renderer: [
        {
          name: 'main_window',
          config: 'vite.renderer.config.ts',
        },
      ],
    }),
    // Fuses are used to enable/disable various Electron functionality
    // at package time, before code signing the application
    new FusesPlugin({
      version: FuseVersion.V1,
      [FuseV1Options.RunAsNode]: false,
      [FuseV1Options.EnableCookieEncryption]: true,
      [FuseV1Options.EnableNodeOptionsEnvironmentVariable]: false,
      [FuseV1Options.EnableNodeCliInspectArguments]: false,
      [FuseV1Options.EnableEmbeddedAsarIntegrityValidation]: true,
      [FuseV1Options.OnlyLoadAppFromAsar]: false,
    }),
  ],
};

export default config;
