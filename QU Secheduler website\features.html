<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Features - QU Scheduler</title>
    <meta name="description" content="Explore the comprehensive features of QU Scheduler including auto-scheduling, Arabic support, constraint satisfaction programming, and advanced timetable management.">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/favicon-16x16.png">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="assets/qu-scheduler-logo.svg" alt="QU Scheduler Logo" class="logo-img">
                <span class="logo-text">QU Scheduler</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="features.html" class="nav-link active">Features</a>
                </li>
                <li class="nav-item">
                    <a href="download.html" class="nav-link">Download</a>
                </li>

            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1 class="page-title">Comprehensive Features</h1>
            <p class="page-description">
                Discover the powerful capabilities that make QU Scheduler the premier choice
                for academic timetable management at Qatar University.
            </p>
        </div>
    </section>

    <!-- Auto-Scheduling Section -->
    <section class="feature-detail">
        <div class="container">
            <div class="feature-detail-content">
                <div class="feature-detail-text">
                    <h2>Intelligent Auto-Scheduling</h2>
                    <h3>Constraint Satisfaction Programming (CSP)</h3>
                    <p>
                        QU Scheduler employs advanced Constraint Satisfaction Programming algorithms to automatically
                        generate optimal timetables. The system considers multiple constraints simultaneously:
                    </p>
                    <ul class="feature-list">
                        <li><strong>Academic Level Prioritization:</strong> PhD → Masters → Diploma → Undergraduate (4th to 1st year)</li>
                        <li><strong>Gender-Separated Scheduling:</strong> Intelligent handling of male and female sections</li>
                        <li><strong>Lecturer Workload Management:</strong> Automatic load balancing and conflict prevention</li>
                        <li><strong>Time Pattern Optimization:</strong> Distributes courses across optimal time slots</li>
                        <li><strong>Hard Constraint Enforcement:</strong> 100% compliance with university scheduling rules</li>
                    </ul>
                    <div class="feature-highlight">
                        <strong>Success Rate:</strong> 100% refinement threshold ensures optimal scheduling results
                    </div>
                </div>
                <div class="feature-detail-image">
                    <img src="assets/images/the application with fully scheduled canvas.png" alt="Auto-Scheduling Interface" class="feature-img">
                </div>
            </div>
        </div>
    </section>

    <!-- Manual Scheduling Section -->
    <section class="feature-detail alternate">
        <div class="container">
            <div class="feature-detail-content">
                <div class="feature-detail-image">
                    <img src="assets/images/The Application with empty canvas.png" alt="Drag and Drop Manual Scheduling" class="feature-img">
                </div>
                <div class="feature-detail-text">
                    <h2>Drag and Drop Manual Scheduling</h2>
                    <h3>Intuitive Visual Timetable Creation</h3>
                    <p>
                        For situations requiring precise manual control, QU Scheduler provides an intuitive
                        drag-and-drop interface that makes manual scheduling effortless and accurate:
                    </p>
                    <ul class="feature-list">
                        <li><strong>Visual Drag and Drop:</strong> Simply drag course sessions to desired time slots with instant visual feedback</li>
                        <li><strong>Real-Time Conflict Detection:</strong> Immediate warnings for scheduling conflicts as you drag</li>
                        <li><strong>Smart Snap-to-Grid:</strong> Sessions automatically align to proper time boundaries</li>
                        <li><strong>Undo/Redo Support:</strong> Full history tracking for easy correction of scheduling mistakes</li>
                        <li><strong>Hybrid Approach:</strong> Combine auto-scheduling with manual fine-tuning for optimal results</li>
                        <li><strong>Visual Feedback:</strong> Color-coded indicators show valid drop zones and conflicts</li>
                    </ul>
                    <div class="feature-highlight">
                        <strong>Perfect Control:</strong> Manual scheduling gives you complete control over specific course placements
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Arabic Support Section -->
    <section class="feature-detail">
        <div class="container">
            <div class="feature-detail-content">
                <div class="feature-detail-image">
                    <img src="assets/images/The Application with empty canvas.png" alt="Arabic Language Support" class="feature-img">
                </div>
                <div class="feature-detail-text">
                    <h2>Complete Arabic Language Support</h2>
                    <h3>Bilingual Interface with Tajawal Font</h3>
                    <p>
                        QU Scheduler provides comprehensive Arabic language support, essential for Qatar University's
                        bilingual academic environment:
                    </p>
                    <ul class="feature-list">
                        <li><strong>Tajawal Font Integration:</strong> Professional Arabic typography for course and lecturer names</li>
                        <li><strong>Right-to-Left (RTL) Support:</strong> Proper text direction handling for Arabic content</li>
                        <li><strong>PDF Export with Arabic:</strong> Embedded fonts ensure perfect rendering in exported documents</li>
                        <li><strong>Mixed Content Handling:</strong> Seamless integration of Arabic and English text</li>
                        <li><strong>Offline Font Support:</strong> No internet required for Arabic text rendering</li>
                    </ul>
                    <div class="feature-highlight arabic-text">
                        <strong>مثال:</strong> يدعم النظام أسماء المقررات والمحاضرين باللغة العربية بشكل كامل
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gender-Separated Scheduling -->
    <section class="feature-detail">
        <div class="container">
            <div class="feature-detail-content">
                <div class="feature-detail-text">
                    <h2>Gender-Separated Scheduling</h2>
                    <h3>Specialized Academic Environment Support</h3>
                    <p>
                        Designed specifically for Qatar University's gender-separated academic structure:
                    </p>
                    <ul class="feature-list">
                        <li><strong>Paired Section Scheduling:</strong> Automatically schedules male and female sections together</li>
                        <li><strong>Lecturer Assignment Logic:</strong> Intelligent assignment considering gender-specific requirements</li>
                        <li><strong>Conflict Prevention:</strong> Prevents scheduling conflicts between same-gender sections</li>
                        <li><strong>Visual Gender Indicators:</strong> Clear icons and color coding for section identification</li>
                        <li><strong>Distribution Analytics:</strong> Comprehensive statistics on gender distribution across courses</li>
                    </ul>
                </div>
                <div class="feature-detail-image">
                    <img src="assets/images/Course Statistics of the App.png" alt="Gender-Separated Interface" class="feature-img">
                </div>
            </div>
        </div>
    </section>

    <!-- Export Capabilities -->
    <section class="feature-detail alternate">
        <div class="container">
            <div class="feature-detail-content">
                <div class="feature-detail-image">
                    <img src="assets/images/the application with fully scheduled canvas.png" alt="Export Capabilities" class="feature-img">
                </div>
                <div class="feature-detail-text">
                    <h2>Professional Export & Sharing</h2>
                    <h3>Multiple Format Support</h3>
                    <p>
                        Export and share timetables in various professional formats:
                    </p>
                    <ul class="feature-list">
                        <li><strong>PDF Export:</strong> High-quality PDFs with embedded Arabic fonts and professional formatting</li>
                        <li><strong>HTML Export:</strong> Web-ready timetables with interactive features</li>
                        <li><strong>JSON Data Export:</strong> Complete data backup and transfer capabilities</li>
                        <li><strong>Email Integration:</strong> Direct email sharing with embedded HTML content</li>
                        <li><strong>Print Optimization:</strong> Browser-based printing with perfect layout preservation</li>
                    </ul>
                    <div class="feature-highlight">
                        <strong>File Naming:</strong> Automatic professional naming conventions for all exports
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lecturer Management -->
    <section class="feature-detail">
        <div class="container">
            <div class="feature-detail-content">
                <div class="feature-detail-text">
                    <h2>Advanced Lecturer Management</h2>
                    <h3>Workload Optimization & Assignment</h3>
                    <p>
                        Comprehensive lecturer management with intelligent workload distribution:
                    </p>
                    <ul class="feature-list">
                        <li><strong>Workload Calculation:</strong> Automatic semester and yearly load tracking</li>
                        <li><strong>Teaching Load Analysis:</strong> Calculates lecturer teaching and supervision load across the academic year</li>
                        <li><strong>Capacity Management:</strong> Prevents overloading with visual indicators</li>
                        <li><strong>Course Eligibility:</strong> Matches lecturers to courses they're qualified to teach</li>
                        <li><strong>Conflict Detection:</strong> Real-time scheduling conflict prevention</li>
                        <li><strong>Statistics Dashboard:</strong> Comprehensive analytics on lecturer utilization</li>
                    </ul>
                </div>
                <div class="feature-detail-image">
                    <img src="assets/images/Course Statistics of the App.png" alt="Lecturer Management" class="feature-img">
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Features -->
    <section class="technical-features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Technical Excellence</h2>
                <p class="section-description">
                    Built with modern technologies and best practices for reliability and performance.
                </p>
            </div>
            <div class="tech-grid">
                <div class="tech-card">
                    <h3>Offline-First Design</h3>
                    <p>Complete functionality without internet dependency. Perfect for secure environments and air-gapped systems.</p>
                </div>
                <div class="tech-card">
                    <h3>Electron Framework</h3>
                    <p>Cross-platform desktop application built with modern web technologies for consistent performance.</p>
                </div>
                <div class="tech-card">
                    <h3>TypeScript & React</h3>
                    <p>Type-safe development with React for robust, maintainable, and scalable user interface.</p>
                </div>
                <div class="tech-card">
                    <h3>Performance Optimized</h3>
                    <p>Efficient algorithms with caching and profiling for handling large datasets and complex scheduling.</p>
                </div>
                <div class="tech-card">
                    <h3>Data Security</h3>
                    <p>Local data storage with secure IPC communication and enterprise-grade security implementation.</p>
                </div>
                <div class="tech-card">
                    <h3>Responsive Design</h3>
                    <p>Adaptive interface that works seamlessly across different screen sizes and resolutions.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Academic Patterns -->
    <section class="academic-patterns">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Academic Pattern Recognition</h2>
                <p class="section-description">
                    Intelligent pattern matching for different course types and academic levels.
                </p>
            </div>
            <div class="patterns-grid">
                <div class="pattern-card">
                    <h3>Undergraduate Theory Courses</h3>
                    <div class="pattern-details">
                        <p><strong>2 Credit Hours:</strong> Two regular days, same time slot</p>
                        <p><strong>3 Credit Hours:</strong> Three regular days or two long days, same time</p>
                        <p><strong>5 Credit Hours:</strong> Distributed across at least 3 days</p>
                    </div>
                </div>
                <div class="pattern-card">
                    <h3>Postgraduate Courses</h3>
                    <div class="pattern-details">
                        <p><strong>3 Credit Hours:</strong> Consecutive sessions on one day</p>
                        <p><strong>Distribution:</strong> Spread across the week to avoid overlaps</p>
                        <p><strong>Gender Limits:</strong> Maximum one same-gender section per day</p>
                    </div>
                </div>
                <div class="pattern-card">
                    <h3>Laboratory Courses</h3>
                    <div class="pattern-details">
                        <p><strong>Exclusion:</strong> Lab courses excluded from auto-scheduling</p>
                        <p><strong>Manual Scheduling:</strong> Requires manual placement for specialized equipment</p>
                        <p><strong>Theory Focus:</strong> Auto-scheduling optimized for theory courses only</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Transform Your Scheduling?</h2>
                <p>
                    Experience the power of intelligent academic scheduling with QU Scheduler.
                    Download now and see how it can streamline your department's timetable management.
                </p>
                <div class="cta-buttons">
                    <a href="download.html" class="btn btn-primary">Download QU Scheduler</a>
                    <a href="mailto:<EMAIL>" class="btn btn-secondary">Contact Developer</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/qu-scheduler-logo.svg" alt="QU Scheduler Logo" class="footer-logo-img">
                        <span class="footer-logo-text">QU Scheduler</span>
                    </div>
                    <p class="footer-description">
                        Professional timetable scheduling application designed specifically
                        for Qatar University's academic departments.
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Features</h4>
                    <ul class="footer-links">
                        <li><a href="#auto-scheduling">Auto-Scheduling</a></li>
                        <li><a href="#manual-scheduling">Manual Scheduling</a></li>
                        <li><a href="#arabic-support">Arabic Support</a></li>
                        <li><a href="#gender-scheduling">Gender Scheduling</a></li>
                        <li><a href="#export-features">Export Features</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <ul class="footer-links">
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a href="https://qu.edu.qa">Qatar University</a></li>
                        <li>Version 1.0.0</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Qatar University. All rights reserved. | Developed by Prof Ayman Saleh</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
