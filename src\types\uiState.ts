// UI State types for Electron store
export interface UIState {
  // Theme and appearance
  darkMode?: boolean;
  zoomFactor?: number;
  
  // Panel visibility
  showCoursesPanel?: boolean;
  showLecturersPanel?: boolean;
  showPgCanvas?: boolean;
  
  // Department and academic info
  departmentName?: string;
  academicYear?: string;
  
  // View settings
  currentView?: string;
  viewMode?: string;
  activeTab?: number;
  
  // Filters
  courseFilter?: string;
  lecturerFilter?: string;
  
  // Expanded states
  expandedCourses?: string[];
  expandedLecturers?: string[];
}

// Type guard to check if an unknown value is a UIState
export function isUIState(value: unknown): value is UIState {
  return value !== null && typeof value === 'object';
}

// Helper function to safely get UI state with defaults
export function getUIStateWithDefaults(uiState: unknown): UIState {
  if (!isUIState(uiState)) {
    return {};
  }
  return uiState;
}
