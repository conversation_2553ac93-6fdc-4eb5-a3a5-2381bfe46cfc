#!/usr/bin/env node

/**
 * QU Scheduler ICO File Fix Script
 * 
 * This script creates a proper Windows ICO file from PNG sources
 * that is compatible with rcedit and Windows executable embedding.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 QU Scheduler ICO File Fix');
console.log('============================');

const iconsDir = path.resolve('assets/icons');
const outputIcoPath = path.resolve('assets/icons/icon-fixed.ico');

function checkPngFiles() {
    console.log('\n🔍 Checking PNG source files...');
    
    const requiredSizes = ['16x16', '32x32', '48x48', '64x64', '128x128', '256x256'];
    const foundFiles = [];
    
    for (const size of requiredSizes) {
        const pngPath = path.join(iconsDir, `icon-${size}.png`);
        if (fs.existsSync(pngPath)) {
            const stats = fs.statSync(pngPath);
            console.log(`✅ ${size}: Found (${(stats.size / 1024).toFixed(1)} KB)`);
            foundFiles.push(pngPath);
        } else {
            console.log(`❌ ${size}: Not found`);
        }
    }
    
    return foundFiles;
}

function installImageMagick() {
    console.log('\n📥 Installing png-to-ico converter...');
    
    try {
        // Install png-to-ico package
        execSync('npm install --no-save png-to-ico', { stdio: 'inherit' });
        console.log('✅ png-to-ico installed successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to install png-to-ico:', error.message);
        return false;
    }
}

function createIcoWithPngToIco(pngFiles) {
    console.log('\n🎨 Creating ICO file with png-to-ico...');
    
    try {
        const pngToIco = require('png-to-ico');
        
        // Read PNG files
        const pngBuffers = pngFiles.map(file => fs.readFileSync(file));
        
        // Convert to ICO
        const icoBuffer = pngToIco(pngBuffers);
        
        // Write ICO file
        fs.writeFileSync(outputIcoPath, icoBuffer);
        
        const stats = fs.statSync(outputIcoPath);
        console.log(`✅ ICO file created: ${(stats.size / 1024).toFixed(1)} KB`);
        
        return true;
    } catch (error) {
        console.error('❌ png-to-ico conversion failed:', error.message);
        return false;
    }
}

function createIcoWithImageMagick(pngFiles) {
    console.log('\n🎨 Creating ICO file with ImageMagick...');
    
    try {
        // Try using ImageMagick convert command
        const inputFiles = pngFiles.join(' ');
        const command = `magick convert ${inputFiles} "${outputIcoPath}"`;
        
        console.log('Command:', command);
        execSync(command, { stdio: 'inherit' });
        
        if (fs.existsSync(outputIcoPath)) {
            const stats = fs.statSync(outputIcoPath);
            console.log(`✅ ICO file created with ImageMagick: ${(stats.size / 1024).toFixed(1)} KB`);
            return true;
        }
        
        return false;
    } catch (error) {
        console.log('⚠️  ImageMagick not available or failed');
        return false;
    }
}

function createIcoManually(pngFiles) {
    console.log('\n🎨 Creating ICO file manually...');
    
    try {
        // Simple ICO creation using Node.js
        // This is a basic implementation - for production use a proper library
        
        const iconSizes = [16, 32, 48, 64, 128, 256];
        const iconData = [];
        
        // ICO header (6 bytes)
        const header = Buffer.alloc(6);
        header.writeUInt16LE(0, 0);     // Reserved (must be 0)
        header.writeUInt16LE(1, 2);     // Type (1 = ICO)
        header.writeUInt16LE(Math.min(pngFiles.length, 6), 4); // Number of images
        
        iconData.push(header);
        
        // For now, let's just copy the largest PNG as a basic ICO
        const largestPng = pngFiles[pngFiles.length - 1];
        const pngData = fs.readFileSync(largestPng);
        
        // Simple ICO directory entry (16 bytes)
        const dirEntry = Buffer.alloc(16);
        dirEntry.writeUInt8(0, 0);      // Width (0 = 256)
        dirEntry.writeUInt8(0, 1);      // Height (0 = 256)
        dirEntry.writeUInt8(0, 2);      // Color count
        dirEntry.writeUInt8(0, 3);      // Reserved
        dirEntry.writeUInt16LE(1, 4);   // Planes
        dirEntry.writeUInt16LE(32, 6);  // Bits per pixel
        dirEntry.writeUInt32LE(pngData.length, 8); // Size
        dirEntry.writeUInt32LE(22, 12); // Offset (6 + 16)
        
        iconData.push(dirEntry);
        iconData.push(pngData);
        
        const icoBuffer = Buffer.concat(iconData);
        fs.writeFileSync(outputIcoPath, icoBuffer);
        
        const stats = fs.statSync(outputIcoPath);
        console.log(`✅ ICO file created manually: ${(stats.size / 1024).toFixed(1)} KB`);
        
        return true;
    } catch (error) {
        console.error('❌ Manual ICO creation failed:', error.message);
        return false;
    }
}

function replaceOriginalIco() {
    console.log('\n🔄 Replacing original ICO file...');
    
    try {
        const originalIcoPath = path.resolve('assets/icons/icon.ico');
        
        // Backup original
        if (fs.existsSync(originalIcoPath)) {
            fs.copyFileSync(originalIcoPath, path.resolve('assets/icons/icon-backup.ico'));
            console.log('✅ Original ICO backed up');
        }
        
        // Replace with fixed version
        fs.copyFileSync(outputIcoPath, originalIcoPath);
        console.log('✅ ICO file replaced with fixed version');
        
        return true;
    } catch (error) {
        console.error('❌ Failed to replace ICO file:', error.message);
        return false;
    }
}

function main() {
    console.log('\n🚀 Starting ICO file fix process...');
    
    // Step 1: Check PNG files
    const pngFiles = checkPngFiles();
    if (pngFiles.length === 0) {
        console.error('❌ No PNG source files found');
        process.exit(1);
    }
    
    // Step 2: Try multiple methods to create ICO
    let success = false;
    
    // Method 1: png-to-ico package
    if (!success && installImageMagick()) {
        success = createIcoWithPngToIco(pngFiles);
    }
    
    // Method 2: ImageMagick
    if (!success) {
        success = createIcoWithImageMagick(pngFiles);
    }
    
    // Method 3: Manual creation
    if (!success) {
        success = createIcoManually(pngFiles);
    }
    
    if (!success) {
        console.error('❌ All ICO creation methods failed');
        process.exit(1);
    }
    
    // Step 3: Replace original ICO
    if (!replaceOriginalIco()) {
        console.error('❌ Failed to replace original ICO file');
        process.exit(1);
    }
    
    // Step 4: Success message
    console.log('\n🎉 ICO FILE FIX COMPLETE!');
    console.log('=========================');
    console.log('\n📋 Next Steps:');
    console.log('   1. Test icon embedding: npm run embed:icon-manual');
    console.log('   2. Verify embedding: npm run verify:icon');
    console.log('   3. Build installer: node installer/build-nsis.js');
    
    console.log('\n✅ The ICO file should now be compatible with Windows executable embedding!');
}

// Run the ICO fix process
main();
