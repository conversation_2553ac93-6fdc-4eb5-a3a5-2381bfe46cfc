import React, { useMemo, useRef } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import { Course, Section, Session, Semester } from '../../../types/models';
import ManIcon from '@mui/icons-material/Man';
import WomanIcon from '@mui/icons-material/Woman';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { exportCardAsPdf } from '../../../utils/exportUtils';

// Props for the GenderDistributionTab component
interface GenderDistributionTabProps {
  courses: Record<Semester, Course[]>;
  sections: Record<Semester, Section[]>;
  sessions: Record<Semester, Session[]>;
  currentSemester: Semester;
}

const GenderDistributionTab: React.FC<GenderDistributionTabProps> = ({
  courses,
  sections,
  sessions,
  currentSemester
}) => {
  // Create refs for the cards
  const sectionDistributionRef = useRef<HTMLDivElement>(null);
  const courseTypeDistributionRef = useRef<HTMLDivElement>(null);
  const academicLevelDistributionRef = useRef<HTMLDivElement>(null);
  const timeDistributionRef = useRef<HTMLDivElement>(null);
  const dayDistributionRef = useRef<HTMLDivElement>(null);
  const courseGenderDistributionRef = useRef<HTMLDivElement>(null);

  // Handle export to PDF
  const handleExportToPdf = (element: HTMLElement, title: string, fileName: string) => {
    exportCardAsPdf(title, element, fileName);
  };
  // Calculate gender distribution statistics
  const genderStats = useMemo(() => {
    const currentCourses = courses[currentSemester];
    const currentSections = sections[currentSemester];
    const currentSessions = sessions[currentSemester];

    // Basic section counts by gender
    const maleSections = currentSections.filter(section => section.gender === 'M').length;
    const femaleSections = currentSections.filter(section => section.gender === 'F').length;
    const totalSections = currentSections.length;

    // Calculate scheduled sections by gender
    const scheduledSectionIds = new Set(currentSessions.map(session => session.sectionId));
    const scheduledMaleSections = currentSections.filter(
      section => section.gender === 'M' && scheduledSectionIds.has(section.id)
    ).length;
    const scheduledFemaleSections = currentSections.filter(
      section => section.gender === 'F' && scheduledSectionIds.has(section.id)
    ).length;

    // Calculate course type distribution by gender
    const maleTheorySections = currentSections.filter(section => {
      const course = currentCourses.find(c => c.id === section.courseId);
      return section.gender === 'M' && course && course.courseType === 'Theory';
    }).length;

    const maleLaboratorySections = currentSections.filter(section => {
      const course = currentCourses.find(c => c.id === section.courseId);
      return section.gender === 'M' && course && course.courseType === 'Lab';
    }).length;

    const femaleTheorySections = currentSections.filter(section => {
      const course = currentCourses.find(c => c.id === section.courseId);
      return section.gender === 'F' && course && course.courseType === 'Theory';
    }).length;

    const femaleLaboratorySections = currentSections.filter(section => {
      const course = currentCourses.find(c => c.id === section.courseId);
      return section.gender === 'F' && course && course.courseType === 'Lab';
    }).length;

    // Calculate academic level distribution by gender
    const maleGraduateSections = currentSections.filter(section => {
      const course = currentCourses.find(c => c.id === section.courseId);
      return section.gender === 'M' && course &&
        (course.academicLevel === 'Graduate' ||
         (course.courseCode && parseInt(course.courseCode.replace(/\D/g, '')) >= 500));
    }).length;

    const maleUndergraduateSections = maleSections - maleGraduateSections;

    const femaleGraduateSections = currentSections.filter(section => {
      const course = currentCourses.find(c => c.id === section.courseId);
      return section.gender === 'F' && course &&
        (course.academicLevel === 'Graduate' ||
         (course.courseCode && parseInt(course.courseCode.replace(/\D/g, '')) >= 500));
    }).length;

    const femaleUndergraduateSections = femaleSections - femaleGraduateSections;

    // Calculate time of day distribution by gender
    const morningPeriods = [1, 2, 3, 4, 5, 6];
    const eveningPeriods = [7, 8, 9, 10, 11, 12];

    const maleMorningSessions = currentSessions.filter(session => {
      const section = currentSections.find(s => s.id === session.sectionId);
      return section && section.gender === 'M' && morningPeriods.includes(session.startPeriod);
    }).length;

    const maleEveningSessions = currentSessions.filter(session => {
      const section = currentSections.find(s => s.id === session.sectionId);
      return section && section.gender === 'M' && eveningPeriods.includes(session.startPeriod);
    }).length;

    const femaleMorningSessions = currentSessions.filter(session => {
      const section = currentSections.find(s => s.id === session.sectionId);
      return section && section.gender === 'F' && morningPeriods.includes(session.startPeriod);
    }).length;

    const femaleEveningSessions = currentSessions.filter(session => {
      const section = currentSections.find(s => s.id === session.sectionId);
      return section && section.gender === 'F' && eveningPeriods.includes(session.startPeriod);
    }).length;

    // Calculate day distribution by gender
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];
    const maleDayDistribution = days.map(day => {
      const count = currentSessions.filter(session => {
        const section = currentSections.find(s => s.id === session.sectionId);
        return section && section.gender === 'M' && session.day === day;
      }).length;

      return {
        day,
        count,
        percentage: maleSections > 0 ? (count / maleSections) * 100 : 0
      };
    });

    const femaleDayDistribution = days.map(day => {
      const count = currentSessions.filter(session => {
        const section = currentSections.find(s => s.id === session.sectionId);
        return section && section.gender === 'F' && session.day === day;
      }).length;

      return {
        day,
        count,
        percentage: femaleSections > 0 ? (count / femaleSections) * 100 : 0
      };
    });

    // Course distribution by gender
    const courseGenderDistribution = currentCourses.map(course => {
      const courseMaleSections = currentSections.filter(
        section => section.courseId === course.id && section.gender === 'M'
      ).length;

      const courseFemaleSections = currentSections.filter(
        section => section.courseId === course.id && section.gender === 'F'
      ).length;

      const courseTotalSections = courseMaleSections + courseFemaleSections;

      return {
        id: course.id,
        courseCode: course.courseCode,
        courseName: course.courseName,
        courseType: course.courseType,
        academicLevel: course.academicLevel ||
          (course.courseCode && parseInt(course.courseCode.replace(/\D/g, '')) >= 500
            ? 'Graduate'
            : 'Undergraduate'),
        maleSections: courseMaleSections,
        femaleSections: courseFemaleSections,
        totalSections: courseTotalSections,
        malePercentage: courseTotalSections > 0 ? (courseMaleSections / courseTotalSections) * 100 : 0,
        femalePercentage: courseTotalSections > 0 ? (courseFemaleSections / courseTotalSections) * 100 : 0
      };
    });

    return {
      totalSections,
      maleSections,
      femaleSections,
      malePercentage: totalSections > 0 ? (maleSections / totalSections) * 100 : 0,
      femalePercentage: totalSections > 0 ? (femaleSections / totalSections) * 100 : 0,
      scheduledMaleSections,
      scheduledFemaleSections,
      maleScheduledPercentage: maleSections > 0 ? (scheduledMaleSections / maleSections) * 100 : 0,
      femaleScheduledPercentage: femaleSections > 0 ? (scheduledFemaleSections / femaleSections) * 100 : 0,
      maleTheorySections,
      maleLaboratorySections,
      femaleTheorySections,
      femaleLaboratorySections,
      maleTheoryPercentage: maleSections > 0 ? (maleTheorySections / maleSections) * 100 : 0,
      maleLaboratoryPercentage: maleSections > 0 ? (maleLaboratorySections / maleSections) * 100 : 0,
      femaleTheoryPercentage: femaleSections > 0 ? (femaleTheorySections / femaleSections) * 100 : 0,
      femaleLaboratoryPercentage: femaleSections > 0 ? (femaleLaboratorySections / femaleSections) * 100 : 0,
      maleGraduateSections,
      maleUndergraduateSections,
      femaleGraduateSections,
      femaleUndergraduateSections,
      maleGraduatePercentage: maleSections > 0 ? (maleGraduateSections / maleSections) * 100 : 0,
      maleUndergraduatePercentage: maleSections > 0 ? (maleUndergraduateSections / maleSections) * 100 : 0,
      femaleGraduatePercentage: femaleSections > 0 ? (femaleGraduateSections / femaleSections) * 100 : 0,
      femaleUndergraduatePercentage: femaleSections > 0 ? (femaleUndergraduateSections / femaleSections) * 100 : 0,
      maleMorningSessions,
      maleEveningSessions,
      femaleMorningSessions,
      femaleEveningSessions,
      maleMorningPercentage: (maleMorningSessions + maleEveningSessions) > 0
        ? (maleMorningSessions / (maleMorningSessions + maleEveningSessions)) * 100
        : 0,
      maleEveningPercentage: (maleMorningSessions + maleEveningSessions) > 0
        ? (maleEveningSessions / (maleMorningSessions + maleEveningSessions)) * 100
        : 0,
      femaleMorningPercentage: (femaleMorningSessions + femaleEveningSessions) > 0
        ? (femaleMorningSessions / (femaleMorningSessions + femaleEveningSessions)) * 100
        : 0,
      femaleEveningPercentage: (femaleMorningSessions + femaleEveningSessions) > 0
        ? (femaleEveningSessions / (femaleMorningSessions + femaleEveningSessions)) * 100
        : 0,
      maleDayDistribution,
      femaleDayDistribution,
      courseGenderDistribution
    };
  }, [courses, sections, sessions, currentSemester]);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Summary Cards */}
      <Grid container spacing={1}>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card variant="outlined" sx={{ height: '100%' }} ref={sectionDistributionRef}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  Section Distribution
                </Typography>
                <IconButton
                  size="small"
                  title="Export as PDF"
                  sx={{ p: 0.5 }}
                  onClick={() => sectionDistributionRef.current && handleExportToPdf(sectionDistributionRef.current, 'Section Distribution', 'section_distribution.pdf')}
                >
                  <FileDownloadIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ManIcon color="primary" sx={{ fontSize: 30, mr: 0.5 }} />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                    {genderStats.maleSections} ({genderStats.malePercentage.toFixed(1)}%)
                  </Typography>
                  <Typography variant="caption">Male Sections</Typography>
                </Box>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WomanIcon color="secondary" sx={{ fontSize: 30, mr: 0.5 }} />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle2" color="secondary" sx={{ fontWeight: 'bold' }}>
                    {genderStats.femaleSections} ({genderStats.femalePercentage.toFixed(1)}%)
                  </Typography>
                  <Typography variant="caption">Female Sections</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card variant="outlined" sx={{ height: '100%' }} ref={courseTypeDistributionRef}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  Course Type Distribution
                </Typography>
                <IconButton
                  size="small"
                  title="Export as PDF"
                  sx={{ p: 0.5 }}
                  onClick={() => courseTypeDistributionRef.current && handleExportToPdf(courseTypeDistributionRef.current, 'Course Type Distribution', 'course_type_distribution.pdf')}
                >
                  <FileDownloadIcon fontSize="small" />
                </IconButton>
              </Box>
              <Grid container spacing={1}>
                <Grid size={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'block' }}>
                    Male Sections
                  </Typography>
                  <Typography variant="caption" display="block">
                    Theory: {genderStats.maleTheorySections} ({genderStats.maleTheoryPercentage.toFixed(1)}%)
                  </Typography>
                  <Typography variant="caption" display="block">
                    Lab: {genderStats.maleLaboratorySections} ({genderStats.maleLaboratoryPercentage.toFixed(1)}%)
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'secondary.main', display: 'block' }}>
                    Female Sections
                  </Typography>
                  <Typography variant="caption" display="block">
                    Theory: {genderStats.femaleTheorySections} ({genderStats.femaleTheoryPercentage.toFixed(1)}%)
                  </Typography>
                  <Typography variant="caption" display="block">
                    Lab: {genderStats.femaleLaboratorySections} ({genderStats.femaleLaboratoryPercentage.toFixed(1)}%)
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Card variant="outlined" sx={{ height: '100%' }} ref={academicLevelDistributionRef}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  Academic Level Distribution
                </Typography>
                <IconButton
                  size="small"
                  title="Export as PDF"
                  sx={{ p: 0.5 }}
                  onClick={() => academicLevelDistributionRef.current && handleExportToPdf(academicLevelDistributionRef.current, 'Academic Level Distribution', 'academic_level_distribution.pdf')}
                >
                  <FileDownloadIcon fontSize="small" />
                </IconButton>
              </Box>
              <Grid container spacing={1}>
                <Grid size={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'block' }}>
                    Male Sections
                  </Typography>
                  <Typography variant="caption" display="block">
                    UG: {genderStats.maleUndergraduateSections} ({genderStats.maleUndergraduatePercentage.toFixed(1)}%)
                  </Typography>
                  <Typography variant="caption" display="block">
                    Grad: {genderStats.maleGraduateSections} ({genderStats.maleGraduatePercentage.toFixed(1)}%)
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'secondary.main', display: 'block' }}>
                    Female Sections
                  </Typography>
                  <Typography variant="caption" display="block">
                    UG: {genderStats.femaleUndergraduateSections} ({genderStats.femaleUndergraduatePercentage.toFixed(1)}%)
                  </Typography>
                  <Typography variant="caption" display="block">
                    Grad: {genderStats.femaleGraduateSections} ({genderStats.femaleGraduatePercentage.toFixed(1)}%)
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Time Distribution */}
      <Card variant="outlined" ref={timeDistributionRef}>
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Time of Day Distribution
            </Typography>
            <IconButton
              size="small"
              title="Export as PDF"
              sx={{ p: 0.5 }}
              onClick={() => timeDistributionRef.current && handleExportToPdf(timeDistributionRef.current, 'Time of Day Distribution', 'time_distribution.pdf')}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <Grid container spacing={1}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'block', mb: 0.5 }}>
                Male Sections
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="caption">
                  Morning: {genderStats.maleMorningSessions} ({genderStats.maleMorningPercentage.toFixed(1)}%)
                </Typography>
                <Typography variant="caption">
                  Evening: {genderStats.maleEveningSessions} ({genderStats.maleEveningPercentage.toFixed(1)}%)
                </Typography>
              </Box>
              <Box
                sx={{
                  height: 8,
                  width: '100%',
                  backgroundColor: '#e0e0e0',
                  borderRadius: 4,
                  display: 'flex',
                  overflow: 'hidden'
                }}
              >
                <Box
                  sx={{
                    height: '100%',
                    width: `${genderStats.maleMorningPercentage}%`,
                    backgroundColor: 'primary.light',
                  }}
                />
                <Box
                  sx={{
                    height: '100%',
                    width: `${genderStats.maleEveningPercentage}%`,
                    backgroundColor: 'primary.dark',
                  }}
                />
              </Box>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'secondary.main', display: 'block', mb: 0.5 }}>
                Female Sections
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography variant="caption">
                  Morning: {genderStats.femaleMorningSessions} ({genderStats.femaleMorningPercentage.toFixed(1)}%)
                </Typography>
                <Typography variant="caption">
                  Evening: {genderStats.femaleEveningSessions} ({genderStats.femaleEveningPercentage.toFixed(1)}%)
                </Typography>
              </Box>
              <Box
                sx={{
                  height: 8,
                  width: '100%',
                  backgroundColor: '#e0e0e0',
                  borderRadius: 4,
                  display: 'flex',
                  overflow: 'hidden'
                }}
              >
                <Box
                  sx={{
                    height: '100%',
                    width: `${genderStats.femaleMorningPercentage}%`,
                    backgroundColor: 'secondary.light',
                  }}
                />
                <Box
                  sx={{
                    height: '100%',
                    width: `${genderStats.femaleEveningPercentage}%`,
                    backgroundColor: 'secondary.dark',
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Day Distribution */}
      <Card variant="outlined" ref={dayDistributionRef}>
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Day Distribution
            </Typography>
            <IconButton
              size="small"
              title="Export as PDF"
              sx={{ p: 0.5 }}
              onClick={() => dayDistributionRef.current && handleExportToPdf(dayDistributionRef.current, 'Day Distribution', 'day_distribution.pdf')}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <Grid container spacing={1}>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'block', mb: 0.5 }}>
                Male Sections
              </Typography>
              <Grid container spacing={0.5}>
                {genderStats.maleDayDistribution.map(day => (
                  <Grid size={{ xs: 12, sm: 2.4 }} key={day.day}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>{day.count}</Typography>
                      <Typography variant="caption" display="block">{day.day}</Typography>
                      <Box
                        sx={{
                          height: 6,
                          width: '100%',
                          backgroundColor: '#e0e0e0',
                          borderRadius: 3,
                          mt: 0.5
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${day.percentage}%`,
                            backgroundColor: 'primary.main',
                            borderRadius: 3
                          }}
                        />
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Grid>
            <Grid size={{ xs: 12, sm: 6 }}>
              <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'secondary.main', display: 'block', mb: 0.5 }}>
                Female Sections
              </Typography>
              <Grid container spacing={0.5}>
                {genderStats.femaleDayDistribution.map(day => (
                  <Grid size={{ xs: 12, sm: 2.4 }} key={day.day}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>{day.count}</Typography>
                      <Typography variant="caption" display="block">{day.day}</Typography>
                      <Box
                        sx={{
                          height: 6,
                          width: '100%',
                          backgroundColor: '#e0e0e0',
                          borderRadius: 3,
                          mt: 0.5
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${day.percentage}%`,
                            backgroundColor: 'secondary.main',
                            borderRadius: 3
                          }}
                        />
                      </Box>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Course Gender Distribution Table */}
      <Card variant="outlined" ref={courseGenderDistributionRef}>
        <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1 } }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Course Gender Distribution
            </Typography>
            <IconButton
              size="small"
              title="Export as PDF"
              sx={{ p: 0.5 }}
              onClick={() => courseGenderDistributionRef.current && handleExportToPdf(courseGenderDistributionRef.current, 'Course Gender Distribution', 'course_gender_distribution.pdf')}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Box>
          <TableContainer sx={{ flexGrow: 1, overflow: 'auto' }}>
            <Table stickyHeader size="small" sx={{ '& .MuiTableCell-root': { py: 0.75, px: 1 } }}>
              <TableHead>
                <TableRow>
                  <TableCell>Course Code</TableCell>
                  <TableCell>Course Name</TableCell>
                  <TableCell align="center">Type</TableCell>
                  <TableCell align="center">Level</TableCell>
                  <TableCell align="center">Male</TableCell>
                  <TableCell align="center">Female</TableCell>
                  <TableCell align="center">Total</TableCell>
                  <TableCell align="center">Distribution</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {genderStats.courseGenderDistribution.map((course) => (
                  <TableRow key={course.id} hover>
                    <TableCell>
                      <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                        {course.courseCode}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="caption">
                        {course.courseName}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.courseType}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.academicLevel}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <ManIcon color="primary" fontSize="small" sx={{ mr: 0.25, fontSize: '0.875rem' }} />
                        <Typography variant="caption">
                          {course.maleSections}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <WomanIcon color="secondary" fontSize="small" sx={{ mr: 0.25, fontSize: '0.875rem' }} />
                        <Typography variant="caption">
                          {course.femaleSections}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="caption">
                        {course.totalSections}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box
                        sx={{
                          height: 6,
                          width: '100%',
                          backgroundColor: '#e0e0e0',
                          borderRadius: 3,
                          display: 'flex',
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${course.malePercentage}%`,
                            backgroundColor: 'primary.main',
                          }}
                        />
                        <Box
                          sx={{
                            height: '100%',
                            width: `${course.femalePercentage}%`,
                            backgroundColor: 'secondary.main',
                          }}
                        />
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default GenderDistributionTab;
