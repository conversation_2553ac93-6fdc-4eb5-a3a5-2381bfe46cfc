Pls help implementing a rule-based scheduling automation system to help user  building of the timetable in line with manual building. 
This rule based system will work as follows:
	1. The system will enable the user to execute auto timetabling for a single section or all sections in all courses at once , which means it will be enabled to be triggered by an icon on the course section on the course card. When clicked the section will be scheduled according to the predefined rules, and it also can be executed to schedule all courses by a button on the dedicated modal component that will manage the rules of the system as we will describe later.
	2. The system will work and place the generated sessions on the main timetable canvas directly. 
	3. The creation of sessions on the timetable canvas  will be as if the user has dragged the section from the course card on courses panel to the timetable canvas manually, so the created sessions by this system should be identical to the sessions created by manual dragging and dropping.
    4. though identical the app and the user should be able to distinquish between Autogenerated sessions and manualy craeted sessions, by a change in the border color and syle (dotted border) of the session card.
	5. The rules applied for scheduling will be listed in a dedicated component triggered by a gear icon on the header of the app.
	6.in general the system should not over schedual a section or under schedule it. either fully schdule or no schedule at all.
	7. Each rule listed in the component will have a check box to allow the user to enable or disable it and a box to set the number of its priority (order in implementation). 
	8. The listed rules are as follows:

		○ First: Courses/sections/timeslot rules:
			1- Don’t schedule any session of all course types and academic level in blocked/break time slots. Priority: 1. blocked slots are two types:
				□ Systemicaly blocked (timeslots on period 5, 6, 11, 12 on long days)
				□ User defined breaks. (Allow user to choose any timeslot he likes to block).
				
			2- Don’t duplicate scheduling of a session in the same time slot: priority: 2 
			3- Don’t schedule undergraduate theory courses' sessions in timeslots that reached the max number of undergraduate theory courses' sessions allowed to be scheduled in it (priority:2). Max number will be user defined per day type per period. e.g. regular days period 1 max sessions allowed is 4.
            For easy handling create a table with columns: day type/period no/max sessions. Default value of max no of undergraduate theory sessions per timeslot will be calculated as follows:
				□ period 1,2,3,4,5,6 on regular days (Su, Tu, Th): round(total number of sections of theory undegradute courses populated in the app X 0.05)
				□ Period 7,8,9,10.11 on regular days: round(total number of sections theory undegradute courses populated in the app X 0.06)
				□ period 1,2,3,4, on long days (Mo, We): round(total number of sections theory undegradute courses populated in the app X 0.05)
				□ Period 7,8,9, on long days: round(total number of sections theory undegradute courses populated in the app X 0.066666667)
				□ Period 12 on regular days and period 10 on long days: 0
                Note: user can change the default value and it will be saved in store.
			4- Follow the evening timing and scheduling pattern for postgraduate courses (Master, Diploma, PhD). Priority: 2.
			5- Follow Consistent timing and pattern for theory, undergraduate,  2 CHrs courses. Priority: 3.
			6- Follow Consistent timing and pattern for theory, undergraduate,  3 CHrs courses. Priority: 3.
			7- Follow Consistent timing and pattern for theory, undergraduate,  4 CHrs courses. Priority: 3.
			8- Follow Consistent timing and pattern for theory, undergraduate,  5 CHrs courses. Priority: 3.
			9- Don’t schedule sessions on a day that reached the max number of undergraduate theory courses' sessions allowed to be scheduled in that day. (disabled by default): priority: 4.
            The max no of sessions per day should be (user defined). Default value is: 
				□ For regular days: round the sum of all sections of theory undegradute courses in the app times each section contact hrs divided by 5. e.g. if the sum of sections in the app are 30 and all sections are having 3 CHrs, the default max will be 30 X 3/5 = 18 sessions per day.
				□ For long days: round the sum of all sections of theory undegradute coursesin the app times each section contact hrs divided by 1.5 divided by 5. e.g. if the sum of sections in the app are 30 and all sections are having 3 CHrs, the default max will be 30 X 3/1.5/5 = 12 sessions per day.
			10- Sessions of 4th-year academic level theory courses should not overlap (schedules in one slot). priority: 5.
			11- Sessions of 3rd-year academic level theory courses should not overlap (schedules in one slot). priority: 5.
			12- Sessions of 2nd-year academic level theory courses should not overlap (schedules in one slot). priority: 5. 
			13- Sessions of 1st-year academic level theory courses should not overlap (schedules in one slot). priority: 5. (disabled by default).
			14- Maximum period gap between sessions of 4th-year academic theory undergraduate courses of the same gender: Default 5 periods (user-defined) Priority: 6.
			15- Maximum period gap between sessions of 3rd-year academic theory undergraduate courses of the same gender: Default 5 periods (user-defined) Priority: 6.

		○ Second: Lecturer rules:
			1- Don’t duplicate a lecturer per timeslot. Priority: 1
			2- Don’t assign a lecturer to a course session he is unable to teach (as set in lecturer data) priority: 2.
			3- Consider the preferred teaching time of the day (morning or evening) of the lecturer  (as set in lecturer data). Priority: 3
			• Consider the Maximum number of teaching days per week: (as set in lecturer data). Priority: 4.
			4- Consider Maximum consecutive periods for a lecturer:  (as set in lecturer data). Priority: 5.
			5- Consider Maximum gap between periods: (as set in lecturer data). Priority: 6.
			6- Consider Maximum semester load of a lecturer: (as set in lecturer data). Priority: 7.

Notes: 
	1. Course Academic level (1st year undergraduate, 2nd year undergraduate, 3d year undergraduate,  4th year undergraduate, Diploma Postgraduate, Master postgraduate, Ph.D. Postgraduate) is inferred from the course code number. e.g. course codes which contain: 100-199, 200-299, 300- 399, 400-499, are undergraduate from 1st year to 4th year. 500-599 Diploma, 600-699 Masters, 700-899 Ph.D., are postgraduate courses. this is determened by a function in add/edit course modal.
    2. course type: Theory/Lab is set in corse data.
	3. Morning time for long days is 1-4 periods, and for regular days it is 1-6 periods, while evening time for long days are periods from 7-10, and for regular days  are periods from 7-12.
	4. Patterns of scheduling:
		§ For section with 2 contact hours it should be scheduled over two regular days at the same time. e.g. 8 am on Sunday and Tuesday.
		§ For undergraduate section with 3 contact hours it should be scheduled over either three regular days at the same time or over two long days at the same time: Example: 8–9 AM on regular days (Su, Tu, Th) or 8–9:30 AM on long days (Mo, We).
		§ For postgraduate section it should be scheduled on one day only: either three consecutive periods on regular days or two consecutive periods on long days : Example: periods 10-12 on regular days (Su, Tu, Th) or periods 9-10 on long days (Mo, We).
		§  For section with 4 contact hours it should be scheduled over multiple days as follows: 
			□ If scheduled over two regular days, it must be in two consecutive timeslots in each day at the same time (e.g. 2 consecutive sessions  on Sunday and two consecutive sessions on Tuesday at the same time)
			□ If scheduled over two long days and on one regular day, it must follow a 1.5-hour slot pattern at the same time and the remaining 1 hour on the next regular day on the nearest time. For example: 1.5 on Monday and Wednesday at the same time. e.g. 9:30-11 am, and 1hr on Thursday at 9 am
		§ For section with 5 contact hours: it should be scheduled over at least three days: 
			□ in the pattern: 2-2-1, 1-2-2, or 2-1-2 (for regular days) at the same time. 
			□ Or In the pattern: 1-1-2 for two long days and one regular day e.g. 1.5 hr. on each of Monday and Wednesday, and 2 consecutive hrs. on Sunday or Thursday at the same or nearest time.
