import { validateTimeslotForSection, getAcademicLevel, calculateSemesterLoad, calculateMaxSemesterLoad, getLecturerTeachingDaysCount, Session } from './ruleValidation';
import { Lecturer } from '../types/models';
import { useRuleSystemStore } from '../store/ruleSystem';
import { applyCPSRefinement, CPSRefinementReport } from './cpsRefinement';

// Interface for auto-scheduling sessions
export interface AutoSession {
  id: string;
  sectionId: string;
  courseCode: string;
  courseType: 'Theory' | 'Lab';
  academicLevel: string;
  gender: string;
  lecturerId: string;
  day: string;
  period: number;
  isAutoGenerated: boolean;
  isLongDay?: boolean;
}

// Interface for app's Session model
export interface AppSession {
  id: string;
  sectionId: string;
  lecturerId: string;
  lecturerIds?: string[];
  day: string;
  startPeriod: number;
  endPeriod: number;
  viewType: 'week' | 'regular' | 'long';
  timeOfDay?: 'morning' | 'evening';
  isAutoGenerated?: boolean;
}

export interface Section {
  id: string;
  courseCode: string;
  courseType: 'Theory' | 'Lab';
  contactHours: number;
  gender: string;
  lecturerId: string;
  academicLevel: string;
}

// Convert AutoSession to AppSession
export const convertToAppSession = (autoSession: AutoSession): AppSession => {
  // Convert day format from 'Mon', 'Tue' to 'Monday', 'Tuesday', etc.
  const dayMap: Record<string, string> = {
    'Sun': 'Sunday',
    'Mon': 'Monday',
    'Tue': 'Tuesday',
    'Wed': 'Wednesday',
    'Thu': 'Thursday',
    'Fri': 'Friday',
    'Sat': 'Saturday'
  };

  const fullDay = dayMap[autoSession.day] || autoSession.day;
  const isLongDay = ['Monday', 'Wednesday'].includes(fullDay);
  const isMorning = autoSession.period <= (isLongDay ? 4 : 6);

  return {
    id: autoSession.id,
    sectionId: autoSession.sectionId,
    lecturerId: autoSession.lecturerId,
    lecturerIds: autoSession.lecturerId ? [autoSession.lecturerId] : [],
    day: fullDay,
    startPeriod: autoSession.period,
    endPeriod: autoSession.period, // Always set endPeriod equal to startPeriod for individual sessions
    viewType: isLongDay ? 'long' : 'regular',
    timeOfDay: isMorning ? 'morning' : 'evening',
    isAutoGenerated: true
  };
};

// Convert AutoSession to Session for rule validation functions
// This adapter function helps with TypeScript compatibility
export const convertAutoSessionToSession = (autoSession: AutoSession): Session => {
  return {
    id: autoSession.id,
    sectionId: autoSession.sectionId,
    courseCode: autoSession.courseCode,
    courseType: autoSession.courseType,
    academicLevel: autoSession.academicLevel,
    gender: autoSession.gender,
    lecturerId: autoSession.lecturerId,
    day: autoSession.day,
    period: autoSession.period,
    startPeriod: autoSession.period,
    endPeriod: autoSession.period,
    isAutoGenerated: autoSession.isAutoGenerated
  };
};

// Convert an array of AutoSessions to an array of Sessions
export const convertAutoSessionsToSessions = (autoSessions: AutoSession[]): Session[] => {
  return autoSessions.map(convertAutoSessionToSession);
};

interface SchedulePattern {
  id: string;
  name: string;
  patterns: Array<{ days: string[]; periodsPerDay: number[] }>;
  contactHours: number;
  courseType: 'Theory' | 'Lab';
  academicLevel: 'undergraduate' | 'postgraduate';
}

// Predefined scheduling patterns based on requirements
//
// IMPORTANT NOTES ON PERIOD INTERPRETATION:
// - On regular days (Sun, Tue, Thu), 1 period = 1 hour
// - On long days (Mon, Wed), 1 period = 1.5 hours
// - For consecutive periods, use the number of consecutive periods in periodsPerDay
// - For multiple days, periodsPerDay array corresponds to the days array in the same order
//   e.g., [2, 1] means 2 periods on the first day, 1 period on the second day
const schedulePatterns: SchedulePattern[] = [
  {
    id: 'undergrad-2ch',
    name: '2 Credit Hours Undergraduate',
    contactHours: 2,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Two regular days at the same time
        days: ['Sun', 'Tue'],
        periodsPerDay: [1]
      },
      {
        days: ['Sun', 'Thu'],
        periodsPerDay: [1]
      },
      {
        days: ['Tue', 'Thu'],
        periodsPerDay: [1]
      }
    ]
  },
  {
    id: 'undergrad-3ch-long',
    name: '3 Credit Hours Undergraduate (Long Days)',
    contactHours: 3,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Two long days at the same time
        // On long days, 1 period = 1.5 hours, so we need 1 period on each day
        days: ['Mon', 'Wed'],
        periodsPerDay: [1, 1] // 1 period on each long day (1.5 hours each)
      }
    ]
  },
  {
    id: 'undergrad-3ch-regular',
    name: '3 Credit Hours Undergraduate (Regular Days)',
    contactHours: 3,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Three regular days at the same time
        days: ['Sun', 'Tue', 'Thu'],
        periodsPerDay: [1]
      }
    ]
  },
  {
    id: 'postgrad-3ch',
    name: 'Postgraduate Course (3 Credit Hours)',
    contactHours: 3,
    courseType: 'Theory',
    academicLevel: 'postgraduate',
    patterns: [
      {
        // Regular days (Su, Tu, Th) - periods 10-12
        // For 3 credit hours, we need 3 periods on a regular day
        days: ['Sun'],
        periodsPerDay: [3] // This means 3 consecutive periods starting from period 10
      },
      {
        days: ['Tue'],
        periodsPerDay: [3] // This means 3 consecutive periods starting from period 10
      },
      {
        days: ['Thu'],
        periodsPerDay: [3] // This means 3 consecutive periods starting from period 10
      },
      {
        // Long days (Mo, We) - periods 9-10
        // For 3 credit hours, we need 2 periods on a long day (1.5 hours each)
        days: ['Mon'],
        periodsPerDay: [2] // This means 2 consecutive periods starting from period 9
      },
      {
        days: ['Wed'],
        periodsPerDay: [2] // This means 2 consecutive periods starting from period 9
      }
    ]
  },

  {
    id: 'undergrad-4ch-regular',
    name: '4 Credit Hours Undergraduate (Regular Days)',
    contactHours: 4,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Two regular days, 2 consecutive periods each day at the same time
        days: ['Sun', 'Tue'],
        periodsPerDay: [2, 2] // 2 consecutive periods on each day
      },
      {
        days: ['Sun', 'Thu'],
        periodsPerDay: [2, 2] // 2 consecutive periods on each day
      },
      {
        days: ['Tue', 'Thu'],
        periodsPerDay: [2, 2] // 2 consecutive periods on each day
      }
    ]
  },
  {
    id: 'undergrad-4ch-mixed',
    name: '4 Credit Hours Undergraduate (Mixed Days)',
    contactHours: 4,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Two long days (1.5 hours each) and one regular day (1 hour)
        // On long days, 1 period = 1.5 hours
        days: ['Mon', 'Wed', 'Thu'],
        periodsPerDay: [1, 1, 1] // 1.5 hours on Mon, 1.5 hours on Wed, 1 hour on Thu
      },
      {
        days: ['Mon', 'Wed', 'Sun'],
        periodsPerDay: [1, 1, 1] // 1.5 hours on Mon, 1.5 hours on Wed, 1 hour on Sun
      },
      {
        days: ['Mon', 'Wed', 'Tue'],
        periodsPerDay: [1, 1, 1] // 1.5 hours on Mon, 1.5 hours on Wed, 1 hour on Tue
      }
    ]
  },
  {
    id: 'undergrad-5ch-regular',
    name: '5 Credit Hours Undergraduate (Regular Days)',
    contactHours: 5,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Pattern 2-2-1: 2 consecutive periods on Sun, 2 on Tue, 1 on Thu
        days: ['Sun', 'Tue', 'Thu'],
        periodsPerDay: [2, 2, 1]
      },
      {
        // Pattern 1-2-2: 1 period on Sun, 2 consecutive periods on Tue, 2 on Thu
        days: ['Sun', 'Tue', 'Thu'],
        periodsPerDay: [1, 2, 2]
      },
      {
        // Pattern 2-1-2: 2 consecutive periods on Sun, 1 on Tue, 2 on Thu
        days: ['Sun', 'Tue', 'Thu'],
        periodsPerDay: [2, 1, 2]
      }
      // Removed 2-2-2 pattern as requested
    ]
  },
  {
    id: 'undergrad-5ch-mixed',
    name: '5 Credit Hours Undergraduate (Mixed Days)',
    contactHours: 5,
    courseType: 'Theory',
    academicLevel: 'undergraduate',
    patterns: [
      {
        // Two long days (1.5 hours each) and one regular day (2 hours)
        // On long days, 1 period = 1.5 hours
        days: ['Mon', 'Wed', 'Sun'],
        periodsPerDay: [1, 1, 2] // 1.5 hours on Mon, 1.5 hours on Wed, 2 consecutive hours on Sun
      },
      {
        days: ['Mon', 'Wed', 'Tue'],
        periodsPerDay: [1, 1, 2] // 1.5 hours on Mon, 1.5 hours on Wed, 2 consecutive hours on Tue
      },
      {
        days: ['Mon', 'Wed', 'Thu'],
        periodsPerDay: [1, 1, 2] // 1.5 hours on Mon, 1.5 hours on Wed, 2 consecutive hours on Thu
      }
    ]
  }
];

// Get the appropriate schedule patterns for a section
export const getSchedulePatterns = (contactHours: number, courseType: string, academicLevel: string): SchedulePattern[] => {
  // Determine if this is a postgraduate course
  const isPostgraduate = academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma';

  // Set academic level type for pattern matching
  const academicLevelType = isPostgraduate ? 'postgraduate' : 'undergraduate';

  // Match by contact hours, course type, and academic level
  const matchingPatterns = schedulePatterns.filter(pattern =>
    pattern.contactHours === contactHours &&
    pattern.courseType === courseType &&
    pattern.academicLevel === academicLevelType
  );

  return matchingPatterns;
};

// Intelligent pattern selection with scoring
function selectOptimalPatterns(
  availablePatterns: SchedulePattern[],
  section: Section,
  existingSchedule: AutoSession[]
): SchedulePattern[] {
  // Score each pattern and sort by score (highest first)
  const scoredPatterns = availablePatterns
    .map(pattern => ({
      pattern,
      score: calculatePatternScore(pattern, section, existingSchedule)
    }))
    .sort((a, b) => b.score - a.score);

  // Return patterns sorted by score
  return scoredPatterns.map(sp => sp.pattern);
}

function calculatePatternScore(
  pattern: SchedulePattern,
  section: Section,
  existingSchedule: AutoSession[]
): number {
  let score = 100; // Base score

  // Prefer patterns that minimize conflicts with existing schedule
  score += calculateConflictScore(pattern, section, existingSchedule);

  // Prefer patterns that balance lecturer load
  score += calculateLoadBalanceScore(pattern, section, existingSchedule);

  // Prefer patterns that follow academic conventions
  score += calculateConventionScore(pattern, section);

  return score;
}

function calculateConflictScore(
  pattern: SchedulePattern,
  section: Section,
  existingSchedule: AutoSession[]
): number {
  let conflictScore = 0;

  // Check each pattern variation for potential conflicts
  for (const patternVariation of pattern.patterns) {
    let variationConflicts = 0;

    patternVariation.days.forEach((day: string, dayIndex: number) => {
      const periodsCount = patternVariation.periodsPerDay[dayIndex] !== undefined
        ? patternVariation.periodsPerDay[dayIndex]
        : patternVariation.periodsPerDay[0];

      // Count existing sessions on this day that could conflict
      const dayConflicts = existingSchedule.filter(session =>
        session.day === day &&
        session.lecturerId === section.lecturerId
      ).length;

      variationConflicts += dayConflicts * periodsCount;
    });

    // Prefer patterns with fewer conflicts (negative score for conflicts)
    conflictScore -= variationConflicts * 10;
  }

  return conflictScore / pattern.patterns.length; // Average across variations
}

function calculateLoadBalanceScore(
  _pattern: SchedulePattern,
  section: Section,
  existingSchedule: AutoSession[]
): number {
  if (!section.lecturerId) return 0;

  // Calculate current lecturer load
  const currentLoad = existingSchedule
    .filter(session => session.lecturerId === section.lecturerId)
    .reduce((total, session) => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      return total + (isLongDay ? 1.5 : 1.0);
    }, 0);

  // Prefer patterns that don't overload the lecturer
  const additionalLoad = section.contactHours;
  const totalLoad = currentLoad + additionalLoad;

  // Penalty for high load (assuming 12 hours is max reasonable load)
  if (totalLoad > 12) {
    return -(totalLoad - 12) * 20;
  } else if (totalLoad > 8) {
    return -(totalLoad - 8) * 5;
  }

  return 0;
}

function calculateConventionScore(
  pattern: SchedulePattern,
  section: Section
): number {
  let conventionScore = 0;

  // FIXED: Removed day-type bias that was causing clustering
  // The original logic strongly favored long days for 3+ credit hours
  // and regular days for 2- credit hours, creating systematic bias

  // Instead, we now apply neutral scoring that doesn't bias toward specific day types
  // This allows the balanced distribution scoring in getAvailableTimeSlotsForPattern
  // to properly manage day-type distribution

  // Small bonus for patterns that match the contact hours requirement
  // without biasing toward specific day types
  if (pattern.contactHours === section.contactHours) {
    conventionScore += 5; // Neutral bonus for matching contact hours
  }

  // Slight preference for patterns with fewer days (more concentrated schedule)
  // This is a general efficiency preference without day-type bias
  const totalDays = pattern.patterns.reduce((sum, p) => sum + p.days.length, 0);
  if (totalDays <= 2) {
    conventionScore += 3; // Small bonus for concentrated schedules
  }

  return conventionScore;
}

// Optimized function to get available time slots for a specific scheduling pattern
const getAvailableTimeSlotsForPattern = (
  pattern: { days: string[]; periodsPerDay: number[] },
  section: Section,
  existingSchedule: AutoSession[],
  lecturers: Lecturer[] = [], // Add lecturers parameter with default empty array
  assignedLecturerId = '' // Add parameter for the lecturer ID determined by auto-scheduling
): Array<{ startDay: string; startPeriod: number; score: number }> => {
  const availableTimeSlots: Array<{ startDay: string; startPeriod: number; score: number }> = [];

  // Get academic level for validation
  const academicLevel = getAcademicLevel(section.courseCode);

  // Get rule system state once at the beginning
  const ruleSystemState = useRuleSystemStore.getState();
  const { userDefinedBreaks } = ruleSystemState;

  // Determine if this is an undergraduate theory course
  const isUndergraduateTheory =
    section.courseType === 'Theory' &&
    !academicLevel.includes('diploma') &&
    !academicLevel.includes('masters') &&
    !academicLevel.includes('phd');

  // Note: Lecturer constraints pre-computation removed as it contained gap-related logic
  // that is no longer used after removing gap validation from auto-scheduling.

  // Pre-compute timeslot usage for undergraduate theory courses
  const timeslotUsage = new Map<string, number>();
  if (isUndergraduateTheory) {
    existingSchedule.forEach(session => {
      if (session.courseType === 'Theory' &&
          !session.academicLevel.includes('diploma') &&
          !session.academicLevel.includes('masters') &&
          !session.academicLevel.includes('phd')) {
        const key = `${session.day}-${session.period}`;
        timeslotUsage.set(key, (timeslotUsage.get(key) || 0) + 1);
      }
    });
  }

  // For each day in the pattern
  pattern.days.forEach((day, dayIndex) => {
    const isLongDay = ['Mon', 'Wed'].includes(day);
    // Fix: Change maxPeriod to 12 for all days, since period 10 is no longer blocked
    const maxPeriod = 12;

    // Get the number of consecutive periods needed for this day
    // Use the corresponding periodsPerDay value if available, otherwise use the first value
    const periodsNeeded = pattern.periodsPerDay[dayIndex] !== undefined
      ? pattern.periodsPerDay[dayIndex]
      : pattern.periodsPerDay[0];

    // Removed verbose debug logging to reduce console output

    // Note: Postgraduate pattern restrictions are now handled by the rule system
    // via the 'postgrad-pattern' rule to respect enabled/disabled settings

    // Check all possible starting periods for this pattern
    const startingPeriods = Array.from({ length: maxPeriod - periodsNeeded + 1 }, (_, i) => i + 1);

    // Check each valid starting period
    for (const startPeriod of startingPeriods) {
      let validSlot = true;

      // Check validity for consecutive periods
      for (let i = 0; i < periodsNeeded; i++) {
        const currentPeriod = startPeriod + i;

        // Optimized blocked timeslot check
        const shortDayFormat = day.length > 3 ? day.substring(0, 3) : day;
        const breakTimeslotKey = `${shortDayFormat}-${currentPeriod}`;

        // Fast check for system-blocked timeslots
        if ((isLongDay && (currentPeriod === 5 || currentPeriod === 6)) ||
            (!isLongDay && currentPeriod === 12)) {
          validSlot = false;
          break;
        }

        // Fast check for user-defined breaks
        if (userDefinedBreaks.includes(breakTimeslotKey)) {
          validSlot = false;
          break;
        }

        // Note: Postgraduate pattern validation is now handled by the rule system
        // via the 'postgrad-pattern' rule to respect enabled/disabled settings

        // We'll do an early check for maximum sessions per timeslot
        // before proceeding with the full validation

        // Log the current state of the schedule for debugging
        const undergraduateSessionsInTimeslot = existingSchedule.filter(
          s => s.day === day &&
               s.period === currentPeriod &&
               s.courseType === 'Theory' &&
               !s.academicLevel.includes('diploma') &&
               !s.academicLevel.includes('masters') &&
               !s.academicLevel.includes('phd')
        );

        // Removed timeslot checking logs to reduce console output

        // Get the maximum allowed sessions for this timeslot from the rule system state
        const timeslotKey = `${day}-${currentPeriod}`;

        // Removed max sessions debugging logs to reduce console output

        // Get the maximum allowed sessions for this timeslot
        const maxAllowed = ruleSystemState.maxSessionsPerTimeslot[timeslotKey] || 0;

        // Removed maximum allowed sessions logs to reduce console output

        // Check if adding this session would exceed the maximum allowed
        // Only apply this rule to undergraduate theory courses
        if (isUndergraduateTheory) {
          // If maxAllowed is explicitly set to 0, it means no sessions are allowed in this timeslot
          if (maxAllowed === 0) {
            // Removed timeslot skipping logs to reduce console output
            validSlot = false;
            break;
          }
          // If maxAllowed is greater than 0, check if we've reached the limit
          else if (maxAllowed > 0) {
            if (undergraduateSessionsInTimeslot.length >= maxAllowed) {
              // Removed maximum sessions reached logs to reduce console output
              validSlot = false;
              break;
            } else {
              // Removed slot available logs to reduce console output
            }
          }
          // If maxAllowed is undefined or null, there's no limit set
          else {
            // Removed no maximum sessions logs to reduce console output
          }
        }

        // If we passed the early check, proceed with full validation
        // Create a copy of the existing schedule for validation
        const validationSchedule = [...existingSchedule];

        // We don't have access to the current semester here, but that's okay
        // The lecturer-max-load rule will be checked in autoScheduleSection

        // Note: Same course-gender conflict checking is now handled by the rule system
        // via the 'no-same-course-gender-overlap' rule to respect enabled/disabled settings

        // Note: All lecturer constraint checks are now handled by the rule system
        // in validateTimeslotForSection() to respect enabled/disabled rule settings

        // Check cache first for validation result
        const validationStartTime = performance.now();
        const scheduleHash = generateScheduleHash(validationSchedule);
        const cachedResult = getCachedValidation(section.id, day, currentPeriod, scheduleHash);

        let validation: { valid: boolean; violatedRules: string[] };

        if (cachedResult !== null) {
          // Use cached result
          validation = { valid: cachedResult, violatedRules: [] };
          globalProfiler.recordCacheHit();
        } else {
          // Convert AutoSession[] to Session[] for type compatibility
          const validationSessionsConverted = convertAutoSessionsToSessions(validationSchedule);
          validation = validateTimeslotForSection(
            section.id,
            section.courseCode,
            section.courseType,
            section.contactHours,
            academicLevel,
            section.gender,
            assignedLecturerId, // Use the lecturer ID determined by auto-scheduling logic
            day,
            currentPeriod,
            validationSessionsConverted,
            validationSessionsConverted.filter(s => s.sectionId === section.id),
            lecturers // Pass the lecturers parameter
          );

          // Cache the result
          setCachedValidation(section.id, day, currentPeriod, scheduleHash, validation.valid);
          globalProfiler.recordCacheMiss();
        }

        const validationEndTime = performance.now();
        globalProfiler.recordValidationTime(validationEndTime - validationStartTime);

        if (!validation.valid) {
          validSlot = false;
          globalProfiler.recordRuleViolation();
          break;
        }
      }

      if (validSlot) {
        // Calculate score for this timeslot (higher is better)
        let score = 100;

        // Prefer morning slots (periods 1-6)
        if (startPeriod <= 6) {
          score += 20;
        }

        // Prefer earlier periods within the same time of day
        score -= startPeriod * 2;

        // Prefer less congested timeslots for undergraduate theory courses
        if (isUndergraduateTheory) {
          const usage = timeslotUsage.get(`${day}-${startPeriod}`) || 0;
          score -= usage * 10;
        }

        // FIXED: Balanced day-type distribution scoring for undergraduate theory courses
        // Instead of using fixed preferences, we now promote balanced distribution
        if (isUndergraduateTheory) {
          const isLongDay = ['Mon', 'Wed'].includes(day);
          const isMorning = startPeriod <= 6;

          // Calculate current day-type distribution in existing schedule
          const existingLongDaySessions = existingSchedule.filter(s =>
            s.courseType === 'Theory' &&
            !s.academicLevel.includes('diploma') &&
            !s.academicLevel.includes('masters') &&
            !s.academicLevel.includes('phd') &&
            ['Mon', 'Wed'].includes(s.day)
          ).length;

          const existingRegularDaySessions = existingSchedule.filter(s =>
            s.courseType === 'Theory' &&
            !s.academicLevel.includes('diploma') &&
            !s.academicLevel.includes('masters') &&
            !s.academicLevel.includes('phd') &&
            ['Sun', 'Tue', 'Thu'].includes(s.day)
          ).length;

          const totalExistingSessions = existingLongDaySessions + existingRegularDaySessions;

          // Calculate current distribution ratio
          let longDayRatio = 0;
          if (totalExistingSessions > 0) {
            longDayRatio = existingLongDaySessions / totalExistingSessions;
          }

          // Ideal ratio is around 0.4 for long days (40%) and 0.6 for regular days (60%)
          // This reflects the fact that there are 2 long days vs 3 regular days
          const idealLongDayRatio = 0.4;

          // Promote balance by favoring the underrepresented day type
          if (isLongDay && longDayRatio < idealLongDayRatio) {
            score += 15; // Bonus for long days when they're underrepresented
          } else if (!isLongDay && longDayRatio > idealLongDayRatio) {
            score += 15; // Bonus for regular days when long days are overrepresented
          }

          // Small bonus for morning periods to maintain preference
          if (isMorning) {
            score += 5;
          }
        }

        // Lecturer preference bonus
        if (assignedLecturerId) {
          const isMorning = startPeriod <= 6;
          const lecturer = lecturers.find(l => l.id === assignedLecturerId);
          if (lecturer?.preferredTiming === 'Morning' && isMorning) {
            score += 15;
          } else if (lecturer?.preferredTiming === 'Evening' && !isMorning) {
            score += 15;
          } else if (lecturer?.preferredTiming === 'Both') {
            score += 5;
          }
        }

        availableTimeSlots.push({ startDay: day, startPeriod, score });
      }
    }
  });

  // Removed total available slots log to reduce console output
  return availableTimeSlots;
};

// Performance optimization: Validation result caching
const validationCache = new Map<string, boolean>();

// Performance monitoring
interface PerformanceMetrics {
  startTime: number;
  endTime: number;
  sectionsProcessed: number;
  rulesViolated: number;
  cacheHits: number;
  cacheMisses: number;
  validationTime: number;
  patternSelectionTime: number;
  lecturerAssignmentAttempts: number;
  lecturerAssignmentSuccesses: number;
  lecturerAssignmentFailures: number;
  lecturerConstraintViolations: number;
}

interface PerformanceReport {
  totalDuration: number;
  averageTimePerSection: number;
  cacheEfficiency: number;
  rulesViolated: number;
  sectionsProcessed: number;
  validationTimePercentage: number;
  patternSelectionTimePercentage: number;
  lecturerAssignmentSuccessRate: number;
  lecturerAssignmentAttempts: number;
  lecturerConstraintViolations: number;
}

class SchedulingProfiler {
  private metrics: PerformanceMetrics = {
    startTime: 0,
    endTime: 0,
    sectionsProcessed: 0,
    rulesViolated: 0,
    cacheHits: 0,
    cacheMisses: 0,
    validationTime: 0,
    patternSelectionTime: 0,
    lecturerAssignmentAttempts: 0,
    lecturerAssignmentSuccesses: 0,
    lecturerAssignmentFailures: 0,
    lecturerConstraintViolations: 0
  };

  startProfiling(): void {
    this.metrics.startTime = performance.now();
  }

  endProfiling(): PerformanceReport {
    this.metrics.endTime = performance.now();
    return this.generateReport();
  }

  recordCacheHit(): void {
    this.metrics.cacheHits++;
  }

  recordCacheMiss(): void {
    this.metrics.cacheMisses++;
  }

  recordRuleViolation(): void {
    this.metrics.rulesViolated++;
  }

  recordSectionProcessed(): void {
    this.metrics.sectionsProcessed++;
  }

  recordValidationTime(time: number): void {
    this.metrics.validationTime += time;
  }

  recordPatternSelectionTime(time: number): void {
    this.metrics.patternSelectionTime += time;
  }

  recordLecturerAssignmentAttempt(): void {
    this.metrics.lecturerAssignmentAttempts++;
  }

  recordLecturerAssignmentSuccess(): void {
    this.metrics.lecturerAssignmentSuccesses++;
  }

  recordLecturerAssignmentFailure(): void {
    this.metrics.lecturerAssignmentFailures++;
  }

  recordLecturerConstraintViolation(): void {
    this.metrics.lecturerConstraintViolations++;
  }

  public generateReport(): PerformanceReport {
    const duration = this.metrics.endTime - this.metrics.startTime;
    const avgTimePerSection = this.metrics.sectionsProcessed > 0
      ? duration / this.metrics.sectionsProcessed
      : 0;
    const cacheEfficiency = (this.metrics.cacheHits + this.metrics.cacheMisses) > 0
      ? this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses)
      : 0;

    const lecturerAssignmentSuccessRate = this.metrics.lecturerAssignmentAttempts > 0
      ? (this.metrics.lecturerAssignmentSuccesses / this.metrics.lecturerAssignmentAttempts) * 100
      : 0;

    return {
      totalDuration: duration,
      averageTimePerSection: avgTimePerSection,
      cacheEfficiency,
      rulesViolated: this.metrics.rulesViolated,
      sectionsProcessed: this.metrics.sectionsProcessed,
      validationTimePercentage: duration > 0 ? (this.metrics.validationTime / duration) * 100 : 0,
      patternSelectionTimePercentage: duration > 0 ? (this.metrics.patternSelectionTime / duration) * 100 : 0,
      lecturerAssignmentSuccessRate,
      lecturerAssignmentAttempts: this.metrics.lecturerAssignmentAttempts,
      lecturerConstraintViolations: this.metrics.lecturerConstraintViolations
    };
  }
}

// Global profiler instance
const globalProfiler = new SchedulingProfiler();

// Performance monitoring exports
export const startPerformanceProfiling = (): void => {
  globalProfiler.startProfiling();
};

export const getPerformanceReport = (): PerformanceReport => {
  return globalProfiler.endProfiling();
};

export const resetPerformanceMetrics = (): void => {
  // Create a new profiler instance to reset all metrics
  Object.assign(globalProfiler, new SchedulingProfiler());
};

// Retry mechanism interfaces and types
interface RetryAttemptResult {
  success: boolean;
  scheduledSections: string[];
  unscheduledSections: string[];
  sessions: AutoSession[];
  appSessions: AppSession[];
  successRate: number;
  strategy: string;
  duration: number;
}

interface RetryReport {
  totalAttempts: number;
  finalResult: RetryAttemptResult;
  bestResult: RetryAttemptResult;
  attemptHistory: RetryAttemptResult[];
  totalDuration: number;
  improvementAchieved: boolean;
  terminationReason: 'success' | 'max_retries' | 'no_improvement' | 'timeout';
}

// Note: LecturerCapacityMatrix interface removed as it contained gap-related logic
// that is no longer used after removing gap validation from auto-scheduling.

// Note: Enhanced scheduling result interface removed as it's no longer used
// after removing hardcoded constraint checks

// Retry strategies enum
enum RetryStrategy {
  STANDARD = 'standard',
  RELAXED_LECTURER_PREFERENCES = 'relaxed_lecturer_preferences',
  RANDOMIZED_ORDER = 'randomized_order',
  RELAXED_SOFT_CONSTRAINTS = 'relaxed_soft_constraints',
  EMERGENCY_MINIMAL_CONSTRAINTS = 'emergency_minimal_constraints'
}

// Removed local CPSRefinementReport interface - using imported one from cpsRefinement.ts

// Removed unused CPSConstraintViolation interface

// Helper function to calculate success rate
function calculateSuccessRate(scheduledSections: string[], totalSections: number): number {
  if (totalSections === 0) return 1.0;
  return scheduledSections.length / totalSections;
}

// Helper function to shuffle array (for randomized order strategy)
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// CRITICAL: Analyze actual lecturer utilization vs optimal load distribution
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function analyzeLecturerUtilization(_lecturers: Lecturer[], _finalSchedule: AutoSession[]): void {
  // Lecturer utilization analysis logging removed to reduce console output

  let _totalLecturers = 0;
  let _underutilizedLecturers = 0;
  let _optimallyUtilizedLecturers = 0;
  let _overutilizedLecturers = 0;
  let _unassignedLecturers = 0;

  const utilizationDetails: Array<{
    name: string;
    currentLoad: number;
    maxLoad: number;
    utilizationRate: number;
    status: string;
    teachingHours: number;
    supervisionHours: number;
  }> = [];

  _lecturers.forEach((lecturer: Lecturer) => {
    _totalLecturers++;

    // Calculate current teaching load from schedule
    const lecturerSessions = _finalSchedule.filter((s: AutoSession) => s.lecturerId === lecturer.id);
    let teachingHours = 0;

    lecturerSessions.forEach(session => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      teachingHours += isLongDay ? 1.5 : 1.0;
    });

    // Get supervision hours (assuming Fall semester for now)
    const supervisionHours = lecturer.supervisionHoursFall || 0;
    const currentLoad = teachingHours + supervisionHours;
    const maxLoad = lecturer.maxYearLoad || 12; // Default max load

    const utilizationRate = maxLoad > 0 ? (currentLoad / maxLoad) * 100 : 0;

    let status = '';
    if (currentLoad === 0) {
      status = 'UNASSIGNED';
      _unassignedLecturers++;
    } else if (utilizationRate < 50) {
      status = 'UNDERUTILIZED';
      _underutilizedLecturers++;
    } else if (utilizationRate >= 50 && utilizationRate <= 90) {
      status = 'OPTIMAL';
      _optimallyUtilizedLecturers++;
    } else {
      status = 'OVERUTILIZED';
      _overutilizedLecturers++;
    }

    utilizationDetails.push({
      name: `${lecturer.firstName} ${lecturer.lastName}`,
      currentLoad,
      maxLoad,
      utilizationRate,
      status,
      teachingHours,
      supervisionHours
    });
  });

  // Sort by utilization rate (lowest first to highlight underutilization)
  utilizationDetails.sort((a, b) => a.utilizationRate - b.utilizationRate);

  // Detailed breakdown removed to reduce console output

  // Calculate overall system efficiency
  const totalCurrentLoad = utilizationDetails.reduce((sum, l) => sum + l.currentLoad, 0);
  const totalMaxLoad = utilizationDetails.reduce((sum, l) => sum + l.maxLoad, 0);
  const systemEfficiency = totalMaxLoad > 0 ? (totalCurrentLoad / totalMaxLoad) * 100 : 0;

  console.log(`\n🎯 SYSTEM EFFICIENCY: ${systemEfficiency.toFixed(1)}% (${totalCurrentLoad.toFixed(1)}/${totalMaxLoad} total hours utilized)`);

  if (systemEfficiency < 60) {
    console.log(`❌ CRITICAL: System efficiency is below 60% - significant underutilization detected!`);
  } else if (systemEfficiency < 80) {
    console.log(`⚠️ WARNING: System efficiency is below 80% - room for improvement in lecturer utilization`);
  } else {
    console.log(`✅ GOOD: System efficiency is above 80%`);
  }
}

// CRITICAL: Analyze course-lecturer matching to understand assignment failures
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function analyzeCourseeLecturerMatching(_sections: Section[], _lecturers: Lecturer[]): void {
  // Course-lecturer matching analysis logging removed to reduce console output

  // Get unique courses from sections
  const uniqueCourses = [...new Set(_sections.map((s: Section) => s.courseCode))];

  // Analyze each course
  const courseAnalysis: Array<{
    courseCode: string;
    totalSections: number;
    eligibleLecturers: number;
    lecturerNames: string[];
    hasNoLecturers: boolean;
  }> = [];

  uniqueCourses.forEach((courseCode: string) => {
    const courseSections = _sections.filter((s: Section) => s.courseCode === courseCode);
    const eligibleLecturers = _lecturers.filter((lecturer: Lecturer) =>
      lecturer.coursesAbleToTeach &&
      lecturer.coursesAbleToTeach.includes(courseCode)
    );

    courseAnalysis.push({
      courseCode,
      totalSections: courseSections.length,
      eligibleLecturers: eligibleLecturers.length,
      lecturerNames: eligibleLecturers.map((l: Lecturer) => `${l.firstName} ${l.lastName || ''}`).slice(0, 3),
      hasNoLecturers: eligibleLecturers.length === 0
    });
  });

  // Sort by courses with no lecturers first, then by fewest lecturers
  courseAnalysis.sort((a, b) => {
    if (a.hasNoLecturers && !b.hasNoLecturers) return -1;
    if (!a.hasNoLecturers && b.hasNoLecturers) return 1;
    return a.eligibleLecturers - b.eligibleLecturers;
  });

  // Count problematic courses
  const _coursesWithNoLecturers = courseAnalysis.filter(c => c.hasNoLecturers).length;
  const _coursesWithFewLecturers = courseAnalysis.filter(c => c.eligibleLecturers > 0 && c.eligibleLecturers <= 2).length;

  // Detailed course breakdown removed to reduce console output

  // Analyze lecturer course coverage
  const lecturerCoverage: Array<{
    name: string;
    coursesCount: number;
    courses: string[];
    hasNoCourses: boolean;
  }> = [];

  _lecturers.forEach((lecturer: Lecturer) => {
    const courses = lecturer.coursesAbleToTeach || [];
    lecturerCoverage.push({
      name: `${lecturer.firstName} ${lecturer.lastName || ''}`,
      coursesCount: courses.length,
      courses: courses.slice(0, 5),
      hasNoCourses: courses.length === 0
    });
  });

  // Sort by lecturers with no courses first
  lecturerCoverage.sort((a, b) => {
    if (a.hasNoCourses && !b.hasNoCourses) return -1;
    if (!a.hasNoCourses && b.hasNoCourses) return 1;
    return a.coursesCount - b.coursesCount;
  });

  const _lecturersWithNoCourses = lecturerCoverage.filter(l => l.hasNoCourses).length;
  const _lecturersWithFewCourses = lecturerCoverage.filter(l => l.coursesCount > 0 && l.coursesCount <= 2).length;

  // Detailed lecturer coverage breakdown removed to reduce console output

  // Root cause analysis (logging removed to reduce console output)

  const totalPotentialAssignments = courseAnalysis.reduce((sum, c) => sum + (c.totalSections * c.eligibleLecturers), 0);
  const totalSections = _sections.length;
  const _avgLecturersPerSection = totalSections > 0 ? totalPotentialAssignments / totalSections : 0;

  // Assignment potential analysis logging removed to reduce console output
}

// CRITICAL: Analyze constraint violations to understand assignment failures
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function analyzeConstraintViolations(_lecturers: Lecturer[], _sections: Section[], _finalSchedule: AutoSession[]): void {
  // Analysis logging removed to reduce console output

  // Analyze lecturer constraint patterns
  const constraintViolationPatterns: Array<{
    lecturerId: string;
    lecturerName: string;
    maxTeachingDays: number;
    currentTeachingDays: number;
    maxYearLoad: number;
    currentLoad: number;
    preferredTiming: string;
    violationReasons: string[];
  }> = [];

  _lecturers.forEach((lecturer: Lecturer) => {
    const lecturerSessions = _finalSchedule.filter((s: AutoSession) => s.lecturerId === lecturer.id);
    const teachingDays = new Set(lecturerSessions.map((s: AutoSession) => s.day));

    let currentLoad = 0;
    lecturerSessions.forEach((session: AutoSession) => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      currentLoad += isLongDay ? 1.5 : 1.0;
    });

    const violationReasons: string[] = [];

    // Check teaching days constraint
    const maxDays = lecturer.maxTeachingDaysPerWeek || 5;
    if (teachingDays.size >= maxDays) {
      violationReasons.push(`At max teaching days (${teachingDays.size}/${maxDays})`);
    }

    // Check load constraint
    const maxLoad = lecturer.maxYearLoad || 18;
    if (currentLoad >= maxLoad * 0.8) { // 80% threshold
      violationReasons.push(`Near max load (${currentLoad.toFixed(1)}/${maxLoad})`);
    }

    // Note: Gap constraint checking has been removed from lecturer analysis.
    // Empty hours validation is handled by the rule system via the 'lecturer-max-gap' rule.

    constraintViolationPatterns.push({
      lecturerId: lecturer.id,
      lecturerName: `${lecturer.firstName} ${lecturer.lastName || ''}`,
      maxTeachingDays: maxDays,
      currentTeachingDays: teachingDays.size,
      maxYearLoad: maxLoad,
      currentLoad,
      preferredTiming: lecturer.preferredTiming || 'Both',
      violationReasons
    });
  });

  // Sort by number of violations (most constrained first)
  constraintViolationPatterns.sort((a, b) => b.violationReasons.length - a.violationReasons.length);

  // Count constraint types
  const constraintCounts = {
    maxDaysReached: 0,
    nearMaxLoad: 0,
    timingRestrictions: 0,
    totalConstrained: 0
  };

  constraintViolationPatterns.forEach(pattern => {
    if (pattern.violationReasons.length > 0) {
      constraintCounts.totalConstrained++;
    }

    pattern.violationReasons.forEach(reason => {
      if (reason.includes('max teaching days')) constraintCounts.maxDaysReached++;
      if (reason.includes('max load')) constraintCounts.nearMaxLoad++;
    });

    if (pattern.preferredTiming !== 'Both') {
      constraintCounts.timingRestrictions++;
    }
  });

  // Detailed constraint breakdown removed to reduce console output

  // Analyze unassigned sections
  const _unassignedSections = _sections.filter((section: Section) => {
    return !_finalSchedule.some((session: AutoSession) => session.sectionId === section.id);
  });

  // Unassigned sections analysis logging removed to reduce console output

  // Root cause analysis (logging removed to reduce console output)

  // Calculate constraint flexibility score
  const _flexibilityScore = 100 - (constraintCounts.totalConstrained / _lecturers.length) * 100;

  // Constraint flexibility analysis logging removed to reduce console output
}

// Note: buildLecturerCapacityMatrix function has been removed as it contained gap-related logic
// that is no longer used after removing gap validation from auto-scheduling.

// Note: diagnoseLecturerAssignmentFailures function has been removed as it was unused
// after removing gap validation from auto-scheduling.

// Helper function to apply retry strategy modifications
function applyRetryStrategy(
  sections: Section[],
  strategy: RetryStrategy,
  lecturers: Lecturer[]
): { modifiedSections: Section[], modifiedLecturers: Lecturer[] } {
  let modifiedSections = [...sections];
  let modifiedLecturers = [...lecturers];

  switch (strategy) {
    case RetryStrategy.STANDARD:
      // No modifications for standard strategy
      break;

    case RetryStrategy.RELAXED_LECTURER_PREFERENCES:
      // Modify lecturer preferences to be more flexible
      modifiedLecturers = modifiedLecturers.map(lecturer => ({
        ...lecturer,
        preferredTiming: 'Both', // Make all lecturers flexible with timing
        maxTeachingDaysPerWeek: Math.min((lecturer.maxTeachingDaysPerWeek || 5) + 1, 5), // Slightly increase max days
      }));
      // console.log('🔄 Applied relaxed lecturer preferences strategy');
      break;

    case RetryStrategy.RANDOMIZED_ORDER: {
      // Randomize section order within each academic level
      const sectionsByLevel: Record<string, Section[]> = {};
      modifiedSections.forEach(section => {
        const level = getAcademicLevel(section.courseCode);
        if (!sectionsByLevel[level]) sectionsByLevel[level] = [];
        sectionsByLevel[level].push(section);
      });

      // Shuffle sections within each level
      Object.keys(sectionsByLevel).forEach(level => {
        sectionsByLevel[level] = shuffleArray(sectionsByLevel[level]);
      });

      // Reconstruct the sections array
      modifiedSections = Object.values(sectionsByLevel).flat();
      // console.log('🔄 Applied randomized order strategy');
      break;
    }

    case RetryStrategy.RELAXED_SOFT_CONSTRAINTS:
      // Increase lecturer flexibility further
      modifiedLecturers = modifiedLecturers.map(lecturer => ({
        ...lecturer,
        maxTeachingDaysPerWeek: 5, // Allow maximum teaching days
        preferredTiming: 'Both',
      }));
      // console.log('🔄 Applied relaxed soft constraints strategy');
      break;

    case RetryStrategy.EMERGENCY_MINIMAL_CONSTRAINTS:
      // Remove most lecturer constraints for emergency scheduling
      modifiedLecturers = modifiedLecturers.map(lecturer => ({
        ...lecturer,
        maxTeachingDaysPerWeek: 5, // Maximum days
        maxConsecutivePeriods: 8, // Very relaxed consecutive periods
        preferredTiming: 'Both',
      }));
      // console.log('🔄 Applied emergency minimal constraints strategy');
      break;

    default:
      console.warn(`Unknown retry strategy: ${strategy}`);
  }

  return { modifiedSections, modifiedLecturers };
}

// Generate hash for existing schedule to use in cache keys
function generateScheduleHash(existingSchedule: AutoSession[]): string {
  // Create a simple hash based on schedule content
  const scheduleString = existingSchedule
    .map(s => `${s.sectionId}-${s.day}-${s.period}-${s.lecturerId}`)
    .sort()
    .join('|');

  // Simple hash function
  let hash = 0;
  for (let i = 0; i < scheduleString.length; i++) {
    const char = scheduleString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString();
}

function getCachedValidation(
  sectionId: string,
  day: string,
  period: number,
  existingScheduleHash: string
): boolean | null {
  const key = `${sectionId}-${day}-${period}-${existingScheduleHash}`;
  return validationCache.get(key) ?? null;
}

function setCachedValidation(
  sectionId: string,
  day: string,
  period: number,
  existingScheduleHash: string,
  result: boolean
): void {
  const key = `${sectionId}-${day}-${period}-${existingScheduleHash}`;
  validationCache.set(key, result);

  // Limit cache size to prevent memory issues
  if (validationCache.size > 10000) {
    const firstKey = validationCache.keys().next().value;
    if (firstKey) {
      validationCache.delete(firstKey);
    }
  }
}

// Note: LecturerConstraints interface and computeLecturerConstraints function removed
// as they contained gap-related logic that is no longer used after removing gap validation.

// Auto-schedule a specific section with optimizations
export const autoScheduleSection = (
  section: Section,
  existingSchedule: AutoSession[],
  lecturers: Lecturer[] = [] // Add lecturers parameter with default empty array
): { success: boolean; sessions: AutoSession[]; appSessions: AppSession[]; message?: string } => {
  // Start profiling for this section
  globalProfiler.recordSectionProcessed();

  // Get academic level from course code
  const academicLevel = getAcademicLevel(section.courseCode);

  // Always use the correct academic level for pattern matching
  // We'll handle the rule constraints separately
  const effectiveAcademicLevel = academicLevel;

  // Check if we have a lecturer ID already assigned to this section
  let lecturerId = section.lecturerId || '';

  // Get rule system state
  const ruleSystemState = useRuleSystemStore.getState();

  // Check if lecturer assignment is enabled
  const assignLecturers = ruleSystemState.assignLecturersInAutoScheduling;



  // If lecturer assignment is enabled and we have lecturers data, try to find an eligible lecturer
  if (assignLecturers && lecturers.length > 0 && !lecturerId) {
    globalProfiler.recordLecturerAssignmentAttempt();

    // Find lecturers who can teach this course
    const eligibleLecturers = lecturers.filter(lecturer =>
      lecturer.coursesAbleToTeach &&
      lecturer.coursesAbleToTeach.includes(section.courseCode)
    );



    // SILENT MODE: Only track failures, don't log details to avoid spam
    if (eligibleLecturers.length === 0) {
      // Just record the failure without detailed logging
      globalProfiler.recordLecturerAssignmentFailure();
      // IMPORTANT: When no eligible lecturers are found and lecturer assignment is enabled,
      // we should still proceed with scheduling without a lecturer (as per the rule description)
      // The lecturerId will remain empty, which is the correct behavior
    }

    if (eligibleLecturers.length > 0) {
      // Silent mode - removed verbose logging

      // Get the current semester from the rule system state
      const currentSemester = 'Fall'; // Default to Fall if not available

      // Calculate current load for each eligible lecturer using the proper load calculation
      const lecturerLoads = new Map<string, number>();
      const lecturerMaxLoads = new Map<string, number>();

      // Calculate actual semester load for all eligible lecturers
      // Convert AutoSession[] to Session[] for type compatibility
      const existingScheduleConverted = convertAutoSessionsToSessions(existingSchedule);
      eligibleLecturers.forEach(lecturer => {
        // Calculate the current semester load (teaching + supervision hours)
        const currentLoad = calculateSemesterLoad(lecturer, currentSemester, existingScheduleConverted);
        lecturerLoads.set(lecturer.id, currentLoad);

        // Calculate the maximum semester load
        const maxLoad = calculateMaxSemesterLoad(lecturer, currentSemester);
        lecturerMaxLoads.set(lecturer.id, maxLoad);
      });

      // Silent mode - no load debugging logs

      // Consider lecturer preferences and constraints
      // We don't know the specific day/period yet, so we'll use a general approach
      // FIXED: Removed timing preference bias that was causing clustering

      // Score each lecturer based on load percentage and preferences
      const lecturerScores = new Map<string, number>();

      eligibleLecturers.forEach(lecturer => {
        const currentLoad = lecturerLoads.get(lecturer.id) || 0;
        const maxLoad = lecturerMaxLoads.get(lecturer.id) || 1; // Avoid division by zero
        const loadPercentage = (currentLoad / maxLoad) * 100;

        // Base score inversely proportional to load percentage
        // The closer to max load, the lower the score
        let score = 100 - loadPercentage;

        // FIXED: Removed timing preference bias that was causing day-type clustering
        // The original logic gave +20/-30 scoring based on timing preferences,
        // which systematically assigned lecturers to specific day types

        // NEW: Calculate lecturer's current day-type distribution to promote balance
        const lecturerSessions = existingScheduleConverted.filter(s => s.lecturerId === lecturer.id);
        const longDaySessions = lecturerSessions.filter(s => ['Mon', 'Wed'].includes(s.day)).length;
        const regularDaySessions = lecturerSessions.filter(s => ['Sun', 'Tue', 'Thu'].includes(s.day)).length;
        const totalSessions = longDaySessions + regularDaySessions;

        // Calculate current day-type ratio for this lecturer
        let lecturerLongDayRatio = 0;
        if (totalSessions > 0) {
          lecturerLongDayRatio = longDaySessions / totalSessions;
        }

        // Promote balance: favor lecturers who are underrepresented in day types
        const idealLongDayRatio = 0.4; // 40% long days, 60% regular days

        // Small bonus for balanced distribution (much smaller than the original timing bias)
        // This promotes lecturers being assigned to both day types instead of clustering
        if (totalSessions > 0) {
          if (lecturerLongDayRatio < idealLongDayRatio) {
            // Lecturer has fewer long day sessions, small bonus to encourage balance
            score += 3; // Small bonus to promote balance
          } else if (lecturerLongDayRatio > idealLongDayRatio) {
            // Lecturer has more long day sessions, small bonus to encourage balance
            score += 3; // Small bonus to promote balance
          }
        }

        // Adjust score based on gender restrictions (if applicable)
        if (lecturer.femaleOnlyTeaching && section.gender === 'M') {
          score -= 100; // Large penalty for violating gender restriction
        }

        // If the lecturer is already at or above max load, give a large penalty
        if (loadPercentage >= 100) {
          score -= 200; // Very large penalty for exceeding max load
        }

        // IMPORTANT: Consider the number of teaching days
        // Get the current number of teaching days for this lecturer
        // Use the already converted schedule
        const teachingDaysCount = getLecturerTeachingDaysCount(lecturer.id, existingScheduleConverted);
        const maxTeachingDays = lecturer.maxTeachingDaysPerWeek || 5; // Default to 5 if not specified

        // Calculate a percentage of days used
        const daysPercentage = (teachingDaysCount / maxTeachingDays) * 100;

        // Apply a penalty based on the percentage of days used
        // The more days already used, the lower the score
        score -= daysPercentage * 0.5; // Adjust the multiplier as needed

        // If the lecturer is already at max teaching days, give a large penalty
        if (teachingDaysCount >= maxTeachingDays) {
          score -= 150; // Large penalty for being at max teaching days
        }

        // Note: Gap-based scoring has been removed from lecturer selection.
        // Empty hours validation is handled by the rule system via the 'lecturer-max-gap' rule.

        // Store the final score
        lecturerScores.set(lecturer.id, score);
      });

      // Silent mode - no detailed scoring logs

      // FIXED: Implement round-robin lecturer selection to prevent clustering
      // Instead of always selecting the highest-scoring lecturer, we'll use a more balanced approach

      // Filter out lecturers who are overloaded or have critical constraints
      const viableLecturers = eligibleLecturers.filter(lecturer => {
        const score = lecturerScores.get(lecturer.id) || 0;
        const currentLoad = lecturerLoads.get(lecturer.id) || 0;
        const maxLoad = lecturerMaxLoads.get(lecturer.id) || 1;
        const loadPercentage = (currentLoad / maxLoad) * 100;

        // Only consider lecturers who aren't overloaded and don't have critical violations
        return score > -100 && loadPercentage < 95; // Allow up to 95% load
      });

      let selectedLecturer = eligibleLecturers[0]; // Fallback

      if (viableLecturers.length > 0) {
        // Use a round-robin approach based on current workload distribution
        // Sort by current load (ascending) to favor less loaded lecturers
        const sortedByLoad = viableLecturers.sort((a, b) => {
          const loadA = lecturerLoads.get(a.id) || 0;
          const loadB = lecturerLoads.get(b.id) || 0;
          return loadA - loadB;
        });

        // Select from the least loaded lecturers (top 50% or at least 1)
        const topCandidates = sortedByLoad.slice(0, Math.max(1, Math.ceil(sortedByLoad.length * 0.5)));

        // Among top candidates, use round-robin based on section ID hash for consistency
        const sectionHash = section.id.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
        selectedLecturer = topCandidates[sectionHash % topCandidates.length];

      } else {
        // Fallback to highest score if no viable lecturers
        let maxScore = Number.MIN_SAFE_INTEGER;
        eligibleLecturers.forEach(lecturer => {
          const score = lecturerScores.get(lecturer.id) || 0;
          if (score > maxScore) {
            maxScore = score;
            selectedLecturer = lecturer;
          }
        });
      }

      // Check if the selected lecturer would exceed their maximum load with the new sessions
      // We need to estimate the additional load from the pattern we're going to schedule

      // Assign the selected lecturer
      lecturerId = selectedLecturer.id;
      // Note: Lecturer constraint checking is now handled by the rule system
      // to respect enabled/disabled rule settings. We'll assign the lecturer
      // and let the validation system handle constraint checking.
      globalProfiler.recordLecturerAssignmentSuccess();
    } else {
      globalProfiler.recordLecturerAssignmentFailure();
    }
  } else if (!assignLecturers) {
    // Removed lecturer assignment disabled logging to reduce console output
  }



  // Note: isUndergraduateTheory variable removed as it was unused after gap validation removal.

  // Silent mode - no rule system state logging

  // Debug: Check if existingSchedule has all the necessary information
  const hasValidCourseInfo = existingSchedule.every(s =>
    s.courseCode && s.courseType && s.academicLevel
  );

  if (!hasValidCourseInfo) {
    // Removed course information warning logs to reduce console output
  }

  // Get all matching patterns for this section
  const availablePatterns = getSchedulePatterns(
    section.contactHours,
    section.courseType,
    effectiveAcademicLevel
  );

  if (availablePatterns.length === 0) {
    return {
      success: false,
      sessions: [],
      appSessions: [],
      message: `No scheduling pattern available for ${section.contactHours} credit hours ${section.courseType} course`
    };
  }

  // Select optimal pattern using intelligent scoring with timing
  const patternSelectionStartTime = performance.now();
  const schedulePatterns = selectOptimalPatterns(availablePatterns, section, existingSchedule);
  const patternSelectionEndTime = performance.now();
  globalProfiler.recordPatternSelectionTime(patternSelectionEndTime - patternSelectionStartTime);

  // Calculate existing scheduled hours for this section
  const existingSectionSessions = existingSchedule.filter(s => s.sectionId === section.id);
  let existingScheduledHours = 0;

  existingSectionSessions.forEach(session => {
    // Calculate hours based on day type
    const isLongDay = ['Mon', 'Wed'].includes(session.day);
    existingScheduledHours += isLongDay ? 1.5 : 1.0;
  });

  // Try each pattern type (e.g., long days first, then regular days)
  for (const schedulePattern of schedulePatterns) {
    // Try each pattern variation within this pattern type
    for (const pattern of schedulePattern.patterns) {
      // Calculate total hours this pattern would add
      let patternTotalHours = 0;
      pattern.days.forEach((day, dayIndex) => {
        const isLongDay = ['Mon', 'Wed'].includes(day);
        const periodsCount = pattern.periodsPerDay[dayIndex] !== undefined
          ? pattern.periodsPerDay[dayIndex]
          : pattern.periodsPerDay[0];

        // Special handling for postgraduate courses
        const isPostgraduate = academicLevel === 'masters' || academicLevel === 'phd' || academicLevel === 'diploma';
        if (isPostgraduate && section.courseType === 'Theory' && section.contactHours === 3) {
          // For postgraduate courses, we need to calculate hours differently
          if (isLongDay) {
            // On long days, 2 periods (9-10) = 3 hours
            patternTotalHours = 3;
          } else {
            // On regular days, 3 periods (10-12) = 3 hours
            patternTotalHours = 3;
          }
        } else {
          // For all other courses, calculate normally
          patternTotalHours += isLongDay ? (periodsCount * 1.5) : periodsCount;
        }
      });

      // Ensure this pattern exactly matches the required contact hours
      // Allow a small epsilon for floating point comparison
      if (Math.abs(patternTotalHours - section.contactHours) > 0.01) {
        continue;
      }

      const availableTimeSlots = getAvailableTimeSlotsForPattern(
        pattern,
        section,
        existingSchedule,
        lecturers,
        lecturerId // Pass the lecturer ID determined by auto-scheduling logic
      );



      if (availableTimeSlots.length > 0) {
        // Sort available timeslots by score (highest first), then by preference
        const sortedTimeSlots = [...availableTimeSlots].sort((a, b) => {
          // Primary sort: by score (higher is better)
          if (a.score !== b.score) {
            return b.score - a.score;
          }

          // Secondary sort: by day preference (Sun, Mon, Tue, Wed, Thu)
          const dayOrder = { 'Sun': 0, 'Mon': 1, 'Tue': 2, 'Wed': 3, 'Thu': 4 };
          const dayComparison = dayOrder[a.startDay as keyof typeof dayOrder] - dayOrder[b.startDay as keyof typeof dayOrder];

          if (dayComparison !== 0) {
            return dayComparison;
          }

          // Tertiary sort: by period (earlier is better)
          return a.startPeriod - b.startPeriod;
        });

        // Take the best available timeslot (highest score)
        const slot = sortedTimeSlots[0];

        // Create sessions for this pattern
        const newSessions: AutoSession[] = [];
        const newAppSessions: AppSession[] = [];

        // Create a temporary validation schedule that will be updated as we process each day
        const tempValidationSchedule = [...existingSchedule];

        // Note: Lecturer constraint validation is now handled by the rule system
        // during the actual session creation and validation phase

        // Process each day in the pattern sequentially, updating the validation schedule after each day
        for (let dayIndex = 0; dayIndex < pattern.days.length; dayIndex++) {
          const day = pattern.days[dayIndex];
          // Get the number of consecutive periods needed for this day
          const periodsCount = pattern.periodsPerDay[dayIndex] !== undefined
            ? pattern.periodsPerDay[dayIndex]
            : pattern.periodsPerDay[0];

          // Always create individual sessions for each period
          // This ensures consistent behavior for all course types
          for (let i = 0; i < periodsCount; i++) {
            const currentPeriod = slot.startPeriod + i;

            // We've already done a comprehensive check for lecturer conflicts
            // in the pre-check phase, so we don't need to check again here.
            // The lecturer will either be assigned to all sessions or none.

            const sessionId = `auto-${section.id}-${day}-${currentPeriod}`;

            const autoSession: AutoSession = {
              id: sessionId,
              sectionId: section.id,
              courseCode: section.courseCode,
              courseType: section.courseType,
              academicLevel,
              gender: section.gender,
              lecturerId: lecturerId, // Use the assigned or found lecturer ID (may be empty if conflict found)
              day,
              period: currentPeriod,
              isAutoGenerated: true
            };

            newSessions.push(autoSession);
            newAppSessions.push(convertToAppSession(autoSession));

            // IMPORTANT: Add to temporary validation schedule to ensure subsequent days in the pattern
            // consider the days already added by this pattern
            tempValidationSchedule.push(autoSession);
          }
        }

        // Verify the total scheduled hours will match exactly the contact hours
        let totalScheduledHours = existingScheduledHours;
        newSessions.forEach(session => {
          const isLongDay = ['Mon', 'Wed'].includes(session.day);
          totalScheduledHours += isLongDay ? 1.5 : 1.0;
        });

        // Ensure we're not overscheduling or underscheduling
        // Allow a small epsilon for floating point comparison
        if (Math.abs(totalScheduledHours - section.contactHours) > 0.01) {
          // Removed hour mismatch logging to reduce console output
          continue;
        }

        return {
          success: true,
          sessions: newSessions,
          appSessions: newAppSessions
        };
      }
    }
  }





  return {
    success: false,
    sessions: [],
    appSessions: [],
    message: 'No available time slots found that satisfy all rules'
  };
};

// Helper function to find the best lecturer for both male and female sections of the same course
const findBestLecturerForBothGenders = (
  courseCode: string,
  maleSection: Section | undefined,
  femaleSection: Section | undefined,
  existingSchedule: AutoSession[],
  lecturers: Lecturer[]
): string => {
  if (!maleSection || !femaleSection || lecturers.length === 0) {
    return ''; // No sections or no lecturers available
  }

  // Removed lecturer finding logs to reduce console output

  // Find lecturers who can teach this course
  const eligibleLecturers = lecturers.filter(lecturer =>
    lecturer.coursesAbleToTeach &&
    lecturer.coursesAbleToTeach.includes(courseCode)
  );

  if (eligibleLecturers.length === 0) {
    return '';
  }

  // Filter out lecturers with female-only teaching restriction
  const lecturersForBothGenders = eligibleLecturers.filter(lecturer =>
    !lecturer.femaleOnlyTeaching // Exclude lecturers who can only teach female sections
  );

  if (lecturersForBothGenders.length === 0) {
    return '';
  }

  // Calculate current load for each eligible lecturer
  const lecturerLoads = new Map<string, number>();
  const lecturerMaxLoads = new Map<string, number>();
  const lecturerScores = new Map<string, number>();

  // Calculate current load for each lecturer
  lecturersForBothGenders.forEach(lecturer => {
    // Get all sessions assigned to this lecturer
    const lecturerSessions = existingSchedule.filter(s => s.lecturerId === lecturer.id);

    // Calculate total load hours
    let totalLoadHours = 0;
    lecturerSessions.forEach(session => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      totalLoadHours += isLongDay ? 1.5 : 1.0;
    });

    // Store the load
    lecturerLoads.set(lecturer.id, totalLoadHours);

    // Store the max load (default to 12 if not specified)
    const maxLoad = lecturer.maxLoadHoursPerWeek || 12;
    lecturerMaxLoads.set(lecturer.id, maxLoad);
  });

  // Score each lecturer based on various factors
  lecturersForBothGenders.forEach(lecturer => {
    const currentLoad = lecturerLoads.get(lecturer.id) || 0;
    const maxLoad = lecturerMaxLoads.get(lecturer.id) || 1; // Avoid division by zero
    const loadPercentage = (currentLoad / maxLoad) * 100;

    // Base score inversely proportional to load percentage
    // The closer to max load, the lower the score
    const score = 100 - loadPercentage;

    // Note: Lecturer constraint checking is now handled by the rule system
    // We'll use a simpler scoring based on current load only

    // Store the final score
    lecturerScores.set(lecturer.id, score);
  });

  // FIXED: Implement round-robin lecturer selection for both-gender courses
  // Filter out overloaded lecturers
  const viableLecturers = lecturersForBothGenders.filter(lecturer => {
    const currentLoad = lecturerLoads.get(lecturer.id) || 0;
    const maxLoad = lecturerMaxLoads.get(lecturer.id) || 1;
    const loadPercentage = (currentLoad / maxLoad) * 100;
    return loadPercentage < 95; // Allow up to 95% load
  });

  let selectedLecturerId = '';

  if (viableLecturers.length > 0) {
    // Sort by current load (ascending) to favor less loaded lecturers
    const sortedByLoad = viableLecturers.sort((a, b) => {
      const loadA = lecturerLoads.get(a.id) || 0;
      const loadB = lecturerLoads.get(b.id) || 0;
      return loadA - loadB;
    });

    // Select from the least loaded lecturers (top 50% or at least 1)
    const topCandidates = sortedByLoad.slice(0, Math.max(1, Math.ceil(sortedByLoad.length * 0.5)));

    // Use course code hash for consistent round-robin selection
    const courseHash = courseCode.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
    selectedLecturerId = topCandidates[courseHash % topCandidates.length].id;

  } else {
    // Fallback to highest score if no viable lecturers
    let maxScore = Number.MIN_SAFE_INTEGER;
    lecturersForBothGenders.forEach(lecturer => {
      const score = lecturerScores.get(lecturer.id) || 0;
      if (score > maxScore) {
        maxScore = score;
        selectedLecturerId = lecturer.id;
      }
    });
  }

  // Removed lecturer selection logging to reduce console output

  return selectedLecturerId;
};

// Core scheduling function (renamed from autoScheduleAllSections)
const autoScheduleAllSectionsCore = (
  sections: Section[],
  existingSchedule: AutoSession[],
  lecturers: Lecturer[] = [] // Add lecturers parameter with default empty array
): {
  success: boolean;
  scheduledSections: string[];
  unscheduledSections: string[];
  sessions: AutoSession[];
  appSessions: AppSession[];
} => {
  let scheduledSections: string[] = [];
  let unscheduledSections: string[] = [];
  let updatedSchedule = [...existingSchedule];
  let allNewSessions: AutoSession[] = [];
  let allNewAppSessions: AppSession[] = [];

  const levelOrder: Record<string, number> = {
    'phd': 0, 'masters': 1, 'diploma': 2,
    '4th-year': 3, '3rd-year': 4, '2nd-year': 5, '1st-year': 6, 'unknown': 7
  };

  const sectionsByLevel: Record<string, Record<string, Section[]>> = {};
  Object.keys(levelOrder).forEach(level => { sectionsByLevel[level] = {}; });

  sections.forEach(section => {
    const academicLevel = getAcademicLevel(section.courseCode);
    if (!sectionsByLevel[academicLevel]) sectionsByLevel[academicLevel] = {};
    if (!sectionsByLevel[academicLevel][section.courseCode]) sectionsByLevel[academicLevel][section.courseCode] = [];
    sectionsByLevel[academicLevel][section.courseCode].push(section);
  });

  // Removed section grouping and distribution logs to reduce console output

  // Helper function to determine if a course is undergraduate theory
  const isUndergraduateTheory = (courseCode: string, courseType: string): boolean => {
    const academicLevel = getAcademicLevel(courseCode);
    return courseType === 'Theory' &&
           !academicLevel.includes('diploma') &&
           !academicLevel.includes('masters') &&
           !academicLevel.includes('phd');
  };

  // FIXED: Simplified distribution strategy that doesn't assign fixed preferences
  const applySectionDistribution = (sections: Section[], courseCode: string, courseType: string): Section[] => {
    // Only apply to undergraduate theory courses
    if (!isUndergraduateTheory(courseCode, courseType)) {
      return sections;
    }

    // IMPORTANT: We no longer assign fixed distribution preferences to avoid clustering bias
    // Instead, we rely on the balanced scoring system in getAvailableTimeSlotsForPattern
    // to promote even distribution across day types

    // Group sections by gender for processing order
    const maleSection = sections.filter(s => s.gender === 'M');
    const femaleSections = sections.filter(s => s.gender === 'F');

    // Randomize the order within each gender to prevent systematic bias
    const shuffledFemaleSections = [...femaleSections].sort(() => Math.random() - 0.5);
    const shuffledMaleSections = [...maleSection].sort(() => Math.random() - 0.5);

    // Clear any existing distribution preferences to ensure clean slate
    [...shuffledFemaleSections, ...shuffledMaleSections].forEach(section => {
      delete (section as Section & { _distributionPreference?: unknown })._distributionPreference;
    });

    // Return sections in randomized order to prevent processing bias
    return [...shuffledFemaleSections, ...shuffledMaleSections];
  };

  // Helper to process a successfully scheduled section (handles lecturer conflicts and updates global state)
  const processSuccessfulScheduledSection = (
    sectionToProcess: Section,
    scheduleResult: { success: boolean; sessions: AutoSession[]; appSessions: AppSession[]; message?: string; },
    currentScheduleBase: AutoSession[], // Schedule state *before* this section's sessions are added
    level: string,
    courseCode: string
  ): { successProcessing: boolean, finalSessions: AutoSession[], finalAppSessions: AppSession[], updatedGlobalSchedule: AutoSession[] } => {
    let sessionsForThisSection = [...scheduleResult.sessions];
    const lecturerIdForSection = sessionsForThisSection.find(s => s.lecturerId)?.lecturerId || "";
    let canAssignLecturerToAll = true;

    if (lecturerIdForSection) {
      for (const session of sessionsForThisSection) {
        const lecturerConflicts = currentScheduleBase.filter(
          s => s.lecturerId === lecturerIdForSection && s.day === session.day && s.period === session.period
        );
        if (lecturerConflicts.length > 0) {
          // Removed lecturer conflict logging to reduce console output
          canAssignLecturerToAll = false; break;
        }
        const lecturerInfo = lecturers.find(l => l.id === lecturerIdForSection);
        if (lecturerInfo) {
          const teachingDaysForLecturer = new Set(currentScheduleBase.filter(s => s.lecturerId === lecturerIdForSection).map(s => s.day));
          if (!teachingDaysForLecturer.has(session.day) && (teachingDaysForLecturer.size + 1 > (lecturerInfo.maxTeachingDaysPerWeek || 5))) {
            // Removed max teaching days logging to reduce console output
            canAssignLecturerToAll = false; break;
          }
        }
      }
      if (!canAssignLecturerToAll) {
        // Removed lecturer assignment logging to reduce console output
        sessionsForThisSection = sessionsForThisSection.map(s => ({ ...s, lecturerId: '' }));
        globalProfiler.recordLecturerConstraintViolation();
      }
    }

    const finalAppSessions = sessionsForThisSection.map(convertToAppSession);
    const newGlobalSchedule = [...currentScheduleBase, ...sessionsForThisSection];

    allNewSessions = [...allNewSessions, ...sessionsForThisSection];
    allNewAppSessions = [...allNewAppSessions, ...finalAppSessions];
    if (!scheduledSections.includes(sectionToProcess.id)) scheduledSections.push(sectionToProcess.id);
    unscheduledSections = unscheduledSections.filter(id => id !== sectionToProcess.id);

    if (sectionsByLevel[level]?.[courseCode]) {
      sectionsByLevel[level][courseCode] = sectionsByLevel[level][courseCode].filter(s => s.id !== sectionToProcess.id);
    }
    // Removed successful processing logging to reduce console output
    return { successProcessing: true, finalSessions: sessionsForThisSection, finalAppSessions, updatedGlobalSchedule: newGlobalSchedule };
  };

  // Helper to revert scheduling for a section
  const revertScheduledSection = (
    section: Section, sessionsToRemove: AutoSession[], appSessionsToRemove: AppSession[],
    level: string, courseCode: string, scheduleStateToRevertTo: AutoSession[]
  ) => {
    updatedSchedule = [...scheduleStateToRevertTo]; // Revert global schedule to the state before this section was added
    allNewSessions = allNewSessions.filter(s => !sessionsToRemove.some(r => r.id === s.id));
    allNewAppSessions = allNewAppSessions.filter(ap => !appSessionsToRemove.some(r => r.id === ap.id)); // Assuming appSession id matches autoSession id
    scheduledSections = scheduledSections.filter(id => id !== section.id);

    if (sectionsByLevel[level]?.[courseCode] && !sectionsByLevel[level][courseCode].find(s => s.id === section.id)) {
      sectionsByLevel[level][courseCode].push(section); // Add back for individual attempt
    }
    // Removed revert logging to reduce console output
  };

  Object.keys(levelOrder)
    .sort((a, b) => levelOrder[a] - levelOrder[b])
    .forEach(level => {
      if (!sectionsByLevel[level] || Object.keys(sectionsByLevel[level]).length === 0) {
        // Removed level logging to reduce console output
        return;
      }
      // Removed level processing logging to reduce console output
      const courseCodesForLevel = Object.keys(sectionsByLevel[level]);
      let madeProgressThisLevelIteration = true;

      while (madeProgressThisLevelIteration) {
        madeProgressThisLevelIteration = false;
        for (const courseCode of courseCodesForLevel) {
          let currentCourseSections = sectionsByLevel[level]?.[courseCode]?.filter(s => !scheduledSections.includes(s.id) && !unscheduledSections.includes(s.id));
          if (!currentCourseSections || currentCourseSections.length === 0) continue;

          // Apply distribution strategy for undergraduate theory courses
          // Get course type from the first section (all sections of same course have same type)
          const firstSection = currentCourseSections[0];
          if (firstSection) {
            const courseType = firstSection.courseType;
            currentCourseSections = applySectionDistribution(currentCourseSections, courseCode, courseType);

            // Update the sections in the level structure with the distributed order
            sectionsByLevel[level][courseCode] = [
              ...sectionsByLevel[level][courseCode].filter(s => scheduledSections.includes(s.id) || unscheduledSections.includes(s.id)),
              ...currentCourseSections
            ];
          }

          const femaleSection = currentCourseSections.find(s => s.gender === 'F');
          const maleSection = currentCourseSections.find(s => s.gender === 'M');

          // Track scheduling status for paired scheduling logic

          if (femaleSection && maleSection) {
            // Removed paired scheduling logging to reduce console output
            const scheduleBeforePairAttempt = [...updatedSchedule]; // Snapshot before trying the pair
            const commonLecturerId = findBestLecturerForBothGenders(courseCode, maleSection, femaleSection, updatedSchedule, lecturers);

            if (commonLecturerId) { // Check if a non-empty string was returned
              // Removed common lecturer logging to reduce console output
              const femaleSectionWithLecturer = { ...femaleSection, lecturerId: commonLecturerId };
              const femaleResult = autoScheduleSection(femaleSectionWithLecturer, updatedSchedule, lecturers);

              if (femaleResult.success) {
                const femaleProcessing = processSuccessfulScheduledSection(femaleSection, femaleResult, updatedSchedule, level, courseCode);
                if (femaleProcessing.successProcessing) {
                  const scheduleAfterFemaleSuccess = femaleProcessing.updatedGlobalSchedule;
                  const maleSectionWithLecturer = { ...maleSection, lecturerId: commonLecturerId };
                  const maleResult = autoScheduleSection(maleSectionWithLecturer, scheduleAfterFemaleSuccess, lecturers);

                  if (maleResult.success) {
                    const maleProcessing = processSuccessfulScheduledSection(maleSection, maleResult, scheduleAfterFemaleSuccess, level, courseCode);
                    if (maleProcessing.successProcessing) {
                      updatedSchedule = maleProcessing.updatedGlobalSchedule;
                      madeProgressThisLevelIteration = true;
                      // Removed successful pair scheduling logging to reduce console output
                      continue; // Pair successfully scheduled, move to next course code
                    } else { // Male processing failed
                      revertScheduledSection(femaleSection, femaleProcessing.finalSessions, femaleProcessing.finalAppSessions, level, courseCode, scheduleBeforePairAttempt);
                    }
                  } else { // Male autoScheduleSection failed
                    revertScheduledSection(femaleSection, femaleProcessing.finalSessions, femaleProcessing.finalAppSessions, level, courseCode, scheduleBeforePairAttempt);
                  }
                } // Female processing failed - fall through to individual
              } // Female autoScheduleSection failed - fall through to individual
            } // No common lecturer or common lecturer failed - fall through to individual
          }

          // Individual scheduling attempts if pair failed or not applicable
          const femaleToSchedule = sectionsByLevel[level]?.[courseCode]?.find(s => s.id === femaleSection?.id && !scheduledSections.includes(s.id) && !unscheduledSections.includes(s.id));
          if (femaleToSchedule) {
            const resF = autoScheduleSection(femaleToSchedule, updatedSchedule, lecturers);
            if (resF.success) {
              const processingOutcome = processSuccessfulScheduledSection(femaleToSchedule, resF, updatedSchedule, level, courseCode);
              if (processingOutcome.successProcessing) {
                updatedSchedule = processingOutcome.updatedGlobalSchedule;
                madeProgressThisLevelIteration = true;
              }
            } else {
                 if (!unscheduledSections.includes(femaleToSchedule.id)) unscheduledSections.push(femaleToSchedule.id);
                 if (sectionsByLevel[level]?.[courseCode]) sectionsByLevel[level][courseCode] = sectionsByLevel[level][courseCode].filter(s => s.id !== femaleToSchedule.id);
            }
          }

          const maleToSchedule = sectionsByLevel[level]?.[courseCode]?.find(s => s.id === maleSection?.id && !scheduledSections.includes(s.id) && !unscheduledSections.includes(s.id));
          if (maleToSchedule) {
            const resM = autoScheduleSection(maleToSchedule, updatedSchedule, lecturers);
            if (resM.success) {
              const processingOutcome = processSuccessfulScheduledSection(maleToSchedule, resM, updatedSchedule, level, courseCode);
              if (processingOutcome.successProcessing) {
                updatedSchedule = processingOutcome.updatedGlobalSchedule;
                madeProgressThisLevelIteration = true;
              }
            } else {
                if (!unscheduledSections.includes(maleToSchedule.id)) unscheduledSections.push(maleToSchedule.id);
                if (sectionsByLevel[level]?.[courseCode]) sectionsByLevel[level][courseCode] = sectionsByLevel[level][courseCode].filter(s => s.id !== maleToSchedule.id);
            }
          }
        } // End for courseCode loop

        const remainingAtLevel = Object.values(sectionsByLevel[level] || {}).flat().filter(s => !scheduledSections.includes(s.id) && !unscheduledSections.includes(s.id)).length;
        if (remainingAtLevel === 0) {
          madeProgressThisLevelIteration = false; // All done for this level
        } else if (!madeProgressThisLevelIteration && remainingAtLevel > 0) {
          // Removed no progress logging to reduce console output
          Object.values(sectionsByLevel[level] || {}).flat().forEach(sec => {
            if (!scheduledSections.includes(sec.id) && !unscheduledSections.includes(sec.id)) {
              unscheduledSections.push(sec.id);
            }
          });
          sectionsByLevel[level] = {}; // Clear them as they are now handled (unscheduled)
          madeProgressThisLevelIteration = false; // Stop for this level
        }
      } // End while(madeProgressThisLevelIteration)
    }); // End forEach level

  // Completion logging removed to minimize console output
  return {
    success: scheduledSections.length > 0,
    scheduledSections,
    unscheduledSections,
    sessions: allNewSessions,
    appSessions: allNewAppSessions
  };
};

// Enhanced auto-scheduling with iterative retry mechanism and optional CPS refinement
export const autoScheduleAllSections = (
  sections: Section[],
  existingSchedule: AutoSession[],
  lecturers: Lecturer[] = [],
  retryConfig?: {
    enabled: boolean;
    maxRetries: number;
    minSuccessRate: number;
    timeoutMs: number;
    enableCPSRefinement?: boolean; // New option for CPS layer
  }
): {
  success: boolean;
  scheduledSections: string[];
  unscheduledSections: string[];
  sessions: AutoSession[];
  appSessions: AppSession[];
  retryReport?: RetryReport;
  cpsReport?: CPSRefinementReport; // New CPS report
} => {
  const startTime = performance.now();

  // Default retry configuration
  const defaultRetryConfig = {
    enabled: true,
    maxRetries: 5,
    minSuccessRate: 0.85,
    timeoutMs: 300000, // 5 minutes
    enableCPSRefinement: true // Enable CPS refinement by default
  };

  const config = retryConfig || defaultRetryConfig;

  // If retry mechanism is disabled, use core function directly
  if (!config.enabled) {
    const result = autoScheduleAllSectionsCore(sections, existingSchedule, lecturers);
    return {
      ...result,
      cpsReport: {
        enabled: false,
        triggered: false,
        initialViolations: 0,
        finalViolations: 0,
        improvementAchieved: false,
        refinementStrategies: [],
        processingTime: 0,
        constraintViolationDetails: {
          timeslotPercentage: 0,
          lecturerOverload: 0,
          patternViolations: 0,
          conflictViolations: 0
        },
        lecturerAssignmentStats: {
          sectionsScheduled: 0,
          sectionsWithLecturers: 0,
          sectionsWithoutLecturers: 0,
          lecturerAssignmentRate: 0
        },
        optimizationResults: {
          strategiesApplied: [],
          lecturerSwaps: 0,
          sessionRepositions: 0,
          intelligentReschedules: 0,
          constraintRelaxations: 0,
          overallOptimizationScore: 0
        }
      }
    };
  }

  // Diagnostic disabled to reduce log output
  // if (sections.length <= 10) {
  //   diagnoseLecturerAssignmentFailures(sections, lecturers, existingSchedule);
  // }

  const totalSections = sections.length;
  const strategies = [
    RetryStrategy.STANDARD,
    RetryStrategy.RELAXED_LECTURER_PREFERENCES,
    RetryStrategy.RANDOMIZED_ORDER,
    RetryStrategy.RELAXED_SOFT_CONSTRAINTS,
    RetryStrategy.EMERGENCY_MINIMAL_CONSTRAINTS
  ];

  let bestResult: RetryAttemptResult | null = null;
  const attemptHistory: RetryAttemptResult[] = [];
  let currentAttempt = 0;

  // Clear cache before starting retry attempts
  validationCache.clear();

  while (currentAttempt < config.maxRetries) {
    const attemptStartTime = performance.now();
    const strategy = strategies[Math.min(currentAttempt, strategies.length - 1)];

    // Check timeout
    if (performance.now() - startTime > config.timeoutMs) {
      break;
    }

    // Apply strategy modifications
    const { modifiedSections, modifiedLecturers } = applyRetryStrategy(sections, strategy, lecturers);

    // FIXED: Clear distribution preferences from previous attempts to prevent bias
    modifiedSections.forEach(section => {
      delete (section as Section & { _distributionPreference?: unknown })._distributionPreference;
    });

    // Distribution preference clearing logging removed to reduce console output

    // Run core scheduling with modified parameters
    const result = autoScheduleAllSectionsCore(modifiedSections, existingSchedule, modifiedLecturers);

    const attemptEndTime = performance.now();
    const attemptDuration = attemptEndTime - attemptStartTime;
    const successRate = calculateSuccessRate(result.scheduledSections, totalSections);

    const attemptResult: RetryAttemptResult = {
      success: result.success,
      scheduledSections: result.scheduledSections,
      unscheduledSections: result.unscheduledSections,
      sessions: result.sessions,
      appSessions: result.appSessions,
      successRate,
      strategy,
      duration: attemptDuration
    };

    attemptHistory.push(attemptResult);

    console.log(`   → ${result.scheduledSections.length}/${totalSections} sections (${(successRate * 100).toFixed(1)}%)`);

    // Update best result if this attempt is better
    if (!bestResult || successRate > bestResult.successRate) {
      bestResult = attemptResult;
      console.log(`   🎯 New best: ${(successRate * 100).toFixed(1)}%`);
    }

    // Check if we've achieved the target success rate
    if (successRate >= config.minSuccessRate) {
      console.log(`✅ Target success rate achieved: ${(successRate * 100).toFixed(1)}% >= ${(config.minSuccessRate * 100).toFixed(1)}%`);
      break;
    }

    // Check if we've achieved 100% success
    if (successRate >= 1.0) {
      console.log('🎉 Perfect scheduling achieved (100% success rate)');
      break;
    }

    // Check for improvement (if this is not the first attempt)
    if (currentAttempt > 0) {
      const previousBestRate = attemptHistory
        .slice(0, -1)
        .reduce((max, attempt) => Math.max(max, attempt.successRate), 0);

      if (successRate <= previousBestRate) {
        console.log(`📉 No improvement over previous attempts (${(successRate * 100).toFixed(1)}% vs ${(previousBestRate * 100).toFixed(1)}%)`);

        // If no improvement for 2 consecutive attempts, consider stopping
        if (currentAttempt >= 2) {
          const lastTwoAttempts = attemptHistory.slice(-2);
          if (lastTwoAttempts.every(attempt => attempt.successRate <= previousBestRate)) {
            console.log('🛑 No improvement for 2 consecutive attempts, terminating early');
            break;
          }
        }
      }
    }

    currentAttempt++;
  }

  const totalDuration = performance.now() - startTime;

  // Determine termination reason
  let terminationReason: 'success' | 'max_retries' | 'no_improvement' | 'timeout';
  if (bestResult && bestResult.successRate >= config.minSuccessRate) {
    terminationReason = 'success';
  } else if (performance.now() - startTime > config.timeoutMs) {
    terminationReason = 'timeout';
  } else if (currentAttempt >= config.maxRetries) {
    terminationReason = 'max_retries';
  } else {
    terminationReason = 'no_improvement';
  }

  // Create retry report
  const retryReport: RetryReport = {
    totalAttempts: attemptHistory.length,
    finalResult: bestResult!,
    bestResult: bestResult!,
    attemptHistory,
    totalDuration,
    improvementAchieved: attemptHistory.length > 1 &&
      bestResult!.successRate > attemptHistory[0].successRate,
    terminationReason
  };

  // Auto-scheduling summary logging removed to minimize console output
  const _lecturerAssignmentReport = globalProfiler.generateReport();

  // Detailed analysis disabled to reduce log output
  // if (totalSections <= 20) {
  //   analyzeLecturerUtilization(lecturers, bestResult!.sessions);
  //   analyzeCourseeLecturerMatching(sections, lecturers);
  //   analyzeConstraintViolations(lecturers, sections, bestResult!.sessions);
  // }

  // Constraint violation logging removed to reduce console output

  // Auto-scheduling completion logging reduced to minimize console output

  // Apply CPS refinement if enabled and success rate is below target
  let finalResult = bestResult!;
  let cpsReport: CPSRefinementReport = {
    enabled: config.enableCPSRefinement || false,
    triggered: false,
    initialViolations: 0,
    finalViolations: 0,
    improvementAchieved: false,
    refinementStrategies: [],
    processingTime: 0,
    constraintViolationDetails: {
      timeslotPercentage: 0,
      lecturerOverload: 0,
      patternViolations: 0,
      conflictViolations: 0
    },
    lecturerAssignmentStats: {
      sectionsScheduled: 0,
      sectionsWithLecturers: 0,
      sectionsWithoutLecturers: 0,
      lecturerAssignmentRate: 0
    },
    optimizationResults: {
      strategiesApplied: [],
      lecturerSwaps: 0,
      sessionRepositions: 0,
      intelligentReschedules: 0,
      constraintRelaxations: 0,
      overallOptimizationScore: 0
    }
  };

  if (config.enableCPSRefinement && bestResult!.successRate < config.minSuccessRate) {
    const cpsStartTime = performance.now();

    const refinementResult = applyCPSRefinement(
      bestResult!,
      sections,
      lecturers,
      config.minSuccessRate
    );

    const cpsEndTime = performance.now();
    cpsReport = {
      ...refinementResult.report,
      processingTime: cpsEndTime - cpsStartTime
    };

    if (refinementResult.improved) {
      finalResult = refinementResult.result;
    }
  }

  return {
    success: finalResult.success,
    scheduledSections: finalResult.scheduledSections,
    unscheduledSections: finalResult.unscheduledSections,
    sessions: finalResult.sessions,
    appSessions: finalResult.appSessions,
    retryReport,
    cpsReport
  };
};