// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts

import { contextBridge, ipcRenderer, webFrame, shell } from 'electron';

// Helper function to save zoom factor to store
const saveZoomFactor = async (zoomFactor: number) => {
  try {
    const currentUIState = await ipcRenderer.invoke('store:get', 'uiState');
    const newUIState = {
      ...currentUIState,
      zoomFactor: Math.round(zoomFactor * 10) / 10 // Round to 1 decimal place
    };
    await ipcRenderer.invoke('store:set', 'uiState', newUIState);
  } catch (error) {
    console.error('Error saving zoom factor:', error);
  }
};

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Data operations
  store: {
    get: (key: string) => ipcRenderer.invoke('store:get', key),
    set: (key: string, value: unknown) => ipcRenderer.invoke('store:set', key, value),
    delete: (key: string) => ipcRenderer.invoke('store:delete', key),
    clear: () => ipcRenderer.invoke('store:clear'),
  },
  // File dialog operations
  dialog: {
    showSaveDialog: (options: unknown) => ipcRenderer.invoke('dialog:showSaveDialog', options),
    showOpenDialog: (options: unknown) => ipcRenderer.invoke('dialog:showOpenDialog', options),
  },
  // File system operations
  fs: {
    writeFile: (filePath: string, data: string) => ipcRenderer.invoke('fs:writeFile', filePath, data),
    readFile: (filePath: string) => ipcRenderer.invoke('fs:readFile', filePath),
  },
  // Zoom operations
  zoom: {
    zoomIn: () => {
      const currentZoom = webFrame.getZoomFactor();
      const newZoom = Math.min(currentZoom + 0.1, 2.0);
      webFrame.setZoomFactor(newZoom);
      const actualZoom = webFrame.getZoomFactor();
      saveZoomFactor(actualZoom);
      return actualZoom;
    },
    zoomOut: () => {
      const currentZoom = webFrame.getZoomFactor();
      const newZoom = Math.max(currentZoom - 0.1, 0.5);
      webFrame.setZoomFactor(newZoom);
      const actualZoom = webFrame.getZoomFactor();
      saveZoomFactor(actualZoom);
      return actualZoom;
    },
    resetZoom: () => {
      webFrame.setZoomFactor(1.0);
      const actualZoom = webFrame.getZoomFactor();
      saveZoomFactor(actualZoom);
      return actualZoom;
    },
    setZoomFactor: (zoomFactor: number) => {
      webFrame.setZoomFactor(zoomFactor);
      const actualZoom = webFrame.getZoomFactor();
      saveZoomFactor(actualZoom);
      return actualZoom;
    },
    getZoomFactor: () => webFrame.getZoomFactor(),
  },
  // PDF generation operations
  pdf: {
    generate: (htmlContent: string, filename: string) => ipcRenderer.invoke('pdf:generate', htmlContent, filename),
  },
  // Shell operations
  shell: {
    openExternal: (url: string) => shell.openExternal(url),
  }
});
