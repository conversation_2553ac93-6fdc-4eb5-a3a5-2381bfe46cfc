import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  DialogTitle,
  DialogContent,
  Button,
  Typography,
  Box,
  Paper,
  IconButton,
  Tooltip,
  List,
  ListItemButton,
  ListItemText
} from '@mui/material';
import AccessibleDialog from '../common/AccessibleDialog';
import Grid from '@mui/material/Grid2';
import { useAppContext } from '../../context/AppContext';
import type { Lecturer, Session, Semester, Section, Course } from '../../types/models';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import EmailIcon from '@mui/icons-material/Email';
import HtmlIcon from '@mui/icons-material/Html';
import ManIcon from '@mui/icons-material/Man';
import WomanIcon from '@mui/icons-material/Woman';
import FilterAltOffIcon from '@mui/icons-material/FilterAltOff';
import CloseIcon from '@mui/icons-material/Close';

import DroppableSessionCard from '../draggable/DroppableSessionCard';
import { generateLecturerTimetableHtml, exportLecturerTimetablePdf } from '../../utils/exportUtils';
import type { PdfTemplateData } from '../../utils/pdfTemplates';
import { getArabicTextClass } from '../../utils/arabicUtils';

interface ExtendedSession extends Omit<Session, 'lecturerId'> {
  startTime: string;
  endTime: string;
  lecturerId?: string;
  lecturerIds?: string[];
  startPeriod: number;
  endPeriod: number;
}

interface SessionWithLecturers extends ExtendedSession {
  sectionId: string;
}

interface LecturerTimetableModalProps {
  open: boolean;
  onClose: () => void;
  lecturer: Lecturer | null;
}

// We're now using the imported Section and Course types from types/models.ts

// Remove unused Semester type definition

const LecturerTimetableModal: React.FC<LecturerTimetableModalProps> = ({
  open,
  onClose,
  lecturer
}) => {
  // Move the useAppContext hook inside the component
  const {
    currentSemester,
    courses,
    sections,
    sessions,
    lecturers
  } = useAppContext();

  // Get context data

  // Reference to the timetable container for PDF export
  const timetableRef = useRef<HTMLDivElement>(null);

  // Days of the week
  const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];

  // Time periods for Morning tab
  const morningPeriods = [
    { period: 1, regularTime: '8 AM', longTime: '8 AM' },
    { period: 2, regularTime: '9 AM', longTime: '9:30 AM' },
    { period: 3, regularTime: '10 AM', longTime: '11 AM' },
    { period: 4, regularTime: '11 AM', longTime: '12:30 PM' },
    { period: 5, regularTime: '12 PM', longTime: '2 PM' },
    { period: 6, regularTime: '1 PM', longTime: '3:30 PM' }
  ];

  // Time periods for Evening tab
  const eveningPeriods = [
    { period: 7, regularTime: '2 PM', longTime: '2 PM' },
    { period: 8, regularTime: '3 PM', longTime: '3:30 PM' },
    { period: 9, regularTime: '4 PM', longTime: '5 PM' },
    { period: 10, regularTime: '5 PM', longTime: '6:30 PM' },
    { period: 11, regularTime: '6 PM', longTime: '8 PM' },
    { period: 12, regularTime: '7 PM', longTime: '9:30 PM' }
  ];

  // Get all periods for the full timetable
  const getAllPeriods = () => {
    return [...morningPeriods, ...eveningPeriods];
  };

  // Get teaching hours for a lecturer in the current semester
  const getTeachingHours = (lecturer: Lecturer): number => {
    // Find all sessions assigned to this lecturer in the current semester
    // Check both lecturerId and lecturerIds array
    const lecturerSessions = sessions[currentSemester].filter(
      session => session.lecturerId === lecturer.id ||
                (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach(session => {
      // Find the section and course for this session
      const section = sections[currentSemester].find(s => s.id === session.sectionId);
      if (!section) return;

      const course = courses[currentSemester].find(c => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  // Get teaching hours for a lecturer in a specific semester
  const getTeachingHoursForSemester = (lecturer: Lecturer, semester: Semester): number => {
    // Find all sessions assigned to this lecturer in the specified semester
    // Check both lecturerId and lecturerIds array
    const lecturerSessions = sessions[semester].filter(
      (session: Session) => session.lecturerId === lecturer.id ||
                (session.lecturerIds && session.lecturerIds.includes(lecturer.id))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach((session: Session) => {
      // Find the section and course for this session
      const section = sections[semester].find((s: Section) => s.id === session.sectionId);
      if (!section) return;

      const course = courses[semester].find((c: Course) => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  // Calculate semester load based on current semester
  const calculateSemesterLoad = (lecturer: Lecturer): number => {
    // Get supervision hours for current semester
    // Don't include supervision hours for Summer semester
    const supervisionHours = currentSemester === 'Summer' ? 0 :
                            currentSemester === 'Fall' ?
                            lecturer.supervisionHoursFall :
                            lecturer.supervisionHoursSpring;

    // Get teaching hours for current semester
    const teachingHours = getTeachingHours(lecturer);

    return supervisionHours + teachingHours;
  };

  // Note: calculateMaxSemesterLoad function removed as it's no longer needed

  // Calculate yearly load - used for total year load calculation
  const calculateYearlyLoad = (lecturer: Lecturer): number => {
    // Supervision hours from both semesters
    const supervisionHours = lecturer.supervisionHoursFall + lecturer.supervisionHoursSpring;

    // Calculate Fall teaching load
    const fallTeachingLoad = getTeachingHoursForSemester(lecturer, 'Fall');

    // Calculate Spring teaching load
    const springTeachingLoad = getTeachingHoursForSemester(lecturer, 'Spring');

    // Summer is not included in yearly load as per requirements
    return supervisionHours + fallTeachingLoad + springTeachingLoad;
  };

  // Helper function to convert Session to ExtendedSession
  function toExtendedSession(session: Session): ExtendedSession {
    return {
      ...session,
      startTime: '',
      endTime: '',
      startPeriod: session.startPeriod || 0,
      endPeriod: session.endPeriod || 0
    };
  }

  // Helper type guard for SessionWithLecturers
  function isSessionWithLecturers(session: Session | ExtendedSession): session is SessionWithLecturers {
    return 'startTime' in session && 'endTime' in session;
  }

  // Get lecturer sessions for the timetable
  const getLecturerSessions = (): ExtendedSession[] => {
    if (!filteredLecturer) return sessions[currentSemester].map(session => toExtendedSession(session)) || [];

    return sessions[currentSemester]
      .map(session => toExtendedSession(session))
      .filter((session): session is SessionWithLecturers => {
        if (!isSessionWithLecturers(session)) return false;
        return session.lecturerId === filteredLecturer.id ||
               (session.lecturerIds ? session.lecturerIds.includes(filteredLecturer.id) : false);
      });
  };

  // Get periods that have sessions for the lecturer
  const getActivePeriods = () => {
    const lecturerSessions = getLecturerSessions();
    const activePeriods = new Set<number>();

    lecturerSessions.forEach(session => {
      for (let i = session.startPeriod; i <= session.endPeriod; i++) {
        activePeriods.add(i);
      }
    });

    return Array.from(activePeriods).sort((a, b) => a - b);
  };

  // Filter periods to only show those with sessions
  const getFilteredPeriods = () => {
    const activePeriods = getActivePeriods();
    const allPeriods = getAllPeriods();

    // If viewing all lecturers or no active periods, show all periods
    if (!filteredLecturer || activePeriods.length === 0) return allPeriods;

    return allPeriods.filter(period => activePeriods.includes(period.period));
  };

  // Export timetable as PDF using Electron
  const exportToPdf = async () => {
    if (!lecturer) return;

    try {
      // Check if exporting all lecturers or specific lecturer
      const isAllLecturers = !filteredLecturer;
      const exportLecturer = filteredLecturer || lecturer;

      // Calculate loads for the export lecturer
      const pdfSemesterLoad = calculateSemesterLoad(exportLecturer);
      const pdfFallTeachingHours = getTeachingHoursForSemester(exportLecturer, 'Fall');
      const pdfFallLoad = exportLecturer.supervisionHoursFall + pdfFallTeachingHours;
      const pdfSpringTeachingHours = getTeachingHoursForSemester(exportLecturer, 'Spring');
      const pdfSpringLoad = exportLecturer.supervisionHoursSpring + pdfSpringTeachingHours;
      const pdfTotalYearLoad = calculateYearlyLoad(exportLecturer);
      const pdfOverload = pdfTotalYearLoad - exportLecturer.maxYearLoad;

      // Create session adapter function
      const sessionAdapter = (day: string, period: number) => {
        const extendedSessions = findSessionsForDayAndPeriod(day, period);
        return extendedSessions.map(session => ({
          id: session.id,
          sectionId: session.sectionId,
          lecturerId: session.lecturerId || '',
          lecturerIds: session.lecturerIds,
          day: session.day,
          startPeriod: session.startPeriod,
          endPeriod: session.endPeriod,
          viewType: session.viewType,
          timeOfDay: session.timeOfDay
        }));
      };

      // Prepare data for PDF template
      const pdfData: PdfTemplateData = {
        lecturer: exportLecturer,
        currentSemester,
        departmentName,
        academicYear,
        semesterLoad: pdfSemesterLoad,
        fallLoad: pdfFallLoad,
        springLoad: pdfSpringLoad,
        totalYearLoad: pdfTotalYearLoad,
        overload: pdfOverload,
        periods: getFilteredPeriods(),
        days,
        sessions,
        sections,
        courses,
        lecturers,
        findSessionsForDayAndPeriod: sessionAdapter,
        genderFilter,
        isAllLecturers
      };

      // Create filename
      const safeAcademicYear = academicYear.replace(/\//g, '-');
      let filename: string;

      if (isAllLecturers) {
        const safeDepartmentName = departmentName.replace(/[<>:"/\\|?*]/g, '');
        filename = `${safeDepartmentName} All Lecturers Timetable - ${safeAcademicYear} ${currentSemester}.pdf`;
      } else {
        const fullName = `${exportLecturer.title} ${exportLecturer.firstName} ${exportLecturer.lastName}`;
        const safeFullName = fullName.replace(/[<>:"/\\|?*]/g, '');
        filename = `${safeFullName}'s Timetable - ${safeAcademicYear} ${currentSemester}.pdf`;
      }

      // Export using Electron
      await exportLecturerTimetablePdf(pdfData, filename);

    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Failed to export PDF. Please try again.');
    }
  };

  // Export timetable as interactive HTML
  const exportAsHtml = () => {
    if (!lecturer) return;

    // Use the active lecturer (filtered or original)
    const exportLecturer = filteredLecturer || lecturer;

    // Create an adapter function to convert ExtendedSession[] to Session[]
    const sessionAdapter = (day: string, period: number): Session[] => {
      const extendedSessions = findSessionsForDayAndPeriod(day, period);
      return extendedSessions.map(session => ({
        id: session.id,
        sectionId: session.sectionId,
        lecturerId: session.lecturerId || '', // Provide default empty string for optional lecturerId
        lecturerIds: session.lecturerIds,
        day: session.day,
        startPeriod: session.startPeriod,
        endPeriod: session.endPeriod,
        viewType: session.viewType,
        timeOfDay: session.timeOfDay
      }));
    };

    // Generate HTML content using the utility function
    const htmlContent = generateLecturerTimetableHtml(
      exportLecturer,
      currentSemester,
      lecturers,
      sessions,
      sections,
      courses,
      getFilteredPeriods,
      days,
      semesterLoad,
      fallLoad,
      springLoad,
      totalYearLoad,
      yearWorkload,
      overload,
      sessionAdapter,
      genderFilter,
      departmentName,
      academicYear
    );

    // Create a Blob with the HTML content
    const blob = new Blob([htmlContent], { type: 'text/html' });

    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);

    // Create a link element to download the file
    const link = document.createElement('a');
    link.href = url;

    // Create filename using department-based naming convention
    // Replace forward slashes with hyphens for cross-platform compatibility
    const safeAcademicYear = academicYear.replace(/\//g, '-');
    const safeDepartmentName = departmentName.replace(/[<>:"/\\|?*]/g, ''); // Remove invalid filename characters
    link.download = `${safeDepartmentName} Dept Timetable ${safeAcademicYear} - ${currentSemester}.html`;

    // Append the link to the document, click it, and remove it
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);
  };

  // Send timetable as email with HTML content embedded
  const sendAsEmail = () => {
    if (!lecturer) return;

    // Check if exporting all lecturers or specific lecturer
    const isAllLecturersEmail = !filteredLecturer;

    // Disable email functionality for "All Lecturers" mode due to data size limitations
    if (isAllLecturersEmail) {
      alert('Email functionality is not available for "All Lecturers" mode due to data size limitations. Please use the PDF export feature instead.');
      return;
    }

    // Individual lecturer mode only
    const emailLecturer = filteredLecturer;
    if (!emailLecturer) return;

    // Create a wrapper function that returns Session[] instead of ExtendedSession[]
    const sessionAdapter = (day: string, period: number): Session[] => {
      return findSessionsForDayAndPeriod(day, period).map(extSession => ({
        ...extSession,
        lecturerId: extSession.lecturerId || '' // Ensure lecturerId is always a string
      }));
    };

    // Generate HTML content for email using the existing utility function
    const htmlContent = generateLecturerTimetableHtml(
      emailLecturer,
      currentSemester,
      lecturers,
      sessions,
      sections,
      courses,
      getFilteredPeriods,
      days,
      semesterLoad,
      fallLoad,
      springLoad,
      totalYearLoad,
      yearWorkload,
      overload,
      sessionAdapter,
      genderFilter,
      departmentName,
      academicYear
    );

    // Compress HTML content for mailto: URL length limitations
    const compressedHtml = compressHtmlForEmail(htmlContent);

    // Prepare email data
    const subject = encodeURIComponent(`${emailLecturer.title} ${emailLecturer.firstName} ${emailLecturer.lastName || ''}'s Timetable`);
    const emailAddress = emailLecturer.email;

    // Create mailto URL with HTML content in body
    const mailtoUrl = `mailto:${emailAddress}?subject=${subject}&body=${encodeURIComponent(compressedHtml)}`;

    // Check URL length and handle accordingly
    if (mailtoUrl.length > 2000) {
      // If URL is too long, use a simplified version
      const simplifiedBody = `Please find the timetable for ${emailLecturer.title} ${emailLecturer.firstName} ${emailLecturer.lastName || ''} below:\n\n${generateSimplifiedTimetableText(emailLecturer)}\n\nRegards,\nTimetable System`;
      const simplifiedMailtoUrl = `mailto:${emailAddress}?subject=${subject}&body=${encodeURIComponent(simplifiedBody)}`;

      // Create a temporary link element and click it to avoid persistent window
      const tempLink = document.createElement('a');
      tempLink.href = simplifiedMailtoUrl;
      tempLink.style.display = 'none';
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
    } else {
      // Use full HTML content with temporary link approach
      const tempLink = document.createElement('a');
      tempLink.href = mailtoUrl;
      tempLink.style.display = 'none';
      document.body.appendChild(tempLink);
      tempLink.click();
      document.body.removeChild(tempLink);
    }
  };

  // Helper function to compress HTML content for email
  const compressHtmlForEmail = (htmlContent: string): string => {
    // Remove unnecessary whitespace and comments
    let compressed = htmlContent
      .replace(/<!--[\s\S]*?-->/g, '') // Remove HTML comments
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/>\s+</g, '><') // Remove whitespace between tags
      .trim();

    // If still too long, remove some styling to reduce size
    if (compressed.length > 1500) {
      compressed = compressed
        .replace(/style="[^"]*"/g, '') // Remove inline styles
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, ''); // Remove style blocks
    }

    // If still too long, create a minimal version
    if (compressed.length > 1000) {
      return createMinimalHtmlEmail(compressed);
    }

    return compressed;
  };

  // Helper function to create minimal HTML email
  const createMinimalHtmlEmail = (originalHtml: string): string => {
    // Extract just the timetable content without heavy styling
    const tableMatch = originalHtml.match(/<table[^>]*>([\s\S]*?)<\/table>/i);
    if (tableMatch) {
      return `<html><body><h3>Timetable</h3>${tableMatch[0]}</body></html>`;
    }

    // Fallback to text version
    return `<html><body><pre>${generateSimplifiedTimetableText(filteredLecturer!)}</pre></body></html>`;
  };

  // Helper function to generate simplified text timetable with accurate timing information
  const generateSimplifiedTimetableText = (lecturer: Lecturer): string => {
    const lines: string[] = [];
    lines.push(`${lecturer.title} ${lecturer.firstName} ${lecturer.lastName || ''}'s Timetable`);
    lines.push(`${departmentName} | ${academicYear} | ${currentSemester}`);
    lines.push('');

    // Add load information
    lines.push(`Fall Load: ${fallLoad.toFixed(1)} hrs${currentSemester === 'Fall' ? ' (Current)' : ''}`);
    lines.push(`Spring Load: ${springLoad.toFixed(1)} hrs${currentSemester === 'Spring' ? ' (Current)' : ''}`);
    lines.push(`Summer Load: ${currentSemester === 'Summer' ? semesterLoad.toFixed(1) : '0.0'} hrs${currentSemester === 'Summer' ? ' (Current)' : ''}`);
    lines.push(`Year Load: ${totalYearLoad.toFixed(1)}/${yearWorkload.toFixed(1)} hrs`);
    lines.push(`Overload: ${overload > 0 ? '+' : ''}${overload.toFixed(1)} hrs`);
    lines.push('');

    // Helper function to get accurate end time using the application's logic
    const getEndTime = (period: { period: number; regularTime: string; longTime: string }, isLongDay: boolean): string => {
      const allPeriods = getAllPeriods();
      const nextPeriodIndex = allPeriods.findIndex(p => p.period === period.period) + 1;

      if (isLongDay) {
        if (nextPeriodIndex < allPeriods.length) {
          return allPeriods[nextPeriodIndex].longTime;
        } else if (period.period === 6) {
          return '3:30 PM';
        } else if (period.period === 12) {
          return '9:30 PM';
        }
      } else {
        if (nextPeriodIndex < allPeriods.length) {
          return allPeriods[nextPeriodIndex].regularTime;
        } else if (period.period === 6) {
          return '2 PM';
        } else if (period.period === 12) {
          return '8 PM';
        }
      }

      // Fallback - shouldn't happen with proper period data
      return period.regularTime;
    };

    // Helper function to get accurate time range for a period and day
    const getAccurateTimeRange = (period: { period: number; regularTime: string; longTime: string }, day: string): string => {
      const isLongDay = day === 'Monday' || day === 'Wednesday';
      const startTime = isLongDay ? period.longTime : period.regularTime;
      const endTime = getEndTime(period, isLongDay);
      return `${startTime}-${endTime}`;
    };

    // Create a map to group sessions by time and course
    const timeSlotMap = new Map<string, {
      timeRange: string;
      period: number;
      sessions: Array<{
        course: string;
        section: string;
        gender: string;
        days: string[];
      }>;
    }>();

    // Process all sessions for the lecturer
    getFilteredPeriods().forEach(period => {
      days.forEach(day => {
        const sessionsForCell = findSessionsForDayAndPeriod(day, period.period);

        sessionsForCell.forEach(session => {
          const section = sections[currentSemester].find(s => s.id === session.sectionId);
          const course = courses[currentSemester].find(c => c.id === section?.courseId);

          if (section && course) {
            const timeRange = getAccurateTimeRange(period, day);
            const courseKey = `${course.courseCode}(${section.sectionNumber})[${section.gender === 'M' ? 'M' : 'F'}]`;
            const mapKey = `${timeRange}-${courseKey}`;

            if (!timeSlotMap.has(mapKey)) {
              timeSlotMap.set(mapKey, {
                timeRange,
                period: period.period,
                sessions: [{
                  course: course.courseCode,
                  section: section.sectionNumber.toString(),
                  gender: section.gender === 'M' ? 'M' : 'F',
                  days: [day]
                }]
              });
            } else {
              const existing = timeSlotMap.get(mapKey)!;
              if (!existing.sessions[0].days.includes(day)) {
                existing.sessions[0].days.push(day);
              }
            }
          }
        });
      });
    });

    // Convert to sorted list format
    lines.push('TIMETABLE:');

    // Sort by period number to maintain chronological order
    const sortedEntries = Array.from(timeSlotMap.values())
      .sort((a, b) => a.period - b.period);

    if (sortedEntries.length === 0) {
      lines.push('No scheduled classes for this lecturer.');
    } else {
      sortedEntries.forEach(entry => {
        entry.sessions.forEach(session => {
          // Sort days in the standard order
          const dayOrder = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];
          const sortedDays = session.days.sort((a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b));
          const dayString = sortedDays.join('/');

          lines.push(`${entry.timeRange} on ${dayString}: ${session.course}(${session.section})[${session.gender}]`);
        });
      });
    }

    return lines.join('\n');
  };

  // Find sessions for a specific day and period
  const findSessionsForDayAndPeriod = (day: string, period: number): ExtendedSession[] => {
    let filteredSessions = sessions[currentSemester]
      .map(session => toExtendedSession(session))
      .filter(session =>
        session.day === day &&
        session.startPeriod !== undefined &&
        session.endPeriod !== undefined &&
        period >= session.startPeriod &&
        period <= session.endPeriod
      );

    // Apply lecturer filter if a lecturer is selected
    if (filteredLecturer) {
      filteredSessions = filteredSessions.filter(session =>
        session.lecturerId === filteredLecturer.id ||
        (session.lecturerIds ? session.lecturerIds.includes(filteredLecturer.id) : false)
      );
    }

    // Apply gender filter if selected
    if (genderFilter) {
      filteredSessions = filteredSessions.filter(session => {
        const section = sections[currentSemester].find(s => s.id === session.sectionId);
        return section && section.gender === genderFilter;
      });
    }

    return filteredSessions;
  };

  // Render a session cell
  const renderSessionCell = (day: string, period: number) => {
    const sessionsForCell = findSessionsForDayAndPeriod(day, period);

    if (sessionsForCell.length === 0) {
      return <div className="h-full w-full"></div>;
    }

    return (
      <div className="flex flex-col gap-1">
        {sessionsForCell.map((session: SessionWithLecturers) => {
          // Find the section
          const section = sections[currentSemester].find((s: Section) => s.id === session.sectionId);
          if (!section) return null;

          // Find the course
          const course = courses[currentSemester].find((c: Course) => c.id === section.courseId);
          if (!course) return null;

          // Get lecturer occurrences
          const lecturerOccurrences = [];

          // Process the main lecturerId
          if (session.lecturerId) {
            const lecturer = lecturers.find(l => l.id === session.lecturerId);
            if (lecturer) {
              lecturerOccurrences.push({
                id: session.id + '-' + lecturer.id,
                lecturerId: lecturer.id,
                firstName: lecturer.firstName,
                lastName: lecturer.lastName
              });
            }
          }

          // Process additional lecturerIds
          if (session.lecturerIds && Array.isArray(session.lecturerIds)) {
            const additionalLecturerIds = session.lecturerIds.filter(id => id !== session.lecturerId);
            additionalLecturerIds.forEach(lecturerId => {
              const lecturer = lecturers.find(l => l.id === lecturerId);
              if (lecturer) {
                lecturerOccurrences.push({
                  id: session.id + '-' + lecturer.id,
                  lecturerId: lecturer.id,
                  firstName: lecturer.firstName,
                  lastName: lecturer.lastName
                });
              }
            });
          }

          return (
            <DroppableSessionCard
              key={session.id}
              day={day}
              period={period}
              sessionId={session.id}
              sectionId={section.id}
              courseCode={course.courseCode}
              courseName={course.courseName}
              courseColor={course.color}
              sectionNumber={section.sectionNumber}
              gender={section.gender}
              lecturerOccurrences={lecturerOccurrences}
            />
          );
        })}
      </div>
    );
  };

  // State for filtered lecturer
  const [filteredLecturer, setFilteredLecturer] = useState<Lecturer | null>(null);

  // State for gender filter
  const [genderFilter, setGenderFilter] = useState<'M' | 'F' | null>(null);

  // State for academic year and department name
  const [academicYear, setAcademicYear] = useState<string>('');
  const [departmentName, setDepartmentName] = useState<string>('');

  // Load academic year and department name from store
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const uiState = await window.electronAPI.store.get('uiState') as { departmentName?: string; academicYear?: string } | null;

        // Set department name from store if available
        if (uiState && uiState.departmentName) {
          setDepartmentName(uiState.departmentName);
        }

        // Set academic year from store if available, otherwise calculate default
        if (uiState && uiState.academicYear) {
          setAcademicYear(uiState.academicYear);
        } else {
          const currentDate = new Date();
          currentDate.setMonth(currentDate.getMonth() - 6);
          const year = currentDate.getFullYear();
          setAcademicYear(`${year}/${year + 1}`);
        }
      } catch (error) {
        console.error('Error loading UI state:', error);

        // Fallback to calculating default academic year
        const currentDate = new Date();
        currentDate.setMonth(currentDate.getMonth() - 6);
        const year = currentDate.getFullYear();
        setAcademicYear(`${year}/${year + 1}`);
      }
    };

    loadUIState();
  }, []);



  // Initialize filtered lecturer when modal opens
  useEffect(() => {
    if (lecturer) {
      setFilteredLecturer(lecturer);
    }
  }, [lecturer]);

  // Get sorted list of all lecturers
  const sortedLecturers = useMemo(() => {
    if (!lecturers || typeof lecturers !== 'object') {
      return [];
    }

    // Handle both semester-specific and flat array structures
    let allLecturers: Lecturer[] = [];
    if (Array.isArray(lecturers)) {
      allLecturers = lecturers;
    } else if (typeof lecturers === 'object') {
      // If structured by semester, get current semester's lecturers
      allLecturers = lecturers[currentSemester] || [];
      // If empty, try to get all lecturers from the object
      if (allLecturers.length === 0) {
        allLecturers = Object.values(lecturers).flat() as Lecturer[];
      }
    }

    const validLecturers = allLecturers
      .filter(l => l && typeof l === 'object' && l.firstName);
    const sorted = validLecturers
      .sort((a, b) => a.firstName.localeCompare(b.firstName));
    return sorted;
  }, [lecturers, currentSemester]);

  // If no lecturer is provided, don't render anything
  if (!lecturer) return null;

  // Calculate loads for the selected lecturer (or filtered lecturer if available)
  const activeLecturer = filteredLecturer || lecturer;

  // Calculate semester load (used for Summer load display)
  const semesterLoad = calculateSemesterLoad(activeLecturer);

  // Get max year workload
  const yearWorkload = activeLecturer.maxYearLoad;

  // Calculate fall load (supervision + teaching)
  const fallTeachingHours = getTeachingHoursForSemester(activeLecturer, 'Fall');
  const fallLoad = activeLecturer.supervisionHoursFall + fallTeachingHours;

  // Calculate spring load (supervision + teaching)
  const springTeachingHours = getTeachingHoursForSemester(activeLecturer, 'Spring');
  const springLoad = activeLecturer.supervisionHoursSpring + springTeachingHours;

  // Calculate total year load using our yearly load calculation function
  const totalYearLoad = calculateYearlyLoad(activeLecturer);

  // Calculate overload
  const overload = totalYearLoad - activeLecturer.maxYearLoad;

  return (
    <AccessibleDialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      fullScreen
      sx={{
        '& .MuiDialog-paper': {
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <DialogTitle sx={{ p: 1, bgcolor: 'background.paper' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 0.5 }}>
              <Typography
                variant="subtitle1"
                sx={{ mr: 1, color: 'text.primary' }}
                className={filteredLecturer ? getArabicTextClass(`${filteredLecturer.firstName}${filteredLecturer.lastName ? ' ' + filteredLecturer.lastName : ''}`) : ''}
              >
                {filteredLecturer ? `${filteredLecturer.title} ${filteredLecturer.firstName}${filteredLecturer.lastName ? ' ' + filteredLecturer.lastName : ''}'s Timetable` : 'All Lecturers Timetable'}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                {departmentName} | {academicYear} | {currentSemester}
              </Typography>
            </Box>

            {filteredLecturer && (
              <Grid container spacing={1} sx={{ mt: 0 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Fall Load:</span>
                    <span>{Number(fallLoad).toFixed(1)} hrs {currentSemester === 'Fall' && '(Current)'}</span>
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Spring Load:</span>
                    <span>{Number(springLoad).toFixed(1)} hrs {currentSemester === 'Spring' && '(Current)'}</span>
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Summer Load:</span>
                    <span>{currentSemester === 'Summer' ? Number(semesterLoad).toFixed(1) : '0.0'} hrs {currentSemester === 'Summer' && '(Current)'}</span>
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Fall Supervision:</span>
                    <span>{Number(activeLecturer.supervisionHoursFall).toFixed(1)} hrs {currentSemester === 'Fall' && '(Current)'}</span>
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Spring Supervision:</span>
                    <span>{Number(activeLecturer.supervisionHoursSpring).toFixed(1)} hrs {currentSemester === 'Spring' && '(Current)'}</span>
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Year Load:</span>
                    <span>{Number(totalYearLoad).toFixed(1)}/{Number(yearWorkload).toFixed(1)} hrs</span>
                  </Typography>
                </Grid>

                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                  <Typography variant="caption" color={overload > 0 ? 'error.main' : 'success.main'} sx={{ display: 'flex' }}>
                    <span style={{ fontWeight: 'bold', marginRight: '4px' }}>Overload:</span>
                    <span>{overload > 0 ? '+' : ''}{Number(overload).toFixed(1)} hrs</span>
                  </Typography>
                </Grid>
              </Grid>
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{
              display: 'flex',
              mr: 2,
              border: theme => `2px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.08)'}`,
              borderRadius: 2,
              p: 0.5,
              background: theme => theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)'
                : 'linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0.01) 100%)',
              boxShadow: theme => theme.palette.mode === 'dark'
                ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                : '0 2px 8px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                boxShadow: theme => theme.palette.mode === 'dark'
                  ? '0 4px 12px rgba(0, 0, 0, 0.4)'
                  : '0 4px 12px rgba(0, 0, 0, 0.15)',
                transform: 'translateY(-1px)'
              }
            }}>
              <Tooltip title="Show Male Sections Only">
                <IconButton
                  onClick={() => setGenderFilter(genderFilter === 'M' ? null : 'M')}
                  size="small"
                  sx={{
                    mx: 0.25,
                    p: 0.75,
                    borderRadius: 1.5,
                    background: genderFilter === 'M'
                      ? 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)'
                      : 'transparent',
                    color: genderFilter === 'M' ? '#fff' : theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                    boxShadow: genderFilter === 'M'
                      ? '0 2px 8px rgba(25, 118, 210, 0.3)'
                      : 'none',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      background: genderFilter === 'M'
                        ? 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)'
                        : theme => theme.palette.mode === 'dark'
                          ? 'rgba(25, 118, 210, 0.1)'
                          : 'rgba(25, 118, 210, 0.08)',
                      color: genderFilter === 'M' ? '#fff' : '#1976d2',
                      transform: 'scale(1.05)',
                      boxShadow: genderFilter === 'M'
                        ? '0 4px 12px rgba(25, 118, 210, 0.4)'
                        : '0 2px 8px rgba(25, 118, 210, 0.2)'
                    }
                  }}
                >
                  <ManIcon sx={{ fontSize: '1.1rem', fontWeight: 'bold' }} />
                </IconButton>
              </Tooltip>
              <Tooltip title="Show Female Sections Only">
                <IconButton
                  onClick={() => setGenderFilter(genderFilter === 'F' ? null : 'F')}
                  size="small"
                  sx={{
                    mx: 0.25,
                    p: 0.75,
                    borderRadius: 1.5,
                    background: genderFilter === 'F'
                      ? 'linear-gradient(135deg, #e91e63 0%, #c2185b 100%)'
                      : 'transparent',
                    color: genderFilter === 'F' ? '#fff' : theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                    boxShadow: genderFilter === 'F'
                      ? '0 2px 8px rgba(233, 30, 99, 0.3)'
                      : 'none',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      background: genderFilter === 'F'
                        ? 'linear-gradient(135deg, #c2185b 0%, #ad1457 100%)'
                        : theme => theme.palette.mode === 'dark'
                          ? 'rgba(233, 30, 99, 0.1)'
                          : 'rgba(233, 30, 99, 0.08)',
                      color: genderFilter === 'F' ? '#fff' : '#e91e63',
                      transform: 'scale(1.05)',
                      boxShadow: genderFilter === 'F'
                        ? '0 4px 12px rgba(233, 30, 99, 0.4)'
                        : '0 2px 8px rgba(233, 30, 99, 0.2)'
                    }
                  }}
                >
                  <WomanIcon sx={{ fontSize: '1.1rem', fontWeight: 'bold' }} />
                </IconButton>
              </Tooltip>
              <Tooltip title="Clear Gender Filter">
                <span>
                  <IconButton
                    onClick={() => setGenderFilter(null)}
                    size="small"
                    disabled={genderFilter === null}
                    sx={{
                      mx: 0.25,
                      p: 0.75,
                      borderRadius: 1.5,
                      color: theme => genderFilter === null
                        ? theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)'
                        : theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover:not(:disabled)': {
                        background: theme => theme.palette.mode === 'dark'
                          ? 'rgba(255, 152, 0, 0.1)'
                          : 'rgba(255, 152, 0, 0.08)',
                        color: '#ff9800',
                        transform: 'scale(1.05)',
                        boxShadow: '0 2px 8px rgba(255, 152, 0, 0.2)'
                      },
                      '&:disabled': {
                        opacity: 0.4
                      }
                    }}
                  >
                    <FilterAltOffIcon sx={{ fontSize: '1.1rem' }} />
                  </IconButton>
                </span>
              </Tooltip>
            </Box>
            <Tooltip title="Export as HTML">
              <IconButton
                onClick={exportAsHtml}
                size="small"
                sx={{
                  mx: 0.5,
                  p: 0.75,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
                  color: '#fff',
                  boxShadow: '0 2px 8px rgba(255, 152, 0, 0.3)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #f57c00 0%, #ef6c00 100%)',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 16px rgba(255, 152, 0, 0.4)'
                  },
                  '&:active': {
                    transform: 'translateY(0) scale(0.98)'
                  }
                }}
              >
                <HtmlIcon sx={{ fontSize: '1.1rem', fontWeight: 'bold' }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Export as PDF">
              <IconButton
                onClick={exportToPdf}
                size="small"
                sx={{
                  mx: 0.5,
                  p: 0.75,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #f44336 0%, #d32f2f 100%)',
                  color: '#fff',
                  boxShadow: '0 2px 8px rgba(244, 67, 54, 0.3)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #d32f2f 0%, #c62828 100%)',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 16px rgba(244, 67, 54, 0.4)'
                  },
                  '&:active': {
                    transform: 'translateY(0) scale(0.98)'
                  }
                }}
              >
                <PictureAsPdfIcon sx={{ fontSize: '1.1rem', fontWeight: 'bold' }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Send as Email">
              <IconButton
                onClick={sendAsEmail}
                size="small"
                sx={{
                  mx: 0.5,
                  p: 0.75,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                  color: '#fff',
                  boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 16px rgba(76, 175, 80, 0.4)'
                  },
                  '&:active': {
                    transform: 'translateY(0) scale(0.98)'
                  }
                }}
              >
                <EmailIcon sx={{ fontSize: '1.1rem', fontWeight: 'bold' }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Close">
              <IconButton
                onClick={onClose}
                size="small"
                sx={{
                  ml: 1,
                  p: 0.75,
                  borderRadius: 2,
                  background: theme => theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
                    : 'linear-gradient(135deg, rgba(0, 0, 0, 0.08) 0%, rgba(0, 0, 0, 0.04) 100%)',
                  color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
                  border: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                  boxShadow: theme => theme.palette.mode === 'dark'
                    ? '0 2px 8px rgba(0, 0, 0, 0.3)'
                    : '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: theme => theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(244, 67, 54, 0.1) 100%)'
                      : 'linear-gradient(135deg, rgba(244, 67, 54, 0.1) 0%, rgba(244, 67, 54, 0.05) 100%)',
                    color: '#f44336',
                    transform: 'translateY(-2px) scale(1.05)',
                    boxShadow: '0 4px 16px rgba(244, 67, 54, 0.2)',
                    borderColor: 'rgba(244, 67, 54, 0.3)'
                  },
                  '&:active': {
                    transform: 'translateY(0) scale(0.98)'
                  }
                }}
              >
                <CloseIcon sx={{ fontSize: '1.1rem', fontWeight: 'bold' }} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 2, flexGrow: 1, display: 'flex', gap: 2, overflow: 'hidden', bgcolor: 'background.paper' }}>
        {/* Lecturer list section */}
        <Paper elevation={0} variant="outlined" sx={{ width: '200px', p: 2, overflow: 'auto', bgcolor: 'background.paper' }}>
          <Button
            fullWidth
            variant={!filteredLecturer ? 'contained' : 'outlined'}
            onClick={() => setFilteredLecturer(null)}
            sx={{
              mb: 1,
              color: theme => {
                if (!filteredLecturer) return 'white'; // Text is white for contained variant
                return theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'primary.main';
              }
            }}
          >
            ALL LECTURERS
          </Button>
          <List dense>
            {sortedLecturers?.map((l) => (
              <ListItemButton
                key={l.id}
                selected={filteredLecturer?.id === l.id}
                onClick={() => setFilteredLecturer(l)}
                sx={{
                  borderRadius: 1,
                  '&.Mui-selected': {
                    backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.16)' : 'primary.light',
                    color: theme => theme.palette.mode === 'dark' ? '#fff' : 'inherit',
                    '&:hover': {
                      backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(144, 202, 249, 0.24)' : 'primary.light'
                    }
                  }
                }}
              >
                <ListItemText
                  primary={`${l.firstName}${l.lastName ? ' ' + l.lastName : ''}`}
                  slotProps={{
                    primary: {
                      variant: 'body2',
                      sx: {
                        color: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'inherit',
                        fontFamily: getArabicTextClass(`${l.firstName}${l.lastName ? ' ' + l.lastName : ''}`) ? "'Tajawal', 'Arabic UI Text', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif" : undefined
                      }
                    }
                  }}
                />
              </ListItemButton>
            ))}
          </List>
        </Paper>

        {/* Main content area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 2 }}>


          {/* Timetable grid */}
          <Paper
            elevation={0}
            variant="outlined"
            sx={{
              flexGrow: 1,
              display: 'flex',
              flexDirection: 'column',
              overflow: 'auto',
              p: 1,
              bgcolor: 'background.paper'
            }}
            ref={timetableRef}
          >
            <Grid container sx={{ flexGrow: 1, minHeight: 0 }}>
              {/* Header row with days */}
              <Grid size={12} sx={{ display: 'flex', borderBottom: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}` }}>
                <Box sx={{ width: '60px', p: 1, borderRight: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}` }}> {/* Reduced width */}
                  <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
                    Period
                  </Typography>
                </Box>
                {days.map(day => (
                  <Box
                    key={day}
                    sx={{
                      flex: 1,
                      p: 1,
                      textAlign: 'center',
                      fontWeight: 'bold',
                      borderRight: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                      color: 'text.primary'
                    }}
                  >
                    {day}
                  </Box>
                ))}
              </Grid>

              {/* Time periods rows */}
              {getFilteredPeriods().map(period => (
                <Grid size={12} key={period.period} sx={{ display: 'flex', borderBottom: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}` }}>
                  {/* Simplified period cell */}
                  <Box
                    sx={{
                      width: '60px',
                      borderRight: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: theme => theme.palette.mode === 'dark' ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.05)'
                    }}
                  >
                    <Typography variant="h5" sx={{ fontWeight: 'bold', color: 'text.primary' }}>
                      {period.period}
                    </Typography>
                  </Box>

                  {days.map(day => (
                    <Box
                      key={`${day}-${period.period}`}
                      sx={{
                        flex: 1,
                        p: 1,
                        borderRight: theme => `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)'}`,
                        minHeight: '60px',
                        position: 'relative', // Add this for absolute positioning of time pill
                        bgcolor: theme => theme.palette.mode === 'dark' ? 'rgba(18, 18, 18, 1)' : '#fff'
                      }}
                    >
                      {/* Add time pill */}
                      {(() => {
                        // Determine if it's a long day
                        const isLongDay = day === 'Monday' || day === 'Wednesday';
                        const timeDisplay = isLongDay ? period.longTime : period.regularTime;

                        // Calculate end time
                        let endTimeDisplay = '';
                        const periods = getAllPeriods();
                        const nextPeriodIndex = periods.findIndex(p => p.period === period.period) + 1;

                        if (isLongDay) {
                          if (nextPeriodIndex < periods.length) {
                            endTimeDisplay = periods[nextPeriodIndex].longTime;
                          } else if (period.period === 6) {
                            endTimeDisplay = '3:30 PM';
                          } else if (period.period === 12) {
                            endTimeDisplay = '9:30 PM';
                          }
                        } else {
                          if (nextPeriodIndex < periods.length) {
                            endTimeDisplay = periods[nextPeriodIndex].regularTime;
                          } else if (period.period === 6) {
                            endTimeDisplay = '2 PM';
                          } else if (period.period === 12) {
                            endTimeDisplay = '8 PM';
                          }
                        }

                        // Check if this is a blocked time slot
                        const isBlockedTimeSlot = (day === 'Monday' || day === 'Wednesday') &&
                          ((period.period === 5 || period.period === 6) ||
                           (period.period === 11 || period.period === 12));

                        // Only render time pill if not blocked
                        return !isBlockedTimeSlot && timeDisplay && endTimeDisplay && (
                          <Typography
                            variant="caption"
                            sx={{
                              position: 'absolute',
                              top: '2px',
                              right: '4px',
                              backgroundColor: theme => theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                              color: 'text.secondary',
                              padding: '1px 4px',
                              borderRadius: '10px',
                              fontSize: '0.6rem',
                              opacity: 0.7,
                              '&:hover': {
                                opacity: 1
                              },
                              transition: 'opacity 0.2s'
                            }}
                          >
                            {timeDisplay} - {endTimeDisplay}
                          </Typography>
                        );
                      })()}
                      {/* Add margin-top to match TimetableCanvas spacing */}
                      <div className="mt-4">
                        {renderSessionCell(day, period.period)}
                      </div>
                    </Box>
                  ))}
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Box>
      </DialogContent>
    </AccessibleDialog>
  );
};

export default LecturerTimetableModal;