#!/usr/bin/env node

/**
 * QU Scheduler Manual Icon Embedding Script
 * 
 * This script manually embeds the enhanced QU Scheduler icon using rcedit
 * with proper error handling and verification.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎨 QU Scheduler Manual Icon Embedding');
console.log('====================================');

const executablePath = path.resolve('out/QU Scheduler-win32-x64/qu-scheduler.exe');
const iconPath = path.resolve('assets/icons/icon.ico');

function checkFiles() {
    console.log('\n🔍 Checking required files...');
    
    if (!fs.existsSync(executablePath)) {
        console.error('❌ Executable not found:', executablePath);
        console.log('💡 Run: npm run package');
        return false;
    }
    
    if (!fs.existsSync(iconPath)) {
        console.error('❌ Icon file not found:', iconPath);
        return false;
    }
    
    const execStats = fs.statSync(executablePath);
    const iconStats = fs.statSync(iconPath);
    
    console.log(`✅ Executable: ${(execStats.size / (1024 * 1024)).toFixed(1)} MB`);
    console.log(`✅ Icon file: ${(iconStats.size / 1024).toFixed(1)} KB`);
    
    return true;
}

function downloadRcedit() {
    console.log('\n📥 Downloading rcedit...');
    
    try {
        // Download rcedit directly
        execSync('npm install --no-save rcedit', { stdio: 'inherit' });
        console.log('✅ rcedit downloaded successfully');
        return true;
    } catch (error) {
        console.error('❌ Failed to download rcedit:', error.message);
        return false;
    }
}

function embedIconWithRcedit() {
    console.log('\n🎨 Embedding icon with rcedit...');

    // Try multiple methods to find and use rcedit
    const rceditPaths = [
        'npx rcedit',
        'node_modules\\.bin\\rcedit.exe',
        'node_modules\\rcedit\\bin\\rcedit.exe'
    ];

    for (const rceditPath of rceditPaths) {
        try {
            const command = `${rceditPath} "${executablePath}" --set-icon "${iconPath}"`;
            console.log('Trying command:', command);

            execSync(command, { stdio: 'inherit' });
            console.log('✅ Icon embedded successfully with rcedit!');
            return true;
        } catch (error) {
            console.log(`⚠️  ${rceditPath} failed:`, error.message);
        }
    }

    console.error('❌ All rcedit methods failed');
    return false;
}

function verifyEmbedding() {
    console.log('\n🔍 Verifying icon embedding...');

    // Try multiple methods to verify
    const rceditPaths = [
        'npx rcedit',
        'node_modules\\.bin\\rcedit.exe',
        'node_modules\\rcedit\\bin\\rcedit.exe'
    ];

    for (const rceditPath of rceditPaths) {
        try {
            // Try to extract the icon to verify it's embedded
            const tempIconPath = path.join(__dirname, 'temp-verify-icon.ico');

            const command = `${rceditPath} "${executablePath}" --get-icon "${tempIconPath}"`;
            execSync(command, { stdio: 'pipe' });

            if (fs.existsSync(tempIconPath)) {
                const extractedStats = fs.statSync(tempIconPath);
                console.log(`✅ Icon successfully extracted (${extractedStats.size} bytes)`);

                // Clean up
                fs.unlinkSync(tempIconPath);
                return true;
            }
        } catch (error) {
            console.log(`⚠️  Verification with ${rceditPath} failed`);
        }
    }

    console.log('❌ Icon verification failed with all methods');
    return false;
}

function main() {
    console.log('\n🚀 Starting manual icon embedding process...');
    
    // Step 1: Check files
    if (!checkFiles()) {
        process.exit(1);
    }
    
    // Step 2: Download rcedit if needed
    if (!downloadRcedit()) {
        console.error('❌ Cannot proceed without rcedit');
        process.exit(1);
    }
    
    // Step 3: Embed icon
    if (!embedIconWithRcedit()) {
        console.error('❌ Icon embedding failed');
        process.exit(1);
    }
    
    // Step 4: Verify embedding
    if (!verifyEmbedding()) {
        console.warn('⚠️  Icon verification failed, but embedding may have succeeded');
    }
    
    // Step 5: Success message
    console.log('\n🎉 ICON EMBEDDING COMPLETE!');
    console.log('===========================');
    console.log('\n📋 Next Steps:');
    console.log('   1. Build installer: node installer/build-nsis.js');
    console.log('   2. Test installation on clean system');
    console.log('   3. Verify desktop shortcut shows enhanced icon');
    console.log('   4. Clear Windows icon cache if needed: npm run clear:icon-cache');
    
    console.log('\n✅ The enhanced QU Scheduler icon should now be embedded in the executable!');
}

// Run the manual embedding process
main();
