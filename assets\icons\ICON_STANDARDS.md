# QU Scheduler Icon Standards

## Master Icon Files

### Primary Source
- **File**: `icon.ico` - Windows ICO format (32x32, 256 colors)
- **Usage**: Windows application icon, installer icon, taskbar icon
- **Source**: Single source of truth for all QU Scheduler icons

### Vector Source
- **File**: `icon.svg` - Scalable Vector Graphics
- **Usage**: Website logo, high-resolution displays, print materials
- **Features**: Enhanced QU Scheduler design with simplified 3×3 grid and improved readability

## Generated Icon Files

### PNG Files (Generated from SVG)
- `icon-16x16.png` - 16×16 pixels
- `icon-24x24.png` - 24×24 pixels
- `icon-32x32.png` - 32×32 pixels
- `icon-48x48.png` - 48×48 pixels
- `icon-64x64.png` - 64×64 pixels
- `icon-128x128.png` - 128×128 pixels
- `icon-256x256.png` - 256×256 pixels
- `icon-512x512.png` - 512×512 pixels

### Platform-Specific Files
- **Windows**: `icon.ico` (multiple sizes embedded)
- **Website**: `favicon.ico` (copied from master icon.ico)
- **Website Logo**: `qu-scheduler-logo.svg` (copied from master icon.svg)

## Usage Guidelines

### Application Icons
- **Electron App**: Uses `icon.ico` for Windows builds
- **Installer**: Uses `icon.ico` for setup executable
- **Shortcuts**: Automatically use embedded icon from executable

### Website Icons
- **Favicon**: `favicon.ico` in website root
- **Logo**: `qu-scheduler-logo.svg` for navigation and branding
- **Touch Icons**: Generate from master SVG for mobile devices

## Design Specifications

### Enhanced Visual Elements
- **Colors**: Qatar University maroon (#8B1538), white (#FFFFFF), gold (#F1C40F)
- **Design**: Three golden course sections with maroon borders, simplified 3×3 timetable grid
- **Grid Structure**: 3×3 cells with consistent 16px maroon borders for optimal small-size readability
- **Corner Radius**: Enhanced throughout - 48px (outer), 16px (sections/grid), 6px (cells)
- **Background**: Clean white background for maximum contrast and modern appearance

### Technical Specifications
- **Format**: ICO (Windows), SVG (web), PNG (cross-platform)
- **Sizes**: 16×16 to 512×512 pixels
- **Color Depth**: 32-bit with alpha transparency
- **Compression**: Optimized for file size while maintaining quality

## Maintenance

### When to Update Icons
- Major version releases
- Rebranding initiatives
- Platform-specific requirements
- User feedback on visibility/recognition

### Update Process
1. Modify master `icon.svg` file
2. Regenerate `icon.ico` from SVG
3. Run standardization script: `npm run standardize:icons`
4. Test across all platforms and contexts
5. Update documentation if needed

## File Organization

```
assets/icons/
├── icon.ico                 # Master Windows icon (source of truth)
├── icon.svg                 # Master vector icon (design source)
├── icon-16x16.png          # Generated PNG files
├── icon-24x24.png
├── icon-32x32.png
├── icon-48x48.png
├── icon-64x64.png
├── icon-128x128.png
├── icon-256x256.png
├── icon-512x512.png
└── ICON_STANDARDS.md       # This documentation
```

## Conversion Tools

### Recommended Tools
- **SVG to PNG**: ImageMagick, Inkscape, or online converters
- **PNG to ICO**: ImageMagick, GIMP, or ico-convert
- **Batch Processing**: Sharp (Node.js), Pillow (Python)

### Command Examples
```bash
# Convert SVG to PNG (ImageMagick)
magick icon.svg -resize 256x256 icon-256x256.png

# Convert PNG to ICO (ImageMagick)
magick icon-256x256.png icon.ico

# Batch convert multiple sizes
for size in 16 32 48 64 128 256 512; do
  magick icon.svg -resize ${size}x${size} icon-${size}x${size}.png
done
```

---

**Last Updated**: January 2025
**Version**: 1.0.0 Enhanced Design
**Maintainer**: QU Scheduler Development Team
**Design**: Enhanced with improved readability and modern appearance
