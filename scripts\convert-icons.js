/**
 * QU Scheduler Icon Conversion Script
 * 
 * This script converts the master SVG icon to various PNG formats and sizes
 * required for the QU Scheduler application and website.
 * 
 * Requirements:
 * - Node.js with sharp package for image processing
 * - Master SVG file at assets/icons/icon.svg
 * 
 * Usage: node scripts/convert-icons.js
 */

const fs = require('fs');
const path = require('path');

// Configuration
const MASTER_SVG_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.svg');
const ICONS_DIR = path.join(__dirname, '..', 'assets', 'icons');
const WEBSITE_ASSETS_DIR = path.join(__dirname, '..', 'website', 'assets');

// Icon sizes to generate
const ICON_SIZES = [16, 24, 32, 48, 64, 128, 256, 512];

// Special sizes for specific platforms
const SPECIAL_SIZES = {
    favicon: [16, 32, 48],
    apple: [180],
    android: [192, 512]
};

function log(message) {
    console.log(`🎨 ${message}`);
}

function logSuccess(message) {
    console.log(`✅ ${message}`);
}

function logError(message) {
    console.log(`❌ ${message}`);
}

function logWarning(message) {
    console.log(`⚠️  ${message}`);
}

// Check if sharp is available
function checkSharpAvailability() {
    try {
        require('sharp');
        return true;
    } catch (error) {
        return false;
    }
}

// Convert SVG to PNG using sharp (if available)
async function convertWithSharp() {
    const sharp = require('sharp');
    
    log('Converting SVG to PNG using Sharp...');
    
    try {
        const svgBuffer = fs.readFileSync(MASTER_SVG_PATH);
        
        for (const size of ICON_SIZES) {
            const outputPath = path.join(ICONS_DIR, `icon-${size}x${size}.png`);
            
            await sharp(svgBuffer)
                .resize(size, size)
                .png({
                    quality: 100,
                    compressionLevel: 6,
                    adaptiveFiltering: true
                })
                .toFile(outputPath);
            
            log(`Generated: icon-${size}x${size}.png`);
        }
        
        // Generate favicon sizes
        for (const size of SPECIAL_SIZES.favicon) {
            const outputPath = path.join(WEBSITE_ASSETS_DIR, `favicon-${size}x${size}.png`);
            
            await sharp(svgBuffer)
                .resize(size, size)
                .png({
                    quality: 100,
                    compressionLevel: 6
                })
                .toFile(outputPath);
            
            log(`Generated: favicon-${size}x${size}.png`);
        }
        
        logSuccess('Sharp conversion completed successfully!');
        return true;
    } catch (error) {
        logError(`Sharp conversion failed: ${error.message}`);
        return false;
    }
}

// Generate HTML-based conversion templates
function generateHTMLConverters() {
    log('Generating HTML conversion templates...');
    
    const svgContent = fs.readFileSync(MASTER_SVG_PATH, 'utf8');
    
    ICON_SIZES.forEach(size => {
        const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QU Scheduler Icon ${size}x${size}</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: white;
            font-family: Arial, sans-serif;
        }
        .icon-container {
            display: inline-block;
            width: ${size}px;
            height: ${size}px;
            border: 1px solid #ddd;
            margin: 10px;
        }
        .icon-container svg {
            width: 100%;
            height: 100%;
        }
        .info {
            margin: 20px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .instructions {
            margin: 20px 0;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <h1>QU Scheduler Icon - ${size}×${size} pixels</h1>
    
    <div class="info">
        <strong>Size:</strong> ${size}×${size} pixels<br>
        <strong>Format:</strong> SVG (scalable)<br>
        <strong>Usage:</strong> ${getUsageDescription(size)}
    </div>
    
    <div class="icon-container">
        ${svgContent}
    </div>
    
    <div class="instructions">
        <h3>How to convert to PNG:</h3>
        <ol>
            <li><strong>Browser Method:</strong> Right-click the icon above and "Save image as..." (may not work in all browsers)</li>
            <li><strong>Screenshot Method:</strong> Take a screenshot of the icon above and crop to ${size}×${size} pixels</li>
            <li><strong>Online Converter:</strong> Use convertio.co, cloudconvert.com, or similar services</li>
            <li><strong>ImageMagick:</strong> <code>magick icon.svg -resize ${size}x${size} icon-${size}x${size}.png</code></li>
            <li><strong>Inkscape:</strong> <code>inkscape icon.svg --export-png=icon-${size}x${size}.png --export-width=${size} --export-height=${size}</code></li>
        </ol>
        <p><strong>Save as:</strong> <code>icon-${size}x${size}.png</code> in the <code>assets/icons/</code> folder</p>
    </div>
    
    <script>
        // Add click handler to copy SVG content
        document.querySelector('.icon-container').addEventListener('click', function() {
            const svgElement = this.querySelector('svg');
            const svgString = new XMLSerializer().serializeToString(svgElement);
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(svgString).then(() => {
                    alert('SVG content copied to clipboard!');
                });
            } else {
                console.log('SVG Content:', svgString);
                alert('SVG content logged to console (F12 to view)');
            }
        });
    </script>
</body>
</html>`;
        
        const htmlPath = path.join(ICONS_DIR, `convert-${size}x${size}.html`);
        fs.writeFileSync(htmlPath, htmlTemplate);
        log(`Generated: convert-${size}x${size}.html`);
    });
    
    logSuccess('HTML conversion templates generated!');
}

function getUsageDescription(size) {
    if (size <= 16) return 'Small icons, favicons, taskbar';
    if (size <= 32) return 'Standard icons, toolbars, menus';
    if (size <= 48) return 'Desktop shortcuts, file associations';
    if (size <= 64) return 'Large icons, quick launch';
    if (size <= 128) return 'High-DPI displays, retina screens';
    if (size <= 256) return 'Application icons, installer graphics';
    return 'High-resolution displays, print materials';
}

// Generate a master conversion page
function generateMasterConverter() {
    log('Generating master conversion page...');
    
    const svgContent = fs.readFileSync(MASTER_SVG_PATH, 'utf8');
    
    const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QU Scheduler Icon Converter</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B1538;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .icon-item:hover {
            border-color: #8B1538;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 21, 56, 0.1);
        }
        .icon-container {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .icon-container svg {
            width: 100%;
            height: 100%;
        }
        .icon-size {
            font-weight: bold;
            color: #8B1538;
            margin-bottom: 5px;
        }
        .icon-usage {
            font-size: 0.9em;
            color: #666;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            margin: 30px 0;
        }
        .download-btn {
            background: #8B1538;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            transition: background 0.3s ease;
        }
        .download-btn:hover {
            background: #A61E42;
        }
        .master-icon {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #8B1538, #A61E42);
            border-radius: 8px;
            color: white;
        }
        .master-icon svg {
            width: 128px;
            height: 128px;
            background: white;
            border-radius: 8px;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 QU Scheduler Icon Converter</h1>
        
        <div class="master-icon">
            <h2>Master Icon (SVG)</h2>
            ${svgContent}
            <p>This is the master SVG icon that serves as the source for all other formats.</p>
        </div>
        
        <div class="instructions">
            <h3>📋 Conversion Instructions</h3>
            <p>Generate PNG files from the master SVG icon for different sizes and platforms:</p>
            <ol>
                <li><strong>Automatic (Recommended):</strong> Install Sharp and run <code>npm run convert:icons</code></li>
                <li><strong>Manual:</strong> Right-click each icon below and save as PNG</li>
                <li><strong>Batch:</strong> Use ImageMagick or similar tools for bulk conversion</li>
            </ol>
        </div>
        
        <h2>Required Icon Sizes</h2>
        <div class="icon-grid">
            ${ICON_SIZES.map(size => `
                <div class="icon-item">
                    <div class="icon-container" style="width: ${Math.min(size, 64)}px; height: ${Math.min(size, 64)}px;">
                        ${svgContent}
                    </div>
                    <div class="icon-size">${size}×${size}</div>
                    <div class="icon-usage">${getUsageDescription(size)}</div>
                    <button class="download-btn" onclick="downloadIcon(${size})">
                        Download PNG
                    </button>
                </div>
            `).join('')}
        </div>
        
        <div class="instructions">
            <h3>🔧 Command Line Tools</h3>
            <p><strong>ImageMagick:</strong></p>
            <pre>magick icon.svg -resize 256x256 icon-256x256.png</pre>
            
            <p><strong>Inkscape:</strong></p>
            <pre>inkscape icon.svg --export-png=icon-256x256.png --export-width=256 --export-height=256</pre>
            
            <p><strong>Batch conversion (ImageMagick):</strong></p>
            <pre>for size in 16 24 32 48 64 128 256 512; do
  magick icon.svg -resize \${size}x\${size} icon-\${size}x\${size}.png
done</pre>
        </div>
    </div>
    
    <script>
        function downloadIcon(size) {
            // Create a canvas element
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            // Create an image from SVG
            const svgData = \`${svgContent.replace(/`/g, '\\`')}\`;
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                // Convert to PNG and download
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = \`icon-\${size}x\${size}.png\`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
            };
            img.src = url;
        }
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                alert('Use the Download PNG buttons to save individual icons');
            }
        });
    </script>
</body>
</html>`;
    
    const htmlPath = path.join(ICONS_DIR, 'icon-converter.html');
    fs.writeFileSync(htmlPath, htmlTemplate);
    logSuccess('Master conversion page generated: icon-converter.html');
}

// Update package.json with conversion script
function updatePackageScripts() {
    log('Updating package.json scripts...');
    
    const packagePath = path.join(__dirname, '..', 'package.json');
    
    try {
        const packageContent = fs.readFileSync(packagePath, 'utf8');
        const packageJson = JSON.parse(packageContent);
        
        // Add icon conversion script
        if (!packageJson.scripts['convert:icons']) {
            packageJson.scripts['convert:icons'] = 'node scripts/convert-icons.js';
            
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
            logSuccess('Added convert:icons script to package.json');
        } else {
            log('convert:icons script already exists in package.json');
        }
    } catch (error) {
        logError(`Failed to update package.json: ${error.message}`);
    }
}

// Main execution function
async function main() {
    console.log('🎨 QU Scheduler Icon Conversion');
    console.log('===============================');
    console.log('');
    
    // Check if master SVG exists
    if (!fs.existsSync(MASTER_SVG_PATH)) {
        logError(`Master SVG file not found: ${MASTER_SVG_PATH}`);
        process.exit(1);
    }
    
    // Create website assets directory if it doesn't exist
    if (!fs.existsSync(WEBSITE_ASSETS_DIR)) {
        fs.mkdirSync(WEBSITE_ASSETS_DIR, { recursive: true });
        log('Created website assets directory.');
    }
    
    // Try Sharp conversion first
    const sharpAvailable = checkSharpAvailability();
    let conversionSuccess = false;
    
    if (sharpAvailable) {
        log('Sharp package detected. Attempting automatic conversion...');
        conversionSuccess = await convertWithSharp();
    } else {
        logWarning('Sharp package not found. Install with: npm install sharp');
    }
    
    // Generate HTML conversion templates as fallback
    if (!conversionSuccess) {
        log('Generating HTML conversion templates...');
        generateHTMLConverters();
    }
    
    // Always generate the master converter page
    generateMasterConverter();
    
    // Update package.json
    updatePackageScripts();
    
    console.log('');
    if (conversionSuccess) {
        console.log('🎉 Icon conversion complete!');
        console.log('✅ All PNG files have been generated automatically.');
    } else {
        console.log('🎉 Icon conversion templates generated!');
        console.log('📋 Next Steps:');
        console.log('1. Open assets/icons/icon-converter.html in your browser');
        console.log('2. Download PNG files for each required size');
        console.log('3. Or install Sharp: npm install sharp && npm run convert:icons');
    }
    console.log('');
    console.log('💡 Tip: Run "npm run convert:icons" to execute this script');
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    convertWithSharp,
    generateHTMLConverters,
    generateMasterConverter,
    updatePackageScripts
};
