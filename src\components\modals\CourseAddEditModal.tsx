import React, { useState, useEffect, useCallback } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import AccessibleDialog from '../common/AccessibleDialog';
import { ChromePicker } from 'react-color';
import ColorLensIcon from '@mui/icons-material/ColorLens';
import { useAppContext } from '../../context/AppContext';

// Define the course structure based on the requirements
interface CourseData {
  id?: string;
  courseCode: string;
  courseName: string;
  loadHours: number;
  contactHours: number;
  courseType: 'Theory' | 'Lab';
  maleSectionsCount: number;
  femaleSectionsCount: number;
  color: string;
  academicLevel?: string;
}

// Define props for the modal component
interface CourseAddEditModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (course: CourseData) => void;
  course?: CourseData; // If provided, we're in edit mode
}

// Sample standard colors for course color selection
const standardColors = [
  '#3b82f6', // blue
  '#10b981', // green
  '#f59e0b', // amber
  '#ef4444', // red
  '#8b5cf6', // purple
  '#ec4899', // pink
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#6366f1'  // indigo
];

// Default empty course - defined outside the component to avoid recreation on each render
const defaultEmptyCourse: CourseData = {
  courseCode: '',
  courseName: '',
  loadHours: 3,
  contactHours: 3,
  courseType: 'Theory',
  maleSectionsCount: 1,
  femaleSectionsCount: 1,
  color: standardColors[0]
};

/**
 * CourseAddEditModal component for adding or editing course details
 * Includes validation, color picker, and automatic academic level detection
 */
const CourseAddEditModal: React.FC<CourseAddEditModalProps> = ({
  open,
  onClose,
  onSave,
  course
}) => {
  // Get current semester from context
  const { currentSemester } = useAppContext();



  // State for course data
  const [courseData, setCourseData] = useState<CourseData>(course || defaultEmptyCourse);

  // State for form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // State for color picker
  const [showColorPicker, setShowColorPicker] = useState(false);

  // Update course data when the course prop changes
  useEffect(() => {
    try {
      if (course) {
        setCourseData({...course});
      } else {
        setCourseData({...defaultEmptyCourse});
      }
      setErrors({});
    } catch (error) {
      console.error('Error updating course data in modal:', error);
      // Fallback to default course if there's an error
      setCourseData({...defaultEmptyCourse});
      setErrors({});
    }
  }, [course]);

  // Handle input changes
  const handleInputChange = (field: keyof CourseData, value: string | number) => {
    try {
      let processedValue = value;
      let academicLevelUpdate: string | undefined = undefined;

      // Special handling for courseCode
      if (field === 'courseCode') {
        // Ensure value is a string for string operations
        const strValue = String(value || '');
        // Remove spaces and convert to uppercase
        processedValue = strValue.replace(/\s+/g, '').toUpperCase();

        // Determine academic level based on course number
        const courseNumber = parseInt(String(processedValue).replace(/[^\d]/g, ''), 10);

        if (!isNaN(courseNumber)) {
          if (courseNumber < 200) {
            academicLevelUpdate = 'Undergraduate 1st year';
          } else if (courseNumber < 300) {
            academicLevelUpdate = 'Undergraduate 2nd year';
          } else if (courseNumber < 400) {
            academicLevelUpdate = 'Undergraduate 3rd year';
          } else if (courseNumber < 500) {
            academicLevelUpdate = 'Undergraduate 4th year';
          } else if (courseNumber < 600) {
            academicLevelUpdate = 'Diploma';
          } else if (courseNumber < 700) {
            academicLevelUpdate = 'Master';
          } else if (courseNumber < 900) {
            academicLevelUpdate = 'PhD';
          }
        }
      }

      // Special handling for courseName
      if (field === 'courseName') {
        // Ensure value is a string for string operations
        const strValue = String(value || '');
        // Only process if the string is not empty
        if (strValue.trim()) {
          // Capitalize first letter of each word, handling empty words safely
          processedValue = strValue
            .split(' ')
            .map((word: string) => {
              // Handle empty words (multiple spaces)
              if (!word || word.length === 0) {
                return word;
              }
              return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            })
            .join(' ');
        } else {
          processedValue = strValue; // Keep empty string as is
        }
      }

      // Handle numeric fields
      if (field === 'loadHours' || field === 'contactHours' || field === 'maleSectionsCount' || field === 'femaleSectionsCount') {
        if (value === '' || String(value) === '') {
          // For empty values, use default values
          if (field === 'loadHours' || field === 'contactHours') {
            processedValue = 3; // Default value
          } else {
            processedValue = 0;
          }
        } else {
          const numValue = Number(value);
          if (!isNaN(numValue)) {
            processedValue = numValue;
          } else {
            // If not a valid number, keep the previous value
            return;
          }
        }
      }

      // Update course data with both the field value and academic level if needed
      setCourseData(prev => {
        const updates: Partial<CourseData> = {
          [field]: processedValue
        };

        // Add academic level update if it was determined
        if (academicLevelUpdate !== undefined) {
          updates.academicLevel = academicLevelUpdate;
        }

        return {
          ...prev,
          ...updates
        };
      });

      // Clear error for this field if it exists
      if (errors[field]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[field];
          return newErrors;
        });
      }
    } catch (error) {
      console.error('Error in handleInputChange:', error);
      // Don't update state if there's an error to prevent crashes
    }
  };

  // Handle color change
  const handleColorChange = (color: { hex: string }) => {
    setCourseData(prev => ({
      ...prev,
      color: color.hex
    }));
  };

  // Handle standard color selection
  const handleStandardColorSelect = (color: string) => {
    setCourseData(prev => ({
      ...prev,
      color
    }));
    setShowColorPicker(false);
  };

  // Validate the form
  const validateForm = useCallback((): boolean => {
    const newErrors: Record<string, string> = {};

    // Course code validation
    if (!courseData.courseCode) {
      newErrors.courseCode = 'Course code is required';
    } else if (!/^[A-Z]{4}\d{3,4}$/.test(courseData.courseCode)) {
      newErrors.courseCode = 'Invalid format. Should be like COMP101';
    }

    // Course name validation
    if (!courseData.courseName) {
      newErrors.courseName = 'Course name is required';
    }

    // Load hours validation
    if (courseData.loadHours <= 0) {
      newErrors.loadHours = 'Load hours must be greater than 0';
    }

    // Contact hours validation
    if (courseData.contactHours <= 0) {
      newErrors.contactHours = 'Contact hours must be greater than 0';
    }

    // Section counts validation
    if (courseData.maleSectionsCount < 0) {
      newErrors.maleSectionsCount = 'Male sections count cannot be negative';
    }

    if (courseData.femaleSectionsCount < 0) {
      newErrors.femaleSectionsCount = 'Female sections count cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [courseData]);

  // Reset form to default values
  const resetForm = useCallback(() => {
    setCourseData({...defaultEmptyCourse});
    setErrors({});
  }, [defaultEmptyCourse]);

  // Handle save action
  const handleSave = useCallback(() => {
    try {
      if (validateForm()) {
        // Create a clean copy of the course data to ensure no undefined values
        const cleanCourseData: CourseData = {
          ...courseData,
          courseCode: courseData.courseCode || '',
          courseName: courseData.courseName || '',
          loadHours: courseData.loadHours || 3,
          contactHours: courseData.contactHours || 3,
          courseType: courseData.courseType || 'Theory',
          maleSectionsCount: courseData.maleSectionsCount || 0,
          femaleSectionsCount: courseData.femaleSectionsCount || 0,
          color: courseData.color || '#3b82f6',
          academicLevel: courseData.academicLevel || ''
        };

        onSave(cleanCourseData);
        resetForm();
        onClose();
      }
    } catch (error) {
      console.error('Error saving course:', error);
      // Don't close the modal if there's an error, let the user try again
    }
  }, [validateForm, courseData, resetForm, onSave, onClose]);

  return (
    <AccessibleDialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2
          }
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          {course ? 'Edit Course' : 'Add New Course'} - {currentSemester} Semester
        </Typography>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Left column */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ mb: 3 }}>
              <TextField
                label="Course Code"
                fullWidth
                required
                value={courseData.courseCode}
                onChange={(e) => handleInputChange('courseCode', e.target.value)}
                error={!!errors.courseCode}
                helperText={errors.courseCode || 'Format: SUBJ123 (e.g., COMP101)'}
                placeholder="COMP101"
                slotProps={{
                  input: {
                    startAdornment: courseData.courseCode ? (
                      <Chip
                        label={courseData.courseCode}
                        size="small"
                        sx={{
                          bgcolor: courseData.color,
                          color: 'white',
                          fontWeight: 'bold',
                          mr: 1
                        }}
                      />
                    ) : null
                  }
                }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <TextField
                label="Course Name"
                fullWidth
                required
                value={courseData.courseName}
                onChange={(e) => handleInputChange('courseName', e.target.value)}
                error={!!errors.courseName}
                helperText={errors.courseName}
                placeholder="Introduction to Computing"
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Course Type</InputLabel>
                <Select
                  value={courseData.courseType}
                  label="Course Type"
                  onChange={(e) => handleInputChange('courseType', e.target.value)}
                >
                  <MenuItem value="Theory">Theory</MenuItem>
                  <MenuItem value="Lab">Lab</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>

          {/* Right column */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2}>
                <Grid size={6}>
                  <TextField
                    label="Load Hours"
                    type="number"
                    fullWidth
                    value={courseData.loadHours}
                    onChange={(e) => handleInputChange('loadHours', e.target.value)}
                    error={!!errors.loadHours}
                    helperText={errors.loadHours || 'Used for teaching load'}
                    slotProps={{ htmlInput: { min: 1 } }}
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    label="Contact Hours"
                    type="number"
                    fullWidth
                    value={courseData.contactHours}
                    onChange={(e) => handleInputChange('contactHours', e.target.value)}
                    error={!!errors.contactHours}
                    helperText={errors.contactHours || 'Used for timetabling'}
                    slotProps={{ htmlInput: { min: 1 } }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2}>
                <Grid size={6}>
                  <TextField
                    label="Male Sections"
                    type="number"
                    fullWidth
                    value={courseData.maleSectionsCount}
                    onChange={(e) => handleInputChange('maleSectionsCount', e.target.value)}
                    error={!!errors.maleSectionsCount}
                    helperText={errors.maleSectionsCount}
                    slotProps={{ htmlInput: { min: 0 } }}
                  />
                </Grid>
                <Grid size={6}>
                  <TextField
                    label="Female Sections"
                    type="number"
                    fullWidth
                    value={courseData.femaleSectionsCount}
                    onChange={(e) => handleInputChange('femaleSectionsCount', e.target.value)}
                    error={!!errors.femaleSectionsCount}
                    helperText={errors.femaleSectionsCount}
                    slotProps={{ htmlInput: { min: 0 } }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>
                Course Color
              </Typography>
              <Box display="flex" alignItems="center">
                <Box
                  sx={{
                    width: 36,
                    height: 36,
                    borderRadius: '50%',
                    bgcolor: courseData.color,
                    mr: 2,
                    border: '2px solid #ddd'
                  }}
                />

                <Box display="flex" flexWrap="wrap" sx={{ flex: 1 }}>
                  {standardColors.map((color) => (
                    <Tooltip title={color} key={color}>
                      <Box
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: '50%',
                          bgcolor: color,
                          m: 0.5,
                          cursor: 'pointer',
                          border: courseData.color === color ? '2px solid black' : '2px solid transparent'
                        }}
                        onClick={() => handleStandardColorSelect(color)}
                      />
                    </Tooltip>
                  ))}
                </Box>

                <IconButton onClick={() => setShowColorPicker(!showColorPicker)}>
                  <ColorLensIcon />
                </IconButton>
              </Box>

              {showColorPicker && (
                <Box sx={{ mt: 2, position: 'relative', zIndex: 1 }}>
                  <ChromePicker
                    color={courseData.color}
                    onChange={handleColorChange}
                    disableAlpha
                  />
                </Box>
              )}
            </Box>
          </Grid>

          {/* Academic level (read-only) */}
          <Grid size={12}>
            <Divider sx={{ mb: 2 }} />
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Academic Level (Auto-detected)
              </Typography>
              {courseData.academicLevel ? (
                <Chip
                  label={courseData.academicLevel}
                  color="primary"
                  variant="outlined"
                />
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Enter a valid course code to detect academic level
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
        >
          {course ? 'Save Changes' : 'Add Course'}
        </Button>
      </DialogActions>
    </AccessibleDialog>
  );
};

export default CourseAddEditModal;
