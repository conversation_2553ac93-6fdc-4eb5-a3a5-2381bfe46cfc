<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QU Scheduler - Professional Timetable Scheduling for Qatar University Departments Departments</title>
    <meta name="description" content="QU Scheduler is a professional timetable scheduling application designed for Qatar University. Features auto-scheduling, Arabic support, and advanced constraint satisfaction programming.">
    <meta name="keywords" content="timetable, scheduling, university, Qatar University, academic, course management, lecturer assignment">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/favicon-16x16.png">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="assets/qu-scheduler-logo.svg" alt="QU Scheduler Logo" class="logo-img">
                <span class="logo-text">QU Scheduler</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#features" class="nav-link">Features</a>
                </li>
                <li class="nav-item">
                    <a href="#download" class="nav-link">Download</a>
                </li>

            </ul>
            <div class="nav-toggle" id="mobile-menu">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">
                    Professional Timetable Scheduling for
                    <span class="highlight">Qatar University Departments</span>
                </h1>
                <p class="hero-description">
                    QU Scheduler is an advanced desktop application designed specifically for Qatar University's academic departments.
                    Create, manage, and optimize course timetables with intelligent auto-scheduling, Arabic language support,
                    and sophisticated constraint satisfaction programming.
                </p>
                <div class="hero-buttons">
                    <a href="#download" class="btn btn-primary">Download Now</a>
                    <a href="#features" class="btn btn-secondary">Learn More</a>
                </div>

            </div>
            <div class="hero-image">
                <img src="assets/images/The Application with empty canvas.png" alt="QU Scheduler Interface" class="hero-img">
            </div>
        </div>
    </section>

    <!-- Features Overview -->
    <section id="features" class="features-overview">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Powerful Features for Academic Scheduling</h2>
                <p class="section-description">
                    QU Scheduler combines advanced algorithms with user-friendly design to deliver
                    the most comprehensive timetable scheduling solution for academic institutions.
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                            <path d="M2 17l10 5 10-5"/>
                            <path d="M2 12l10 5 10-5"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Auto-Scheduling with CSP</h3>
                    <p class="feature-description">
                        Advanced Constraint Satisfaction Programming automatically generates optimal timetables
                        while respecting all academic rules and preferences.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 0 1 0 2.828l-7 7a2 2 0 0 1-2.828 0l-7-7A1.994 1.994 0 0 1 2 12V7a2 2 0 0 1 2-2z"/>
                            <path d="M9 12l2 2 4-4"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Drag and Drop Manual Scheduling</h3>
                    <p class="feature-description">
                        Intuitive drag-and-drop interface for manual timetable creation with real-time
                        conflict detection and visual feedback for precise control.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                            <path d="M8 9h8"/>
                            <path d="M8 13h6"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Arabic Language Support</h3>
                    <p class="feature-description">
                        Full bilingual support with proper Arabic text rendering using Tajawal font
                        for course names and lecturer information.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                            <circle cx="9" cy="7" r="4"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Gender-Separated Scheduling</h3>
                    <p class="feature-description">
                        Specialized handling for male and female course sections with intelligent
                        lecturer assignment and conflict resolution.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                            <polyline points="14,2 14,8 20,8"/>
                            <line x1="16" y1="13" x2="8" y2="13"/>
                            <line x1="16" y1="17" x2="8" y2="17"/>
                            <polyline points="10,9 9,9 8,9"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Professional Export</h3>
                    <p class="feature-description">
                        Export timetables to PDF, HTML, and JSON formats with professional formatting
                        and embedded Arabic fonts for perfect rendering.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                            <polyline points="22,6 12,13 2,6"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Email Integration</h3>
                    <p class="feature-description">
                        Direct email functionality for sharing individual lecturer timetables
                        with embedded HTML content and professional formatting.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4l3 3h4a2 2 0 0 0 2-2v-5a2 2 0 0 0-2-2H9z"/>
                            <path d="M9 11V9a3 3 0 0 1 6 0v2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Lecturer Load Calculation</h3>
                    <p class="feature-description">
                        Automatically calculates lecturer teaching and supervision load across the academic year.
                        Tracks semester and yearly workloads with visual indicators for capacity management.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                            <polyline points="7.5,4.21 12,6.81 16.5,4.21"/>
                            <polyline points="7.5,19.79 7.5,14.6 3,12"/>
                            <polyline points="21,12 16.5,14.6 16.5,19.79"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Offline Operation</h3>
                    <p class="feature-description">
                        Complete functionality without internet dependency. Perfect for secure
                        environments and air-gapped systems with all assets bundled locally.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Screenshots Section -->
    <section class="screenshots">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">See QU Scheduler in Action</h2>
                <p class="section-description">
                    Experience the intuitive interface and powerful features that make
                    academic scheduling effortless and efficient.
                </p>
            </div>
            <div class="screenshots-grid">
                <div class="screenshot-item">
                    <img src="assets/images/The Application with empty canvas.png" alt="Main Timetable Interface" class="screenshot-img">
                    <div class="screenshot-overlay">
                        <h3>Clean Interface</h3>
                        <p>Start with an empty canvas ready for course and lecturer management</p>
                    </div>
                </div>
                <div class="screenshot-item">
                    <img src="assets/images/Course Statistics of the App.png" alt="Statistics Dashboard" class="screenshot-img">
                    <div class="screenshot-overlay">
                        <h3>Course Statistics</h3>
                        <p>Comprehensive analytics and lecturer workload management</p>
                    </div>
                </div>
                <div class="screenshot-item">
                    <img src="assets/images/the application with fully scheduled canvas.png" alt="Scheduled Timetable" class="screenshot-img">
                    <div class="screenshot-overlay">
                        <h3>Fully Scheduled Timetable</h3>
                        <p>Color-coded sessions with lecturer assignments and gender indicators</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="download">
        <div class="container">
            <div class="download-content">
                <div class="download-info">
                    <h2 class="download-title">Download QU Scheduler</h2>
                    <p class="download-description">
                        Get the latest version of QU Scheduler for Windows. The application is
                        designed for Qatar University department heads and academic staff.
                    </p>
                    <div class="download-features">
                        <div class="download-feature">
                            <span class="checkmark">✓</span>
                            <span>Windows 10/11 Compatible (64-bit)</span>
                        </div>
                        <div class="download-feature">
                            <span class="checkmark">✓</span>
                            <span>Complete Offline Functionality</span>
                        </div>
                        <div class="download-feature">
                            <span class="checkmark">✓</span>
                            <span>Arabic Language Support</span>
                        </div>
                        <div class="download-feature">
                            <span class="checkmark">✓</span>
                            <span>Professional PDF Export</span>
                        </div>
                    </div>
                </div>
                <div class="download-options">
                    <div class="download-card">
                        <h3>QU Scheduler Installer</h3>
                        <p>Professional installation with shortcuts and integration</p>
                        <div class="download-size">Size: ~117 MB</div>
                        <a href="QU-Scheduler-Setup.exe" class="btn btn-primary download-btn">
                            Download Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="assets/qu-scheduler-logo.svg" alt="QU Scheduler Logo" class="footer-logo-img">
                        <span class="footer-logo-text">QU Scheduler</span>
                    </div>
                    <p class="footer-description">
                        Professional timetable scheduling application designed specifically
                        for Qatar University's academic departments.
                    </p>
                </div>
                <div class="footer-section">
                    <h4>Application</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#download">Download</a></li>
                        <li><a href="assets/docs/system-requirements.html">System Requirements</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <ul class="footer-links">
                        <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><a href="https://qu.edu.qa">Qatar University</a></li>
                        <li>Version 1.0.0</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Qatar University. All rights reserved. | Developed by Prof Ayman Saleh</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
