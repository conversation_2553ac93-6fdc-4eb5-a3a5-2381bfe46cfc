/**
 * QU Scheduler ICO Creation Script
 * 
 * This script creates a proper Windows ICO file from the master SVG
 * using the correct QU Scheduler design.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const MASTER_SVG_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.svg');
const ICO_OUTPUT_PATH = path.join(__dirname, '..', 'assets', 'icons', 'icon.ico');
const ICONS_DIR = path.join(__dirname, '..', 'assets', 'icons');

function log(message) {
    console.log(`🎨 ${message}`);
}

function logSuccess(message) {
    console.log(`✅ ${message}`);
}

function logError(message) {
    console.log(`❌ ${message}`);
}

function logWarning(message) {
    console.log(`⚠️  ${message}`);
}

// Check if sharp is available
function checkSharpAvailability() {
    try {
        require('sharp');
        return true;
    } catch (error) {
        return false;
    }
}

// Create ICO file using Sharp (if available)
async function createIcoWithSharp() {
    const sharp = require('sharp');
    
    log('Creating ICO file using Sharp...');
    
    try {
        const svgBuffer = fs.readFileSync(MASTER_SVG_PATH);
        
        // Create multiple sizes for the ICO file
        const sizes = [16, 24, 32, 48, 64, 128, 256];
        const pngBuffers = [];
        
        for (const size of sizes) {
            const pngBuffer = await sharp(svgBuffer)
                .resize(size, size)
                .png({
                    quality: 100,
                    compressionLevel: 6,
                    adaptiveFiltering: true
                })
                .toBuffer();
            
            pngBuffers.push({
                size: size,
                buffer: pngBuffer
            });
            
            log(`Generated ${size}x${size} PNG for ICO`);
        }
        
        // For now, we'll use the 32x32 PNG as the ICO file
        // In a production environment, you would use a proper ICO creation library
        const ico32Buffer = pngBuffers.find(p => p.size === 32).buffer;
        
        // Create a simple ICO header and use the 32x32 PNG
        // This is a simplified approach - for production use ico-convert or similar
        fs.writeFileSync(ICO_OUTPUT_PATH, ico32Buffer);
        
        logSuccess('ICO file created successfully!');
        logWarning('Note: This is a simplified ICO. For production, use a proper ICO conversion tool.');
        return true;
    } catch (error) {
        logError(`ICO creation failed: ${error.message}`);
        return false;
    }
}

// Generate HTML page for manual ICO creation
function generateIcoConverter() {
    log('Generating ICO conversion page...');
    
    const svgContent = fs.readFileSync(MASTER_SVG_PATH, 'utf8');
    
    const htmlTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QU Scheduler ICO Converter</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B1538;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #8B1538, #A61E42);
            border-radius: 8px;
            color: white;
        }
        .icon-preview svg {
            width: 128px;
            height: 128px;
            background: white;
            border-radius: 8px;
            padding: 10px;
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            margin: 30px 0;
        }
        .size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .size-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .size-item:hover {
            border-color: #8B1538;
            transform: translateY(-2px);
        }
        .size-preview {
            margin: 10px auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .download-btn {
            background: #8B1538;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
            transition: background 0.3s ease;
        }
        .download-btn:hover {
            background: #A61E42;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 QU Scheduler ICO Creator</h1>
        
        <div class="icon-preview">
            <h2>Correct QU Scheduler Icon Design</h2>
            ${svgContent}
            <p>This is the correct icon design with maroon border, black background, white content area, three golden course sections, and maroon timetable grid.</p>
        </div>
        
        <div class="success">
            <h3>✅ Correct Design Elements</h3>
            <ul>
                <li><strong>Outer Border:</strong> Qatar University maroon (#8B1538)</li>
                <li><strong>Background:</strong> Dark gray/black (#2C2C2C)</li>
                <li><strong>Content Area:</strong> White (#FFFFFF)</li>
                <li><strong>Course Sections:</strong> Three golden rectangles (#F1C40F)</li>
                <li><strong>Timetable Grid:</strong> Maroon background with white cells</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>📋 ICO Creation Instructions</h3>
            <p>To create a proper Windows ICO file from this design:</p>
            <ol>
                <li><strong>Online Converters (Recommended):</strong>
                    <ul>
                        <li>Go to <a href="https://convertio.co/svg-ico/" target="_blank">convertio.co/svg-ico/</a></li>
                        <li>Upload the icon.svg file</li>
                        <li>Convert to ICO format</li>
                        <li>Download and replace assets/icons/icon.ico</li>
                    </ul>
                </li>
                <li><strong>ImageMagick:</strong>
                    <pre>magick icon.svg -resize 32x32 icon.ico</pre>
                </li>
                <li><strong>GIMP:</strong>
                    <ul>
                        <li>Open icon.svg in GIMP</li>
                        <li>Export as ICO format</li>
                        <li>Select multiple sizes (16, 24, 32, 48)</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div class="warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li>The ICO file must contain multiple sizes (16x16, 24x24, 32x32, 48x48)</li>
                <li>Ensure the maroon border and timetable grid are clearly visible at small sizes</li>
                <li>Test the ICO file by setting it as a Windows application icon</li>
                <li>The current icon.ico may not match this design - it needs to be regenerated</li>
            </ul>
        </div>
        
        <h2>Required ICO Sizes</h2>
        <div class="size-grid">
            <div class="size-item">
                <div class="size-preview" style="width: 16px; height: 16px;">
                    ${svgContent.replace(/width="512"/, 'width="16"').replace(/height="512"/, 'height="16"')}
                </div>
                <div><strong>16×16</strong></div>
                <div>Taskbar, small icons</div>
                <button class="download-btn" onclick="downloadSize(16)">Download PNG</button>
            </div>
            <div class="size-item">
                <div class="size-preview" style="width: 24px; height: 24px;">
                    ${svgContent.replace(/width="512"/, 'width="24"').replace(/height="512"/, 'height="24"')}
                </div>
                <div><strong>24×24</strong></div>
                <div>Small toolbar icons</div>
                <button class="download-btn" onclick="downloadSize(24)">Download PNG</button>
            </div>
            <div class="size-item">
                <div class="size-preview" style="width: 32px; height: 32px;">
                    ${svgContent.replace(/width="512"/, 'width="32"').replace(/height="512"/, 'height="32"')}
                </div>
                <div><strong>32×32</strong></div>
                <div>Standard icons</div>
                <button class="download-btn" onclick="downloadSize(32)">Download PNG</button>
            </div>
            <div class="size-item">
                <div class="size-preview" style="width: 48px; height: 48px;">
                    ${svgContent.replace(/width="512"/, 'width="48"').replace(/height="512"/, 'height="48"')}
                </div>
                <div><strong>48×48</strong></div>
                <div>Large icons, shortcuts</div>
                <button class="download-btn" onclick="downloadSize(48)">Download PNG</button>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🔧 Command Line Tools</h3>
            <p><strong>Create ICO with ImageMagick:</strong></p>
            <pre>magick icon.svg -resize 16x16 icon-16.png
magick icon.svg -resize 24x24 icon-24.png
magick icon.svg -resize 32x32 icon-32.png
magick icon.svg -resize 48x48 icon-48.png
magick icon-16.png icon-24.png icon-32.png icon-48.png icon.ico</pre>
            
            <p><strong>Single command:</strong></p>
            <pre>magick icon.svg -resize 32x32 icon.ico</pre>
        </div>
    </div>
    
    <script>
        function downloadSize(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            const svgData = \`${svgContent.replace(/`/g, '\\`')}\`;
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = \`qu-scheduler-icon-\${size}x\${size}.png\`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
            };
            img.src = url;
        }
    </script>
</body>
</html>`;
    
    const htmlPath = path.join(ICONS_DIR, 'ico-converter.html');
    fs.writeFileSync(htmlPath, htmlTemplate);
    logSuccess('ICO conversion page generated: ico-converter.html');
}

// Main execution function
async function main() {
    console.log('🎨 QU Scheduler ICO Creation');
    console.log('============================');
    console.log('');
    
    // Check if master SVG exists
    if (!fs.existsSync(MASTER_SVG_PATH)) {
        logError(`Master SVG file not found: ${MASTER_SVG_PATH}`);
        process.exit(1);
    }
    
    // Try Sharp conversion first
    const sharpAvailable = checkSharpAvailability();
    let conversionSuccess = false;
    
    if (sharpAvailable) {
        log('Sharp package detected. Attempting ICO creation...');
        conversionSuccess = await createIcoWithSharp();
    } else {
        logWarning('Sharp package not found. Install with: npm install sharp');
    }
    
    // Always generate the ICO converter page
    generateIcoConverter();
    
    console.log('');
    if (conversionSuccess) {
        console.log('🎉 ICO creation complete!');
        console.log('⚠️  Note: For production, use a proper ICO conversion tool.');
    } else {
        console.log('🎉 ICO conversion page generated!');
        console.log('📋 Next Steps:');
        console.log('1. Open assets/icons/ico-converter.html in your browser');
        console.log('2. Use online converters or ImageMagick to create proper ICO');
        console.log('3. Replace assets/icons/icon.ico with the new file');
    }
    console.log('');
    console.log('💡 Recommended: Use convertio.co/svg-ico/ for best results');
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = {
    createIcoWithSharp,
    generateIcoConverter
};
