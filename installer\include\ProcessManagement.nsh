; Process Management Functions
; Alternative implementation using Windows API calls

!ifndef PROCESS_MANAGEMENT_INCLUDED
!define PROCESS_MANAGEMENT_INCLUDED

; Find process by name
!macro FindProcessByName ProcessName ResultVar
    Push ${ProcessName}
    Call FindProcess
    Pop ${ResultVar}
!macroend
!define FindProcessByName "!insertmacro FindProcessByName"

; Close process by name
!macro CloseProcessByName ProcessName ResultVar
    Push ${ProcessName}
    Call CloseProcess
    Pop ${ResultVar}
!macroend
!define CloseProcessByName "!insertMacro CloseProcessByName"

; Kill process by name
!macro KillProcessByName ProcessName ResultVar
    Push ${ProcessName}
    Call KillProcess
    Pop ${ResultVar}
!macroend
!define KillProcessByName "!insertMacro KillProcessByName"

!endif ; PROCESS_MANAGEMENT_INCLUDED
