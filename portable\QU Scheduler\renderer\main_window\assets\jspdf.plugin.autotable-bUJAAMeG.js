function q(e,t,r,i,n){i=i||{};var a=1.15,s=n.internal.scaleFactor,h=n.internal.getFontSize()/s,f=n.getLineHeightFactor?n.getLineHeightFactor():a,o=h*f,l=/\r\n|\r|\n/g,g="",u=1;if((i.valign==="middle"||i.valign==="bottom"||i.halign==="center"||i.halign==="right")&&(g=typeof e=="string"?e.split(l):e,u=g.length||1),r+=h*(2-a),i.valign==="middle"?r-=u/2*o:i.valign==="bottom"&&(r-=u*o),i.halign==="center"||i.halign==="right"){var v=h;if(i.halign==="center"&&(v*=.5),g&&u>=1){for(var d=0;d<g.length;d++)n.text(g[d],t-n.getStringUnitWidth(g[d])*v,r),r+=o;return n}t-=n.getStringUnitWidth(e)*v}return i.halign==="justify"?n.text(e,t,r,{maxWidth:i.maxWidth||100,align:"justify"}):n.text(e,t,r),n}var M={},P=function(){function e(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return e.setDefaults=function(t,r){r===void 0&&(r=null),r?r.__autoTableDocumentDefaults=t:M=t},e.unifyColor=function(t){return Array.isArray(t)?t:typeof t=="number"?[t,t,t]:typeof t=="string"?[t]:null},e.prototype.applyStyles=function(t,r){var i,n,a;r===void 0&&(r=!1),t.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(t.fontStyle);var s=this.jsPDFDocument.internal.getFont(),h=s.fontStyle,f=s.fontName;if(t.font&&(f=t.font),t.fontStyle){h=t.fontStyle;var o=this.getFontList()[f];o&&o.indexOf(h)===-1&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(o[0]),h=o[0])}if(this.jsPDFDocument.setFont(f,h),t.fontSize&&this.jsPDFDocument.setFontSize(t.fontSize),!r){var l=e.unifyColor(t.fillColor);l&&(i=this.jsPDFDocument).setFillColor.apply(i,l),l=e.unifyColor(t.textColor),l&&(n=this.jsPDFDocument).setTextColor.apply(n,l),l=e.unifyColor(t.lineColor),l&&(a=this.jsPDFDocument).setDrawColor.apply(a,l),typeof t.lineWidth=="number"&&this.jsPDFDocument.setLineWidth(t.lineWidth)}},e.prototype.splitTextToSize=function(t,r,i){return this.jsPDFDocument.splitTextToSize(t,r,i)},e.prototype.rect=function(t,r,i,n,a){return this.jsPDFDocument.rect(t,r,i,n,a)},e.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},e.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},e.prototype.getDocument=function(){return this.jsPDFDocument},e.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},e.prototype.addPage=function(){return this.jsPDFDocument.addPage()},e.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},e.prototype.getGlobalOptions=function(){return M||{}},e.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},e.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return t.width==null&&(t={width:t.getWidth(),height:t.getHeight()}),t},e.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},e.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},e.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},e.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},e}(),z=function(e,t){return z=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(r[n]=i[n])},z(e,t)};function G(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");z(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var J=function(e){G(t,e);function t(r){var i=e.call(this)||this;return i._element=r,i}return t}(Array);function nt(e){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/e,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}function at(e){var t={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}};return t[e]}function D(e,t,r){r.applyStyles(t,!0);var i=Array.isArray(e)?e:[e],n=i.map(function(a){return r.getTextWidth(a)}).reduce(function(a,s){return Math.max(a,s)},0);return n}function U(e,t,r,i){var n=t.settings.tableLineWidth,a=t.settings.tableLineColor;e.applyStyles({lineWidth:n,lineColor:a});var s=$(n,!1);s&&e.rect(r.x,r.y,t.getWidth(e.pageSize().width),i.y-r.y,s)}function $(e,t){var r=e>0,i=t||t===0;return r&&i?"DF":r?"S":i?"F":null}function F(e,t){var r,i,n,a;if(e=e||t,Array.isArray(e)){if(e.length>=4)return{top:e[0],right:e[1],bottom:e[2],left:e[3]};if(e.length===3)return{top:e[0],right:e[1],bottom:e[2],left:e[1]};if(e.length===2)return{top:e[0],right:e[1],bottom:e[0],left:e[1]};e.length===1?e=e[0]:e=t}return typeof e=="object"?(typeof e.vertical=="number"&&(e.top=e.vertical,e.bottom=e.vertical),typeof e.horizontal=="number"&&(e.right=e.horizontal,e.left=e.horizontal),{left:(r=e.left)!==null&&r!==void 0?r:t,top:(i=e.top)!==null&&i!==void 0?i:t,right:(n=e.right)!==null&&n!==void 0?n:t,bottom:(a=e.bottom)!==null&&a!==void 0?a:t}):(typeof e!="number"&&(e=t),{top:e,right:e,bottom:e,left:e})}function Q(e,t){var r=F(t.settings.margin,0);return e.pageSize().width-(r.left+r.right)}function ot(e,t,r,i,n){var a={},s=1.3333333333333333,h=b(t,function(c){return n.getComputedStyle(c).backgroundColor});h!=null&&(a.fillColor=h);var f=b(t,function(c){return n.getComputedStyle(c).color});f!=null&&(a.textColor=f);var o=ht(i,r);o&&(a.cellPadding=o);var l="borderTopColor",g=s*r,u=i.borderTopWidth;if(i.borderBottomWidth===u&&i.borderRightWidth===u&&i.borderLeftWidth===u){var v=(parseFloat(u)||0)/g;v&&(a.lineWidth=v)}else a.lineWidth={top:(parseFloat(i.borderTopWidth)||0)/g,right:(parseFloat(i.borderRightWidth)||0)/g,bottom:(parseFloat(i.borderBottomWidth)||0)/g,left:(parseFloat(i.borderLeftWidth)||0)/g},a.lineWidth.top||(a.lineWidth.right?l="borderRightColor":a.lineWidth.bottom?l="borderBottomColor":a.lineWidth.left&&(l="borderLeftColor"));var d=b(t,function(c){return n.getComputedStyle(c)[l]});d!=null&&(a.lineColor=d);var m=["left","right","center","justify"];m.indexOf(i.textAlign)!==-1&&(a.halign=i.textAlign),m=["middle","bottom","top"],m.indexOf(i.verticalAlign)!==-1&&(a.valign=i.verticalAlign);var p=parseInt(i.fontSize||"");isNaN(p)||(a.fontSize=p/s);var y=st(i);y&&(a.fontStyle=y);var x=(i.fontFamily||"").toLowerCase();return e.indexOf(x)!==-1&&(a.font=x),a}function st(e){var t="";return(e.fontWeight==="bold"||e.fontWeight==="bolder"||parseInt(e.fontWeight)>=700)&&(t="bold"),(e.fontStyle==="italic"||e.fontStyle==="oblique")&&(t+="italic"),t}function b(e,t){var r=X(e,t);if(!r)return null;var i=r.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!i||!Array.isArray(i))return null;var n=[parseInt(i[1]),parseInt(i[2]),parseInt(i[3])],a=parseInt(i[4]);return a===0||isNaN(n[0])||isNaN(n[1])||isNaN(n[2])?null:n}function X(e,t){var r=t(e);return r==="rgba(0, 0, 0, 0)"||r==="transparent"||r==="initial"||r==="inherit"?e.parentElement==null?null:X(e.parentElement,t):r}function ht(e,t){var r=[e.paddingTop,e.paddingRight,e.paddingBottom,e.paddingLeft],i=96/(72/t),n=(parseInt(e.lineHeight)-parseInt(e.fontSize))/t/2,a=r.map(function(h){return parseInt(h||"0")/i}),s=F(a,0);return n>s.top&&(s.top=n),n>s.bottom&&(s.bottom=n),s}function Z(e,t,r,i,n){var a,s;i===void 0&&(i=!1),n===void 0&&(n=!1);var h;typeof t=="string"?h=r.document.querySelector(t):h=t;var f=Object.keys(e.getFontList()),o=e.scaleFactor(),l=[],g=[],u=[];if(!h)return console.error("Html table could not be found with input: ",t),{head:l,body:g,foot:u};for(var v=0;v<h.rows.length;v++){var d=h.rows[v],m=(s=(a=d==null?void 0:d.parentElement)===null||a===void 0?void 0:a.tagName)===null||s===void 0?void 0:s.toLowerCase(),p=ft(f,o,r,d,i,n);p&&(m==="thead"?l.push(p):m==="tfoot"?u.push(p):g.push(p))}return{head:l,body:g,foot:u}}function ft(e,t,r,i,n,a){for(var s=new J(i),h=0;h<i.cells.length;h++){var f=i.cells[h],o=r.getComputedStyle(f);if(n||o.display!=="none"){var l=void 0;a&&(l=ot(e,f,t,o,r)),s.push({rowSpan:f.rowSpan,colSpan:f.colSpan,styles:l,_element:f,content:lt(f)})}}var g=r.getComputedStyle(i);if(s.length>0&&(n||g.display!=="none"))return s}function lt(e){var t=e.cloneNode(!0);return t.innerHTML=t.innerHTML.replace(/\n/g,"").replace(/ +/g," "),t.innerHTML=t.innerHTML.split(/<br.*?>/).map(function(r){return r.trim()}).join(`
`),t.innerText||t.textContent||""}function gt(e,t,r){for(var i=0,n=[e,t,r];i<n.length;i++){var a=n[i];a&&typeof a!="object"&&console.error("The options parameter should be of type object, is: "+typeof a),a.startY&&typeof a.startY!="number"&&(console.error("Invalid value for startY option",a.startY),delete a.startY)}}function w(e,t,r,i,n){if(e==null)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(e),s=1;s<arguments.length;s++){var h=arguments[s];if(h!=null)for(var f in h)Object.prototype.hasOwnProperty.call(h,f)&&(a[f]=h[f])}return a}function B(e,t){var r=new P(e),i=r.getDocumentOptions(),n=r.getGlobalOptions();gt(n,i,t);var a=w({},n,i,t),s;typeof window<"u"&&(s=window);var h=ut(n,i,t),f=vt(n,i,t),o=dt(r,a),l=yt(r,a,s);return{id:t.tableId,content:l,hooks:f,styles:h,settings:o}}function ut(e,t,r){for(var i={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},n=function(f){if(f==="columnStyles"){var o=e[f],l=t[f],g=r[f];i.columnStyles=w({},o,l,g)}else{var u=[e,t,r],v=u.map(function(d){return d[f]||{}});i[f]=w({},v[0],v[1],v[2])}},a=0,s=Object.keys(i);a<s.length;a++){var h=s[a];n(h)}return i}function vt(e,t,r){for(var i=[e,t,r],n={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},a=0,s=i;a<s.length;a++){var h=s[a];h.didParseCell&&n.didParseCell.push(h.didParseCell),h.willDrawCell&&n.willDrawCell.push(h.willDrawCell),h.didDrawCell&&n.didDrawCell.push(h.didDrawCell),h.willDrawPage&&n.willDrawPage.push(h.willDrawPage),h.didDrawPage&&n.didDrawPage.push(h.didDrawPage)}return n}function dt(e,t){var r,i,n,a,s,h,f,o,l,g,u,v,d=F(t.margin,40/e.scaleFactor()),m=(r=pt(e,t.startY))!==null&&r!==void 0?r:d.top,p;t.showFoot===!0?p="everyPage":t.showFoot===!1?p="never":p=(i=t.showFoot)!==null&&i!==void 0?i:"everyPage";var y;t.showHead===!0?y="everyPage":t.showHead===!1?y="never":y=(n=t.showHead)!==null&&n!==void 0?n:"everyPage";var x=(a=t.useCss)!==null&&a!==void 0?a:!1,c=t.theme||(x?"plain":"striped"),C=!!t.horizontalPageBreak,rt=(s=t.horizontalPageBreakRepeat)!==null&&s!==void 0?s:null;return{includeHiddenHtml:(h=t.includeHiddenHtml)!==null&&h!==void 0?h:!1,useCss:x,theme:c,startY:m,margin:d,pageBreak:(f=t.pageBreak)!==null&&f!==void 0?f:"auto",rowPageBreak:(o=t.rowPageBreak)!==null&&o!==void 0?o:"auto",tableWidth:(l=t.tableWidth)!==null&&l!==void 0?l:"auto",showHead:y,showFoot:p,tableLineWidth:(g=t.tableLineWidth)!==null&&g!==void 0?g:0,tableLineColor:(u=t.tableLineColor)!==null&&u!==void 0?u:200,horizontalPageBreak:C,horizontalPageBreakRepeat:rt,horizontalPageBreakBehaviour:(v=t.horizontalPageBreakBehaviour)!==null&&v!==void 0?v:"afterAllRows"}}function pt(e,t){var r=e.getLastAutoTable(),i=e.scaleFactor(),n=e.pageNumber(),a=!1;if(r&&r.startPageNumber){var s=r.startPageNumber+r.pageNumber-1;a=s===n}return typeof t=="number"?t:(t==null||t===!1)&&a&&(r==null?void 0:r.finalY)!=null?r.finalY+20/i:null}function yt(e,t,r){var i=t.head||[],n=t.body||[],a=t.foot||[];if(t.html){var s=t.includeHiddenHtml;if(r){var h=Z(e,t.html,r,s,t.useCss)||{};i=h.head||i,n=h.body||i,a=h.foot||i}else console.error("Cannot parse html in non browser environment")}var f=t.columns||mt(i,n,a);return{columns:f,head:i,body:n,foot:a}}function mt(e,t,r){var i=e[0]||t[0]||r[0]||[],n=[];return Object.keys(i).filter(function(a){return a!=="_element"}).forEach(function(a){var s=1,h;Array.isArray(i)?h=i[parseInt(a)]:h=i[a],typeof h=="object"&&!Array.isArray(h)&&(s=(h==null?void 0:h.colSpan)||1);for(var f=0;f<s;f++){var o=void 0;Array.isArray(i)?o=n.length:o=a+(f>0?"_".concat(f):"");var l={dataKey:o};n.push(l)}}),n}var A=function(){function e(t,r,i){this.table=r,this.pageNumber=r.pageNumber,this.settings=r.settings,this.cursor=i,this.doc=t.getDocument()}return e}(),ct=function(e){G(t,e);function t(r,i,n,a,s,h){var f=e.call(this,r,i,h)||this;return f.cell=n,f.row=a,f.column=s,f.section=a.section,f}return t}(A),wt=function(){function e(t,r){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=r.columns,this.head=r.head,this.body=r.body,this.foot=r.foot}return e.prototype.getHeadHeight=function(t){return this.head.reduce(function(r,i){return r+i.getMaxCellHeight(t)},0)},e.prototype.getFootHeight=function(t){return this.foot.reduce(function(r,i){return r+i.getMaxCellHeight(t)},0)},e.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},e.prototype.callCellHooks=function(t,r,i,n,a,s){for(var h=0,f=r;h<f.length;h++){var o=f[h],l=new ct(t,this,i,n,a,s),g=o(l)===!1;if(i.text=Array.isArray(i.text)?i.text:[i.text],g)return!1}return!0},e.prototype.callEndPageHooks=function(t,r){t.applyStyles(t.userStyles);for(var i=0,n=this.hooks.didDrawPage;i<n.length;i++){var a=n[i];a(new A(t,this,r))}},e.prototype.callWillDrawPageHooks=function(t,r){for(var i=0,n=this.hooks.willDrawPage;i<n.length;i++){var a=n[i];a(new A(t,this,r))}},e.prototype.getWidth=function(t){if(typeof this.settings.tableWidth=="number")return this.settings.tableWidth;if(this.settings.tableWidth==="wrap"){var r=this.columns.reduce(function(n,a){return n+a.wrappedWidth},0);return r}else{var i=this.settings.margin;return t-i.left-i.right}},e}(),V=function(){function e(t,r,i,n,a){a===void 0&&(a=!1),this.height=0,this.raw=t,t instanceof J&&(this.raw=t._element,this.element=t._element),this.index=r,this.section=i,this.cells=n,this.spansMultiplePages=a}return e.prototype.getMaxCellHeight=function(t){var r=this;return t.reduce(function(i,n){var a;return Math.max(i,((a=r.cells[n.index])===null||a===void 0?void 0:a.height)||0)},0)},e.prototype.hasRowSpan=function(t){var r=this;return t.filter(function(i){var n=r.cells[i.index];return n?n.rowSpan>1:!1}).length>0},e.prototype.canEntireRowFit=function(t,r){return this.getMaxCellHeight(r)<=t},e.prototype.getMinimumRowHeight=function(t,r){var i=this;return t.reduce(function(n,a){var s=i.cells[a.index];if(!s)return 0;var h=r.getLineHeight(s.styles.fontSize),f=s.padding("vertical"),o=f+h;return o>n?o:n},0)},e}(),tt=function(){function e(t,r,i){var n;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=r,this.section=i,this.raw=t;var a=t;t!=null&&typeof t=="object"&&!Array.isArray(t)?(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,a=(n=t.content)!==null&&n!==void 0?n:t,t._element&&(this.raw=t._element)):(this.rowSpan=1,this.colSpan=1);var s=a!=null?""+a:"",h=/\r\n|\r|\n/g;this.text=s.split(h)}return e.prototype.getTextPos=function(){var t;if(this.styles.valign==="top")t=this.y+this.padding("top");else if(this.styles.valign==="bottom")t=this.y+this.height-this.padding("bottom");else{var r=this.height-this.padding("vertical");t=this.y+r/2+this.padding("top")}var i;if(this.styles.halign==="right")i=this.x+this.width-this.padding("right");else if(this.styles.halign==="center"){var n=this.width-this.padding("horizontal");i=this.x+n/2+this.padding("left")}else i=this.x+this.padding("left");return{x:i,y:t}},e.prototype.getContentHeight=function(t,r){r===void 0&&(r=1.15);var i=Array.isArray(this.text)?this.text.length:1,n=this.styles.fontSize/t*r,a=i*n+this.padding("vertical");return Math.max(a,this.styles.minCellHeight)},e.prototype.padding=function(t){var r=F(this.styles.cellPadding,0);return t==="vertical"?r.top+r.bottom:t==="horizontal"?r.left+r.right:r[t]},e}(),St=function(){function e(t,r,i){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=r,this.index=i}return e.prototype.getMaxCustomCellWidth=function(t){for(var r=0,i=0,n=t.allRows();i<n.length;i++){var a=n[i],s=a.cells[this.index];s&&typeof s.styles.cellWidth=="number"&&(r=Math.max(r,s.styles.cellWidth))}return r},e}();function xt(e,t){Ct(e,t);var r=[],i=0;t.columns.forEach(function(a){var s=a.getMaxCustomCellWidth(t);s?a.width=s:(a.width=a.wrappedWidth,r.push(a)),i+=a.width});var n=t.getWidth(e.pageSize().width)-i;n&&(n=j(r,n,function(a){return Math.max(a.minReadableWidth,a.minWidth)})),n&&(n=j(r,n,function(a){return a.minWidth})),n=Math.abs(n),!t.settings.horizontalPageBreak&&n>.1/e.scaleFactor()&&(n=n<1?n:Math.round(n),console.warn("Of the table content, ".concat(n," units width could not fit page"))),Ht(t),Dt(t,e),Pt(t)}function Ct(e,t){var r=e.scaleFactor(),i=t.settings.horizontalPageBreak,n=Q(e,t);t.allRows().forEach(function(a){for(var s=0,h=t.columns;s<h.length;s++){var f=h[s],o=a.cells[f.index];if(o){var l=t.hooks.didParseCell;t.callCellHooks(e,l,o,a,f,null);var g=o.padding("horizontal");o.contentWidth=D(o.text,o.styles,e)+g;var u=D(o.text.join(" ").split(/[^\S\u00A0]+/),o.styles,e);if(o.minReadableWidth=u+o.padding("horizontal"),typeof o.styles.cellWidth=="number")o.minWidth=o.styles.cellWidth,o.wrappedWidth=o.styles.cellWidth;else if(o.styles.cellWidth==="wrap"||i===!0)o.contentWidth>n?(o.minWidth=n,o.wrappedWidth=n):(o.minWidth=o.contentWidth,o.wrappedWidth=o.contentWidth);else{var v=10/r;o.minWidth=o.styles.minCellWidth||v,o.wrappedWidth=o.contentWidth,o.minWidth>o.wrappedWidth&&(o.wrappedWidth=o.minWidth)}}}}),t.allRows().forEach(function(a){for(var s=0,h=t.columns;s<h.length;s++){var f=h[s],o=a.cells[f.index];if(o&&o.colSpan===1)f.wrappedWidth=Math.max(f.wrappedWidth,o.wrappedWidth),f.minWidth=Math.max(f.minWidth,o.minWidth),f.minReadableWidth=Math.max(f.minReadableWidth,o.minReadableWidth);else{var l=t.styles.columnStyles[f.dataKey]||t.styles.columnStyles[f.index]||{},g=l.cellWidth||l.minCellWidth;g&&typeof g=="number"&&(f.minWidth=g,f.wrappedWidth=g)}o&&(o.colSpan>1&&!f.minWidth&&(f.minWidth=o.minWidth),o.colSpan>1&&!f.wrappedWidth&&(f.wrappedWidth=o.minWidth))}})}function j(e,t,r){for(var i=t,n=e.reduce(function(v,d){return v+d.wrappedWidth},0),a=0;a<e.length;a++){var s=e[a],h=s.wrappedWidth/n,f=i*h,o=s.width+f,l=r(s),g=o<l?l:o;t-=g-s.width,s.width=g}if(t=Math.round(t*1e10)/1e10,t){var u=e.filter(function(v){return t<0?v.width>r(v):!0});u.length&&(t=j(u,t,r))}return t}function Pt(e){for(var t={},r=1,i=e.allRows(),n=0;n<i.length;n++)for(var a=i[n],s=0,h=e.columns;s<h.length;s++){var f=h[s],o=t[f.index];if(r>1)r--,delete a.cells[f.index];else if(o)o.cell.height+=a.height,r=o.cell.colSpan,delete a.cells[f.index],o.left--,o.left<=1&&delete t[f.index];else{var l=a.cells[f.index];if(!l)continue;if(l.height=a.height,l.rowSpan>1){var g=i.length-n,u=l.rowSpan>g?g:l.rowSpan;t[f.index]={cell:l,left:u,row:a}}}}}function Ht(e){for(var t=e.allRows(),r=0;r<t.length;r++)for(var i=t[r],n=null,a=0,s=0,h=0;h<e.columns.length;h++){var f=e.columns[h];if(s-=1,s>1&&e.columns[h+1])a+=f.width,delete i.cells[f.index];else if(n){var o=n;delete i.cells[f.index],n=null,o.width=f.width+a}else{var o=i.cells[f.index];if(!o)continue;if(s=o.colSpan,a=0,o.colSpan>1){n=o,a+=f.width;continue}o.width=f.width+a}}}function Dt(e,t){for(var r={count:0,height:0},i=0,n=e.allRows();i<n.length;i++){for(var a=n[i],s=0,h=e.columns;s<h.length;s++){var f=h[s],o=a.cells[f.index];if(o){t.applyStyles(o.styles,!0);var l=o.width-o.padding("horizontal");if(o.styles.overflow==="linebreak")o.text=t.splitTextToSize(o.text,l+1/t.scaleFactor(),{fontSize:o.styles.fontSize});else if(o.styles.overflow==="ellipsize")o.text=N(o.text,l,o.styles,t,"...");else if(o.styles.overflow==="hidden")o.text=N(o.text,l,o.styles,t,"");else if(typeof o.styles.overflow=="function"){var g=o.styles.overflow(o.text,l);typeof g=="string"?o.text=[g]:o.text=g}o.contentHeight=o.getContentHeight(t.scaleFactor(),t.getLineHeightFactor());var u=o.contentHeight/o.rowSpan;o.rowSpan>1&&r.count*r.height<u*o.rowSpan?r={height:u,count:o.rowSpan}:r&&r.count>0&&r.height>u&&(u=r.height),u>a.height&&(a.height=u)}}r.count--}}function N(e,t,r,i,n){return e.map(function(a){return Wt(a,t,r,i,n)})}function Wt(e,t,r,i,n){var a=1e4*i.scaleFactor();if(t=Math.ceil(t*a)/a,t>=D(e,r,i))return e;for(;t<D(e+n,r,i)&&!(e.length<=1);)e=e.substring(0,e.length-1);return e.trim()+n}function L(e,t){var r=new P(e),i=Ft(t,r.scaleFactor()),n=new wt(t,i);return xt(r,n),r.applyStyles(r.userStyles),n}function Ft(e,t){var r=e.content,i=kt(r.columns);if(r.head.length===0){var n=O(i,"head");n&&r.head.push(n)}if(r.foot.length===0){var n=O(i,"foot");n&&r.foot.push(n)}var a=e.settings.theme,s=e.styles;return{columns:i,head:k("head",r.head,i,s,a,t),body:k("body",r.body,i,s,a,t),foot:k("foot",r.foot,i,s,a,t)}}function k(e,t,r,i,n,a){var s={},h=t.map(function(f,o){for(var l=0,g={},u=0,v=0,d=0,m=r;d<m.length;d++){var p=m[d];if(s[p.index]==null||s[p.index].left===0)if(v===0){var y=void 0;Array.isArray(f)?y=f[p.index-u-l]:y=f[p.dataKey];var x={};typeof y=="object"&&!Array.isArray(y)&&(x=(y==null?void 0:y.styles)||{});var c=Tt(e,p,o,n,i,a,x),C=new tt(y,c,e);g[p.dataKey]=C,g[p.index]=C,v=C.colSpan-1,s[p.index]={left:C.rowSpan-1,times:v}}else v--,u++;else s[p.index].left--,v=s[p.index].times,l++}return new V(f,o,e,g)});return h}function O(e,t){var r={};return e.forEach(function(i){if(i.raw!=null){var n=bt(t,i.raw);n!=null&&(r[i.dataKey]=n)}}),Object.keys(r).length>0?r:null}function bt(e,t){if(e==="head"){if(typeof t=="object")return t.header||null;if(typeof t=="string"||typeof t=="number")return t}else if(e==="foot"&&typeof t=="object")return t.footer;return null}function kt(e){return e.map(function(t,r){var i,n;return typeof t=="object"?n=(i=t.dataKey)!==null&&i!==void 0?i:r:n=r,new St(n,t,r)})}function Tt(e,t,r,i,n,a,s){var h=at(i),f;e==="head"?f=n.headStyles:e==="body"?f=n.bodyStyles:e==="foot"&&(f=n.footStyles);var o=w({},h.table,h[e],n.styles,f),l=n.columnStyles[t.dataKey]||n.columnStyles[t.index]||{},g=e==="body"?l:{},u=e==="body"&&r%2===0?w({},h.alternateRow,n.alternateRowStyles):{},v=nt(a),d=w({},v,o,u,g);return w(d,s)}function Rt(e,t,r){var i;r===void 0&&(r={});var n=Q(e,t),a=new Map,s=[],h=[],f=[];Array.isArray(t.settings.horizontalPageBreakRepeat)?f=t.settings.horizontalPageBreakRepeat:(typeof t.settings.horizontalPageBreakRepeat=="string"||typeof t.settings.horizontalPageBreakRepeat=="number")&&(f=[t.settings.horizontalPageBreakRepeat]),f.forEach(function(u){var v=t.columns.find(function(d){return d.dataKey===u||d.index===u});v&&!a.has(v.index)&&(a.set(v.index,!0),s.push(v.index),h.push(t.columns[v.index]),n-=v.wrappedWidth)});for(var o=!0,l=(i=r==null?void 0:r.start)!==null&&i!==void 0?i:0;l<t.columns.length;){if(a.has(l)){l++;continue}var g=t.columns[l].wrappedWidth;if(o||n>=g)o=!1,s.push(l),h.push(t.columns[l]),n-=g;else break;l++}return{colIndexes:s,columns:h,lastIndex:l-1}}function zt(e,t){for(var r=[],i=0;i<t.columns.length;i++){var n=Rt(e,t,{start:i});n.columns.length&&(r.push(n),i=n.lastIndex)}return r}function E(e,t){var r=t.settings,i=r.startY,n=r.margin,a={x:n.left,y:i},s=t.getHeadHeight(t.columns)+t.getFootHeight(t.columns),h=i+n.bottom+s;if(r.pageBreak==="avoid"){var f=t.body,o=f.reduce(function(u,v){return u+v.height},0);h+=o}var l=new P(e);(r.pageBreak==="always"||r.startY!=null&&h>l.pageSize().height)&&(it(l),a.y=n.top),t.callWillDrawPageHooks(l,a);var g=w({},a);t.startPageNumber=l.pageNumber(),r.horizontalPageBreak?At(l,t,g,a):(l.applyStyles(l.userStyles),(r.showHead==="firstPage"||r.showHead==="everyPage")&&t.head.forEach(function(u){return S(l,t,u,a,t.columns)}),l.applyStyles(l.userStyles),t.body.forEach(function(u,v){var d=v===t.body.length-1;W(l,t,u,d,g,a,t.columns)}),l.applyStyles(l.userStyles),(r.showFoot==="lastPage"||r.showFoot==="everyPage")&&t.foot.forEach(function(u){return S(l,t,u,a,t.columns)})),U(l,t,g,a),t.callEndPageHooks(l,a),t.finalY=a.y,e.lastAutoTable=t,l.applyStyles(l.userStyles)}function At(e,t,r,i){var n=zt(e,t),a=t.settings;if(a.horizontalPageBreakBehaviour==="afterAllRows")n.forEach(function(o,l){e.applyStyles(e.userStyles),l>0?H(e,t,r,i,o.columns,!0):Y(e,t,i,o.columns),jt(e,t,r,i,o.columns),T(e,t,i,o.columns)});else for(var s=-1,h=n[0],f=function(){var o=s;if(h){e.applyStyles(e.userStyles);var l=h.columns;s>=0?H(e,t,r,i,l,!0):Y(e,t,i,l),o=I(e,t,s+1,i,l),T(e,t,i,l)}var g=o-s;n.slice(1).forEach(function(u){e.applyStyles(e.userStyles),H(e,t,r,i,u.columns,!0),I(e,t,s+1,i,u.columns,g),T(e,t,i,u.columns)}),s=o};s<t.body.length-1;)f()}function Y(e,t,r,i){var n=t.settings;e.applyStyles(e.userStyles),(n.showHead==="firstPage"||n.showHead==="everyPage")&&t.head.forEach(function(a){return S(e,t,a,r,i)})}function jt(e,t,r,i,n){e.applyStyles(e.userStyles),t.body.forEach(function(a,s){var h=s===t.body.length-1;W(e,t,a,h,r,i,n)})}function I(e,t,r,i,n,a){e.applyStyles(e.userStyles),a=a??t.body.length;var s=Math.min(r+a,t.body.length),h=-1;return t.body.slice(r,s).forEach(function(f,o){var l=r+o===t.body.length-1,g=et(e,t,l,i);f.canEntireRowFit(g,n)&&(S(e,t,f,i,n),h=r+o)}),h}function T(e,t,r,i){var n=t.settings;e.applyStyles(e.userStyles),(n.showFoot==="lastPage"||n.showFoot==="everyPage")&&t.foot.forEach(function(a){return S(e,t,a,r,i)})}function Bt(e,t,r){var i=r.getLineHeight(e.styles.fontSize),n=e.padding("vertical"),a=Math.floor((t-n)/i);return Math.max(0,a)}function Lt(e,t,r,i){var n={};e.spansMultiplePages=!0,e.height=0;for(var a=0,s=0,h=r.columns;s<h.length;s++){var f=h[s],o=e.cells[f.index];if(o){Array.isArray(o.text)||(o.text=[o.text]);var l=new tt(o.raw,o.styles,o.section);l=w(l,o),l.text=[];var g=Bt(o,t,i);o.text.length>g&&(l.text=o.text.splice(g,o.text.length));var u=i.scaleFactor(),v=i.getLineHeightFactor();o.contentHeight=o.getContentHeight(u,v),o.contentHeight>=t&&(o.contentHeight=t,l.styles.minCellHeight-=t),o.contentHeight>e.height&&(e.height=o.contentHeight),l.contentHeight=l.getContentHeight(u,v),l.contentHeight>a&&(a=l.contentHeight),n[f.index]=l}}var d=new V(e.raw,-1,e.section,n,!0);d.height=a;for(var m=0,p=r.columns;m<p.length;m++){var f=p[m],l=d.cells[f.index];l&&(l.height=d.height);var o=e.cells[f.index];o&&(o.height=e.height)}return d}function Et(e,t,r,i){var n=e.pageSize().height,a=i.settings.margin,s=a.top+a.bottom,h=n-s;t.section==="body"&&(h-=i.getHeadHeight(i.columns)+i.getFootHeight(i.columns));var f=t.getMinimumRowHeight(i.columns,e),o=f<r;if(f>h)return console.error("Will not be able to print row ".concat(t.index," correctly since it's minimum height is larger than page height")),!0;if(!o)return!1;var l=t.hasRowSpan(i.columns),g=t.getMaxCellHeight(i.columns)>h;return g?(l&&console.error("The content of row ".concat(t.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!(l||i.settings.rowPageBreak==="avoid")}function W(e,t,r,i,n,a,s){var h=et(e,t,i,a);if(r.canEntireRowFit(h,s))S(e,t,r,a,s);else if(Et(e,r,h,t)){var f=Lt(r,h,t,e);S(e,t,r,a,s),H(e,t,n,a,s),W(e,t,f,i,n,a,s)}else H(e,t,n,a,s),W(e,t,r,i,n,a,s)}function S(e,t,r,i,n){i.x=t.settings.margin.left;for(var a=0,s=n;a<s.length;a++){var h=s[a],f=r.cells[h.index];if(!f){i.x+=h.width;continue}e.applyStyles(f.styles),f.x=i.x,f.y=i.y;var o=t.callCellHooks(e,t.hooks.willDrawCell,f,r,h,i);if(o===!1){i.x+=h.width;continue}Mt(e,f,i);var l=f.getTextPos();q(f.text,l.x,l.y,{halign:f.styles.halign,valign:f.styles.valign,maxWidth:Math.ceil(f.width-f.padding("left")-f.padding("right"))},e.getDocument()),t.callCellHooks(e,t.hooks.didDrawCell,f,r,h,i),i.x+=h.width}i.y+=r.height}function Mt(e,t,r){var i=t.styles;if(e.getDocument().setFillColor(e.getDocument().getFillColor()),typeof i.lineWidth=="number"){var n=$(i.lineWidth,i.fillColor);n&&e.rect(t.x,r.y,t.width,t.height,n)}else typeof i.lineWidth=="object"&&(i.fillColor&&e.rect(t.x,r.y,t.width,t.height,"F"),Nt(e,t,r,i.lineWidth))}function Nt(e,t,r,i){var n,a,s,h;i.top&&(n=r.x,a=r.y,s=r.x+t.width,h=r.y,i.right&&(s+=.5*i.right),i.left&&(n-=.5*i.left),f(i.top,n,a,s,h)),i.bottom&&(n=r.x,a=r.y+t.height,s=r.x+t.width,h=r.y+t.height,i.right&&(s+=.5*i.right),i.left&&(n-=.5*i.left),f(i.bottom,n,a,s,h)),i.left&&(n=r.x,a=r.y,s=r.x,h=r.y+t.height,i.top&&(a-=.5*i.top),i.bottom&&(h+=.5*i.bottom),f(i.left,n,a,s,h)),i.right&&(n=r.x+t.width,a=r.y,s=r.x+t.width,h=r.y+t.height,i.top&&(a-=.5*i.top),i.bottom&&(h+=.5*i.bottom),f(i.right,n,a,s,h));function f(o,l,g,u,v){e.getDocument().setLineWidth(o),e.getDocument().line(l,g,u,v,"S")}}function et(e,t,r,i){var n=t.settings.margin.bottom,a=t.settings.showFoot;return(a==="everyPage"||a==="lastPage"&&r)&&(n+=t.getFootHeight(t.columns)),e.pageSize().height-i.y-n}function H(e,t,r,i,n,a){n===void 0&&(n=[]),a===void 0&&(a=!1),e.applyStyles(e.userStyles),t.settings.showFoot==="everyPage"&&!a&&t.foot.forEach(function(h){return S(e,t,h,i,n)}),t.callEndPageHooks(e,i);var s=t.settings.margin;U(e,t,r,i),it(e),t.pageNumber++,i.x=s.left,i.y=s.top,r.y=s.top,t.callWillDrawPageHooks(e,i),t.settings.showHead==="everyPage"&&(t.head.forEach(function(h){return S(e,t,h,i,n)}),e.applyStyles(e.userStyles))}function it(e){var t=e.pageNumber();e.setPage(t+1);var r=e.pageNumber();return r===t?(e.addPage(),!0):!1}function Ot(e){e.API.autoTable=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var i=t[0],n=B(this,i),a=L(this,n);return E(this,a),this},e.API.lastAutoTable=!1,e.API.autoTableText=function(t,r,i,n){q(t,r,i,n,this)},e.API.autoTableSetDefaults=function(t){return P.setDefaults(t,this),this},e.autoTableSetDefaults=function(t,r){P.setDefaults(t,r)},e.API.autoTableHtmlToJson=function(t,r){var i;if(r===void 0&&(r=!1),typeof window>"u")return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var n=new P(this),a=Z(n,t,window,r,!1),s=a.head,h=a.body,f=((i=s[0])===null||i===void 0?void 0:i.map(function(o){return o.content}))||[];return{columns:f,rows:h,data:h}}}var R;function Yt(e,t){var r=B(e,t),i=L(e,r);E(e,i)}function It(e,t){var r=B(e,t);return L(e,r)}function Kt(e,t){E(e,t)}try{if(typeof window<"u"&&window){var K=window,_=K.jsPDF||((R=K.jspdf)===null||R===void 0?void 0:R.jsPDF);_&&Ot(_)}}catch(e){console.error("Could not apply autoTable plugin",e)}export{tt as Cell,ct as CellHookData,St as Column,A as HookData,V as Row,wt as Table,It as __createTable,Kt as __drawTable,Ot as applyPlugin,Yt as autoTable,Yt as default};
