# QU Scheduler Website Assets

This directory contains all the assets for the QU Scheduler marketing website.

## Directory Structure

```
assets/
├── images/
│   ├── hero-screenshot.png          # Main hero section screenshot
│   ├── screenshot-main.png          # Main timetable interface
│   ├── screenshot-stats.png         # Statistics dashboard
│   ├── screenshot-scheduled.png     # Scheduled timetable view
│   └── qu-scheduler-logo.svg        # Application logo
├── downloads/
│   ├── QU-Scheduler-Setup.exe       # Windows installer (117MB)
│   └── QU-Scheduler-Portable.zip    # Portable version (150MB)
├── docs/
│   ├── QU-Scheduler-User-Guide.pdf  # User documentation
│   └── system-requirements.html     # System requirements page
└── favicon.ico                      # Website favicon

```

## Image Requirements

### Screenshots
- **Format**: PNG with transparency support
- **Resolution**: Minimum 1920x1080 for hero image, 1200x800 for feature screenshots
- **Quality**: High quality, showing clear interface elements
- **Content**: Should demonstrate key features like:
  - Auto-scheduling interface
  - Arabic text support
  - Gender-separated sections
  - Statistics and analytics
  - PDF export capabilities

### Logo
- **Format**: SVG (vector format for scalability)
- **Colors**: Qatar University brand colors (maroon #8B1538, gold #D4AF37)
- **Background**: Transparent
- **Size**: Scalable, optimized for web use

## Download Files

### Installer Version
- **File**: QU-Scheduler-Setup.exe
- **Size**: ~117 MB
- **Type**: NSIS installer with professional UI
- **Features**: 
  - Automatic installation
  - Desktop shortcuts
  - Start menu integration
  - Uninstaller

### Portable Version
- **File**: QU-Scheduler-Portable.zip
- **Size**: ~150 MB
- **Type**: Compressed archive
- **Features**:
  - No installation required
  - Run from any location
  - Self-contained

## Documentation

### User Guide
- **File**: QU-Scheduler-User-Guide.pdf
- **Content**: 
  - Installation instructions
  - Feature overview
  - Step-by-step tutorials
  - Troubleshooting guide
  - Arabic language support guide

### System Requirements
- **File**: system-requirements.html
- **Content**:
  - Minimum system specifications
  - Supported Windows versions
  - Hardware requirements
  - Software dependencies

## Usage Notes

1. **Image Optimization**: All images should be optimized for web use while maintaining quality
2. **File Hosting**: Download files should be hosted on a reliable server with good bandwidth
3. **Updates**: Keep download links and documentation current with latest application version
4. **Accessibility**: Ensure all images have appropriate alt text for screen readers
5. **Performance**: Consider using WebP format for images where supported

## Placeholder Content

Currently, this directory contains placeholder references. To complete the website:

1. Add actual screenshots from the QU Scheduler application
2. Copy the installer and portable files from the build output
3. Create comprehensive user documentation
4. Add the application logo in SVG format
5. Generate a proper favicon from the logo

## File Sources

- **Screenshots**: Capture from running QU Scheduler application
- **Installer**: Copy from `out/make/nsis/x64/QU-Scheduler-Setup.exe`
- **Portable**: Create from `out/QU Scheduler-win32-x64/` directory
- **Logo**: Use existing `assets/qu-scheduler-logo.svg` from main project
- **Documentation**: Create based on application features and user needs
