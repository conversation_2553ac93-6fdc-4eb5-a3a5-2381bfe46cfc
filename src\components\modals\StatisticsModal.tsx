import React, { useState, useRef, useEffect } from 'react';
import {
  DialogTitle,
  DialogContent,
  Box,
  Tabs,
  Tab,
  Typography,
  IconButton,
  Backdrop,
  CircularProgress,
  Snackbar,
  Alert
} from '@mui/material';
import AccessibleDialog from '../common/AccessibleDialog';
import CloseIcon from '@mui/icons-material/Close';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { useAppContext } from '../../context/AppContext';
import LecturerLoadsTab from './tabs/LecturerLoadsTab';
import CourseStatisticsTab from './tabs/CourseStatisticsTab';
import SchedulingOverviewTab from './tabs/SchedulingOverviewTab';
import GenderDistributionTab from './tabs/GenderDistributionTab';
import { Lecturer } from '../../types/models';

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`statistics-tabpanel-${index}`}
      aria-labelledby={`statistics-tab-${index}`}
      style={{ width: '100%' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: { xs: 1, sm: 2, md: 2.5 }, maxWidth: '1800px', mx: 'auto', width: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Tab accessibility props
const a11yProps = (index: number) => {
  return {
    id: `statistics-tab-${index}`,
    'aria-controls': `statistics-tabpanel-${index}`,
  };
};

// StatisticsModal props
interface StatisticsModalProps {
  open: boolean;
  onClose: () => void;
}

const StatisticsModal: React.FC<StatisticsModalProps> = ({ open, onClose }) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);

  // State for loading indicator
  const [loading, setLoading] = useState(false);

  // State for snackbar
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  // Create ref for the dialog content
  const dialogContentRef = useRef<HTMLDivElement>(null);

  // Get context data
  const {
    currentSemester,
    courses,
    sections,
    sessions,
    lecturers
  } = useAppContext();

  // State for department name and academic year
  const [departmentName, setDepartmentName] = useState<string>('');
  const [academicYear, setAcademicYear] = useState<string>('');

  // Load department name and academic year from store
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const uiState = await window.electronAPI.store.get('uiState');

        // Set department name from store if available
        if (uiState && (uiState as any).departmentName) {
          setDepartmentName((uiState as any).departmentName);
        }

        // Set academic year from store if available
        if (uiState && (uiState as any).academicYear) {
          setAcademicYear((uiState as any).academicYear);
        }
      } catch (error) {
        console.error('Error loading UI state:', error);
      }
    };

    loadUIState();
  }, []);

  // Export the entire statistics as PDF
  const exportStatisticsToPdf = () => {
    // Get the active tab panel
    const activeTabPanel = document.getElementById(`statistics-tabpanel-${activeTab}`);
    if (!activeTabPanel) {
      setSnackbarMessage('Could not find the active tab content');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
      return;
    }

    // Show loading indicator
    setLoading(true);

    // Get the tab title
    const tabTitles = ['Lecturer Loads', 'Course Statistics', 'Scheduling Overview', 'Gender Distribution'];
    const activeTabTitle = tabTitles[activeTab];

    try {
      // Import html2canvas dynamically
      import('html2canvas').then((html2canvasModule) => {
        const html2canvas = html2canvasModule.default;

        // Convert the active tab panel to canvas
        html2canvas(activeTabPanel, {
          scale: 2, // Higher scale for better quality
          useCORS: true,
          logging: false,
          allowTaint: true,
          backgroundColor: '#ffffff',
          scrollY: -window.scrollY, // Ensure we capture the full content, even if scrolled
          windowHeight: activeTabPanel.scrollHeight,
          onclone: (_document, element) => {
            // Find all Chip components in the Status column and ensure their text is visible
            const statusChips = element.querySelectorAll('td:nth-child(9) .MuiChip-label');
            statusChips.forEach(chip => {
              if (chip instanceof HTMLElement) {
                chip.style.color = 'black';
                chip.style.fontWeight = 'bold';
              }
            });
          }
        }).then(canvas => {
          try {
            // Create a new PDF document
            import('jspdf').then(({ jsPDF }) => {
              const doc = new jsPDF('p', 'mm', 'a4');

              // Add title
              doc.setFontSize(16);
              doc.text(`Timetable Statistics - ${activeTabTitle}`, 14, 20);

              // Add subtitle with semester, department, and academic year
              doc.setFontSize(12);
              doc.text(`${currentSemester} | ${departmentName || 'Department'} | ${academicYear || 'Academic Year'}`, 14, 30);

              // Convert canvas to image
              const imgData = canvas.toDataURL('image/png');

              // Calculate dimensions to fit on PDF
              const imgWidth = 190; // mm
              const imgHeight = (canvas.height * imgWidth) / canvas.width;

              // Add image to PDF
              doc.addImage(imgData, 'PNG', 10, 40, imgWidth, imgHeight);

              // Save PDF with appropriate filename
              doc.save(`timetable_statistics_${activeTabTitle.toLowerCase().replace(/\s+/g, '_')}.pdf`);

              // Hide loading indicator and show success message
              setLoading(false);
              setSnackbarMessage('PDF exported successfully');
              setSnackbarSeverity('success');
              setSnackbarOpen(true);
            }).catch(error => {
              console.error('Error creating PDF:', error);
              setLoading(false);
              setSnackbarMessage('Error creating PDF');
              setSnackbarSeverity('error');
              setSnackbarOpen(true);
            });
          } catch (error) {
            console.error('Error in PDF generation:', error);
            setLoading(false);
            setSnackbarMessage('Error generating PDF');
            setSnackbarSeverity('error');
            setSnackbarOpen(true);
          }
        }).catch(error => {
          console.error('Error capturing content:', error);
          setLoading(false);
          setSnackbarMessage('Error capturing content');
          setSnackbarSeverity('error');
          setSnackbarOpen(true);
        });
      }).catch(error => {
        console.error('Error loading html2canvas:', error);
        setLoading(false);
        setSnackbarMessage('Error loading required libraries');
        setSnackbarSeverity('error');
        setSnackbarOpen(true);
      });
    } catch (error) {
      console.error('Unexpected error:', error);
      setLoading(false);
      setSnackbarMessage('An unexpected error occurred');
      setSnackbarSeverity('error');
      setSnackbarOpen(true);
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Utility functions for calculating lecturer loads
  const calculateFallTeachingLoad = (lecturerId: string): number => {
    // Find all sessions assigned to this lecturer in the Fall semester
    const lecturerSessions = sessions['Fall'].filter(
      session => session.lecturerId === lecturerId ||
                (session.lecturerIds && session.lecturerIds.includes(lecturerId))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach(session => {
      // Find the section and course for this session
      const section = sections['Fall'].find(s => s.id === session.sectionId);
      if (!section) return;

      const course = courses['Fall'].find(c => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  const calculateSpringTeachingLoad = (lecturerId: string): number => {
    // Find all sessions assigned to this lecturer in the Spring semester
    const lecturerSessions = sessions['Spring'].filter(
      session => session.lecturerId === lecturerId ||
                (session.lecturerIds && session.lecturerIds.includes(lecturerId))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach(session => {
      // Find the section and course for this session
      const section = sections['Spring'].find(s => s.id === session.sectionId);
      if (!section) return;

      const course = courses['Spring'].find(c => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  const calculateSummerTeachingLoad = (lecturerId: string): number => {
    // Find all sessions assigned to this lecturer in the Summer semester
    const lecturerSessions = sessions['Summer'].filter(
      session => session.lecturerId === lecturerId ||
                (session.lecturerIds && session.lecturerIds.includes(lecturerId))
    );

    // Calculate total teaching load based on sessions
    let totalLoadHours = 0;

    lecturerSessions.forEach(session => {
      // Find the section and course for this session
      const section = sections['Summer'].find(s => s.id === session.sectionId);
      if (!section) return;

      const course = courses['Summer'].find(c => c.id === section.courseId);
      if (!course) return;

      // Calculate contact hours based on day
      const contactHours = (session.day === 'Monday' || session.day === 'Wednesday') ? 1.5 : 1.0;

      // Calculate load hours based on course's load-to-contact hours ratio
      const loadToContactRatio = course.loadHours / course.contactHours;
      let sessionLoadHours = contactHours * loadToContactRatio;

      // If multiple lecturers are assigned to this session, divide the load equally
      const lecturerCount = session.lecturerIds && session.lecturerIds.length > 0 ?
                           session.lecturerIds.length :
                           (session.lecturerId ? 1 : 0);

      if (lecturerCount > 1) {
        sessionLoadHours = sessionLoadHours / lecturerCount;
      }

      totalLoadHours += sessionLoadHours;
    });

    return totalLoadHours;
  };

  const calculateYearlyLoad = (lecturer: Lecturer): number => {
    // Supervision hours from both semesters
    const supervisionHours = lecturer.supervisionHoursFall + lecturer.supervisionHoursSpring;

    // Calculate Fall teaching load
    const fallTeachingLoad = calculateFallTeachingLoad(lecturer.id);

    // Calculate Spring teaching load
    const springTeachingLoad = calculateSpringTeachingLoad(lecturer.id);

    // Summer is not included in yearly load
    return supervisionHours + fallTeachingLoad + springTeachingLoad;
  };

  return (
    <AccessibleDialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      fullScreen
      sx={{
        '& .MuiDialog-paper': {
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <DialogTitle sx={{ p: 1, bgcolor: 'primary.main', color: 'white' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center' }}>
              Timetable Statistics
              <Typography variant="caption" sx={{ ml: 1, opacity: 0.9 }}>
                {currentSemester} | {departmentName || 'Department'} | {academicYear || 'Academic Year'}
              </Typography>
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <IconButton
              size="small"
              sx={{ color: 'white', mr: 1, p: 0.5 }}
              title="Export as PDF"
              onClick={exportStatisticsToPdf}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
            <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="statistics tabs"
          variant="scrollable"
          scrollButtons="auto"
          sx={{ px: 2 }}
        >
          <Tab label="Lecturer Loads" {...a11yProps(0)} />
          <Tab label="Course Statistics" {...a11yProps(1)} />
          <Tab label="Scheduling Overview" {...a11yProps(2)} />
          <Tab label="Gender Distribution" {...a11yProps(3)} />
        </Tabs>
      </Box>

      <DialogContent ref={dialogContentRef} dividers sx={{ p: 0, flexGrow: 1, display: 'flex', flexDirection: 'column', overflow: 'auto', bgcolor: 'background.default' }}>
        <TabPanel value={activeTab} index={0}>
          <LecturerLoadsTab
            lecturers={lecturers}
            calculateFallTeachingLoad={calculateFallTeachingLoad}
            calculateSpringTeachingLoad={calculateSpringTeachingLoad}
            calculateSummerTeachingLoad={calculateSummerTeachingLoad}
            calculateYearlyLoad={calculateYearlyLoad}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <CourseStatisticsTab
            courses={courses}
            sections={sections}
            sessions={sessions}
            currentSemester={currentSemester}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <SchedulingOverviewTab
            sections={sections}
            sessions={sessions}
            currentSemester={currentSemester}
          />
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <GenderDistributionTab
            courses={courses}
            sections={sections}
            sessions={sessions}
            currentSemester={currentSemester}
          />
        </TabPanel>
      </DialogContent>
      {/* Loading indicator */}
      <Backdrop
        sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
        open={loading}
      >
        <CircularProgress color="inherit" />
      </Backdrop>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </AccessibleDialog>
  );
};

export default StatisticsModal;
