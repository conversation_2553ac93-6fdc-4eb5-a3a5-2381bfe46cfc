; QU Scheduler NSIS Installer Script
; Professional installer with enhanced features for Qatar University Scheduler
; Features: Process detection, previous installation handling, post-install launch

!define APPNAME "QU Scheduler"
!define COMPANYNAME "Qatar University"
!define DESCRIPTION "Professional timetable scheduling application for Qatar University"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "mailto:<EMAIL>"
!define UPDATEURL "https://qu.edu.qa"
!define ABOUTURL "https://qu.edu.qa"
!define INSTALLSIZE 150000 ; Estimate in KB
!define EXENAME "qu-scheduler.exe"
!define UNINSTKEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
!define REGKEY "Software\${COMPANYNAME}\${APPNAME}"

; Include Modern UI and additional libraries
!include "MUI2.nsh"
!include "LogicLib.nsh"
!include "WinVer.nsh"
!include "x64.nsh"
!include "FileFunc.nsh"
!include "WinMessages.nsh"

; Additional includes for process management
!include "nsDialogs.nsh"

; General settings
Name "${APPNAME}"
OutFile "QU-Scheduler-Setup.exe"
Unicode True
RequestExecutionLevel admin ; Request admin rights on Vista+
InstallDir "$PROGRAMFILES64\${COMPANYNAME}\${APPNAME}"
InstallDirRegKey HKLM "${REGKEY}" "InstallLocation"

; Variables for enhanced features
Var PreviousInstallPath
Var ProcessRunning

; Interface Settings
!define MUI_ABORTWARNING
!define MUI_FINISHPAGE_RUN
!define MUI_FINISHPAGE_RUN_FUNCTION "LaunchApplication"
!define MUI_FINISHPAGE_RUN_TEXT "Launch ${APPNAME} now"
!define MUI_FINISHPAGE_RUN_CHECKED
; Note: Custom graphics can be added by placing files in assets\icons\
; For now, using default NSIS graphics for compatibility

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"

; Version Information
VIProductVersion "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey "ProductName" "${APPNAME}"
VIAddVersionKey "CompanyName" "${COMPANYNAME}"
VIAddVersionKey "LegalCopyright" "Copyright © 2025 ${COMPANYNAME}. All rights reserved."
VIAddVersionKey "FileDescription" "${DESCRIPTION}"
VIAddVersionKey "FileVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey "ProductVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey "InternalName" "qu-scheduler"
VIAddVersionKey "OriginalFilename" "QU-Scheduler-Setup.exe"

; Installation types
InstType "Full Installation (Recommended)"
InstType "Minimal Installation"
InstType "Custom"

; Sections
Section "!${APPNAME} (Required)" SecCore
    SectionIn RO ; Read-only, always installed

    ; Set output path to the installation directory
    SetOutPath $INSTDIR

    ; Copy application files (these will be the packaged Electron app files)
    File /r "..\out\QU Scheduler-win32-x64\*.*"

    ; Copy enhanced icon file for shortcuts
    File "..\assets\icons\icon.ico"

    ; Store installation folder
    WriteRegStr HKLM "${REGKEY}" "InstallLocation" $INSTDIR

    ; Create uninstaller
    WriteUninstaller "$INSTDIR\Uninstall.exe"

    ; Add uninstall information to Add/Remove Programs
    WriteRegStr HKLM "${UNINSTKEY}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "${UNINSTKEY}" "UninstallString" "$\"$INSTDIR\Uninstall.exe$\""
    WriteRegStr HKLM "${UNINSTKEY}" "QuietUninstallString" "$\"$INSTDIR\Uninstall.exe$\" /S"
    WriteRegStr HKLM "${UNINSTKEY}" "InstallLocation" "$INSTDIR"
    WriteRegStr HKLM "${UNINSTKEY}" "DisplayIcon" "$INSTDIR\icon.ico"
    WriteRegStr HKLM "${UNINSTKEY}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "${UNINSTKEY}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "${UNINSTKEY}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "${UNINSTKEY}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "${UNINSTKEY}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "${UNINSTKEY}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "${UNINSTKEY}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "${UNINSTKEY}" "NoModify" 1
    WriteRegDWORD HKLM "${UNINSTKEY}" "NoRepair" 1
    WriteRegDWORD HKLM "${UNINSTKEY}" "EstimatedSize" ${INSTALLSIZE}
SectionEnd

Section "Desktop Shortcut" SecDesktop
    SectionIn 1 3 ; Included in Full and Custom installations
    ; Use executable icon first, fallback to separate icon file
    CreateShortcut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\qu-scheduler.exe" "" "$INSTDIR\qu-scheduler.exe" 0
SectionEnd

Section "Start Menu Shortcuts" SecStartMenu
    SectionIn 1 3 ; Included in Full and Custom installations
    CreateDirectory "$SMPROGRAMS\${COMPANYNAME}"
    ; Use executable icon first, fallback to separate icon file
    CreateShortcut "$SMPROGRAMS\${COMPANYNAME}\${APPNAME}.lnk" "$INSTDIR\qu-scheduler.exe" "" "$INSTDIR\qu-scheduler.exe" 0
    CreateShortcut "$SMPROGRAMS\${COMPANYNAME}\Uninstall ${APPNAME}.lnk" "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0
SectionEnd

Section "Quick Launch Shortcut" SecQuickLaunch
    SectionIn 3 ; Only in Custom installation (optional)
    ; Use executable icon first, fallback to separate icon file
    CreateShortcut "$QUICKLAUNCH\${APPNAME}.lnk" "$INSTDIR\qu-scheduler.exe" "" "$INSTDIR\qu-scheduler.exe" 0
SectionEnd



; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecCore} "Core application files (required)"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} "Create a shortcut on the desktop"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} "Create shortcuts in the Start Menu"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecQuickLaunch} "Create a shortcut in the Quick Launch toolbar"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstaller section
Section "Uninstall"
    ; Close running application before uninstalling
    Call un.CloseApplication

    ; Remove registry keys
    DeleteRegKey HKLM "${UNINSTKEY}"
    DeleteRegKey HKLM "${REGKEY}"

    ; Remove files and uninstaller
    Delete "$INSTDIR\Uninstall.exe"
    Delete "$INSTDIR\icon.ico"
    RMDir /r "$INSTDIR"

    ; Remove shortcuts
    Delete "$DESKTOP\${APPNAME}.lnk"
    Delete "$QUICKLAUNCH\${APPNAME}.lnk"
    RMDir /r "$SMPROGRAMS\${COMPANYNAME}"

    ; Remove installation directory if empty
    RMDir "$INSTDIR"
SectionEnd

; Functions
Function .onInit
    ; Check if installer is already running
    System::Call 'kernel32::CreateMutex(i 0, i 0, t "QUSchedulerInstaller") i .r1 ?e'
    Pop $R0
    StrCmp $R0 0 +3
        MessageBox MB_OK|MB_ICONEXCLAMATION "The installer is already running."
        Abort

    ; Check Windows version
    ${IfNot} ${AtLeastWin7}
        MessageBox MB_OK|MB_ICONSTOP "${APPNAME} requires Windows 7 or later."
        Abort
    ${EndIf}

    ; Check if 64-bit system
    ${IfNot} ${RunningX64}
        MessageBox MB_OK|MB_ICONSTOP "${APPNAME} requires a 64-bit version of Windows."
        Abort
    ${EndIf}

    ; Check for running processes
    Call CheckRunningProcesses

    ; Check for previous installation
    Call CheckPreviousInstallation
FunctionEnd

Function un.onInit
    MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 "Are you sure you want to completely remove ${APPNAME} and all of its components?" IDYES +2
    Abort
FunctionEnd

; Enhanced Functions for Process Detection and Management

; Function to check for running QU Scheduler processes
Function CheckRunningProcesses
    StrCpy $ProcessRunning "false"

    CheckProcess:
    Push "${EXENAME}"
    Call FindProcess
    Pop $R0

    ${If} $R0 == 0
        StrCpy $ProcessRunning "true"
        MessageBox MB_ICONEXCLAMATION|MB_YESNO "${APPNAME} is currently running and must be closed before installation can proceed.$\r$\n$\r$\nClick 'Yes' to close the application and continue, or 'No' to abort." IDYES CloseApp IDNO AbortInstall

        CloseApp:
            Call CloseApplicationGracefully
            Sleep 2000
            Call ForceCloseApplication
            Sleep 1000
            Goto CheckProcess

        AbortInstall:
            Abort
    ${EndIf}
FunctionEnd

; Function to gracefully close the application
Function CloseApplicationGracefully
    Push "${EXENAME}"
    Call CloseProcess
    Pop $R0

    ${If} $R0 == 0
        DetailPrint "Successfully requested ${APPNAME} to close"
    ${Else}
        DetailPrint "Failed to close ${APPNAME} gracefully"
    ${EndIf}
FunctionEnd

; Function to force close the application
Function ForceCloseApplication
    Push "${EXENAME}"
    Call KillProcess
    Pop $R0

    ${If} $R0 == 0
        DetailPrint "Successfully force-closed ${APPNAME}"
    ${Else}
        DetailPrint "Failed to force-close ${APPNAME}"
    ${EndIf}
FunctionEnd

; Function to check for previous installation
Function CheckPreviousInstallation
    ; Check registry for existing installation
    ReadRegStr $PreviousInstallPath HKLM "${UNINSTKEY}" "InstallLocation"

    ${If} $PreviousInstallPath != ""
        ; Check if the installation directory actually exists
        ${If} ${FileExists} "$PreviousInstallPath\${EXENAME}"
            MessageBox MB_ICONQUESTION|MB_YESNO "A previous version of ${APPNAME} is already installed.$\r$\n$\r$\nDo you want to uninstall it first? (Recommended)" IDYES UninstallPrevious IDNO ContinueInstall

            UninstallPrevious:
                Call UninstallPreviousVersion
                Goto ContinueInstall
        ${EndIf}
    ${EndIf}

    ContinueInstall:
FunctionEnd

; Function to uninstall previous version
Function UninstallPreviousVersion
    DetailPrint "Uninstalling previous version of ${APPNAME}..."

    ; Close any running instances first
    Call CloseApplicationGracefully
    Sleep 2000
    Call ForceCloseApplication
    Sleep 1000

    ; Get the uninstaller path
    ReadRegStr $R0 HKLM "${UNINSTKEY}" "UninstallString"

    ${If} $R0 != ""
        ; Extract the uninstaller path (remove quotes if present)
        ${GetParameters} $R1
        ${GetOptions} $R0 '"' $R2
        ${If} $R2 != ""
            StrCpy $R0 $R2
        ${EndIf}

        ; Run the uninstaller silently
        ExecWait '"$R0" /S' $R1

        ${If} $R1 == 0
            DetailPrint "Previous version uninstalled successfully"
        ${Else}
            DetailPrint "Warning: Previous version uninstallation may have failed"
        ${EndIf}

        ; Clean up any remaining registry entries
        DeleteRegKey HKLM "${UNINSTKEY}"
        DeleteRegKey HKLM "${REGKEY}"

        ; Wait a moment for cleanup to complete
        Sleep 2000
    ${EndIf}
FunctionEnd

; Function to launch the application after installation
Function LaunchApplication
    ; Launch the application with normal user privileges (not elevated)
    Exec '"$INSTDIR\${EXENAME}"'
FunctionEnd

; Installer versions of process management functions
Function FindProcess
    Exch $0 ; Process name
    Push $1
    Push $2

    ; Use simple tasklist and search for the process name in output
    ; This is more reliable than complex filtering
    nsExec::ExecToStack 'tasklist | findstr /I "$0"'
    Pop $1 ; Return code
    Pop $2 ; Output

    ; findstr returns 0 if string found, 1 if not found
    ${If} $1 == 0
        StrCpy $0 "0" ; Process found
    ${Else}
        StrCpy $0 "1" ; Process not found
    ${EndIf}

    Pop $2
    Pop $1
    Exch $0
FunctionEnd

Function CloseProcess
    Exch $0 ; Process name
    Push $1

    ; Use taskkill to close process gracefully
    nsExec::ExecToStack 'taskkill /IM "$0" /T'
    Pop $1 ; Return code

    ${If} $1 == 0
        StrCpy $0 "0" ; Success
    ${Else}
        StrCpy $0 "1" ; Failed
    ${EndIf}

    Pop $1
    Exch $0
FunctionEnd

Function KillProcess
    Exch $0 ; Process name
    Push $1

    ; Use taskkill to force close process
    nsExec::ExecToStack 'taskkill /IM "$0" /F /T'
    Pop $1 ; Return code

    ${If} $1 == 0
        StrCpy $0 "0" ; Success
    ${Else}
        StrCpy $0 "1" ; Failed
    ${EndIf}

    Pop $1
    Exch $0
FunctionEnd

; Uninstaller function to close application
Function un.CloseApplication
    ; Check if application is running and close it
    Push "${EXENAME}"
    Call un.FindProcess
    Pop $R0

    ${If} $R0 == 0
        MessageBox MB_ICONINFORMATION|MB_OKCANCEL "${APPNAME} is currently running and will be closed before uninstallation." IDOK CloseForUninstall IDCANCEL AbortUninstall

        CloseForUninstall:
            Push "${EXENAME}"
            Call un.CloseProcess
            Pop $R0
            Sleep 2000

            ; Force close if still running
            Push "${EXENAME}"
            Call un.FindProcess
            Pop $R0
            ${If} $R0 == 0
                Push "${EXENAME}"
                Call un.KillProcess
                Pop $R0
                Sleep 1000
            ${EndIf}
            Goto ContinueUninstall

        AbortUninstall:
            Abort
    ${EndIf}

    ContinueUninstall:
FunctionEnd

; Uninstaller versions of process management functions
Function un.FindProcess
    Exch $0 ; Process name
    Push $1
    Push $2

    ; Use simple tasklist and search for the process name in output
    ; This is more reliable than complex filtering
    nsExec::ExecToStack 'tasklist | findstr /I "$0"'
    Pop $1 ; Return code
    Pop $2 ; Output

    ; findstr returns 0 if string found, 1 if not found
    ${If} $1 == 0
        StrCpy $0 "0" ; Process found
    ${Else}
        StrCpy $0 "1" ; Process not found
    ${EndIf}

    Pop $2
    Pop $1
    Exch $0
FunctionEnd

Function un.CloseProcess
    Exch $0 ; Process name
    Push $1

    ; Use taskkill to close process gracefully
    nsExec::ExecToStack 'taskkill /IM "$0" /T'
    Pop $1 ; Return code

    ${If} $1 == 0
        StrCpy $0 "0" ; Success
    ${Else}
        StrCpy $0 "1" ; Failed
    ${EndIf}

    Pop $1
    Exch $0
FunctionEnd

Function un.KillProcess
    Exch $0 ; Process name
    Push $1

    ; Use taskkill to force close process
    nsExec::ExecToStack 'taskkill /IM "$0" /F /T'
    Pop $1 ; Return code

    ${If} $1 == 0
        StrCpy $0 "0" ; Success
    ${Else}
        StrCpy $0 "1" ; Failed
    ${EndIf}

    Pop $1
    Exch $0
FunctionEnd
