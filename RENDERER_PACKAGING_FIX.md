# QU Scheduler Renderer Packaging Fix

## Problem Description

After building and installing the QU Scheduler application, it failed to start with the error:

```
Not allowed to load local resource: file:///C:/Program%20Files/Qatar%20University/QU%20Scheduler/resources/app.asar/.vite/renderer/main_window/index.html
```

## Root Cause Analysis

The issue was caused by **incorrect path resolution** in the Electron main process when loading the renderer files in the packaged application:

1. **Electron Forge with Vite** was not properly copying renderer files to the packaged application
2. **Path resolution** in `main.ts` was trying to load files from inside the asar file instead of external renderer directory
3. **Missing renderer files** in the correct location outside the asar file

## Solution Implementation

### 1. Fixed Path Resolution in Main Process

**File:** `src/main.ts`

**Before (Incorrect):**
```javascript
const rendererPath = path.join(__dirname, `../renderer/${MAIN_WINDOW_VITE_NAME}/index.html`);
```

**After (Correct):**
```javascript
const appPath = app.getAppPath(); // Gets the asar path
const appRoot = path.dirname(path.dirname(appPath)); // Go up from resources/app.asar to app root
const rendererPath = path.join(appRoot, 'renderer', MAIN_WINDOW_VITE_NAME, 'index.html');
```

**Explanation:**
- `__dirname` in packaged app points to inside asar: `/app/resources/app.asar/.vite/build/`
- `app.getAppPath()` returns: `/app/resources/app.asar`
- `path.dirname(path.dirname(appPath))` gives us: `/app/` (the app root)
- Final path: `/app/renderer/main_window/index.html` (outside asar)

### 2. Created Automated Renderer File Copy Script

**File:** `scripts/fix-renderer-packaging.js`

This script automatically copies the built renderer files from `dist/` to the correct location in the packaged app:
- **Source:** `dist/` (Vite build output)
- **Destination:** `out/QU Scheduler-win32-x64/renderer/main_window/`

### 3. Updated Build Process

**File:** `package.json`

**Before:**
```json
"package": "electron-forge package"
```

**After:**
```json
"package": "electron-forge package && node scripts/fix-renderer-packaging.js"
```

The fix script now runs automatically after every packaging operation.

### 4. Updated Forge Configuration

**File:** `forge.config.ts`

```javascript
[FuseV1Options.OnlyLoadAppFromAsar]: false,
```

This allows the application to load files from outside the asar file.

## Path Resolution Logic

### In Development:
```
MAIN_WINDOW_VITE_DEV_SERVER_URL → http://localhost:5173
```

### In Production (Packaged):
```
app.getAppPath() → /app/resources/app.asar
path.dirname(path.dirname(appPath)) → /app/
Final path → /app/renderer/main_window/index.html
```

## File Structure After Fix

```
QU Scheduler-win32-x64/
├── qu-scheduler.exe
├── resources/
│   └── app.asar (contains main process and preload scripts)
└── renderer/
    └── main_window/
        ├── index.html
        ├── assets/
        │   ├── index-*.js
        │   ├── index-*.css
        │   └── ...
        └── fonts/
```

## Verification

The fix can be verified by:

1. **File Existence Check:**
   ```bash
   Test-Path "out/QU Scheduler-win32-x64/renderer/main_window/index.html"
   # Should return: True
   ```

2. **Path Resolution Test:**
   ```javascript
   const appPath = '/app/resources/app.asar';
   const appRoot = path.dirname(path.dirname(appPath)); // '/app'
   const rendererPath = path.join(appRoot, 'renderer', 'main_window', 'index.html');
   // Result: '/app/renderer/main_window/index.html'
   ```

## Status

✅ **RESOLVED** - Application now loads correctly after installation

## Files Modified

1. `src/main.ts` - Fixed path resolution logic
2. `scripts/fix-renderer-packaging.js` - Created automated fix script
3. `package.json` - Updated build process
4. `forge.config.ts` - Updated fuse configuration

## Build Process

To build the application with the fix:

```bash
npm run package  # Automatically runs the fix script
npm run make:nsis  # Creates the installer
```

The final installer (`installer/QU-Scheduler-Setup.exe`) now contains the properly configured application that will start correctly after installation.
