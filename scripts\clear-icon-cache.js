#!/usr/bin/env node

/**
 * QU Scheduler Icon Cache Clearing Script
 * 
 * This script clears the Windows icon cache to ensure updated icons are displayed
 * immediately after installation or icon changes.
 */

const { execSync } = require('child_process');

console.log('🔄 QU Scheduler Icon Cache Clearing Script');
console.log('==========================================');

function clearIconCache() {
    try {
        console.log('\n🧹 Clearing Windows icon cache...');
        
        // Method 1: Use ie4uinit.exe (Windows 10/11)
        try {
            console.log('📋 Running ie4uinit.exe -show...');
            execSync('ie4uinit.exe -show', { stdio: 'inherit' });
            console.log('✅ ie4uinit.exe -show completed');
        } catch (error) {
            console.log('⚠️  ie4uinit.exe -show failed:', error.message);
        }
        
        // Method 2: Use ie4uinit.exe -ClearIconCache
        try {
            console.log('📋 Running ie4uinit.exe -ClearIconCache...');
            execSync('ie4uinit.exe -ClearIconCache', { stdio: 'inherit' });
            console.log('✅ ie4uinit.exe -ClearIconCache completed');
        } catch (error) {
            console.log('⚠️  ie4uinit.exe -ClearIconCache failed:', error.message);
        }
        
        // Method 3: Restart Windows Explorer (optional)
        console.log('\n🔄 Refreshing Windows Explorer...');
        try {
            execSync('taskkill /f /im explorer.exe', { stdio: 'pipe' });
            console.log('📋 Explorer.exe terminated');
            
            // Wait a moment
            setTimeout(() => {
                execSync('start explorer.exe', { stdio: 'pipe' });
                console.log('📋 Explorer.exe restarted');
            }, 2000);
        } catch (error) {
            console.log('⚠️  Explorer restart failed (this is often normal)');
        }
        
        console.log('\n✅ Icon cache clearing completed!');
        console.log('📋 Recommendations:');
        console.log('   1. Check desktop shortcut icon');
        console.log('   2. Check Start menu icon');
        console.log('   3. If icons still don\'t update, restart the computer');
        console.log('   4. Verify the executable has the embedded icon');
        
    } catch (error) {
        console.error('❌ Error clearing icon cache:', error.message);
        console.log('\n🔧 Manual steps:');
        console.log('   1. Run as administrator: ie4uinit.exe -show');
        console.log('   2. Run as administrator: ie4uinit.exe -ClearIconCache');
        console.log('   3. Restart Windows Explorer or reboot system');
        process.exit(1);
    }
}

// Run the cache clearing
clearIconCache();
