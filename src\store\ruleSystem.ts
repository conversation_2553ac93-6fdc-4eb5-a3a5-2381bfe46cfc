import { create } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';
import { Rule, RuleSystemSettings, defaultRules, RetryConfiguration } from '../types/rules';

// Define the state interface
interface RuleSystemState extends RuleSystemSettings {
  setRule: (ruleId: string, updates: Partial<Rule>) => void;
  setMaxSessionsPerTimeslot: (dayPeriod: string, maxSessions: number) => void;
  setMaxSessionsPerDay: (dayType: string, maxSessions: number) => void;
  setMaxPostgradLevelPerDay: (maxSections: number) => void;
  setMaxPostgradCoursePerDay: (maxSections: number) => void;
  setMaxPostgradCoursesPerDay: (maxCourses: number) => void;
  setMaxGap4thYear: (maxGap: number) => void;
  setMaxGap3rdYear: (maxGap: number) => void;
  setAssignLecturersInAutoScheduling: (assign: boolean) => void;
  setRetryConfiguration: (config: Partial<RetryConfiguration>) => void;
  addUserDefinedBreak: (dayPeriod: string) => void;
  removeUserDefinedBreak: (dayPeriod: string) => void;
  calculateDefaultMaxSessions: (totalSections: number, averageContactHours?: number) => void;
}

// Define the persist configuration
type RuleSystemPersist = {
  rules: Rule[];
  maxSessionsPerTimeslot: Record<string, number>;
  maxSessionsPerDay: Record<string, number>;
  userDefinedBreaks: string[];
  maxPostgradLevelPerDay: number;
  maxPostgradCoursePerDay: number;
  maxPostgradCoursesPerDay: number;
  maxGap4thYear: number;
  maxGap3rdYear: number;
  assignLecturersInAutoScheduling: boolean;
  retryConfiguration: RetryConfiguration;
};

const persistConfig: PersistOptions<RuleSystemState, RuleSystemPersist> = {
  name: 'rule-system-storage',
  partialize: (state) => ({
    rules: state.rules,
    maxSessionsPerTimeslot: state.maxSessionsPerTimeslot,
    maxSessionsPerDay: state.maxSessionsPerDay,
    userDefinedBreaks: state.userDefinedBreaks,
    maxPostgradLevelPerDay: state.maxPostgradLevelPerDay,
    maxPostgradCoursePerDay: state.maxPostgradCoursePerDay,
    maxPostgradCoursesPerDay: state.maxPostgradCoursesPerDay,
    maxGap4thYear: state.maxGap4thYear,
    maxGap3rdYear: state.maxGap3rdYear,
    assignLecturersInAutoScheduling: state.assignLecturersInAutoScheduling,
    retryConfiguration: state.retryConfiguration,
  }),
};

// Create the store with proper typing
export const useRuleSystemStore = create<RuleSystemState>()(
  persist(
    (set) => ({
      rules: defaultRules.map(rule => {
        // Fix for the postgrad-pattern rule to ensure it has the correct name and description
        if (rule.id === 'postgrad-pattern') {
          return {
            ...rule,
            name: 'Follow specific periods for postgraduate courses',
            description: 'Schedule postgraduate courses in periods 10-12 on regular days or 9-10 on long days',
            priority: 2
          };
        }
        return rule;
      }),
      maxSessionsPerTimeslot: {} as Record<string, number>,
      maxSessionsPerDay: {} as Record<string, number>,
      userDefinedBreaks: [] as string[],
      maxPostgradLevelPerDay: 1, // Default value: 1
      maxPostgradCoursePerDay: 1, // Default value: 1
      maxPostgradCoursesPerDay: 2, // Default value: 2 (to handle more than 5 courses)
      maxGap4thYear: 5, // Default value: 5 periods
      maxGap3rdYear: 5, // Default value: 5 periods
      assignLecturersInAutoScheduling: true, // Default value: true
      retryConfiguration: {
        enabled: true,
        maxRetries: 5,
        minSuccessRate: 0.85,
        timeoutMs: 300000, // 5 minutes
        enableCPSRefinement: true // Enable CPS refinement by default
      },

      setRule: (ruleId: string, updates: Partial<Rule>) =>
        set((state) => ({
          rules: state.rules.map((rule: Rule) =>
            rule.id === ruleId ? { ...rule, ...updates } : rule
          ),
        })),

      setMaxSessionsPerTimeslot: (dayPeriod: string, maxSessions: number) =>
        set((state) => ({
          maxSessionsPerTimeslot: {
            ...state.maxSessionsPerTimeslot,
            [dayPeriod]: maxSessions,
          },
        })),

      setMaxSessionsPerDay: (dayType: string, maxSessions: number) =>
        set((state) => ({
          maxSessionsPerDay: {
            ...state.maxSessionsPerDay,
            [dayType]: maxSessions,
          },
        })),

      addUserDefinedBreak: (dayPeriod: string) =>
        set((state) => ({
          userDefinedBreaks: [...state.userDefinedBreaks, dayPeriod],
        })),

      removeUserDefinedBreak: (dayPeriod: string) =>
        set((state) => ({
          userDefinedBreaks: state.userDefinedBreaks.filter((dp: string) => dp !== dayPeriod),
        })),

      setMaxPostgradLevelPerDay: (maxSections: number) =>
        set(() => ({
          maxPostgradLevelPerDay: maxSections,
        })),

      setMaxPostgradCoursePerDay: (maxSections: number) =>
        set(() => ({
          maxPostgradCoursePerDay: maxSections,
        })),

      setMaxPostgradCoursesPerDay: (maxCourses: number) =>
        set(() => ({
          maxPostgradCoursesPerDay: maxCourses,
        })),

      setMaxGap4thYear: (maxGap: number) =>
        set(() => ({
          maxGap4thYear: maxGap,
        })),

      setMaxGap3rdYear: (maxGap: number) =>
        set(() => ({
          maxGap3rdYear: maxGap,
        })),

      setAssignLecturersInAutoScheduling: (assign: boolean) =>
        set(() => ({
          assignLecturersInAutoScheduling: assign,
        })),

      setRetryConfiguration: (config: Partial<RetryConfiguration>) =>
        set((state) => ({
          retryConfiguration: {
            ...state.retryConfiguration,
            ...config,
          },
        })),

      calculateDefaultMaxSessions: (totalSections: number, averageContactHours = 3) =>
        set((state) => {
          // Validate input parameters
          if (totalSections <= 0) {
            totalSections = 10; // Use a reasonable default
          }

          if (averageContactHours <= 0) {
            averageContactHours = 3; // Use a reasonable default
          }

          // Calculate default max sessions per timeslot according to the documentation
          // Using Math.round as requested by the user

          // For regular days (Su, Tu, Th):
          // Periods 1-6: 5% of total sections
          const regularDaysPeriods1to6 = Math.round(totalSections * 0.05);

          // Periods 7-11: 6% of total sections
          const regularDaysPeriods7to11 = Math.round(totalSections * 0.06);

          // For long days (Mo, We):
          // Periods 1-4: 5% of total sections
          const longDaysPeriods1to4 = Math.round(totalSections * 0.05);

          // Periods 7-9: 6.67% of total sections
          const longDaysPeriods7to9 = Math.round(totalSections * 0.0667);

          // Calculate max sessions per day according to the documentation
          // For regular days: (totalSections * averageContactHours) / 5
          const regularDaysMaxPerDay = Math.round((totalSections * averageContactHours) / 5);

          // For long days: (totalSections * averageContactHours) / 1.5 / 5
          // Dividing by 5 for the 5 days of the week, consistent with regular days calculation
          const longDaysMaxPerDay = Math.round((totalSections * averageContactHours) / 1.5 / 5);

          const newMaxSessionsPerTimeslot: Record<string, number> = {};

          // Regular days (Su, Tu, Th)
          ['Sun', 'Tue', 'Thu'].forEach(day => {
            // Periods 1-6: 5% of total sections
            for (let period = 1; period <= 6; period++) {
              newMaxSessionsPerTimeslot[`${day}-${period}`] = regularDaysPeriods1to6;
            }
            // Periods 7-11: 6% of total sections
            for (let period = 7; period <= 11; period++) {
              newMaxSessionsPerTimeslot[`${day}-${period}`] = regularDaysPeriods7to11;
            }
            // Period 12 set to 0 (blocked)
            newMaxSessionsPerTimeslot[`${day}-12`] = 0;
          });

          // Long days (Mo, We)
          ['Mon', 'Wed'].forEach(day => {
            // Periods 1-4: 5% of total sections (morning)
            for (let period = 1; period <= 4; period++) {
              newMaxSessionsPerTimeslot[`${day}-${period}`] = longDaysPeriods1to4;
            }
            // Periods 5-6: 0 (blocked/break time)
            newMaxSessionsPerTimeslot[`${day}-5`] = 0;
            newMaxSessionsPerTimeslot[`${day}-6`] = 0;

            // Periods 7-9: 6.67% of total sections (evening)
            for (let period = 7; period <= 9; period++) {
              newMaxSessionsPerTimeslot[`${day}-${period}`] = longDaysPeriods7to9;
            }
            // Period 10 set to 0 (blocked)
            newMaxSessionsPerTimeslot[`${day}-10`] = 0;
          });

          const newMaxSessionsPerDay: Record<string, number> = {
            'regular': regularDaysMaxPerDay,
            'long': longDaysMaxPerDay
          };

          return {
            maxSessionsPerTimeslot: {
              ...state.maxSessionsPerTimeslot,
              ...newMaxSessionsPerTimeslot
            },
            maxSessionsPerDay: {
              ...state.maxSessionsPerDay,
              ...newMaxSessionsPerDay
            }
          };
        }),
    }),
    persistConfig
  )
);