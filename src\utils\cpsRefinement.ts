/**
 * CPS (Constraint Programming System) Refinement for Rule-Based Auto-Scheduling
 *
 * This module implements optimization-based CPS strategies that can improve existing timetables:
 * 1. Lecturer Swapping: Exchange lecturers between sessions when both are qualified
 * 2. Session Repositioning: Move sessions to better distribute lecturer workload
 * 3. Intelligent Rescheduling: Reschedule sessions that violate lecturer rules
 * 4. Constraint Relaxation Hierarchy: Temporarily relax lower-priority constraints for optimization
 *
 * Unlike the main scheduling system, CPS can modify existing schedules and use flexible
 * constraint handling to achieve better overall solutions through strategic optimization.
 */

import { AutoSession, AppSession, Section, convertToAppSession, convertAutoSessionsToSessions } from './autoScheduling';
import { useRuleSystemStore } from '../store/ruleSystem';
import { Lecturer } from '../types/models';
import { validateTimeslotForSection, Session, getAcademicLevel } from './ruleValidation';

/**
 * Interface for CPS refinement results
 */
export interface CPSRefinementResult {
  improved: boolean;
  result: {
    success: boolean;
    scheduledSections: string[];
    unscheduledSections: string[];
    sessions: AutoSession[];
    appSessions: AppSession[];
    successRate: number;
    strategy: string;
    duration: number;
  };
  report: CPSRefinementReport;
}

/**
 * Interface for CPS optimization strategies
 */
export interface CPSOptimizationStrategy {
  name: string;
  description: string;
  priority: number;
  execute: (
    sessions: AutoSession[],
    sections: Section[],
    lecturers: Lecturer[],
    constraints: CPSConstraintContext
  ) => CPSOptimizationResult;
}

/**
 * Interface for CPS constraint context with relaxation capabilities
 */
export interface CPSConstraintContext {
  hardConstraints: string[]; // Rules that cannot be violated
  softConstraints: string[]; // Rules that can be temporarily relaxed
  constraintPriorities: Map<string, number>; // Priority levels for constraint relaxation
  maxViolations: Map<string, number>; // Maximum allowed violations per constraint type
}

/**
 * Interface for CPS optimization results
 */
export interface CPSOptimizationResult {
  improved: boolean;
  modifiedSessions: AutoSession[];
  swappedLecturers: Array<{
    sessionId1: string;
    sessionId2: string;
    lecturer1: string;
    lecturer2: string;
    reason: string;
  }>;
  repositionedSessions: Array<{
    sessionId: string;
    fromDay: string;
    fromPeriod: number;
    toDay: string;
    toPeriod: number;
    reason: string;
  }>;
  rescheduledSessions: Array<{
    sessionId: string;
    originalTimeslot: string;
    newTimeslot: string;
    violationsResolved: string[];
  }>;
  constraintViolationsResolved: number;
  constraintViolationsIntroduced: number;
  optimizationScore: number;
}

/**
 * Interface for CPS refinement reporting
 */
export interface CPSRefinementReport {
  enabled: boolean;
  triggered: boolean;
  initialViolations: number;
  finalViolations: number;
  improvementAchieved: boolean;
  refinementStrategies: string[];
  processingTime: number;
  constraintViolationDetails: {
    timeslotPercentage: number;
    lecturerOverload: number;
    patternViolations: number;
    conflictViolations: number;
  };
  lecturerAssignmentStats: {
    sectionsScheduled: number;
    sectionsWithLecturers: number;
    sectionsWithoutLecturers: number;
    lecturerAssignmentRate: number;
  };
  optimizationResults: {
    strategiesApplied: string[];
    lecturerSwaps: number;
    sessionRepositions: number;
    intelligentReschedules: number;
    constraintRelaxations: number;
    overallOptimizationScore: number;
  };
}

/**
 * Main CPS refinement function
 * Applies lecturer-independent scheduling with academic pattern validation
 */
export function applyCPSRefinement(
  currentResult: {
    success: boolean;
    scheduledSections: string[];
    unscheduledSections: string[];
    sessions: AutoSession[];
    appSessions: AppSession[];
    successRate: number;
    strategy: string;
    duration: number;
  },
  sections: Section[],
  lecturers: Lecturer[],
  _targetSuccessRate: number
): CPSRefinementResult {
  // CPS refinement logging reduced to minimize console output

  const startTime = performance.now();

  // Initialize constraint context for optimization
  const constraintContext = createConstraintContext();

  // Initialize optimization tracking
  let totalOptimizationScore = 0;
  const strategiesApplied: string[] = [];
  let lecturerSwaps = 0;
  let sessionRepositions = 0;
  let intelligentReschedules = 0;
  let constraintRelaxations = 0;

  // Start with current sessions
  let optimizedSessions = [...currentResult.sessions];
  const newlyScheduledSections: string[] = [];

  // Get unscheduled sections for potential scheduling
  const unscheduledSections = sections.filter(section =>
    currentResult.unscheduledSections.includes(section.id)
  );

  // CPS optimization strategy logging removed to reduce console output

  // Strategy 1: Lecturer Swapping
  const swapResult = applyLecturerSwapping(optimizedSessions, sections, lecturers, constraintContext);
  if (swapResult.improved) {
    optimizedSessions = swapResult.modifiedSessions;
    lecturerSwaps = swapResult.swappedLecturers.length;
    totalOptimizationScore += swapResult.optimizationScore;
    strategiesApplied.push('lecturer-swapping');
  }

  // Strategy 2: Session Repositioning
  const repositionResult = applySessionRepositioning(optimizedSessions, sections, lecturers, constraintContext);
  if (repositionResult.improved) {
    optimizedSessions = repositionResult.modifiedSessions;
    sessionRepositions = repositionResult.repositionedSessions.length;
    totalOptimizationScore += repositionResult.optimizationScore;
    strategiesApplied.push('session-repositioning');
  }

  // Strategy 3: Intelligent Rescheduling for Unscheduled Sections (with lecturer-independent fallback)
  const rescheduleResult = applyIntelligentRescheduling(
    optimizedSessions,
    unscheduledSections,
    sections,
    lecturers,
    constraintContext
  );
  if (rescheduleResult.improved) {
    optimizedSessions = rescheduleResult.modifiedSessions;
    intelligentReschedules = rescheduleResult.rescheduledSessions.length;
    // Extract section IDs from the newly scheduled sessions
    const newSectionIds = rescheduleResult.modifiedSessions
      .filter(session => session.isAutoGenerated && session.id.includes('temp-'))
      .map(session => session.sectionId)
      .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates
    newlyScheduledSections.push(...newSectionIds);
    totalOptimizationScore += rescheduleResult.optimizationScore;
    strategiesApplied.push('intelligent-rescheduling');
  }

  // Strategy 3b: Lecturer-Independent Scheduling (fallback for remaining unscheduled sections)
  const remainingUnscheduled = unscheduledSections.filter(s => !newlyScheduledSections.includes(s.id));

  if (remainingUnscheduled.length > 0) {
    const lecturerIndependentResult = applyLecturerIndependentScheduling(
      optimizedSessions,
      remainingUnscheduled,
      sections,
      lecturers
    );
    if (lecturerIndependentResult.improved) {
      optimizedSessions = lecturerIndependentResult.modifiedSessions;
      // Extract section IDs from the newly scheduled sessions
      const newSectionIds = lecturerIndependentResult.modifiedSessions
        .filter(session => session.isAutoGenerated && session.id.includes('temp-no-lecturer-'))
        .map(session => session.sectionId)
        .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates
      newlyScheduledSections.push(...newSectionIds);
      totalOptimizationScore += lecturerIndependentResult.optimizationScore;
      strategiesApplied.push('lecturer-independent');
    }
  }

  // Strategy 4: Constraint Relaxation for Additional Scheduling
  const relaxationResult = applyConstraintRelaxation(
    optimizedSessions,
    unscheduledSections.filter(s => !newlyScheduledSections.includes(s.id)),
    sections,
    lecturers,
    constraintContext
  );
  if (relaxationResult.improved) {
    optimizedSessions = relaxationResult.modifiedSessions;
    constraintRelaxations = relaxationResult.constraintViolationsIntroduced;
    // Extract section IDs from the newly scheduled sessions
    const newSectionIds = relaxationResult.modifiedSessions
      .filter(session => session.isAutoGenerated && session.id.includes('temp-relaxed-'))
      .map(session => session.sectionId)
      .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates
    newlyScheduledSections.push(...newSectionIds);
    totalOptimizationScore += relaxationResult.optimizationScore;
    strategiesApplied.push('constraint-relaxation');
  }

  // Convert optimized sessions back to app sessions
  const optimizedAppSessions = optimizedSessions.map(session => convertToAppSession(session));

  // Calculate final metrics
  const finalScheduledSections = [...currentResult.scheduledSections, ...newlyScheduledSections];
  const finalUnscheduledSections = currentResult.unscheduledSections.filter(
    sectionId => !newlyScheduledSections.includes(sectionId)
  );
  const finalSuccessRate = finalScheduledSections.length / sections.length;
  const improvementAchieved = newlyScheduledSections.length > 0 || totalOptimizationScore > 0;

  const endTime = performance.now();

  // Create comprehensive CPS optimization report
  const report: CPSRefinementReport = {
    enabled: true,
    triggered: true,
    initialViolations: currentResult.unscheduledSections.length,
    finalViolations: finalUnscheduledSections.length,
    improvementAchieved,
    refinementStrategies: strategiesApplied,
    processingTime: endTime - startTime,
    constraintViolationDetails: {
      timeslotPercentage: 0, // CPS optimization maintains timeslot compliance
      lecturerOverload: 0,   // Optimization improves lecturer distribution
      patternViolations: 0,  // Academic patterns are preserved
      conflictViolations: 0  // No scheduling conflicts introduced
    },
    lecturerAssignmentStats: {
      sectionsScheduled: newlyScheduledSections.length,
      sectionsWithLecturers: 0, // Count sections with actual lecturer assignments
      sectionsWithoutLecturers: newlyScheduledSections.length, // Most CPS sections are scheduled without lecturers
      lecturerAssignmentRate: 0 // CPS focuses on scheduling completion, not lecturer assignment
    },
    optimizationResults: {
      strategiesApplied,
      lecturerSwaps,
      sessionRepositions,
      intelligentReschedules,
      constraintRelaxations,
      overallOptimizationScore: totalOptimizationScore
    }
  };

  // CPS optimization completion logging removed to reduce console output

  if (improvementAchieved) {
    return {
      improved: true,
      result: {
        ...currentResult,
        sessions: optimizedSessions,
        appSessions: optimizedAppSessions,
        scheduledSections: finalScheduledSections,
        unscheduledSections: finalUnscheduledSections,
        successRate: finalSuccessRate,
        strategy: `${currentResult.strategy}+CPS-Optimization`
      },
      report
    };
  }

  return {
    improved: false,
    result: currentResult,
    report
  };
}

/**
 * Calculate which sections are scheduled based on sessions
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function calculateScheduledSections(sessions: AutoSession[], sections: Section[]): string[] {
  const scheduledSectionIds = new Set<string>();

  // Group sessions by section
  const sessionsBySection = new Map<string, AutoSession[]>();
  sessions.forEach(session => {
    if (!sessionsBySection.has(session.sectionId)) {
      sessionsBySection.set(session.sectionId, []);
    }
    const sectionSessions = sessionsBySection.get(session.sectionId);
    if (sectionSessions) {
      sectionSessions.push(session);
    }
  });

  // Check if each section has enough hours scheduled
  sections.forEach(section => {
    const sectionSessions = sessionsBySection.get(section.id) || [];

    // Calculate total scheduled hours for this section
    let totalScheduledHours = 0;
    sectionSessions.forEach(session => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      const sessionHours = isLongDay ? 1.5 : 1.0;
      totalScheduledHours += sessionHours;
    });

    // Consider section scheduled if it has at least the required contact hours
    // Use a small threshold to account for floating point precision
    if (totalScheduledHours >= section.contactHours - 0.1) {
      scheduledSectionIds.add(section.id);
    }
  });

  return Array.from(scheduledSectionIds);
}

/**
 * Convert AutoSessions to AppSessions
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function convertAutoSessionsToAppSessions(sessions: AutoSession[]): AppSession[] {
  return sessions.map(session => convertToAppSession(session));
}

/**
 * CPS CORE FUNCTIONS
 */

/**
 * Schedule sections with optional lecturer assignment using academic pattern validation
 * This is the enhanced CPS strategy that attempts lecturer assignment while maintaining scheduling completion rates
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function scheduleWithOptionalLecturerAssignment(
  unscheduledSections: Section[],
  existingSessions: AutoSession[],
  lecturers: Lecturer[]
): {
  improved: boolean;
  newSessions: AutoSession[];
  scheduledSectionIds: string[];
  lecturerAssignmentStats: {
    sectionsScheduled: number;
    sectionsWithLecturers: number;
    sectionsWithoutLecturers: number;
    lecturerAssignmentRate: number;
  };
} {
  // CPS section scheduling logging removed to reduce console output

  const newSessions: AutoSession[] = [];
  const scheduledSectionIds: string[] = [];
  let sectionsWithLecturers = 0;
  let sectionsWithoutLecturers = 0;

  for (const section of unscheduledSections) {

    // Get appropriate scheduling patterns for this section
    const patterns = getSchedulingPatternsForSection(section);

    for (const pattern of patterns) {
      const patternSessions = trySchedulePatternWithLecturerAssignment(
        section,
        pattern,
        existingSessions,
        newSessions,
        lecturers
      );

      if (patternSessions.length > 0) {
        newSessions.push(...patternSessions);
        scheduledSectionIds.push(section.id);

        // Check if lecturer was assigned
        const hasLecturer = patternSessions.some((session: AutoSession) => session.lecturerId && session.lecturerId !== '');
        if (hasLecturer) {
          sectionsWithLecturers++;
        } else {
          sectionsWithoutLecturers++;
        }
        break; // Move to next section
      }
    }

    // Section scheduling logging removed to reduce console output
  }

  const sectionsScheduled = scheduledSectionIds.length;
  const lecturerAssignmentRate = sectionsScheduled > 0 ? (sectionsWithLecturers / sectionsScheduled) * 100 : 0;
  const improved = scheduledSectionIds.length > 0;

  // CPS result logging reduced to minimize console output

  return {
    improved,
    newSessions,
    scheduledSectionIds,
    lecturerAssignmentStats: {
      sectionsScheduled,
      sectionsWithLecturers,
      sectionsWithoutLecturers,
      lecturerAssignmentRate
    }
  };
}

/**
 * Scheduling pattern interface
 */
interface SchedulingPattern {
  name: string;
  days: string[];
  periodsPerDay: number[];
  totalHours: number;
  consecutivePeriods?: number; // For postgraduate courses that need consecutive periods
}

/**
 * Get appropriate scheduling patterns for a section
 */
function getSchedulingPatternsForSection(section: Section): SchedulingPattern[] {
  // Pattern generation logging removed to reduce console output

  const patterns: SchedulingPattern[] = [];

  // Get academic level from course code
  const academicLevel = getAcademicLevel(section.courseCode);
  const isPostgraduate = academicLevel.includes('diploma') ||
                         academicLevel.includes('masters') ||
                         academicLevel.includes('phd');

  switch (section.contactHours) {
    case 2:
      if (section.courseType === 'Theory' && !isPostgraduate) {
        // 2CH undergraduate: Two regular days at same time (periods 1-11)
        const availablePeriods = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];

        // Generate patterns for different day combinations
        const dayPairs = [
          ['Sun', 'Tue'], ['Sun', 'Thu'], ['Tue', 'Thu']
        ];

        dayPairs.forEach((dayPair) => {
          availablePeriods.forEach(period => {
            patterns.push({
              name: `2CH-Regular-${dayPair.join('-')}-P${period}`,
              days: dayPair,
              periodsPerDay: [period, period], // Same period on both days
              totalHours: 2
            });
          });
        });
      }
      break;

    case 3:
      if (section.courseType === 'Theory') {
        if (isPostgraduate) {
          // 3CH Postgraduate: One day, consecutive periods
          const allDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu'];

          allDays.forEach(day => {
            const isLongDay = ['Mon', 'Wed'].includes(day);

            if (isLongDay) {
              // Long days: 2 consecutive periods (1.5h each = 3h total)
              const longDayPeriods = [1, 2, 3, 4, 7, 8, 9];
              longDayPeriods.forEach(startPeriod => {
                const nextPeriod = startPeriod + 1;
                // Ensure consecutive periods and avoid blocked periods 5, 6
                if (nextPeriod <= 10 && nextPeriod !== 5 && nextPeriod !== 6 &&
                    (startPeriod < 5 || startPeriod >= 7)) {
                  patterns.push({
                    name: `3CH-Postgrad-${day}-Consecutive-P${startPeriod}`,
                    days: [day],
                    periodsPerDay: [startPeriod], // Start period, consecutive logic handled in validation
                    totalHours: 3,
                    consecutivePeriods: 2 // Metadata for validation
                  });
                }
              });
            } else {
              // Regular days: 3 consecutive periods (1h each = 3h total)
              const regularPeriods = [1, 2, 3, 4, 5, 6, 7, 8, 9];
              regularPeriods.forEach(startPeriod => {
                if (startPeriod + 2 <= 11) {
                  patterns.push({
                    name: `3CH-Postgrad-${day}-Consecutive-P${startPeriod}`,
                    days: [day],
                    periodsPerDay: [startPeriod], // Start period, consecutive logic handled in validation
                    totalHours: 3,
                    consecutivePeriods: 3 // Metadata for validation
                  });
                }
              });
            }
          });
        } else {
          // 3CH Undergraduate: Either 3 regular days at same time OR 2 long days at same time
          const availablePeriods = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];

          // Pattern 1: 3 regular days at same time
          availablePeriods.forEach(period => {
            patterns.push({
              name: `3CH-Undergrad-3Regular-P${period}`,
              days: ['Sun', 'Tue', 'Thu'],
              periodsPerDay: [period, period, period], // Same time on all days
              totalHours: 3
            });
          });

          // Pattern 2: 2 long days at same time
          const longDayPeriods = [1, 2, 3, 4, 7, 8, 9, 10];
          longDayPeriods.forEach(period => {
            patterns.push({
              name: `3CH-Undergrad-2Long-P${period}`,
              days: ['Mon', 'Wed'],
              periodsPerDay: [period, period], // Same time on both long days
              totalHours: 3
            });
          });
        }
      }
      break;

    case 5:
      if (section.courseType === 'Theory' && !isPostgraduate) {
        // 5CH undergraduate: Must be distributed across at least 3 days
        // Generate patterns that use at least 3 days
        const fiveCHPatterns = [
          {
            name: '5CH-3Regular-2Long',
            days: ['Sun', 'Tue', 'Thu', 'Mon', 'Wed'],
            periodsPerDay: [8, 8, 8, 9, 9]
          },
          {
            name: '5CH-5Regular-Days',
            days: ['Sun', 'Tue', 'Thu', 'Sun', 'Tue'],
            periodsPerDay: [8, 8, 8, 9, 9]
          },
          {
            name: '5CH-Distributed-Pattern',
            days: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu'],
            periodsPerDay: [8, 9, 8, 9, 8]
          }
        ];

        fiveCHPatterns.forEach(pattern => {
          patterns.push({
            ...pattern,
            totalHours: 5
          });
        });
      }
      break;

    default:
      // Fallback pattern for other contact hours
      patterns.push({
        name: `${section.contactHours}CH-Flexible`,
        days: ['Sun', 'Tue', 'Thu'],
        periodsPerDay: Array(section.contactHours).fill(8),
        totalHours: section.contactHours
      });
  }

  // Pattern generation logging removed to reduce console output

  return patterns;
}

/**
 * Validate a complete scheduling pattern against academic pattern rules
 */
function validateAcademicPattern(
  section: Section,
  pattern: SchedulingPattern,
  academicLevel: string
): { valid: boolean; violatedRules: string[] } {
  const violatedRules: string[] = [];
  const isPostgraduate = academicLevel.includes('diploma') ||
                         academicLevel.includes('masters') ||
                         academicLevel.includes('phd');

  // Get rule system state to check if pattern rules are enabled
  const ruleSystemState = useRuleSystemStore.getState();
  const { rules } = ruleSystemState;

  // Check if pattern rules are enabled
  const undergradPatternRules = {
    '2ch': rules.find(r => r.id === 'undergrad-2ch-pattern')?.enabled || false,
    '3ch': rules.find(r => r.id === 'undergrad-3ch-pattern')?.enabled || false,
    '5ch': rules.find(r => r.id === 'undergrad-5ch-pattern')?.enabled || false
  };
  const postgradPatternRule = rules.find(r => r.id === 'postgrad-pattern')?.enabled || false;

  if (section.courseType === 'Theory') {
    switch (section.contactHours) {
      case 2:
        if (!isPostgraduate && undergradPatternRules['2ch']) {
          // 2CH undergraduate: must be on two regular days at the same time
          if (pattern.days.length !== 2) {
            violatedRules.push('2CH undergraduate courses must use exactly 2 days');
          }

          const hasLongDays = pattern.days.some(day => ['Mon', 'Wed'].includes(day));
          if (hasLongDays) {
            violatedRules.push('2CH undergraduate courses must use regular days only (Sun, Tue, Thu)');
          }

          // Check if all periods are the same (same time requirement)
          const uniquePeriods = new Set(pattern.periodsPerDay);
          if (uniquePeriods.size !== 1) {
            violatedRules.push('2CH undergraduate courses must be scheduled at the same time on different days');
          }
        }
        break;

      case 3:
        if (isPostgraduate && postgradPatternRule) {
          // 3CH postgraduate: must be consecutive on one day
          if (pattern.days.length !== 1) {
            violatedRules.push('3CH postgraduate courses must be scheduled on one day only');
          }

          const day = pattern.days[0];
          const isLongDay = ['Mon', 'Wed'].includes(day);
          const expectedPeriods = isLongDay ? 2 : 3;

          if (pattern.consecutivePeriods !== expectedPeriods) {
            violatedRules.push(`3CH postgraduate courses need ${expectedPeriods} consecutive periods on ${isLongDay ? 'long' : 'regular'} days`);
          }
        } else if (!isPostgraduate && undergradPatternRules['3ch']) {
          // 3CH undergraduate: either 3 regular days at same time OR 2 long days at same time
          const hasLongDays = pattern.days.some(day => ['Mon', 'Wed'].includes(day));
          const hasRegularDays = pattern.days.some(day => !['Mon', 'Wed'].includes(day));

          if (hasLongDays && hasRegularDays) {
            violatedRules.push('3CH undergraduate courses cannot mix long and regular days');
          }

          if (hasLongDays) {
            // Long day pattern: exactly 2 days
            if (pattern.days.length !== 2) {
              violatedRules.push('3CH undergraduate courses on long days must use exactly 2 days');
            }
          } else {
            // Regular day pattern: exactly 3 days
            if (pattern.days.length !== 3) {
              violatedRules.push('3CH undergraduate courses on regular days must use exactly 3 days');
            }
          }

          // Check same time requirement
          const uniquePeriods = new Set(pattern.periodsPerDay);
          if (uniquePeriods.size !== 1) {
            violatedRules.push('3CH undergraduate courses must be scheduled at the same time on different days');
          }
        }
        break;

      case 5:
        if (!isPostgraduate && undergradPatternRules['5ch']) {
          // 5CH undergraduate: must be distributed across at least 3 days
          const uniqueDays = new Set(pattern.days);
          if (uniqueDays.size < 3) {
            violatedRules.push('5CH undergraduate courses must be scheduled over at least 3 days');
          }
        }
        break;
    }
  }

  return {
    valid: violatedRules.length === 0,
    violatedRules
  };
}

/**
 * Helper function to get lecturer name from ID
 */
function _getLecturerName(lecturerId: string, lecturers: Lecturer[]): string {
  if (!lecturerId) return 'No lecturer';
  const lecturer = lecturers.find(l => l.id === lecturerId);
  return lecturer ? `${lecturer.firstName} ${lecturer.lastName || ''}`.trim() : 'Unknown lecturer';
}

/**
 * Find lecturers eligible to teach a specific section
 */
function findEligibleLecturers(section: Section, lecturers: Lecturer[]): Lecturer[] {
  return lecturers.filter(lecturer =>
    lecturer.coursesAbleToTeach &&
    lecturer.coursesAbleToTeach.includes(section.courseCode)
  );
}

/**
 * Select the best lecturer for CPS assignment using simplified scoring
 */
function selectBestLecturerForCPS(
  section: Section,
  patternSessions: AutoSession[],
  eligibleLecturers: Lecturer[],
  allExistingSessions: AutoSession[]
): Lecturer | null {
  if (eligibleLecturers.length === 0) return null;

  // Calculate current loads for all eligible lecturers
  const lecturerLoads = new Map<string, number>();
  const lecturerMaxLoads = new Map<string, number>();

  eligibleLecturers.forEach(lecturer => {
    // Calculate current load from existing sessions
    const lecturerSessions = allExistingSessions.filter(s => s.lecturerId === lecturer.id);
    let totalLoadHours = 0;
    lecturerSessions.forEach(session => {
      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      totalLoadHours += isLongDay ? 1.5 : 1.0;
    });

    lecturerLoads.set(lecturer.id, totalLoadHours);
    lecturerMaxLoads.set(lecturer.id, lecturer.maxYearLoad || 12);
  });

  // Score lecturers based on current load and constraints
  const lecturerScores = new Map<string, number>();

  for (const lecturer of eligibleLecturers) {
    const currentLoad = lecturerLoads.get(lecturer.id) || 0;
    const maxLoad = lecturerMaxLoads.get(lecturer.id) || 12;
    const loadPercentage = (currentLoad / maxLoad) * 100;

    // Base score inversely proportional to load percentage
    let score = 100 - loadPercentage;

    // Check if assigning this lecturer would violate any constraints
    const wouldViolateConstraints = checkLecturerConstraints(
      lecturer,
      patternSessions,
      allExistingSessions,
      section
    );

    if (wouldViolateConstraints) {
      score -= 1000; // Heavy penalty for constraint violations
    }

    lecturerScores.set(lecturer.id, score);
  }

  // Select the lecturer with the highest score
  let bestLecturer = null;
  let bestScore = Number.NEGATIVE_INFINITY;

  for (const lecturer of eligibleLecturers) {
    const score = lecturerScores.get(lecturer.id) || 0;
    if (score > bestScore) {
      bestScore = score;
      bestLecturer = lecturer;
    }
  }

  // Only return lecturer if score is positive (no major constraint violations)
  return bestScore > 0 ? bestLecturer : null;
}

/**
 * Check if assigning a lecturer to pattern sessions would violate constraints
 * Uses the comprehensive rule validation system to ensure all enabled lecturer rules are respected
 */
function checkLecturerConstraints(
  lecturer: Lecturer,
  patternSessions: AutoSession[],
  allExistingSessions: AutoSession[],
  section: Section
): boolean {
  // Convert AutoSessions to Sessions for rule validation
  const existingSchedule: Session[] = convertAutoSessionsToSessions(allExistingSessions);

  // Check each session in the pattern against all enabled lecturer rules
  for (const session of patternSessions) {
    // Create a temporary session with the lecturer assigned
    const sessionWithLecturer = { ...session, lecturerId: lecturer.id };

    // Convert to Session format for validation
    const sessionForValidation: Session = {
      id: sessionWithLecturer.id,
      sectionId: sessionWithLecturer.sectionId,
      courseCode: sessionWithLecturer.courseCode,
      courseType: sessionWithLecturer.courseType,
      academicLevel: sessionWithLecturer.academicLevel,
      gender: sessionWithLecturer.gender,
      lecturerId: lecturer.id,
      day: sessionWithLecturer.day,
      startPeriod: sessionWithLecturer.period,
      endPeriod: sessionWithLecturer.period,
      period: sessionWithLecturer.period,
      isAutoGenerated: true
    };

    // Add this session to the schedule for validation
    const scheduleWithNewSession = [...existingSchedule, sessionForValidation];
    const existingSectionSessions = scheduleWithNewSession.filter(s => s.sectionId === section.id);

    // Use the comprehensive rule validation system
    const validation = validateTimeslotForSection(
      section.id,
      section.courseCode,
      section.courseType,
      section.contactHours,
      getAcademicLevel(section.courseCode),
      section.gender,
      lecturer.id, // Assign the lecturer for validation
      session.day,
      session.period,
      scheduleWithNewSession,
      existingSectionSessions,
      [lecturer], // Pass the lecturer for capability and preference checks
      'Fall' // Default semester
    );

    if (!validation.valid) {
      // Log which specific rules would be violated
      console.log(`   ❌ Lecturer ${lecturer.firstName} ${lecturer.lastName} would violate rules: ${validation.violatedRules.join(', ')}`);
      return true; // Would violate constraints
    }
  }

  return false; // No constraints violated
}

/**
 * Try to schedule a section using a specific pattern with lecturer assignment attempt
 */
function trySchedulePatternWithLecturerAssignment(
  section: Section,
  pattern: SchedulingPattern,
  existingSessions: AutoSession[],
  newSessions: AutoSession[],
  lecturers: Lecturer[]
): AutoSession[] {
  // First, try to schedule the pattern without lecturer constraints
  const patternSessions = trySchedulePattern(section, pattern, existingSessions, newSessions);

  if (patternSessions.length === 0) {
    return []; // Pattern couldn't be scheduled
  }

  // Pattern was successfully scheduled, now try to assign a lecturer
  const eligibleLecturers = findEligibleLecturers(section, lecturers);

  if (eligibleLecturers.length === 0) {
    // No eligible lecturers, return sessions without lecturer assignment
    return patternSessions;
  }

  // Try to assign the best lecturer while respecting all constraints
  const selectedLecturer = selectBestLecturerForCPS(
    section,
    patternSessions,
    eligibleLecturers,
    [...existingSessions, ...newSessions]
  );

  if (selectedLecturer) {
    console.log(`   ✅ Selected lecturer ${selectedLecturer.firstName} ${selectedLecturer.lastName} for ${section.courseCode} (passed all rule validations)`);
    // Assign the lecturer to all sessions in the pattern
    return patternSessions.map(session => ({
      ...session,
      lecturerId: selectedLecturer.id
    }));
  }

  console.log(`   ⚠️ No suitable lecturer found for ${section.courseCode} (all candidates violated enabled rules)`);
  // No suitable lecturer found, return sessions without lecturer assignment
  return patternSessions;
}

/**
 * OPTIMIZATION STRATEGY IMPLEMENTATIONS
 */

/**
 * Create constraint context for CPS optimization
 */
function createConstraintContext(): CPSConstraintContext {
  const ruleSystemState = useRuleSystemStore.getState();
  const { rules } = ruleSystemState;

  // Define hard constraints that cannot be violated (includes ALL course/section rules)
  const hardConstraints = [
    // System constraints
    'block-break-timeslots',
    'no-lecturer-duplicate',
    'lecturer-capability',
    'max-sessions-per-timeslot',

    // Course/Section pattern rules (NEVER relax these)
    'undergrad-2ch-pattern',
    'undergrad-3ch-pattern',
    'undergrad-5ch-pattern',
    'postgrad-pattern',
    'postgrad-same-level-limit',
    'postgrad-distribution',
    'no-duplicate-same-gender',

    // Academic integrity rules
    'contact-hours-validation',
    'section-completeness',
    'course-type-constraints'
  ];

  // Define soft constraints that can be temporarily relaxed (ONLY lecturer preferences/workload)
  const softConstraints = [
    'lecturer-preferred-time',
    'lecturer-max-days',
    'lecturer-max-consecutive',
    'lecturer-max-gap',
    'lecturer-workload-balance'
  ];

  // Create priority map based on rule priorities
  const constraintPriorities = new Map<string, number>();
  const maxViolations = new Map<string, number>();

  rules.forEach(rule => {
    constraintPriorities.set(rule.id, rule.priority);

    // Only allow limited violations for soft constraints (lecturer preferences only)
    if (softConstraints.includes(rule.id)) {
      maxViolations.set(rule.id, 1); // Allow only 1 violation per soft constraint (reduced from 2)
    }

    // Ensure all course/section rules are treated as hard constraints
    // Check for academic pattern rules and course-related constraints
    const isAcademicRule = rule.name.toLowerCase().includes('pattern') ||
                          rule.name.toLowerCase().includes('postgraduate') ||
                          rule.name.toLowerCase().includes('undergraduate') ||
                          rule.name.toLowerCase().includes('contact hours') ||
                          rule.name.toLowerCase().includes('duplicate') ||
                          rule.name.toLowerCase().includes('same gender') ||
                          rule.name.toLowerCase().includes('distribution') ||
                          rule.id.includes('pattern') ||
                          rule.id.includes('postgrad') ||
                          rule.id.includes('undergrad') ||
                          rule.id.includes('duplicate') ||
                          rule.id.includes('same-gender');

    if (isAcademicRule) {
      // Force these into hard constraints regardless of priority
      if (!hardConstraints.includes(rule.id)) {
        hardConstraints.push(rule.id);
      }
    }
  });

  return {
    hardConstraints,
    softConstraints,
    constraintPriorities,
    maxViolations
  };
}

/**
 * Strategy 1: Lecturer Swapping
 * Exchange lecturers between sessions when both are qualified
 */
function applyLecturerSwapping(
  sessions: AutoSession[],
  sections: Section[],
  lecturers: Lecturer[],
  _constraints: CPSConstraintContext
): CPSOptimizationResult {
  console.log('🔄 Analyzing lecturer swapping opportunities...');

  const modifiedSessions = [...sessions];
  const swappedLecturers: Array<{
    sessionId1: string;
    sessionId2: string;
    lecturer1: string;
    lecturer2: string;
    reason: string;
  }> = [];

  let optimizationScore = 0;
  let violationsResolved = 0;

  // Find sessions with lecturer constraint violations
  const problematicSessions = sessions.filter(session => {
    if (!session.lecturerId) return false;

    // Check if this session violates any lecturer constraints
    const section = sections.find(s => s.id === session.sectionId);
    if (!section) return false;

    const validation = validateTimeslotForSection(
      section.id,
      section.courseCode,
      section.courseType,
      section.contactHours,
      getAcademicLevel(section.courseCode),
      section.gender,
      session.lecturerId,
      session.day,
      session.period,
      convertAutoSessionsToSessions(sessions),
      convertAutoSessionsToSessions(sessions.filter(s => s.sectionId === section.id)),
      lecturers
    );

    return !validation.valid;
  });

  console.log(`Found ${problematicSessions.length} sessions with lecturer constraint violations`);

  // Try to swap lecturers between problematic and non-problematic sessions
  for (const problematicSession of problematicSessions) {
    const problematicSection = sections.find(s => s.id === problematicSession.sectionId);
    if (!problematicSection) continue;

    // Find potential swap candidates
    const swapCandidates = sessions.filter(candidateSession =>
      candidateSession.id !== problematicSession.id &&
      candidateSession.lecturerId &&
      candidateSession.lecturerId !== problematicSession.lecturerId
    );

    for (const candidate of swapCandidates) {
      const candidateSection = sections.find(s => s.id === candidate.sectionId);
      if (!candidateSection) continue;

      // Check if both lecturers can teach both courses
      const lecturer1 = lecturers.find(l => l.id === problematicSession.lecturerId);
      const lecturer2 = lecturers.find(l => l.id === candidate.lecturerId);

      if (!lecturer1 || !lecturer2) continue;

      const lecturer1CanTeachCandidate = lecturer1.coursesAbleToTeach?.includes(candidateSection.courseCode);
      const lecturer2CanTeachProblematic = lecturer2.coursesAbleToTeach?.includes(problematicSection.courseCode);

      if (lecturer1CanTeachCandidate && lecturer2CanTeachProblematic) {
        // Simulate the swap and check if it improves the situation
        const tempSessions = [...modifiedSessions];
        const problematicIndex = tempSessions.findIndex(s => s.id === problematicSession.id);
        const candidateIndex = tempSessions.findIndex(s => s.id === candidate.id);

        if (problematicIndex >= 0 && candidateIndex >= 0) {
          // Perform the swap
          tempSessions[problematicIndex] = { ...tempSessions[problematicIndex], lecturerId: lecturer2.id };
          tempSessions[candidateIndex] = { ...tempSessions[candidateIndex], lecturerId: lecturer1.id };

          // CRITICAL: Validate the swap doesn't violate hard constraints
          const problematicSessionAfterSwap = { ...tempSessions[problematicIndex] };
          const candidateSessionAfterSwap = { ...tempSessions[candidateIndex] };

          // Check max sessions per timeslot for both sessions
          const problematicTimeslotValid = validateBasicTimeslotConstraints(
            problematicSessionAfterSwap,
            problematicSection,
            tempSessions.filter(s => s.id !== problematicSessionAfterSwap.id)
          );

          const candidateTimeslotValid = validateBasicTimeslotConstraints(
            candidateSessionAfterSwap,
            candidateSection,
            tempSessions.filter(s => s.id !== candidateSessionAfterSwap.id)
          );

          // If both timeslots are valid after swap, apply it
          if (problematicTimeslotValid && candidateTimeslotValid) {
            modifiedSessions[problematicIndex] = tempSessions[problematicIndex];
            modifiedSessions[candidateIndex] = tempSessions[candidateIndex];

            swappedLecturers.push({
              sessionId1: problematicSession.id,
              sessionId2: candidate.id,
              lecturer1: lecturer1.id,
              lecturer2: lecturer2.id,
              reason: 'Resolved lecturer constraint violations'
            });

            violationsResolved++;
            optimizationScore += 10; // Score for resolving violations
            break; // Move to next problematic session
          }
        }
      }
    }
  }

  console.log(`✅ Lecturer swapping completed: ${swappedLecturers.length} swaps, ${violationsResolved} violations resolved`);

  return {
    improved: swappedLecturers.length > 0,
    modifiedSessions,
    swappedLecturers,
    repositionedSessions: [],
    rescheduledSessions: [],
    constraintViolationsResolved: violationsResolved,
    constraintViolationsIntroduced: 0,
    optimizationScore
  };
}

/**
 * Strategy 2: Session Repositioning
 * Move sessions to different days or time slots to better distribute lecturer workload
 */
function applySessionRepositioning(
  sessions: AutoSession[],
  sections: Section[],
  lecturers: Lecturer[],
  _constraints: CPSConstraintContext
): CPSOptimizationResult {
  console.log('📍 Analyzing session repositioning opportunities...');

  const modifiedSessions = [...sessions];
  const repositionedSessions: Array<{
    sessionId: string;
    fromDay: string;
    fromPeriod: number;
    toDay: string;
    toPeriod: number;
    reason: string;
  }> = [];

  let optimizationScore = 0;
  let violationsResolved = 0;

  // Find lecturers with workload imbalances
  const lecturerWorkloads = new Map<string, {
    sessions: AutoSession[];
    totalHours: number;
    daysUsed: Set<string>;
    maxGapViolations: number;
  }>();

  // Calculate current workloads
  sessions.forEach(session => {
    if (!session.lecturerId) return;

    if (!lecturerWorkloads.has(session.lecturerId)) {
      lecturerWorkloads.set(session.lecturerId, {
        sessions: [],
        totalHours: 0,
        daysUsed: new Set(),
        maxGapViolations: 0
      });
    }

    const workload = lecturerWorkloads.get(session.lecturerId);
    if (workload) {
      workload.sessions.push(session);
      workload.daysUsed.add(session.day);

      const isLongDay = ['Mon', 'Wed'].includes(session.day);
      workload.totalHours += isLongDay ? 1.5 : 1.0;
    }
  });

  // Identify repositioning opportunities
  for (const [lecturerId, workload] of lecturerWorkloads) {
    const lecturer = lecturers.find(l => l.id === lecturerId);
    if (!lecturer) continue;

    const maxDays = lecturer.maxTeachingDaysPerWeek || 5;

    // If lecturer is using too many days, try to consolidate
    if (workload.daysUsed.size > maxDays) {
      console.log(`Lecturer ${lecturer.firstName} using ${workload.daysUsed.size}/${maxDays} days - attempting consolidation`);

      // Try to move sessions from less utilized days to more utilized days
      const dayUsage = new Map<string, AutoSession[]>();
      workload.sessions.forEach(session => {
        if (!dayUsage.has(session.day)) {
          dayUsage.set(session.day, []);
        }
        const daySessions = dayUsage.get(session.day);
        if (daySessions) {
          daySessions.push(session);
        }
      });

      // Sort days by usage (ascending)
      const sortedDays = Array.from(dayUsage.entries()).sort((a, b) => a[1].length - b[1].length);

      // Try to move sessions from least used days
      for (const [day, daySessions] of sortedDays.slice(0, Math.min(2, sortedDays.length))) {
        for (const session of daySessions) {
          const section = sections.find(s => s.id === session.sectionId);
          if (!section) continue;

          // Try to find alternative timeslots on more utilized days
          for (const [targetDay] of sortedDays.slice(-2)) {
            if (targetDay === day) continue;

            // Try different periods on the target day
            for (let period = 1; period <= 11; period++) {
              // Skip blocked periods
              const isLongDay = ['Mon', 'Wed'].includes(targetDay);
              if ((isLongDay && (period === 5 || period === 6)) || (!isLongDay && period === 12)) {
                continue;
              }

              // CRITICAL: Check if this timeslot is available and doesn't violate hard constraints
              const tempSession = { ...session, day: targetDay, period };

              // Use basic validation to check essential constraints only
              const isValidTimeslot = validateBasicTimeslotConstraints(
                tempSession,
                section,
                modifiedSessions.filter(s => s.id !== session.id)
              );

              if (isValidTimeslot) {
                // Apply the repositioning
                const sessionIndex = modifiedSessions.findIndex(s => s.id === session.id);
                if (sessionIndex >= 0) {
                  modifiedSessions[sessionIndex] = { ...modifiedSessions[sessionIndex], day: targetDay, period };

                  repositionedSessions.push({
                    sessionId: session.id,
                    fromDay: day,
                    fromPeriod: session.period,
                    toDay: targetDay,
                    toPeriod: period,
                    reason: `Consolidated lecturer ${lecturer.firstName} schedule to reduce teaching days`
                  });

                  violationsResolved++;
                  optimizationScore += 5; // Score for workload optimization
                  break; // Move to next session
                }
              }
            }
          }
        }
      }
    }
  }

  console.log(`✅ Session repositioning completed: ${repositionedSessions.length} sessions moved, ${violationsResolved} violations resolved`);

  return {
    improved: repositionedSessions.length > 0,
    modifiedSessions,
    swappedLecturers: [],
    repositionedSessions,
    rescheduledSessions: [],
    constraintViolationsResolved: violationsResolved,
    constraintViolationsIntroduced: 0,
    optimizationScore
  };
}

/**
 * Strategy 3: Intelligent Rescheduling
 * Reschedule unscheduled sections by finding valid timeslots, even if it requires moving other sessions
 */
function applyIntelligentRescheduling(
  sessions: AutoSession[],
  unscheduledSections: Section[],
  allSections: Section[],
  lecturers: Lecturer[],
  _constraints: CPSConstraintContext
): CPSOptimizationResult {
  console.log('🧠 Analyzing intelligent rescheduling opportunities...');

  const modifiedSessions = [...sessions];
  const rescheduledSessions: Array<{
    sessionId: string;
    originalTimeslot: string;
    newTimeslot: string;
    violationsResolved: string[];
  }> = [];

  let optimizationScore = 0;
  let violationsResolved = 0;

  // Try to schedule each unscheduled section
  for (const section of unscheduledSections) {
    console.log(`Attempting to reschedule section ${section.courseCode} (${section.contactHours}CH, ${section.gender})`);

    // Find eligible lecturers for this section
    const eligibleLecturers = lecturers.filter(lecturer =>
      lecturer.coursesAbleToTeach?.includes(section.courseCode)
    );

    // Get scheduling patterns for this section
    const patterns = getSchedulingPatternsForSection(section);
    let scheduled = false;

    for (const pattern of patterns) {
      if (scheduled) break;

      // Try with eligible lecturers first
      for (const lecturer of eligibleLecturers) {
        if (scheduled) break;

        // Try to find valid timeslots for this pattern with relaxed validation
        const patternSessions = trySchedulePatternWithBasicValidation(
          section,
          pattern,
          lecturer,
          modifiedSessions,
          allSections,
          lecturers
        );

        if (patternSessions.length > 0) {
          // Successfully scheduled this section
          modifiedSessions.push(...patternSessions);

          patternSessions.forEach((session: AutoSession) => {
            rescheduledSessions.push({
              sessionId: session.id,
              originalTimeslot: 'unscheduled',
              newTimeslot: `${session.day}-${session.period}`,
              violationsResolved: ['Section was unscheduled']
            });
          });

          violationsResolved++;
          optimizationScore += 15; // Higher score for scheduling new sections
          scheduled = true;
          console.log(`✅ Successfully rescheduled ${section.courseCode} with lecturer ${lecturer.firstName}`);
        }
      }

      // If no lecturer worked, try without lecturer assignment
      if (!scheduled) {
        console.log(`Trying to schedule ${section.courseCode} without lecturer assignment...`);
        const patternSessions = trySchedulePatternWithoutLecturer(
          section,
          pattern,
          modifiedSessions,
          allSections
        );

        if (patternSessions.length > 0) {
          // Successfully scheduled this section without lecturer
          modifiedSessions.push(...patternSessions);

          patternSessions.forEach((session: AutoSession) => {
            rescheduledSessions.push({
              sessionId: session.id,
              originalTimeslot: 'unscheduled',
              newTimeslot: `${session.day}-${session.period}`,
              violationsResolved: ['Section scheduled without lecturer']
            });
          });

          violationsResolved++;
          optimizationScore += 12; // Good score for scheduling without lecturer
          scheduled = true;
          console.log(`✅ Successfully rescheduled ${section.courseCode} without lecturer assignment`);
        }
      }
    }

    if (!scheduled) {
      console.log(`⚠️ Could not reschedule ${section.courseCode}`);
    }
  }

  console.log(`✅ Intelligent rescheduling completed: ${rescheduledSessions.length} sections rescheduled`);

  return {
    improved: rescheduledSessions.length > 0,
    modifiedSessions,
    swappedLecturers: [],
    repositionedSessions: [],
    rescheduledSessions,
    constraintViolationsResolved: violationsResolved,
    constraintViolationsIntroduced: 0,
    optimizationScore
  };
}

/**
 * Strategy 4: Constraint Relaxation
 * Temporarily relax ONLY lecturer preference constraints (never course/section rules)
 */
function applyConstraintRelaxation(
  sessions: AutoSession[],
  unscheduledSections: Section[],
  allSections: Section[],
  lecturers: Lecturer[],
  constraints: CPSConstraintContext
): CPSOptimizationResult {
  console.log('🎛️ Analyzing constraint relaxation opportunities...');
  console.log('🛡️ Note: Only lecturer preferences can be relaxed - course/section rules are NEVER violated');

  const modifiedSessions = [...sessions];
  const rescheduledSessions: Array<{
    sessionId: string;
    originalTimeslot: string;
    newTimeslot: string;
    violationsResolved: string[];
  }> = [];

  let optimizationScore = 0;
  let violationsIntroduced = 0;

  // Try to schedule sections with relaxed constraints
  for (const section of unscheduledSections) {

    const eligibleLecturers = lecturers.filter(lecturer =>
      lecturer.coursesAbleToTeach?.includes(section.courseCode)
    );

    if (eligibleLecturers.length === 0) continue;

    const patterns = getSchedulingPatternsForSection(section);
    let scheduled = false;

    for (const pattern of patterns) {
      if (scheduled) break;

      for (const lecturer of eligibleLecturers) {
        if (scheduled) break;

        // Try to schedule with relaxed soft constraints
        const patternSessions = tryScheduleWithRelaxedConstraints(
          section,
          pattern,
          lecturer,
          modifiedSessions,
          allSections,
          lecturers,
          constraints
        );

        if (patternSessions.length > 0) {
          modifiedSessions.push(...patternSessions);

          patternSessions.forEach(session => {
            rescheduledSessions.push({
              sessionId: session.id,
              originalTimeslot: 'unscheduled',
              newTimeslot: `${session.day}-${session.period}`,
              violationsResolved: ['Section scheduled with relaxed constraints']
            });
          });

          violationsIntroduced += 1; // Assume one soft constraint violation per section
          optimizationScore += 8; // Lower score due to constraint violations
          scheduled = true;
          console.log(`✅ Scheduled ${section.courseCode} with relaxed constraints`);
        }
      }
    }
  }

  console.log(`✅ Constraint relaxation completed: ${rescheduledSessions.length} sections scheduled with ${violationsIntroduced} constraint relaxations`);

  return {
    improved: rescheduledSessions.length > 0,
    modifiedSessions,
    swappedLecturers: [],
    repositionedSessions: [],
    rescheduledSessions,
    constraintViolationsResolved: 0,
    constraintViolationsIntroduced: violationsIntroduced,
    optimizationScore
  };
}

/**
 * Helper function for intelligent rescheduling with basic validation
 */
function trySchedulePatternWithBasicValidation(
  section: Section,
  pattern: SchedulingPattern,
  lecturer: Lecturer,
  existingSessions: AutoSession[],
  _allSections: Section[],
  _lecturers: Lecturer[]
): AutoSession[] {
  // CRITICAL: Validate contact hours before attempting to schedule
  if (!validateContactHoursCompliance(section, pattern)) {
    return [];
  }

  const newSessions: AutoSession[] = [];

  // Try to schedule each session in the pattern
  for (let i = 0; i < pattern.days.length; i++) {
    const day = pattern.days[i];

    // Handle different period structures in CPS patterns
    let period: number;
    if (pattern.periodsPerDay.length === pattern.days.length) {
      // Each day has its own period specified
      period = pattern.periodsPerDay[i];
    } else if (pattern.periodsPerDay.length === 1) {
      // All days use the same period
      period = pattern.periodsPerDay[0];
    } else {
      // Fallback to a default period
      period = 8; // Default to period 8
    }

    // Create a temporary session
    const tempSession: AutoSession = {
      id: `temp-basic-${section.id}-${i}`,
      sectionId: section.id,
      courseCode: section.courseCode,
      courseType: section.courseType,
      academicLevel: getAcademicLevel(section.courseCode),
      gender: section.gender,
      lecturerId: lecturer.id,
      day,
      period,
      isAutoGenerated: true
    };

    // Use simplified validation - only check essential constraints
    const isValidTimeslot = validateBasicTimeslotConstraints(
      tempSession,
      section,
      [...existingSessions, ...newSessions]
    );

    if (!isValidTimeslot) {
      return []; // Pattern failed
    }

    // Additional check for lecturer conflicts
    if (lecturer.id) {
      const lecturerConflicts = existingSessions.filter(s =>
        s.lecturerId === lecturer.id &&
        s.day === day &&
        s.period === period
      );
      if (lecturerConflicts.length > 0) {
        return []; // Lecturer conflict
      }
    }

    newSessions.push(tempSession);
  }

  // CRITICAL: Final validation that we haven't over-scheduled this section
  if (!validateSectionNotOverScheduled(section, newSessions, existingSessions)) {
    console.log(`❌ BLOCKED: Scheduling would over-schedule section ${section.courseCode} beyond ${section.contactHours}CH`);
    return [];
  }

  return newSessions;
}

/**
 * Helper function for constraint relaxation scheduling
 */
function tryScheduleWithRelaxedConstraints(
  section: Section,
  pattern: SchedulingPattern,
  lecturer: Lecturer,
  existingSessions: AutoSession[],
  _allSections: Section[],
  lecturers: Lecturer[],
  constraints: CPSConstraintContext
): AutoSession[] {
  // CRITICAL: Validate contact hours before attempting to schedule
  if (!validateContactHoursCompliance(section, pattern)) {
    return [];
  }

  const newSessions: AutoSession[] = [];

  // Try to schedule with relaxed soft constraints
  for (let i = 0; i < pattern.days.length; i++) {
    const day = pattern.days[i];

    // Handle different period structures in CPS patterns
    let period: number;
    if (pattern.periodsPerDay.length === pattern.days.length) {
      // Each day has its own period specified
      period = pattern.periodsPerDay[i];
    } else if (pattern.periodsPerDay.length === 1) {
      // All days use the same period
      period = pattern.periodsPerDay[0];
    } else {
      // Fallback to a default period
      period = 8; // Default to period 8
    }

    const tempSession: AutoSession = {
      id: `temp-relaxed-${section.id}-${i}`,
      sectionId: section.id,
      courseCode: section.courseCode,
      courseType: section.courseType,
      academicLevel: getAcademicLevel(section.courseCode),
      gender: section.gender,
      lecturerId: lecturer.id,
      day,
      period,
      isAutoGenerated: true
    };

    // Check only hard constraints, allow soft constraint violations
    const hardConstraintViolations = checkHardConstraintsOnly(
      tempSession,
      section,
      [...existingSessions, ...newSessions],
      lecturers,
      constraints
    );

    if (hardConstraintViolations.length > 0) {
      return []; // Hard constraints cannot be violated
    }

    newSessions.push(tempSession);
  }

  // CRITICAL: Final validation that we haven't over-scheduled this section
  if (!validateSectionNotOverScheduled(section, newSessions, existingSessions)) {
    console.log(`❌ BLOCKED: Scheduling would over-schedule section ${section.courseCode} beyond ${section.contactHours}CH`);
    return [];
  }

  return newSessions;
}

/**
 * Check only hard constraints for constraint relaxation
 * This function ensures that NO course/section rules or critical constraints are violated
 */
function checkHardConstraintsOnly(
  session: AutoSession,
  section: Section,
  allSessions: AutoSession[],
  lecturers: Lecturer[],
  constraints: CPSConstraintContext
): string[] {
  const violations: string[] = [];

  // Get rule system state for comprehensive validation
  const ruleSystemState = useRuleSystemStore.getState();
  const { userDefinedBreaks, rules } = ruleSystemState;

  // Check all hard constraints (including dynamically identified academic rules)
  for (const constraintId of constraints.hardConstraints) {
    switch (constraintId) {
      case 'block-break-timeslots': {
        // Check if timeslot is blocked
        const breakKey = `${session.day.substring(0, 3)}-${session.period}`;
        if (userDefinedBreaks.includes(breakKey)) {
          violations.push('Cannot schedule in blocked timeslot');
        }
        break;
      }

      case 'no-lecturer-duplicate': {
        // Check for lecturer conflicts
        const conflictingSessions = allSessions.filter(s =>
          s.lecturerId === session.lecturerId &&
          s.day === session.day &&
          s.period === session.period &&
          s.id !== session.id
        );
        if (conflictingSessions.length > 0) {
          violations.push('Lecturer already scheduled at this time');
        }
        break;
      }

      case 'lecturer-capability': {
        // Check lecturer capability
        const lecturer = lecturers.find(l => l.id === session.lecturerId);
        if (lecturer && !lecturer.coursesAbleToTeach?.includes(section.courseCode)) {
          violations.push('Lecturer not qualified to teach this course');
        }
        break;
      }

      case 'max-sessions-per-timeslot': {
        // CRITICAL: Check maximum sessions per timeslot (HARD CONSTRAINT)
        const timeslotSessions = allSessions.filter(s =>
          s.day === session.day &&
          s.period === session.period &&
          s.id !== session.id
        );
        const rule = rules.find(r => r.id === 'max-sessions-per-timeslot');
        const maxSessionsAllowed = rule && rule.enabled ? ((rule as { value?: number }).value || 1) : 1;

        if (timeslotSessions.length >= maxSessionsAllowed) {
          violations.push(`Maximum sessions per timeslot exceeded: ${timeslotSessions.length}/${maxSessionsAllowed} in ${session.day}-${session.period}`);
          console.log(`❌ HARD CONSTRAINT VIOLATION: Timeslot ${session.day}-${session.period} already has ${timeslotSessions.length}/${maxSessionsAllowed} sessions`);
        }
        break;
      }

      // Academic pattern rules - NEVER allow violations
      case 'undergrad-2ch-pattern':
      case 'undergrad-3ch-pattern':
      case 'undergrad-5ch-pattern':
      case 'postgrad-pattern': {
        // Use full validation for academic patterns
        const patternValidation = validateTimeslotForSection(
          section.id,
          section.courseCode,
          section.courseType,
          section.contactHours,
          getAcademicLevel(section.courseCode),
          section.gender,
          session.lecturerId,
          session.day,
          session.period,
          convertAutoSessionsToSessions(allSessions),
          convertAutoSessionsToSessions(allSessions.filter(s => s.sectionId === section.id)),
          lecturers
        );
        if (!patternValidation.valid) {
          violations.push(`Academic pattern violation: ${patternValidation.violatedRules.join(', ')}`);
        }
        break;
      }

      case 'no-duplicate-same-gender': {
        // Check for same gender sections of same course in same timeslot
        const sameTimeslotSessions = allSessions.filter(s =>
          s.day === session.day &&
          s.period === session.period &&
          s.id !== session.id
        );
        for (const otherSession of sameTimeslotSessions) {
          if (otherSession.courseCode === session.courseCode &&
              otherSession.gender === session.gender) {
            violations.push('Cannot schedule same gender sections of same course in same timeslot');
          }
        }
        break;
      }

      default:
        // For any other hard constraints, perform basic validation
        if (constraintId.includes('pattern') || constraintId.includes('postgrad') ||
            constraintId.includes('undergrad') || constraintId.includes('duplicate')) {
          // These are academic rules - use full validation
          const academicValidation = validateTimeslotForSection(
            section.id,
            section.courseCode,
            section.courseType,
            section.contactHours,
            getAcademicLevel(section.courseCode),
            section.gender,
            session.lecturerId,
            session.day,
            session.period,
            convertAutoSessionsToSessions(allSessions),
            convertAutoSessionsToSessions(allSessions.filter(s => s.sectionId === section.id)),
            lecturers
          );
          if (!academicValidation.valid) {
            violations.push(`Academic rule violation (${constraintId}): ${academicValidation.violatedRules.join(', ')}`);
          }
        }
        break;
    }
  }

  return violations;
}

/**
 * Strategy 3b: Lecturer-Independent Scheduling
 * Schedule sections without lecturer assignment when no eligible lecturers are available
 */
function applyLecturerIndependentScheduling(
  sessions: AutoSession[],
  unscheduledSections: Section[],
  allSections: Section[],
  _lecturers: Lecturer[]
): CPSOptimizationResult {
  console.log('🎯 Applying lecturer-independent scheduling for remaining unscheduled sections...');

  const modifiedSessions = [...sessions];
  const rescheduledSessions: Array<{
    sessionId: string;
    originalTimeslot: string;
    newTimeslot: string;
    violationsResolved: string[];
  }> = [];

  let optimizationScore = 0;
  let violationsResolved = 0;

  // Try to schedule each unscheduled section without lecturer assignment
  for (const section of unscheduledSections) {
    console.log(`🔍 Attempting lecturer-independent scheduling for ${section.courseCode} (${section.contactHours}CH, ${section.gender})`);

    // Get scheduling patterns for this section
    const patterns = getSchedulingPatternsForSection(section);
    console.log(`📋 Found ${patterns.length} patterns for ${section.courseCode}:`, patterns.map(p => p.name));

    let scheduled = false;

    for (const pattern of patterns) {
      if (scheduled) break;

      console.log(`🎯 Trying pattern: ${pattern.name} (${pattern.days.length} sessions on days: ${pattern.days.join(', ')})`);

      // Try to schedule this pattern without lecturer assignment
      const patternSessions = trySchedulePatternWithoutLecturer(
        section,
        pattern,
        modifiedSessions,
        allSections
      );

      if (patternSessions.length > 0) {
        // Successfully scheduled this section
        modifiedSessions.push(...patternSessions);

        patternSessions.forEach(session => {
          rescheduledSessions.push({
            sessionId: session.id,
            originalTimeslot: 'unscheduled',
            newTimeslot: `${session.day}-${session.period}`,
            violationsResolved: ['Section scheduled without lecturer assignment']
          });
        });

        violationsResolved++;
        optimizationScore += 12; // Good score for scheduling new sections
        scheduled = true;
      }
    }

    // Section scheduling logging removed to reduce console output
  }

  // Lecturer-independent scheduling completion logging removed to reduce console output

  return {
    improved: rescheduledSessions.length > 0,
    modifiedSessions,
    swappedLecturers: [],
    repositionedSessions: [],
    rescheduledSessions,
    constraintViolationsResolved: violationsResolved,
    constraintViolationsIntroduced: 0,
    optimizationScore
  };
}

/**
 * Try to schedule a pattern without lecturer assignment (simplified validation)
 */
function trySchedulePatternWithoutLecturer(
  section: Section,
  pattern: SchedulingPattern,
  existingSessions: AutoSession[],
  _allSections: Section[]
): AutoSession[] {
  // CRITICAL: Validate contact hours before attempting to schedule
  if (!validateContactHoursCompliance(section, pattern)) {
    return [];
  }

  const newSessions: AutoSession[] = [];

  // Try to schedule each session in the pattern
  for (let i = 0; i < pattern.days.length; i++) {
    const day = pattern.days[i];

    // Handle different period structures in CPS patterns
    let period: number;
    if (pattern.periodsPerDay.length === pattern.days.length) {
      // Each day has its own period specified
      period = pattern.periodsPerDay[i];
    } else if (pattern.periodsPerDay.length === 1) {
      // All days use the same period
      period = pattern.periodsPerDay[0];
    } else {
      // Fallback to a default period
      period = 8; // Default to period 8
    }

    // Session scheduling attempt logging removed to reduce console output

    // Create a session without lecturer assignment
    const tempSession: AutoSession = {
      id: `temp-no-lecturer-${section.id}-${i}`,
      sectionId: section.id,
      courseCode: section.courseCode,
      courseType: section.courseType,
      academicLevel: getAcademicLevel(section.courseCode),
      gender: section.gender,
      lecturerId: '', // No lecturer assigned
      day,
      period,
      isAutoGenerated: true
    };

    // Simplified validation - only check essential constraints
    const isValidTimeslot = validateBasicTimeslotConstraints(
      tempSession,
      section,
      [...existingSessions, ...newSessions]
    );

    if (!isValidTimeslot) {
      return []; // Pattern failed
    }

    newSessions.push(tempSession);
  }

  // CRITICAL: Final validation that we haven't over-scheduled this section
  if (!validateSectionNotOverScheduled(section, newSessions, existingSessions)) {
    return [];
  }

  return newSessions;
}

/**
 * Simplified validation for lecturer-independent scheduling
 */
function validateBasicTimeslotConstraints(
  session: AutoSession,
  section: Section,
  allSessions: AutoSession[]
): boolean {
  // Timeslot validation logging removed to reduce console output

  // Get rule system state
  const ruleSystemState = useRuleSystemStore.getState();
  const { userDefinedBreaks, rules } = ruleSystemState;

  // Check 1: Not in blocked timeslots
  const breakKey = `${session.day.substring(0, 3)}-${session.period}`;
  if (userDefinedBreaks.includes(breakKey)) {
    return false;
  }

  // Check 2: No same gender sections of same course in same timeslot
  const conflictingSessions = allSessions.filter(s =>
    s.day === session.day &&
    s.period === session.period &&
    s.courseCode === session.courseCode &&
    s.gender === session.gender &&
    s.id !== session.id
  );
  if (conflictingSessions.length > 0) {
    return false;
  }

  // Check 3: Maximum sessions per timeslot (CRITICAL HARD CONSTRAINT)
  const timeslotSessions = allSessions.filter(s =>
    s.day === session.day &&
    s.period === session.period &&
    s.id !== session.id
  );

  // Get the maximum sessions allowed per timeslot from rules
  const maxSessionsRule = rules.find(r => r.id === 'max-sessions-per-timeslot');
  const maxSessionsAllowed = maxSessionsRule && maxSessionsRule.enabled ?
    ((maxSessionsRule as { value?: number }).value || 1) : 1;

  // CRITICAL: Never allow more sessions than the maximum allowed
  if (timeslotSessions.length >= maxSessionsAllowed) {
    return false;
  }

  // Check 4: Basic academic pattern validation (simplified)
  const academicLevel = getAcademicLevel(section.courseCode);
  const isPostgraduate = academicLevel.toLowerCase().includes('master') ||
                         academicLevel.toLowerCase().includes('phd') ||
                         academicLevel.toLowerCase().includes('diploma');

  // For postgraduate courses, check if it's in appropriate periods
  if (isPostgraduate && section.courseType === 'Theory') {
    const isLongDay = ['Mon', 'Wed'].includes(session.day);

    if (isLongDay) {
      // Long days: periods 7-10 are acceptable
      if (session.period < 7 || session.period > 10) {
        return false;
      }
    } else {
      // Regular days: periods 7-11 are acceptable
      if (session.period < 7 || session.period > 11) {
        return false;
      }
    }
  }

  return true;
}

/**
 * CRITICAL: Validate that a pattern matches the section's contact hours requirement
 */
function validateContactHoursCompliance(section: Section, pattern: SchedulingPattern): boolean {
  const requiredSessions = section.contactHours;

  // Calculate total sessions from the pattern
  // For CPS patterns, each day in the pattern represents one session
  // The periodsPerDay array indicates which periods to use, but each day = 1 session
  let patternSessions = 0;

  // Count sessions based on pattern structure
  if (pattern.periodsPerDay && pattern.periodsPerDay.length > 0) {
    // If periodsPerDay has multiple values, it means different periods for different days
    // But each day still represents one session
    patternSessions = pattern.days.length;
  } else {
    // Fallback to days count
    patternSessions = pattern.days.length;
  }

  if (patternSessions !== requiredSessions) {
    return false;
  }

  return true;
}

/**
 * CRITICAL: Validate that scheduling new sessions won't over-schedule a section
 */
function validateSectionNotOverScheduled(
  section: Section,
  newSessions: AutoSession[],
  existingSessions: AutoSession[]
): boolean {
  // Count existing sessions for this section
  const existingSessionsForSection = existingSessions.filter(s => s.sectionId === section.id);
  const totalSessionsAfterScheduling = existingSessionsForSection.length + newSessions.length;

  if (totalSessionsAfterScheduling > section.contactHours) {
    return false;
  }

  return true;
}

/**
 * Try to schedule a section using a specific pattern with full rule validation
 */
function trySchedulePattern(
  section: Section,
  pattern: SchedulingPattern,
  existingSessions: AutoSession[],
  newSessions: AutoSession[]
): AutoSession[] {
  const allExistingSessions = [...existingSessions, ...newSessions];
  const patternSessions: AutoSession[] = [];

  // Convert AutoSessions to Sessions for rule validation
  const existingSchedule: Session[] = convertAutoSessionsToSessions(allExistingSessions);
  const existingSectionSessions: Session[] = existingSchedule.filter(s => s.sectionId === section.id);

  // Use empty lecturer array since we're not assigning lecturers in CPS
  const lecturers: Lecturer[] = [];

  // Get academic level from course code
  const academicLevel = getAcademicLevel(section.courseCode);

  // First, validate the pattern against academic pattern rules
  const patternValidation = validateAcademicPattern(section, pattern, academicLevel);
  if (!patternValidation.valid) {
    return []; // Pattern violates academic rules
  }

  // Handle consecutive periods for postgraduate courses
  if (pattern.consecutivePeriods) {
    // This is a postgraduate pattern requiring consecutive periods
    const day = pattern.days[0];
    const startPeriod = pattern.periodsPerDay[0];
    const consecutiveCount = pattern.consecutivePeriods;

    // Validate all consecutive periods
    for (let i = 0; i < consecutiveCount; i++) {
      const period = startPeriod + i;

      // Validate this timeslot against all rules
      const validation = validateTimeslotForSection(
        section.id,
        section.courseCode,
        section.courseType,
        section.contactHours,
        academicLevel,
        section.gender,
        '', // No lecturer assigned
        day,
        period,
        existingSchedule,
        existingSectionSessions,
        lecturers
      );

      if (!validation.valid) {
        return []; // Pattern not valid due to rule violations
      }

      // Create session for this timeslot
      const sessionId = `cps-${section.id}-${day}-${period}`;
      const autoSession: AutoSession = {
        id: sessionId,
        sectionId: section.id,
        courseCode: section.courseCode,
        courseType: section.courseType,
        academicLevel: academicLevel,
        gender: section.gender,
        lecturerId: '', // No lecturer assigned
        day,
        period,
        isAutoGenerated: true
      };

      patternSessions.push(autoSession);

      // Add this session to the existing schedule for subsequent validations
      const newSession: Session = {
        id: sessionId,
        sectionId: section.id,
        courseCode: section.courseCode,
        courseType: section.courseType,
        academicLevel: academicLevel,
        gender: section.gender,
        lecturerId: '',
        day,
        startPeriod: period,
        endPeriod: period,
        period,
        isAutoGenerated: true
      };
      existingSchedule.push(newSession);
      existingSectionSessions.push(newSession);
    }
  } else {
    // Regular pattern - validate each day/period combination
    for (let dayIndex = 0; dayIndex < pattern.days.length; dayIndex++) {
      const day = pattern.days[dayIndex];
      const period = pattern.periodsPerDay[dayIndex];

      // Validate this timeslot against all rules
      const validation = validateTimeslotForSection(
        section.id,
        section.courseCode,
        section.courseType,
        section.contactHours,
        academicLevel,
        section.gender,
        '', // No lecturer assigned
        day,
        period,
        existingSchedule,
        existingSectionSessions,
        lecturers
      );

      if (!validation.valid) {
        return []; // Pattern not valid due to rule violations
      }

      // Create session for this timeslot
      const sessionId = `cps-${section.id}-${day}-${period}`;
      const autoSession: AutoSession = {
        id: sessionId,
        sectionId: section.id,
        courseCode: section.courseCode,
        courseType: section.courseType,
        academicLevel: academicLevel,
        gender: section.gender,
        lecturerId: '', // No lecturer assigned - this is the key difference
        day,
        period,
        isAutoGenerated: true
      };

      patternSessions.push(autoSession);

      // Add this session to the existing schedule for subsequent validations
      const newSession: Session = {
        id: sessionId,
        sectionId: section.id,
        courseCode: section.courseCode,
        courseType: section.courseType,
        academicLevel: academicLevel,
        gender: section.gender,
        lecturerId: '',
        day,
        startPeriod: period,
        endPeriod: period,
        period,
        isAutoGenerated: true
      };
      existingSchedule.push(newSession);
      existingSectionSessions.push(newSession);
    }
  }

  return patternSessions;
}
