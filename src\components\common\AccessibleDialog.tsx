import React, { useEffect, useRef } from 'react';
import { Dialog, DialogProps } from '@mui/material';

/**
 * Enhanced Dialog component that properly manages focus and accessibility
 * to prevent ARIA violations when modals are open
 */
interface AccessibleDialogProps extends DialogProps {
  open: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const AccessibleDialog: React.FC<AccessibleDialogProps> = ({
  open,
  onClose,
  children,
  ...dialogProps
}) => {
  // Simplified modal ref without complex hooks
  const modalRef = useRef<HTMLDivElement | null>(null);

  // Simplified focus management with error handling
  useEffect(() => {
    if (open) {
      try {
        // Simple focus management without complex DOM manipulation
        const timer = setTimeout(() => {
          const activeElement = document.activeElement as HTMLElement;
          if (activeElement && activeElement !== document.body) {
            activeElement.blur();
          }
        }, 100);

        return () => clearTimeout(timer);
      } catch (error) {
        console.error('Error in focus management:', error);
      }
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onClose={(_event, _reason) => {
        try {
          onClose();
        } catch (error) {
          console.error('Error closing dialog:', error);
        }
      }}
      {...dialogProps}
      // Simplified accessibility props
      aria-modal="true"
      keepMounted={false}
      // Simplified backdrop handling
      slotProps={{
        backdrop: {
          onClick: (e) => {
            try {
              e.stopPropagation();
              onClose();
            } catch (error) {
              console.error('Error handling backdrop click:', error);
            }
          },
        },
        ...dialogProps.slotProps,
      }}
    >
      <div ref={modalRef} tabIndex={-1} className="modal-content-wrapper">
        {children}
      </div>
    </Dialog>
  );
};

export default AccessibleDialog;
