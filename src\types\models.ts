// Define the types for our data models
export interface Course {
  id: string;
  courseCode: string;
  courseName: string;
  loadHours: number;
  contactHours: number;
  courseType: 'Theory' | 'Lab';
  maleSectionsCount: number;
  femaleSectionsCount: number;
  color: string;
  academicLevel?: string;
}

export interface Section {
  id: string;
  courseId: string;
  sectionNumber: number;
  gender: 'M' | 'F';
  scheduledHours: number;
  totalHours: number;
  viewTypes?: ('week' | 'day')[];
  lecturerId?: string; // Optional lecturer ID for auto-scheduling
}

export interface Lecturer {
  id: string;
  title: string;
  firstName: string;
  lastName?: string; // Made optional - lecturers may only have a first name
  email: string;
  department?: string; // Department field (optional)
  supervisionHoursFall: number;
  supervisionHoursSpring: number;
  maxYearLoad: number;
  maxLoadHoursPerWeek?: number; // Maximum load hours per week
  coursesAbleToTeach: string[];
  preferredTiming: 'Morning' | 'Evening' | 'Both';
  // Auto timetabling fields
  preferredCourseLevel?: 'Undergraduate' | 'Postgraduate' | 'All';
  femaleOnlyTeaching?: boolean;
  maxTeachingDaysPerWeek?: number;
  maxConsecutivePeriods?: number;
  maxGapBetweenPeriods?: number;
}

export interface Session {
  id: string;
  sectionId: string;
  lecturerId: string;
  lecturerIds?: string[]; // Array of lecturer IDs for multiple lecturers
  day: string;
  startPeriod: number;
  endPeriod: number;
  viewType: 'week' | 'regular' | 'long'; // The view this session belongs to
  timeOfDay?: 'morning' | 'evening'; // Morning or evening session
  isAutoGenerated?: boolean; // Flag to indicate if the session was auto-generated
}

export type Semester = 'Fall' | 'Spring' | 'Summer';

export interface UIState {
  // TimetableCanvas state
  viewMode: 'week' | 'regular' | 'long';
  activeTab: number; // 0 for morning, 1 for evening, 2 for all day (default)

  // Header state
  departmentName: string;
  academicYear: string;
  zoomFactor: number; // Zoom level (0.8 = 80%, 1.0 = 100%, etc.)

  // App state
  darkMode: boolean;
  showCoursesPanel: boolean;
  showLecturersPanel: boolean;

  // CoursesPanel state
  expandedCourses: Record<string, boolean>;

  // LecturersPanel state
  expandedLecturers: Record<string, boolean>;

  // Filter states
  courseFilter: string;
  lecturerFilter: string;
}



export interface Schema {
  courses: Record<Semester, Course[]>;
  sections: Record<Semester, Section[]>;
  lecturers: Lecturer[];
  sessions: Record<Semester, Session[]>;
  currentSemester: Semester;
  uiState: UIState;

}
