#!/usr/bin/env node

/**
 * QU Scheduler Icon Embedding Verification Script
 * 
 * This script verifies that the enhanced QU Scheduler icon is properly embedded
 * in the Windows executable and provides detailed diagnostics.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 QU Scheduler Icon Embedding Verification');
console.log('============================================');

const executablePath = path.resolve('out/QU Scheduler-win32-x64/qu-scheduler.exe');
const iconPath = path.resolve('assets/icons/icon.ico');

function checkFileExists(filePath, description) {
    if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        console.log(`✅ ${description}: Found (${(stats.size / 1024).toFixed(1)} KB)`);
        return true;
    } else {
        console.log(`❌ ${description}: Not found`);
        return false;
    }
}

function verifyIconEmbedding() {
    console.log('\n🔍 Checking file existence...');
    
    const executableExists = checkFileExists(executablePath, 'Executable');
    const iconExists = checkFileExists(iconPath, 'Icon file');
    
    if (!executableExists || !iconExists) {
        console.log('\n❌ Required files missing. Run packaging first.');
        process.exit(1);
    }
    
    console.log('\n🔍 Verifying icon embedding...');
    
    // Method 1: Try to extract icon using rcedit
    try {
        console.log('📋 Method 1: Using rcedit to verify icon...');
        const tempIconPath = path.join(__dirname, 'temp-extracted-icon.ico');
        
        try {
            execSync(`npx rcedit "${executablePath}" --get-icon "${tempIconPath}"`, { 
                stdio: 'pipe' 
            });
            
            if (fs.existsSync(tempIconPath)) {
                const extractedStats = fs.statSync(tempIconPath);
                console.log(`✅ Icon successfully extracted (${extractedStats.size} bytes)`);
                
                // Compare with original
                const originalStats = fs.statSync(iconPath);
                if (extractedStats.size === originalStats.size) {
                    console.log('✅ Extracted icon size matches original');
                } else {
                    console.log(`⚠️  Size difference: Original ${originalStats.size}b vs Extracted ${extractedStats.size}b`);
                }
                
                // Clean up
                fs.unlinkSync(tempIconPath);
                return true;
            }
        } catch (error) {
            console.log('⚠️  rcedit icon extraction failed');
        }
    } catch (error) {
        console.log('⚠️  rcedit not available or failed');
    }
    
    // Method 2: Check executable size (embedded icon should increase size)
    try {
        console.log('\n📋 Method 2: Checking executable size...');
        const execStats = fs.statSync(executablePath);
        const execSizeMB = (execStats.size / (1024 * 1024)).toFixed(1);
        console.log(`📊 Executable size: ${execSizeMB} MB`);
        
        // A typical Electron executable with embedded icon should be > 100MB
        if (execStats.size > 100 * 1024 * 1024) {
            console.log('✅ Executable size suggests icon may be embedded');
        } else {
            console.log('⚠️  Executable size seems small for embedded icon');
        }
    } catch (error) {
        console.log('❌ Could not check executable size');
    }
    
    // Method 3: Use PowerShell to check icon resources (Windows only)
    if (process.platform === 'win32') {
        try {
            console.log('\n📋 Method 3: Using PowerShell to check icon resources...');
            
            const powershellScript = `
                Add-Type -AssemblyName System.Drawing
                try {
                    $icon = [System.Drawing.Icon]::ExtractAssociatedIcon('${executablePath.replace(/\\/g, '\\\\')}')
                    if ($icon) {
                        Write-Host "Icon Width: $($icon.Width)"
                        Write-Host "Icon Height: $($icon.Height)"
                        Write-Host "Icon Handle: $($icon.Handle)"
                        $icon.Dispose()
                        Write-Host "SUCCESS: Icon extracted successfully"
                    } else {
                        Write-Host "FAILED: No icon found"
                    }
                } catch {
                    Write-Host "ERROR: $($_.Exception.Message)"
                }
            `;
            
            const result = execSync(`powershell -Command "${powershellScript}"`, { 
                encoding: 'utf8',
                stdio: 'pipe'
            });
            
            if (result.includes('SUCCESS')) {
                console.log('✅ PowerShell icon extraction successful');
                console.log(result.trim());
            } else {
                console.log('❌ PowerShell icon extraction failed');
                console.log(result.trim());
            }
        } catch (error) {
            console.log('⚠️  PowerShell icon check failed:', error.message);
        }
    }
    
    return false;
}

function provideDiagnostics() {
    console.log('\n🔧 Diagnostics and Recommendations:');
    console.log('=====================================');
    
    console.log('\n📋 Icon Embedding Status:');
    console.log('   • If icon extraction succeeded: Icon is properly embedded');
    console.log('   • If icon extraction failed: Icon embedding needs to be fixed');
    
    console.log('\n🔧 Troubleshooting Steps:');
    console.log('   1. Run: npm run package (to rebuild executable)');
    console.log('   2. Run: npm run embed:icon (to manually embed icon)');
    console.log('   3. Run: npm run verify:icons (to verify embedding)');
    console.log('   4. Clear Windows icon cache: npm run clear:icon-cache');
    
    console.log('\n📁 Expected File Locations:');
    console.log(`   • Executable: ${executablePath}`);
    console.log(`   • Icon: ${iconPath}`);
    
    console.log('\n🎯 Success Criteria:');
    console.log('   • Icon extraction succeeds with rcedit');
    console.log('   • PowerShell can extract icon from executable');
    console.log('   • Desktop shortcut shows enhanced QU Scheduler icon');
    console.log('   • Taskbar shows enhanced QU Scheduler icon when running');
}

// Run verification
try {
    const success = verifyIconEmbedding();
    provideDiagnostics();
    
    if (success) {
        console.log('\n🎉 VERIFICATION SUCCESSFUL: Icon is properly embedded!');
        process.exit(0);
    } else {
        console.log('\n⚠️  VERIFICATION INCOMPLETE: Icon embedding may need attention');
        process.exit(1);
    }
} catch (error) {
    console.error('\n❌ VERIFICATION FAILED:', error.message);
    provideDiagnostics();
    process.exit(1);
}
