import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Chip, LinearProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Button, TextField } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import { useAppContext } from '../../context/AppContext';
import { AutoSession } from '../../utils/autoScheduling';

interface LecturerScheduleInfo {
  id: string;
  name: string;
  totalSessions: number;
  totalHours: number;
  maxLoad: number;
  loadPercentage: number;
  teachingDays: string[];
  maxTeachingDays: number;
  maxGapBetweenPeriods: number;
  maxGapFound: number;
  gapsByDay: Record<string, number[]>;
  hasExcessiveGap: boolean;
  hasExcessiveDays: boolean;
  hasExcessiveLoad: boolean;
  scheduleByDay: Record<string, number[]>;
}

interface LecturerAnalysisTabProps {
  currentSemester: string;
}

const LecturerAnalysisTab: React.FC<LecturerAnalysisTabProps> = ({ currentSemester }) => {
  const { sessions, lecturers } = useAppContext();
  const [lecturerSchedules, setLecturerSchedules] = useState<LecturerScheduleInfo[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'load' | 'days' | 'gaps'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    const currentSessions = sessions[currentSemester as keyof typeof sessions];
    if (!currentSessions || !lecturers) return;

    // Update refresh timestamp
    setLastRefresh(new Date());

    // Debug logging
    console.log(`👨‍🏫 Lecturer Analysis Refresh:`);
    console.log(`   Current semester: ${currentSemester}`);
    console.log(`   Total sessions: ${currentSessions.length}`);
    console.log(`   Total lecturers: ${lecturers.length}`);

    // Convert app sessions to auto sessions format for analysis
    // Create one auto session for each period in the session range
    const autoSessions: AutoSession[] = [];

    currentSessions.forEach((session: {
      id: string;
      sectionId: string;
      day: string;
      startPeriod: number;
      endPeriod: number;
      lecturerId?: string;
      courseCode?: string;
      courseType?: string;
      academicLevel?: string;
      gender?: string;
      isAutoGenerated?: boolean;
    }) => {
      // Determine if this is a long day (Monday or Wednesday)
      const isLongDay = ['Monday', 'Wednesday'].includes(session.day);

      // For each period in the range, create a separate auto session
      for (let period = session.startPeriod; period <= session.endPeriod; period++) {
        // Skip invalid periods for the day type
        // Long days: periods 1-8
        // Regular days: periods 1-12
        if ((isLongDay && period > 8) || (!isLongDay && period > 12)) {
          continue;
        }

        autoSessions.push({
          id: `${session.id}-${period}`,
          sectionId: session.sectionId,
          courseCode: session.courseCode || '',
          courseType: (session.courseType || 'Theory') as 'Theory' | 'Lab',
          academicLevel: session.academicLevel || 'unknown',
          gender: session.gender || 'M',
          lecturerId: session.lecturerId || '',
          day: session.day,
          period: period,
          isAutoGenerated: session.isAutoGenerated || false,
          isLongDay: isLongDay
        });
      }
    });

    // Analyze lecturer schedules
    const scheduleInfo: LecturerScheduleInfo[] = [];

    lecturers.forEach(lecturer => {
      // Get all sessions for this lecturer
      const lecturerSessions = autoSessions.filter(session => session.lecturerId === lecturer.id);

      if (lecturerSessions.length === 0) return; // Skip lecturers with no sessions

      // Calculate total hours
      let totalHours = 0;
      lecturerSessions.forEach(session => {
        const isLongDay = ['Mon', 'Wed'].includes(session.day);
        totalHours += isLongDay ? 1.5 : 1.0;
      });

      // Get teaching days
      const teachingDays = [...new Set(lecturerSessions.map(session => session.day))];

      // Get max teaching days
      const maxTeachingDays = lecturer.maxTeachingDaysPerWeek || 5;

      // Get max gap between periods
      const maxGapBetweenPeriods = lecturer.maxGapBetweenPeriods || 2;

      // Calculate empty hours by day
      const gapsByDay: Record<string, number[]> = {};
      let maxGapFound = 0;

      teachingDays.forEach(day => {
        // Get all sessions for this lecturer on this day
        const daySessions = lecturerSessions.filter(session => session.day === day);

        // If there's only one session on this day, there's no empty hours
        if (daySessions.length <= 1) {
          gapsByDay[day] = [];
          return; // Skip to the next day
        }

        // Sort sessions by period
        const sortedSessions = [...daySessions].sort((a, b) => a.period - b.period);

        // Get first and last session
        const firstSession = sortedSessions[0];
        const lastSession = sortedSessions[sortedSessions.length - 1];

        // Check if this is a long day (Monday or Wednesday)
        const isLongDay = ['Monday', 'Wednesday'].includes(day);

        // Create an array of all periods between first and last session
        const allPeriods = [];
        for (let p = firstSession.period; p <= lastSession.period; p++) {
          allPeriods.push(p);
        }

        // Create an array of all periods occupied by sessions
        const occupiedPeriods: number[] = [];
        for (const session of sortedSessions) {
          occupiedPeriods.push(session.period);
        }

        // Find empty periods (periods that are in allPeriods but not in occupiedPeriods)
        const emptyPeriods = allPeriods.filter(p => !occupiedPeriods.includes(p));

        // For long days, remove periods 5 and 6 from empty periods if they exist
        let adjustedEmptyPeriods = [...emptyPeriods];
        if (isLongDay) {
          adjustedEmptyPeriods = emptyPeriods.filter(p => p !== 5 && p !== 6);
        }

        // Calculate empty hours based on day type
        let emptyHours = 0;
        if (isLongDay) {
          // On long days, each period is 1.5 hours
          emptyHours = adjustedEmptyPeriods.length * 1.5;
        } else {
          // On regular days, each period is 1 hour
          emptyHours = adjustedEmptyPeriods.length;
        }

        // Silent mode - no detailed period logging

        // Store the empty periods for display
        gapsByDay[day] = adjustedEmptyPeriods;

        // Update max empty hours
        if (emptyHours > maxGapFound) {
          maxGapFound = emptyHours;
        }
      });

      // Create schedule by day for visualization
      const scheduleByDay: Record<string, number[]> = {};
      teachingDays.forEach(day => {
        // Get all periods for this day
        const dayPeriods = lecturerSessions
          .filter(session => session.day === day)
          .map(session => session.period);

        // For long days: Morning is 1-4, Evening is 5-8
        // For regular days: Morning is 1-6, Evening is 7-12
        const isLongDay = ['Monday', 'Wednesday'].includes(day);
        const morningCutoff = isLongDay ? 4 : 6;

        // Sort periods considering day type
        scheduleByDay[day] = dayPeriods.sort((a, b) => {
          // If both periods are in the same time range (both morning or both evening)
          const aIsMorning = a <= morningCutoff;
          const bIsMorning = b <= morningCutoff;

          if (aIsMorning === bIsMorning) {
            return a - b; // Normal sort within the same range
          } else {
            // If one is morning and one is evening, morning comes first
            return aIsMorning ? -1 : 1;
          }
        });
      });

      // Calculate max load
      const maxLoad = (lecturer as { maxSemesterLoad?: number }).maxSemesterLoad || 12;
      const loadPercentage = (totalHours / maxLoad) * 100;

      // Silent mode - no final gap calculation logging

      // Check for rule violations
      const hasExcessiveGap = maxGapFound > maxGapBetweenPeriods;
      const hasExcessiveDays = teachingDays.length > maxTeachingDays;
      const hasExcessiveLoad = totalHours > maxLoad;

      scheduleInfo.push({
        id: lecturer.id,
        name: `${lecturer.firstName} ${lecturer.lastName}`,
        totalSessions: lecturerSessions.length,
        totalHours,
        maxLoad,
        loadPercentage,
        teachingDays,
        maxTeachingDays,
        maxGapBetweenPeriods,
        maxGapFound,
        gapsByDay,
        hasExcessiveGap,
        hasExcessiveDays,
        hasExcessiveLoad,
        scheduleByDay
      });
    });

    setLecturerSchedules(scheduleInfo);

    // Final debug logging
    console.log(`   ✅ Analyzed ${scheduleInfo.length} lecturers with sessions`);
    console.log(`   📊 Analysis completed at: ${new Date().toLocaleTimeString()}`);
  }, [sessions, currentSemester, lecturers]);

  // Sort lecturer schedules
  const sortedLecturerSchedules = [...lecturerSchedules]
    .filter(lecturer => lecturer.name.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      if (sortBy === 'name') {
        return sortDirection === 'asc'
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else if (sortBy === 'load') {
        return sortDirection === 'asc'
          ? a.loadPercentage - b.loadPercentage
          : b.loadPercentage - a.loadPercentage;
      } else if (sortBy === 'days') {
        return sortDirection === 'asc'
          ? a.teachingDays.length - b.teachingDays.length
          : b.teachingDays.length - a.teachingDays.length;
      } else if (sortBy === 'gaps') {
        return sortDirection === 'asc'
          ? a.maxGapFound - b.maxGapFound
          : b.maxGapFound - a.maxGapFound;
      }
      return 0;
    });

  const handleSort = (column: 'name' | 'load' | 'days' | 'gaps') => {
    if (sortBy === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortDirection('asc');
    }
  };

  if (lecturerSchedules.length === 0) {
    return (
      <Box className="p-4">
        <Paper elevation={2} className="p-6 text-center">
          <Typography variant="h6" className="mb-4">
            No lecturer schedules to analyze
          </Typography>
          <Typography variant="body1">
            Run auto-scheduling with lecturer assignment enabled to see lecturer schedule analysis.
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box className="p-4 space-y-6">
      <Paper elevation={2} className="p-4">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h6">Lecturer Schedule Analysis</Typography>
          <Typography variant="caption" className="text-gray-500">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </Typography>
        </div>

        <Box className="mb-4">
          <TextField
            label="Search Lecturers"
            variant="outlined"
            size="small"
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </Box>

        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>
                  <Button
                    onClick={() => handleSort('name')}
                    variant="text"
                    size="small"
                  >
                    Lecturer {sortBy === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </Button>
                </TableCell>
                <TableCell>
                  <Button
                    onClick={() => handleSort('load')}
                    variant="text"
                    size="small"
                  >
                    Load {sortBy === 'load' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </Button>
                </TableCell>
                <TableCell>
                  <Button
                    onClick={() => handleSort('days')}
                    variant="text"
                    size="small"
                  >
                    Days {sortBy === 'days' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </Button>
                </TableCell>
                <TableCell>
                  <Button
                    onClick={() => handleSort('gaps')}
                    variant="text"
                    size="small"
                  >
                    Empty Hrs {sortBy === 'gaps' && (sortDirection === 'asc' ? '↑' : '↓')}
                  </Button>
                </TableCell>
                <TableCell>Schedule</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedLecturerSchedules.map((lecturer) => (
                <TableRow key={lecturer.id}>
                  <TableCell>{lecturer.name}</TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2">
                        {lecturer.totalHours.toFixed(1)}/{lecturer.maxLoad} hrs ({lecturer.loadPercentage.toFixed(0)}%)
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={Math.min(lecturer.loadPercentage, 100)}
                        color={lecturer.hasExcessiveLoad ? "error" : "primary"}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {lecturer.teachingDays.length}/{lecturer.maxTeachingDays} days
                      {lecturer.hasExcessiveDays && (
                        <ErrorIcon fontSize="small" color="error" />
                      )}
                    </Typography>
                    <Typography variant="caption" className="text-gray-600">
                      {lecturer.teachingDays.join(', ')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      Empty hrs: {lecturer.maxGapFound.toFixed(1)}/{lecturer.maxGapBetweenPeriods}
                      {lecturer.hasExcessiveGap && (
                        <ErrorIcon fontSize="small" color="error" />
                      )}
                    </Typography>
                    <Typography variant="caption" className="text-gray-600">
                      {Object.entries(lecturer.gapsByDay)
                        .filter(([_, gaps]) => gaps.length > 0)
                        .map(([day, gaps]) => `${day}:[${gaps.join(',')}]`)
                        .join(' ')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box className="flex flex-col">
                      {Object.entries(lecturer.scheduleByDay).map(([day, periods]) => (
                        <Typography key={day} variant="caption" className="text-gray-600">
                          {day}: {periods.join(', ')}
                        </Typography>
                      ))}
                    </Box>
                  </TableCell>
                  <TableCell>
                    {lecturer.hasExcessiveGap || lecturer.hasExcessiveDays || lecturer.hasExcessiveLoad ? (
                      <Chip
                        icon={<ErrorIcon />}
                        label="Rule Violations"
                        color="error"
                        size="small"
                      />
                    ) : (
                      <Chip
                        icon={<CheckCircleIcon />}
                        label="Compliant"
                        color="success"
                        size="small"
                      />
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default LecturerAnalysisTab;
