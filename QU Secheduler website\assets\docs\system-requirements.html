<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Requirements - QU Scheduler</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            line-height: 1.6;
            color: #343A40;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #F8F9FA;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #8B1538;
            border-bottom: 3px solid #D4AF37;
            padding-bottom: 1rem;
        }
        h2 {
            color: #8B1538;
            margin-top: 2rem;
        }
        .requirement-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        .requirement-table th,
        .requirement-table td {
            border: 1px solid #E9ECEF;
            padding: 0.75rem;
            text-align: left;
        }
        .requirement-table th {
            background: #8B1538;
            color: white;
            font-weight: 600;
        }
        .requirement-table tr:nth-child(even) {
            background: #F8F9FA;
        }
        .note {
            background: #E3F2FD;
            border-left: 4px solid #2196F3;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning {
            background: #FFF3E0;
            border-left: 4px solid #FF9800;
            padding: 1rem;
            margin: 1rem 0;
        }
        .success {
            background: #E8F5E8;
            border-left: 4px solid #4CAF50;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QU Scheduler System Requirements</h1>

        <h2>Minimum System Requirements</h2>
        <table class="requirement-table">
            <thead>
                <tr>
                    <th>Component</th>
                    <th>Minimum Requirement</th>
                    <th>Recommended</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>Operating System</strong></td>
                    <td>Windows 7 (64-bit) SP1</td>
                    <td>Windows 10/11 (64-bit)</td>
                </tr>
                <tr>
                    <td><strong>Processor</strong></td>
                    <td>Intel Core i3 or AMD equivalent</td>
                    <td>Intel Core i5 or AMD equivalent</td>
                </tr>
                <tr>
                    <td><strong>Memory (RAM)</strong></td>
                    <td>4 GB</td>
                    <td>8 GB or more</td>
                </tr>
                <tr>
                    <td><strong>Storage Space</strong></td>
                    <td>500 MB available space</td>
                    <td>1 GB available space</td>
                </tr>
                <tr>
                    <td><strong>Display</strong></td>
                    <td>1024x768 resolution</td>
                    <td>1920x1080 resolution</td>
                </tr>
                <tr>
                    <td><strong>Graphics</strong></td>
                    <td>DirectX 9 compatible</td>
                    <td>DirectX 11 compatible</td>
                </tr>
                <tr>
                    <td><strong>Network</strong></td>
                    <td>Not required (offline operation)</td>
                    <td>Not required (offline operation)</td>
                </tr>
            </tbody>
        </table>

        <h2>Software Dependencies</h2>
        <div class="success">
            <strong>No Additional Software Required:</strong> QU Scheduler is a self-contained application that includes all necessary components.
        </div>

        <ul>
            <li><strong>Runtime Environment:</strong> Built-in Electron runtime (included)</li>
            <li><strong>PDF Generation:</strong> Built-in PDF engine (included)</li>
            <li><strong>Arabic Font Support:</strong> Uses system fonts (Windows built-in)</li>
            <li><strong>Database:</strong> Local storage (no external database required)</li>
        </ul>

        <h2>Performance Considerations</h2>

        <h3>For Small Departments (< 50 courses)</h3>
        <ul>
            <li>Minimum system requirements are sufficient</li>
            <li>Auto-scheduling completes in seconds</li>
            <li>PDF generation is near-instantaneous</li>
        </ul>

        <h3>For Large Departments (> 100 courses)</h3>
        <ul>
            <li>Recommended system specifications advised</li>
            <li>Auto-scheduling may take 1-2 minutes</li>
            <li>Additional RAM improves performance</li>
        </ul>

        <h2>Network and Security</h2>
        <div class="success">
            <strong>Offline-First Design:</strong> QU Scheduler operates completely offline and does not require internet connectivity.
        </div>

        <h3>Security Features</h3>
        <ul>
            <li><strong>No External Dependencies:</strong> All assets bundled locally</li>
            <li><strong>Local Data Storage:</strong> All data stored on local machine</li>
            <li><strong>No Network Communication:</strong> Application does not transmit data</li>
            <li><strong>Enterprise Security:</strong> Suitable for air-gapped environments</li>
        </ul>

        <h2>Installation Requirements</h2>

        <h3>Installer Version</h3>
        <div class="warning">
            <strong>Administrator Privileges Required:</strong> The installer version requires administrator rights for installation.
        </div>
        <ul>
            <li>Administrator access for installation</li>
            <li>Write permissions to Program Files</li>
            <li>Registry write access for file associations</li>
        </ul>

        <h2>Compatibility Notes</h2>

        <h3>Windows Versions</h3>
        <ul>
            <li><strong>Windows 11:</strong> Fully supported and tested</li>
            <li><strong>Windows 10:</strong> Fully supported and tested</li>
            <li><strong>Windows 8.1:</strong> Supported with latest updates</li>
            <li><strong>Windows 7:</strong> Supported with Service Pack 1 and latest updates</li>
        </ul>

        <h3>Architecture</h3>
        <div class="warning">
            <strong>64-bit Only:</strong> QU Scheduler requires a 64-bit Windows installation. 32-bit systems are not supported.
        </div>

        <h2>Troubleshooting</h2>

        <h3>Common Issues</h3>
        <ul>
            <li><strong>Application won't start:</strong> Ensure Windows is up to date and antivirus is not blocking the application</li>
            <li><strong>Arabic text not displaying:</strong> Verify Arabic language support is installed in Windows</li>
            <li><strong>PDF export issues:</strong> Check available disk space and write permissions</li>
            <li><strong>Performance issues:</strong> Close other applications and ensure sufficient RAM</li>
        </ul>

        <h2>Support Information</h2>
        <div class="note">
            <strong>Technical Support:</strong> For assistance with system requirements or compatibility issues, contact <a href="mailto:<EMAIL>"><EMAIL></a>
        </div>

        <hr style="margin: 2rem 0; border: none; border-top: 1px solid #E9ECEF;">

        <p style="text-align: center; color: #6C757D; font-size: 0.9rem;">
            <strong>QU Scheduler v1.0.0</strong><br>
            Qatar University | Developed by Prof Ayman Saleh<br>
            Last Updated: August 2025
        </p>
    </div>
</body>
</html>
