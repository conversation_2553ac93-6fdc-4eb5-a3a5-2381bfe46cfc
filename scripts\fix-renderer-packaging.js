#!/usr/bin/env node

/**
 * Fix Renderer Packaging Script
 * 
 * This script fixes the issue where Electron Forge with Vite doesn't properly
 * copy renderer files to the packaged application. It manually copies the
 * built renderer files from the dist directory to the correct location in
 * the packaged app.
 * 
 * Usage: node scripts/fix-renderer-packaging.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing renderer packaging...');

// Paths
const distPath = path.join(__dirname, '..', 'dist');
const packagedAppPath = path.join(__dirname, '..', 'out', 'QU Scheduler-win32-x64');
const rendererPath = path.join(packagedAppPath, 'renderer', 'main_window');

// Check if packaged app exists
if (!fs.existsSync(packagedAppPath)) {
  console.error('❌ Packaged application not found. Please run "npm run package" first.');
  process.exit(1);
}

// Check if dist directory exists
if (!fs.existsSync(distPath)) {
  console.error('❌ Dist directory not found. Please run the build process first.');
  process.exit(1);
}

// Create renderer directory structure
console.log('📁 Creating renderer directory structure...');
const rendererDir = path.join(packagedAppPath, 'renderer');
const mainWindowDir = path.join(rendererDir, 'main_window');

if (!fs.existsSync(rendererDir)) {
  fs.mkdirSync(rendererDir, { recursive: true });
}

if (!fs.existsSync(mainWindowDir)) {
  fs.mkdirSync(mainWindowDir, { recursive: true });
}

// Copy renderer files
console.log('📋 Copying renderer files...');

function copyRecursive(src, dest) {
  const stats = fs.statSync(src);
  
  if (stats.isDirectory()) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    files.forEach(file => {
      copyRecursive(path.join(src, file), path.join(dest, file));
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

try {
  // Copy all files from dist to renderer/main_window
  const distFiles = fs.readdirSync(distPath);
  
  distFiles.forEach(file => {
    const srcPath = path.join(distPath, file);
    const destPath = path.join(mainWindowDir, file);
    
    console.log(`  📄 Copying ${file}...`);
    copyRecursive(srcPath, destPath);
  });
  
  console.log('✅ Renderer files copied successfully!');
  
  // Verify index.html exists
  const indexPath = path.join(mainWindowDir, 'index.html');
  if (fs.existsSync(indexPath)) {
    console.log('✅ index.html found in renderer directory');
  } else {
    console.error('❌ index.html not found in renderer directory');
    process.exit(1);
  }
  
  console.log('🎉 Renderer packaging fix complete!');
  console.log(`📁 Renderer files location: ${mainWindowDir}`);
  
} catch (error) {
  console.error('❌ Error copying renderer files:', error.message);
  process.exit(1);
}
