import React, { useState, useEffect } from 'react';
import { useDragDrop } from '../../context/DragDropContext';
import { Section } from '../../types/models';
import { Tooltip } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import ErrorIcon from '@mui/icons-material/Error';
import CloseIcon from '@mui/icons-material/Close';
import ManIcon from '@mui/icons-material/Man';
import WomanIcon from '@mui/icons-material/Woman';
import AutoScheduleButton from '../AutoScheduleButton';
import { useAppContext } from '../../context/AppContext';
import { Session as RuleSession } from '../../utils/ruleValidation';
import { AppSession } from '../../utils/autoScheduling';
import { useRuleSystemStore } from '../../store/ruleSystem';

interface DraggableSectionCardProps {
  section: Section;
  courseCode: string;
  courseColor: string;
  onDelete: (sectionId: string) => void;
  courseType?: 'Theory' | 'Lab';
  contactHours?: number;
}

/**
 * DraggableSectionCard component that makes a section card draggable
 * Used in the CoursesPanel to allow dragging sections to the timetable
 */
const DraggableSectionCard: React.FC<DraggableSectionCardProps> = ({
  section,
  courseCode,
  courseColor,
  onDelete,
  courseType = 'Theory',
  contactHours = 3
}) => {
  // Get drag-drop context
  const { setDragItem, isSectionFullyScheduled } = useDragDrop();

  // Get app context for sessions and adding sessions
  const {
    sessions,
    setSessions,
    updateSection,
    currentSemester,
    setActiveTab
  } = useAppContext();

  // Get rule system state to track rule changes
  const rules = useRuleSystemStore(state => state.rules);

  // State to force re-render when rules change
  const [, setRuleVersion] = useState(0);

  // Effect to re-render when rules change
  useEffect(() => {
    // Increment the rule version to force re-render
    setRuleVersion(prev => prev + 1);
  }, [rules]);

  // Listen for rule changes from the RuleSystemModal
  useEffect(() => {
    const handleRulesChanged = () => {
      // Force re-render when rules change
      setRuleVersion(prev => prev + 1);
    };

    // Add event listener
    window.addEventListener('rulesChanged', handleRulesChanged);

    // Clean up
    return () => {
      window.removeEventListener('rulesChanged', handleRulesChanged);
    };
  }, []);

  // Check if section is fully scheduled
  const isFullyScheduled = isSectionFullyScheduled(section.id);

  // Get status color for section based on scheduled hours
  const getSectionStatusColor = (scheduledHours: number, totalHours: number) => {
    if (scheduledHours > totalHours) return 'bg-red-100 text-red-800 border-red-300 dark:bg-red-900/40 dark:text-red-200 dark:border-red-800 shadow-sm';
    if (scheduledHours === totalHours) return 'bg-green-100 text-green-800 border-green-300 dark:bg-green-900/40 dark:text-green-200 dark:border-green-800 shadow-sm';
    return 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/40 dark:text-orange-200 dark:border-orange-800 shadow-sm';
  };

  // Get status icon for section based on scheduled hours
  const getSectionStatusIcon = (scheduledHours: number, totalHours: number) => {
    if (scheduledHours > totalHours) return <ErrorIcon fontSize="small" className="text-red-500 dark:text-red-400" />;
    if (scheduledHours === totalHours) return <CheckCircleIcon fontSize="small" className="text-green-500 dark:text-green-400" />;
    return <HourglassEmptyIcon fontSize="small" className="text-orange-500 dark:text-orange-400" />;
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    // Don't allow dragging if section is fully scheduled
    if (isFullyScheduled) {
      e.preventDefault();
      return;
    }

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'SECTION',
      id: section.id
    }));

    // Set drag image (optional)
    const dragImage = document.createElement('div');
    dragImage.className = 'p-2 rounded-md shadow-md';
    dragImage.style.backgroundColor = courseColor + '40';
    dragImage.style.width = '100px';
    dragImage.style.height = '40px';
    dragImage.textContent = `${courseCode}-${section.sectionNumber}`;
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 50, 20);
    setTimeout(() => document.body.removeChild(dragImage), 0);

    // Update drag context
    setDragItem({
      type: 'SECTION',
      id: section.id,
      data: section
    });
  };

  // Handle drag end
  const handleDragEnd = () => {
    // Clear drag context
    setDragItem(null);
  };

  // Get app context for sections and courses
  const appContext = useAppContext();

  // Convert app sessions to rule sessions for validation
  const convertSessionsToRuleSessions = (): RuleSession[] => {

    // Get all sections and courses for the current semester from the app context
    const allSections = appContext.sections[currentSemester];
    const allCourses = appContext.courses[currentSemester];

    // Create a map of section ID to course info for quick lookup
    const sectionToCourseMap = new Map();

    // Populate the map with all sections and their corresponding course info
    allSections.forEach(sec => {
      const course = allCourses.find(c => c.id === sec.courseId);
      if (course) {
        sectionToCourseMap.set(sec.id, {
          courseCode: course.courseCode,
          courseType: course.courseType,
          gender: sec.gender
        });
      }
    });



    // Convert all sessions to rule sessions
    return appContext.sessions[currentSemester].map(session => {
      // Get course info from the map
      const courseInfo = sectionToCourseMap.get(session.sectionId);

      // Default values if course info is not found
      let sessionCourseCode = '';
      let sessionCourseType: 'Theory' | 'Lab' = 'Theory';
      let sessionGender: 'M' | 'F' = 'M';

      if (courseInfo) {
        sessionCourseCode = courseInfo.courseCode;
        sessionCourseType = courseInfo.courseType;
        sessionGender = courseInfo.gender;
      } else if (session.sectionId === section.id) {
        // If this is our current section, use the props passed to this component
        sessionCourseCode = courseCode;
        sessionCourseType = courseType;
        sessionGender = section.gender;
      }

      // Find the academic level for this course
      const academicLevel = sessionCourseCode
        ? getAcademicLevelFromCourseCode(sessionCourseCode)
        : '';



      return {
        id: session.id,
        day: session.day,
        period: session.startPeriod,
        startPeriod: session.startPeriod,
        endPeriod: session.endPeriod,
        sectionId: session.sectionId,
        courseCode: sessionCourseCode,
        courseType: sessionCourseType,
        academicLevel,
        gender: sessionGender,
        lecturerId: session.lecturerId
      };
    });
  };

  // Helper function to get academic level from course code
  const getAcademicLevelFromCourseCode = (code: string): string => {
    const numericPart = code.match(/\d+/)?.[0] || '';

    if (numericPart.length >= 3) {
      const firstTwoDigits = parseInt(numericPart.substring(0, 2));

      if (firstTwoDigits >= 10 && firstTwoDigits <= 19) return '1st-year';
      if (firstTwoDigits >= 20 && firstTwoDigits <= 29) return '2nd-year';
      if (firstTwoDigits >= 30 && firstTwoDigits <= 39) return '3rd-year';
      if (firstTwoDigits >= 40 && firstTwoDigits <= 49) return '4th-year';
      if (firstTwoDigits >= 50 && firstTwoDigits <= 59) return 'diploma';
      if (firstTwoDigits >= 60 && firstTwoDigits <= 69) return 'masters';
      if (firstTwoDigits >= 70 && firstTwoDigits <= 89) return 'phd';
    }

    return 'unknown';
  };

  // Handle successful auto-scheduling
  const handleAutoScheduleSuccess = (appSessions: AppSession[]) => {
    // Check if any sessions were returned
    if (appSessions.length === 0) {
      alert('No available timeslots found for this section. Try adjusting the maximum sessions per timeslot rule.');
      return;
    }

    // Get the rule system state
    const ruleSystemState = useRuleSystemStore.getState();
    const { maxSessionsPerTimeslot, userDefinedBreaks } = ruleSystemState;



    // Check if any of the sessions would exceed the maximum allowed per timeslot
    // or if they are scheduled in user-defined breaks
    const existingSessions = sessions[currentSemester];
    const sessionsToAdd: AppSession[] = [];

    for (const newSession of appSessions) {
      const day = newSession.day;
      const period = newSession.startPeriod;

      // Convert day format to short format (e.g., "Monday" to "Mon")
      const shortDay = day.substring(0, 3);
      const timeslotKey = `${day}-${period}`;
      const shortTimeslotKey = `${shortDay}-${period}`;

      // Check if this timeslot is a user-defined break
      const isUserDefinedBreak = userDefinedBreaks.includes(shortTimeslotKey);
      if (isUserDefinedBreak) {
        continue;
      }

      const maxAllowed = maxSessionsPerTimeslot[timeslotKey] || maxSessionsPerTimeslot[shortTimeslotKey] || 0;

      // Count existing undergraduate theory sessions in this timeslot
      const undergraduateSessionsInTimeslot = existingSessions.filter(
        s => s.day === day &&
             s.startPeriod === period &&
             !s.sectionId.includes('dummy') && // Exclude dummy sessions
             s.sectionId !== section.id // Exclude the current section
      ).length;

      console.log(`Checking timeslot ${timeslotKey} for new session: ${undergraduateSessionsInTimeslot}/${maxAllowed} sessions`);

      // If adding this session would exceed the maximum, skip it
      if (maxAllowed > 0 && undergraduateSessionsInTimeslot >= maxAllowed) {
        console.log(`Skipping session in timeslot ${timeslotKey} which is at capacity: ${undergraduateSessionsInTimeslot}/${maxAllowed}`);
        continue;
      }

      // Otherwise, add it to the list of sessions to add
      sessionsToAdd.push(newSession);
    }

    // If no sessions can be added, show an error
    if (sessionsToAdd.length === 0) {
      alert('No available timeslots found for this section. Try adjusting the maximum sessions per timeslot rule.');
      return;
    }

    // First, reset the section's scheduled hours to 0
    // This ensures we don't double-count when adding new sessions
    const resetSection = { ...section, scheduledHours: 0 };
    updateSection(resetSection, currentSemester);

    // Add only the filtered sessions
    setSessions(prev => {
      // Sort sessions by period (morning first, then evening)
      const sortedSessions = [...sessionsToAdd].sort((a, b) => {
        // First sort by day (Sun, Mon, Tue, Wed, Thu)
        const dayOrder = { 'Sunday': 0, 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4 };
        const dayComparison =
          (dayOrder[a.day as keyof typeof dayOrder] || 0) -
          (dayOrder[b.day as keyof typeof dayOrder] || 0);

        if (dayComparison !== 0) {
          return dayComparison;
        }

        // Then sort by period (morning first, then evening)
        return a.startPeriod - b.startPeriod;
      });

      console.log(`Sorted sessions by period (morning first):`);
      sortedSessions.forEach((session, idx) => {
        console.log(`  ${idx+1}. Day: ${session.day}, Period: ${session.startPeriod}`);
      });

      const newSessions = sortedSessions.map(session => ({
        id: session.id,
        sectionId: session.sectionId,
        lecturerId: session.lecturerId,
        lecturerIds: session.lecturerIds || [],
        day: session.day,
        startPeriod: session.startPeriod,
        endPeriod: session.endPeriod,
        viewType: session.viewType,
        timeOfDay: session.timeOfDay,
        isAutoGenerated: true
      }));

      const updatedSessions = {
        ...prev,
        [currentSemester]: [...prev[currentSemester], ...newSessions]
      };

      // Save to store
      window.electronAPI.store.set('sessions', updatedSessions);

      return updatedSessions;
    });

    // If sessions were created successfully
    if (sessionsToAdd.length > 0) {
      // Switch to all day view (activeTab = 2) to ensure all sessions are visible
      setActiveTab(2);

      // Sort sessions by period (morning first, then evening)
      const sortedSessions = [...sessionsToAdd].sort((a, b) => {
        // First sort by day (Sun, Mon, Tue, Wed, Thu)
        const dayOrder = { 'Sunday': 0, 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4 };
        const dayComparison =
          (dayOrder[a.day as keyof typeof dayOrder] || 0) -
          (dayOrder[b.day as keyof typeof dayOrder] || 0);

        if (dayComparison !== 0) {
          return dayComparison;
        }

        // Then sort by period (morning first, then evening)
        return a.startPeriod - b.startPeriod;
      });

      // Create a custom event to focus on the newly created sessions
      const firstSession = sortedSessions[0];
      window.dispatchEvent(new CustomEvent('focusOnSession', {
        detail: {
          day: firstSession.day,
          period: firstSession.startPeriod,
          sessions: sortedSessions.map(s => s.id)
        }
      }));

      // Force a recalculation of the section's scheduled hours
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('recalculateSectionHours', {
          detail: {
            sectionId: section.id,
            semester: currentSemester
          }
        }));
      }, 100); // Small delay to ensure all sessions are added first
    }
  };

  // Handle auto-scheduling error
  const handleAutoScheduleError = (message: string) => {
    // Show error notification
    alert(`Auto-scheduling failed: ${message}`);
  };

  return (
    <div
      className={`section-card flex justify-between items-center p-1 mb-1 rounded border ${getSectionStatusColor(section.scheduledHours, section.totalHours)} ${isFullyScheduled ? 'opacity-50 cursor-not-allowed' : 'cursor-grab active:cursor-grabbing'}`}
      draggable={!isFullyScheduled}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="flex items-center">
        {/* Gender icon and section number */}
        <span className={`mr-1 ${section.gender === 'M' ? 'text-blue-600 dark:text-blue-400' : 'text-pink-600 dark:text-pink-400'}`}>
          {section.gender === 'M' ? <ManIcon fontSize="small" sx={{ fontSize: '0.85rem' }} /> : <WomanIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />}
        </span>
        <span className="font-medium text-sm">{section.sectionNumber}</span>
      </div>

      <div className="flex items-center">
        {/* Status indicator */}
        <Tooltip title={`${section.scheduledHours}/${section.totalHours} hours scheduled`}>
          <div className="flex items-center mr-1">
            {React.cloneElement(getSectionStatusIcon(section.scheduledHours, section.totalHours), { sx: { fontSize: '0.85rem' } })}
            <span className="ml-0.5 text-xs">
              {section.scheduledHours}/{section.totalHours}
            </span>
          </div>
        </Tooltip>

        {/* Auto-schedule button - only show if not fully scheduled */}
        {!isFullyScheduled && (
          <div className="mr-1">
            <AutoScheduleButton
              section={section}
              courseCode={courseCode}
              courseType={courseType}
              contactHours={contactHours}
              gender={section.gender}
              lecturerId={section.lecturerId || ''}
              existingSessions={convertSessionsToRuleSessions()}
              onScheduleSuccess={handleAutoScheduleSuccess}
              onScheduleError={handleAutoScheduleError}
            />
          </div>
        )}

        {/* Delete button */}
        <Tooltip title="Delete section">
          <div
            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(section.id);
            }}
          >
            <CloseIcon fontSize="small" sx={{ fontSize: '0.85rem' }} />
          </div>
        </Tooltip>
      </div>
    </div>
  );
};

export default DraggableSectionCard;