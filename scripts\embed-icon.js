#!/usr/bin/env node

/**
 * QU Scheduler Icon Embedding Script
 * 
 * This script uses rcedit to directly embed the enhanced icon into the executable
 * after packaging, ensuring the icon is properly displayed in Windows.
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎨 QU Scheduler Icon Embedding Script');
console.log('=====================================');

// Paths
const iconPath = path.join(__dirname, '..', 'assets', 'icons', 'icon.ico');
const executablePath = path.join(__dirname, '..', 'out', 'QU Scheduler-win32-x64', 'qu-scheduler.exe');

// Check if files exist
if (!fs.existsSync(iconPath)) {
    console.error('❌ Icon file not found:', iconPath);
    process.exit(1);
}

if (!fs.existsSync(executablePath)) {
    console.error('❌ Executable not found:', executablePath);
    console.log('ℹ️  Make sure to run "npm run package" first');
    process.exit(1);
}

console.log('✅ Icon file found:', iconPath);
console.log('✅ Executable found:', executablePath);

async function embedIcon() {
    try {
        console.log('\n🚀 Starting enhanced icon embedding process...');

        let rceditSuccess = false;
        // Attempt 1: rcedit (Primary and most reliable method for Electron apps)
        try {
            console.log('\n🎨 Method 1: Using rcedit (Primary)...');
            // Ensure rcedit is available
            try {
                execSync('rcedit --version', { stdio: 'pipe' }); // Check if rcedit is in PATH
                console.log('✅ rcedit is available.');
            } catch (e) {
                console.log('⏳ rcedit not found directly. Attempting to install it globally...');
                try {
                    execSync('npm install -g rcedit', { stdio: 'inherit' });
                    console.log('✅ rcedit installed globally. Attempting to use it directly.');
                    // Verify again after install
                    execSync('rcedit --version', { stdio: 'pipe' });
                    console.log('✅ rcedit is now available directly.');
                } catch (installError) {
                    console.error('❌ Failed to install rcedit globally or make it available in PATH:', installError.message);
                    // Fallback to npx as a last resort, though it failed before
                    console.log('⚠️ Falling back to npx rcedit. This might fail again.');
                    try {
                        execSync('npx rcedit --version', { stdio: 'pipe' });
                        console.log('✅ npx rcedit is available (fallback).');
                    } catch (npxError) {
                        console.error('❌ npx rcedit also not available:', npxError.message);
                        throw new Error('rcedit could not be found or executed via direct call or npx.');
                    }
                }
            }

            // Determine the command to use for rcedit
            let rceditExecCommand = 'rcedit'; // Default to direct call
            try {
                execSync('rcedit --version', { stdio: 'pipe' });
            } catch (e) {
                console.log('⚠️ Direct rcedit call failed, trying npx rcedit as fallback for execution.');
                rceditExecCommand = 'npx rcedit'; // Fallback to npx if direct call still fails
            }

            const rceditCommand = `${rceditExecCommand} "${executablePath}" --set-icon "${iconPath}"`;
            console.log(`🔩 Executing: ${rceditCommand}`);
            execSync(rceditCommand, { stdio: 'inherit' });
            console.log('✅ Icon successfully set using rcedit.');
            rceditSuccess = true;
        } catch (error) {
            console.error('❌ rcedit failed:', error.message);
            console.log('ℹ️  rcedit is the preferred method. If this fails, subsequent methods might also have issues or might not correctly set all icon aspects.');
        }

        // winresourcer section removed as it was causing errors and rcedit is the primary method.
        // If rcedit fails, the script will report it.

        // Verification (Optional but recommended)
        // A simple verification could be trying to extract the icon again, or checking file properties manually.
        // For automated verification, rcedit can be used if it was successful.
        if (rceditSuccess) {
            console.log('\n🔍 Verifying icon embedding...');
            try {
                // Determine the command to use for rcedit verification
                let rceditVerifyExecCommand = 'rcedit'; // Default to direct call
                try {
                    execSync('rcedit --version', { stdio: 'pipe' });
                } catch (e) {
                    console.log('⚠️ Direct rcedit call failed for verification, trying npx rcedit as fallback.');
                    rceditVerifyExecCommand = 'npx rcedit'; // Fallback to npx if direct call still fails
                }

                execSync(`${rceditVerifyExecCommand} "${executablePath}" --get-icon "${executablePath}.extracted.ico"`, { stdio: 'pipe' });
                if (fs.existsSync(`${executablePath}.extracted.ico`)) {
                    console.log('✅ Icon extraction successful. Verification step passed.');
                    fs.unlinkSync(`${executablePath}.extracted.ico`); // Clean up test file
                } else {
                    console.warn('⚠️  Icon extraction seemed to succeed but no file found. Manual verification recommended.');
                }
            } catch (error) {
                console.warn('⚠️  Could not automatically verify icon embedding using rcedit --get-icon:', error.message);
                console.log('   Manual verification of the .exe properties is recommended.');
            }
        } else {
            console.warn('⚠️  Primary embedding method (rcedit) failed. Manual verification of the .exe icon is crucial.');
        }

        console.log('\n🎉 Icon embedding process complete!');
        console.log('-------------------------------------');
        console.log('✅ Ensure `forge.config.ts` also specifies the icon for Electron Packager/Forge.');
        console.log('   This script primarily handles post-packaging embedding for the final .exe.');
        console.log('📋 Next steps:');
        console.log('   1. Re-run the full build process: `npm run package` (if not done or if changes were made).');
        console.log('   2. Then, run this script: `node scripts/embed-icon.js` (if not run as part of a hook).');
        console.log('   3. Build the installer: `node installer/build-nsis.js`.');
        console.log('   4. CRITICAL: Clear Windows icon cache (see `scripts/clear-icon-cache.js` or do it manually).');
        console.log('   5. Test the new installer thoroughly on a clean environment or after clearing cache.');
        console.log('      - Check Desktop Shortcut icon.');
        console.log('      - Check Start Menu icon.');
        console.log('      - Check Taskbar icon (after launching).');
        console.log('      - Check Window Title Bar icon (after launching).');
        console.log('      - Check .exe file properties icon in Explorer.');

    } catch (error) {
        console.error('❌❌❌ FATAL ERROR during icon embedding process:', error.message);
        console.error(error.stack);
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Ensure the executable exists at the specified path (run `npm run package` first).');
        console.log('   2. Verify the icon file (`assets/icons/icon.ico`) is a valid, multi-resolution .ico file.');
        console.log('   3. If permission errors occur, try running the script with administrator privileges.');
        console.log('   4. Ensure `rcedit` is correctly installed and accessible in your PATH or via `npx`.');
        process.exit(1);
    }
}

// Run the async function
embedIcon();
