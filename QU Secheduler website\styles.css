/* QU Scheduler Website Styles */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Qatar University Brand Colors */
    --primary-color: #8B1538;
    --primary-dark: #6B1028;
    --primary-light: #A61E42;
    --secondary-color: #D4AF37;
    --accent-color: #2C5F7C;

    /* Neutral Colors */
    --white: #FFFFFF;
    --light-gray: #F8F9FA;
    --medium-gray: #6C757D;
    --dark-gray: #343A40;
    --black: #000000;

    /* Typography */
    --font-primary: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', system-ui, sans-serif;
    --font-arabic: 'Tajawal', 'Arabic UI Text', 'Geeza Pro', 'Arabic Typesetting', 'Al Bayan', sans-serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Arabic Text Support */
.arabic-text {
    font-family: var(--font-arabic);
    direction: rtl;
    text-align: right;
}

.arabic-text-ltr {
    font-family: var(--font-arabic);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-img {
    height: 40px;
    width: 40px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--dark-gray);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: calc(70px + var(--spacing-lg)) 0 var(--spacing-lg);
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

.highlight {
    color: var(--secondary-color);
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.btn {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid transparent;
    cursor: pointer;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--dark-gray);
}

.btn-primary:hover {
    background: #E6C547;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: var(--transition-slow);
}

.hero-img:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
}

.section-description {
    font-size: 1.1rem;
    color: var(--medium-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Overview */
.features-overview {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.feature-icon svg {
    width: 30px;
    height: 30px;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-sm);
}

.feature-description {
    color: var(--medium-gray);
    line-height: 1.6;
}

/* Screenshots Section */
.screenshots {
    padding: var(--spacing-xxl) 0;
}

.screenshots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.screenshot-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.screenshot-item:hover {
    transform: scale(1.02);
}

.screenshot-img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.screenshot-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: var(--white);
    padding: var(--spacing-lg);
    transform: translateY(100%);
    transition: var(--transition-normal);
}

.screenshot-item:hover .screenshot-overlay {
    transform: translateY(0);
}

.screenshot-overlay h3 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
}

/* Download Section */
.download {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.download-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-xxl);
    align-items: center;
}

.download-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
}

.download-description {
    font-size: 1.1rem;
    color: var(--medium-gray);
    margin-bottom: var(--spacing-xl);
}

.download-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.download-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.checkmark {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.2rem;
}

.download-options {
    display: flex;
    justify-content: center;
}

.download-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    min-width: 280px;
}

.download-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.download-card h3 {
    font-size: 1.3rem;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-xs);
}

.download-size {
    color: var(--medium-gray);
    margin: var(--spacing-sm) 0;
}

.download-btn {
    width: 100%;
    margin-top: var(--spacing-md);
}

/* Support Section */
.support {
    padding: var(--spacing-xxl) 0;
}

.support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.support-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.support-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
}

.support-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto var(--spacing-md);
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.support-icon svg {
    width: 25px;
    height: 25px;
}

.support-card h3 {
    font-size: 1.3rem;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-sm);
}

.support-card p {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
}

.support-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.support-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.footer-logo-img {
    height: 30px;
    width: 30px;
}

.footer-logo-text {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.footer-description {
    color: #B8BCC8;
    line-height: 1.6;
}

.footer-section h4 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.footer-links a {
    color: #B8BCC8;
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid #495057;
    padding-top: var(--spacing-lg);
    text-align: center;
    color: #B8BCC8;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: calc(70px + var(--spacing-xl)) 0 var(--spacing-xl);
    text-align: center;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.page-description {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Feature Detail Sections */
.feature-detail {
    padding: var(--spacing-xxl) 0;
}

.feature-detail.alternate {
    background: var(--light-gray);
}

.feature-detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.feature-detail-text h2 {
    font-size: 2.2rem;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-sm);
}

.feature-detail-text h3 {
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
}

.feature-detail-text p {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

.feature-list {
    list-style: none;
    margin-bottom: var(--spacing-md);
}

.feature-list li {
    padding: var(--spacing-xs) 0;
    color: var(--medium-gray);
    position: relative;
    padding-left: var(--spacing-md);
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: 700;
}

.feature-highlight {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-top: var(--spacing-md);
}

.feature-img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Technical Features */
.technical-features {
    padding: var(--spacing-xxl) 0;
    background: var(--dark-gray);
    color: var(--white);
}

.technical-features .section-title {
    color: var(--white);
}

.technical-features .section-description {
    color: #B8BCC8;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.tech-card {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition-normal);
}

.tech-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.tech-card h3 {
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.tech-card p {
    color: #B8BCC8;
    line-height: 1.6;
}

/* Academic Patterns */
.academic-patterns {
    padding: var(--spacing-xxl) 0;
}

.patterns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.pattern-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition-normal);
}

.pattern-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.pattern-card h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
    font-size: 1.3rem;
}

.pattern-details p {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-xs);
    line-height: 1.6;
}

.pattern-details strong {
    color: var(--primary-color);
}

/* CTA Section */
.cta-section {
    padding: var(--spacing-xxl) 0;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

/* Active Navigation Link */
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* Download Page Styles */
.version-info {
    padding: var(--spacing-xl) 0;
    background: var(--light-gray);
}

.version-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    max-width: 800px;
    margin: 0 auto;
}

.version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.version-header h2 {
    color: var(--dark-gray);
    font-size: 1.8rem;
    margin: 0;
}

.version-badge {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.9rem;
    font-weight: 600;
}

.version-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
}

.version-item {
    color: var(--medium-gray);
}

.version-item strong {
    color: var(--dark-gray);
}

/* Download Options */
.download-options-section {
    padding: var(--spacing-xxl) 0;
}

.download-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.download-option {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.download-option:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.download-header {
    padding: var(--spacing-xl);
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
}

.download-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.download-icon svg {
    width: 30px;
    height: 30px;
}

.download-header h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xs);
}

.download-subtitle {
    opacity: 0.9;
    font-size: 1rem;
}

.download-details {
    padding: var(--spacing-xl);
}

.download-size {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.download-type {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
}

.download-features {
    list-style: none;
    margin-bottom: var(--spacing-lg);
}

.download-features li {
    padding: var(--spacing-xs) 0;
    color: var(--medium-gray);
    position: relative;
    padding-left: var(--spacing-md);
}

.download-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: 700;
}

.download-action {
    padding: var(--spacing-xl);
    background: var(--light-gray);
    text-align: center;
}

.download-note {
    margin-top: var(--spacing-sm);
    font-size: 0.9rem;
    color: var(--medium-gray);
}

/* System Requirements */
.system-requirements {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.requirements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.requirement-category {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.requirement-category h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
    font-size: 1.3rem;
}

.requirement-list {
    list-style: none;
}

.requirement-list li {
    padding: var(--spacing-xs) 0;
    color: var(--medium-gray);
    border-bottom: 1px solid #E9ECEF;
}

.requirement-list li:last-child {
    border-bottom: none;
}

.requirement-list strong {
    color: var(--dark-gray);
}

.additional-requirements {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.additional-requirements h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
    font-size: 1.3rem;
}

.requirement-notes {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.requirement-note {
    padding: var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.requirement-note strong {
    color: var(--primary-color);
    display: block;
    margin-bottom: var(--spacing-xs);
}

/* Installation Instructions */
.installation-instructions {
    padding: var(--spacing-xxl) 0;
}

.instructions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.instruction-set {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.instruction-set h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-md);
    font-size: 1.3rem;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.instruction-list {
    color: var(--medium-gray);
    padding-left: var(--spacing-md);
}

.instruction-list li {
    margin-bottom: var(--spacing-sm);
    line-height: 1.6;
}

/* Security Information */
.security-info {
    padding: var(--spacing-xxl) 0;
    background: var(--dark-gray);
    color: var(--white);
}

.security-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.security-text h2 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: 2rem;
}

.security-text p {
    color: #B8BCC8;
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
}

.security-features {
    list-style: none;
}

.security-features li {
    padding: var(--spacing-xs) 0;
    color: #B8BCC8;
    position: relative;
    padding-left: var(--spacing-md);
}

.security-features li::before {
    content: '🔒';
    position: absolute;
    left: 0;
}

.security-badges {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.security-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 2rem;
}

.badge-text strong {
    display: block;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-xs);
}

.badge-text span {
    color: #B8BCC8;
    font-size: 0.9rem;
}

/* Download Support */
.download-support {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.support-option {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
}

.support-option:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.support-option h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.support-option p {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
}

.support-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.support-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Support Page Styles */
.quick-support {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.quick-support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.support-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.support-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.support-card.urgent {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
}

.support-card.urgent .support-icon {
    background: rgba(255, 255, 255, 0.2);
}

.support-card.urgent h3,
.support-card.urgent p {
    color: var(--white);
}

.support-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.support-icon svg {
    width: 30px;
    height: 30px;
}

.support-card h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.support-card p {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.support-btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.support-btn.btn-primary {
    background: var(--secondary-color);
    color: var(--dark-gray);
}

.support-btn.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.support-card.urgent .support-btn.btn-primary {
    background: var(--white);
    color: var(--primary-color);
}

/* Contact Information */
.contact-info {
    padding: var(--spacing-xxl) 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: start;
}

.contact-details h2 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-xl);
    font-size: 2rem;
}

.contact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid #E9ECEF;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-label {
    font-weight: 600;
    color: var(--dark-gray);
}

.contact-value {
    color: var(--medium-gray);
}

.contact-value a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-value a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Support Form */
.contact-form {
    background: var(--light-gray);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
}

.contact-form h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
}

.support-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: var(--dark-gray);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm);
    border: 2px solid #E9ECEF;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-xxl) 0;
    background: var(--light-gray);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.faq-category {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.faq-category h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.faq-item {
    border-bottom: 1px solid #E9ECEF;
    margin-bottom: var(--spacing-md);
}

.faq-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
    cursor: pointer;
    transition: var(--transition-fast);
}

.faq-question:hover {
    color: var(--primary-color);
}

.faq-question h4 {
    color: var(--dark-gray);
    font-size: 1.1rem;
    margin: 0;
    transition: var(--transition-fast);
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    transition: var(--transition-fast);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer.active {
    max-height: 200px;
}

.faq-answer p {
    color: var(--medium-gray);
    line-height: 1.6;
    padding-bottom: var(--spacing-md);
}

/* Documentation */
.documentation {
    padding: var(--spacing-xxl) 0;
}

.docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.doc-item {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.doc-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.doc-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.doc-item h3 {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-sm);
    font-size: 1.3rem;
}

.doc-item p {
    color: var(--medium-gray);
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.doc-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.doc-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-lg) 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero {
        min-height: 70vh;
        padding: calc(70px + var(--spacing-md)) 0 var(--spacing-md);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .hero-image {
        margin-top: 0;
        order: -1;
    }

    .hero-stats {
        justify-content: center;
    }

    .download-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .screenshots-grid {
        grid-template-columns: 1fr;
    }

    .support-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 2rem;
    }

    .feature-detail-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .feature-detail-text h2 {
        font-size: 1.8rem;
    }

    .tech-grid {
        grid-template-columns: 1fr;
    }

    .patterns-grid {
        grid-template-columns: 1fr;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-buttons .btn {
        width: 100%;
        max-width: 300px;
    }

    /* Download Page Responsive */
    .version-header {
        flex-direction: column;
        text-align: center;
    }

    .download-grid {
        grid-template-columns: 1fr;
    }

    .requirements-grid {
        grid-template-columns: 1fr;
    }

    .instructions-grid {
        grid-template-columns: 1fr;
    }

    .security-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .security-badges {
        align-items: center;
    }

    .support-options {
        grid-template-columns: 1fr;
    }

    /* Support Page Responsive */
    .quick-support-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .contact-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .docs-grid {
        grid-template-columns: 1fr;
    }
}

/* Download Page Specific Styles */
.download-options-section .download-grid {
    display: flex;
    justify-content: center;
}

.download-option {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.download-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.download-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.download-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.download-icon svg {
    width: 40px;
    height: 40px;
}

.download-header h3 {
    font-size: 1.5rem;
    color: var(--dark-gray);
    margin-bottom: var(--spacing-xs);
}

.download-subtitle {
    color: var(--medium-gray);
    font-size: 1rem;
}

.download-details {
    margin-bottom: var(--spacing-lg);
}

.download-size, .download-type {
    text-align: center;
    margin: var(--spacing-sm) 0;
    font-weight: 600;
}

.download-size {
    color: var(--primary-color);
}

.download-type {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.download-features {
    list-style: none;
    margin: var(--spacing-md) 0;
}

.download-features li {
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.download-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: 700;
}

.download-action {
    text-align: center;
}

.download-note {
    margin-top: var(--spacing-sm);
    font-size: 0.9rem;
    color: var(--medium-gray);
}
