import React, { useState, useRef, ChangeEvent } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  Alert,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import AccessibleDialog from '../common/AccessibleDialog';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import * as XLSX from 'xlsx';

// Define the course structure based on the requirements
interface CourseData {
  courseCode: string;
  courseName: string;
  loadHours: number;
  contactHours: number;
  courseType: 'Theory' | 'Lab';
  maleSectionsCount: number;
  femaleSectionsCount: number;
  color?: string;
  academicLevel?: string;
}

// Define props for the modal component
interface CourseImportModalProps {
  open: boolean;
  onClose: () => void;
  onImport: (courses: CourseData[]) => void;
  existingCourseCodes?: string[]; // Array of existing course codes in the app
}



/**
 * CourseImportModal component for importing courses from Excel files
 * Includes validation, intelligent column mapping, and preview
 */
const CourseImportModal: React.FC<CourseImportModalProps> = ({
  open,
  onClose,
  onImport,
  existingCourseCodes = [] // Default to empty array if not provided
}) => {
  // State for file upload and processing
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [parsedData, setParsedData] = useState<CourseData[]>([]);
  const [duplicates, setDuplicates] = useState<string[]>([]);
  const [mappedColumns, setMappedColumns] = useState<Record<string, string>>({});
  const [importError, setImportError] = useState<string | null>(null);
  const [excelHeaders, setExcelHeaders] = useState<string[]>([]);
  const [showManualMapping, setShowManualMapping] = useState(false);
  const [rawData, setRawData] = useState<Record<string, unknown>[]>([]);

  // State for sheet selection
  const [availableSheets, setAvailableSheets] = useState<string[]>([]);
  const [selectedSheet, setSelectedSheet] = useState<string>('');
  const [showSheetSelection, setShowSheetSelection] = useState(false);
  const [workbookData, setWorkbookData] = useState<XLSX.WorkBook | null>(null);

  // Reference to the file input element
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0] || null;
    if (selectedFile) {
      setFile(selectedFile);
      handleFileUpload(selectedFile);
    }
  };

  // Trigger file input click
  const handleChooseFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Process the uploaded Excel file - simplified version
  const handleFileUpload = (selectedFile: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    setImportError(null);

    // Start progress simulation
    const interval = setInterval(() => {
      setUploadProgress(prev => Math.min(prev + 20, 90)); // Faster progress, max 90%
    }, 100);

    // Read the Excel file
    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          throw new Error('Failed to read file');
        }

        // Parse Excel data using ArrayBuffer (modern approach)
        const workbook = XLSX.read(data, { type: 'array' });

        // Store workbook for later use
        setWorkbookData(workbook);

        // Check if there are multiple sheets
        if (workbook.SheetNames.length > 1) {
          // Multiple sheets found - show sheet selection
          setAvailableSheets(workbook.SheetNames);
          setSelectedSheet(workbook.SheetNames[0]); // Default to first sheet
          setShowSheetSelection(true);

          // Complete progress
          clearInterval(interval);
          setUploadProgress(100);
          setIsUploading(false);
          return;
        }

        // Single sheet - proceed with current logic
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];

        // Convert to JSON and normalize in one step
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Complete progress
        clearInterval(interval);
        setUploadProgress(100);

        if (jsonData.length === 0) {
          throw new Error('No data found in the Excel file');
        }

        // Store raw data and headers for potential manual mapping
        setRawData(jsonData as any);
        setExcelHeaders(Object.keys(jsonData[0] as object));

        // Detect columns and map them
        const detectedColumns = detectColumns(jsonData[0] as Record<string, unknown>);
        setMappedColumns(detectedColumns);

        // Check if all required fields are mapped
        const requiredFields = ['courseCode', 'courseName', 'loadHours', 'contactHours', 'courseType', 'maleSectionsCount', 'femaleSectionsCount'];
        const mappedFields = Object.values(detectedColumns);
        const missingFields = requiredFields.filter(field => !mappedFields.includes(field));

        // If there are missing fields, show manual mapping
        if (missingFields.length > 0) {
          setShowManualMapping(true);
          setImportError(`Some required fields could not be automatically mapped: ${missingFields.map(f => getColumnMappingDisplayName(f)).join(', ')}. Please map them manually.`);
        } else {
          // Parse the Excel data
          const parsedData = parseExcelData(jsonData as any, detectedColumns);

          // Log course types for debugging
          console.log('Parsed courses with types:', parsedData.map(course => ({
            courseCode: course.courseCode,
            courseType: course.courseType
          })));

          // Process the data
          const processedData = processCourseData(parsedData);
          setParsedData(processedData.validCourses);

          // Combine both types of duplicates for display
          const allDuplicates = [...processedData.duplicates];

          // Add existing duplicates with a prefix to distinguish them
          if (processedData.existingDuplicates.length > 0) {
            setImportError(`Found ${processedData.existingDuplicates.length} course codes that already exist in the system. These courses will be skipped during import.`);
            allDuplicates.push(...processedData.existingDuplicates);
          }

          setDuplicates(allDuplicates);
        }
      } catch (error) {
        setImportError(`Error parsing Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        clearInterval(interval);
        setUploadProgress(100);
        setIsUploading(false);
      }
    };

    reader.onerror = () => {
      setImportError('Error reading file');
      setIsUploading(false);
      clearInterval(interval);
    };

    // Read as ArrayBuffer (modern approach, replaces deprecated readAsBinaryString)
    reader.readAsArrayBuffer(selectedFile);
  };

  // Detect columns from the Excel file and map them to our data structure
  const detectColumns = (firstRow: Record<string, unknown>): Record<string, string> => {
    const columnMap: Record<string, string> = {};
    const keys = Object.keys(firstRow);

    // Map columns based on header names (case insensitive)
    keys.forEach(key => {
      const lowerKey = key.toLowerCase();

      // Course code detection
      if (lowerKey.includes('code') || (lowerKey.includes('course') && lowerKey.includes('id'))) {
        columnMap[key] = 'courseCode';
      }
      // Course name detection
      else if (lowerKey.includes('name') || lowerKey.includes('title')) {
        columnMap[key] = 'courseName';
      }
      // Load hours detection - also match credit/cridit hours
      else if ((lowerKey.includes('load') && lowerKey.includes('hour')) ||
               (lowerKey.includes('credit') && lowerKey.includes('hour')) ||
               (lowerKey.includes('cridit') && lowerKey.includes('hour')) ||
               lowerKey === 'credit' || lowerKey === 'credits' ||
               lowerKey === 'cridit' || lowerKey === 'cridits') {
        columnMap[key] = 'loadHours';
      }
      // Contact hours detection
      else if (lowerKey.includes('contact') ||
              (lowerKey.startsWith('contact') && (lowerKey.includes('hr') || lowerKey.includes('hour')))) {
        columnMap[key] = 'contactHours';
      }
      // Course type detection - improved to catch more variations
      else if (lowerKey.includes('type') ||
               lowerKey.includes('course type') ||
               lowerKey === 'type' ||
               lowerKey === 'lab/theory' ||
               lowerKey === 'theory/lab' ||
               lowerKey.includes('practical') ||
               lowerKey === 'lab' ||
               lowerKey === 'theory') {
        columnMap[key] = 'courseType';
      }
      // Male sections detection - ensure it's not matching female sections
      else if ((lowerKey.includes('male') && !lowerKey.includes('female') && lowerKey.includes('section')) ||
               (lowerKey.includes('m ') && !lowerKey.includes('f ') && lowerKey.includes('section')) ||
               (lowerKey.startsWith('male') && lowerKey.includes('section')) ||
               (lowerKey === 'male sections')) {
        columnMap[key] = 'maleSectionsCount';
      }
      // Female sections detection - with more specific matching
      else if ((lowerKey.includes('female') && lowerKey.includes('section')) ||
               (lowerKey.includes('f ') && lowerKey.includes('section')) ||
               (lowerKey.startsWith('female') && lowerKey.includes('section')) ||
               (lowerKey === 'female sections')) {
        columnMap[key] = 'femaleSectionsCount';
      }
      // Academic level will be inferred from course code, so we don't need to map it
    });

    return columnMap;
  };

  // Parse Excel data using the mapped columns - simplified version
  const parseExcelData = (data: Record<string, unknown>[], columnMapping: Record<string, string>): CourseData[] => {
    return data.map(row => {
      // Get course code and normalize it in one step (remove spaces and convert to uppercase)
      const courseCode = ((getValueByMappedField(row, columnMapping, 'courseCode') as string) || '').replace(/\s+/g, '').toUpperCase();

      // Infer academic level from course code (e.g., MATH101 is first year, PHYS201 is second year)
      let academicLevel = '';
      if (courseCode) {
        // Extract the first digit after the department code
        const match = courseCode.match(/[A-Z]+([0-9])[0-9]+/);
        if (match && match[1]) {
          const levelDigit = parseInt(match[1]);
          switch(levelDigit) {
            case 1: academicLevel = 'Undergraduate 1st year'; break;
            case 2: academicLevel = 'Undergraduate 2nd year'; break;
            case 3: academicLevel = 'Undergraduate 3rd year'; break;
            case 4: academicLevel = 'Undergraduate 4th year'; break;
            case 5: academicLevel = 'Diploma'; break;
            case 6: case 7: case 8: case 9: academicLevel = 'Master'; break;
            default: academicLevel = 'Unknown';
          }
        }
      }

      // Create course object with normalized values
      // Get course type and normalize it to either 'Theory' or 'Lab'
      let courseType = getValueByMappedField(row, columnMapping, 'courseType') || 'Theory';
      // Normalize course type to match expected values
      if (typeof courseType === 'string') {
        const normalizedType = courseType.trim().toLowerCase();
        if (normalizedType.includes('lab') || normalizedType.includes('practical') || normalizedType === 'l') {
          courseType = 'Lab';
        } else {
          courseType = 'Theory'; // Default to Theory for any other value
        }
      } else {
        courseType = 'Theory'; // Default to Theory if not a string
      }

      return {
        courseCode,
        courseName: (getValueByMappedField(row, columnMapping, 'courseName') as string) || '',
        courseType: courseType as 'Theory' | 'Lab',
        contactHours: Number(getValueByMappedField(row, columnMapping, 'contactHours')) || 3,
        loadHours: Number(getValueByMappedField(row, columnMapping, 'loadHours')) || 3,
        maleSectionsCount: Number(getValueByMappedField(row, columnMapping, 'maleSectionsCount')) || 1,
        femaleSectionsCount: Number(getValueByMappedField(row, columnMapping, 'femaleSectionsCount')) || 1,
        color: (getValueByMappedField(row, columnMapping, 'color') as string) || generateRandomColor(),
        academicLevel
      };
    });
  };

  // Process the parsed data to validate and format - simplified version
  const processCourseData = (courses: CourseData[]): {
    validCourses: CourseData[];
    duplicates: string[];
    existingDuplicates: string[];
  } => {
    const validCourses: CourseData[] = [];
    const seenCodes = new Set<string>();
    const duplicates: string[] = [];
    const existingDuplicates: string[] = [];

    // Create a set of existing course codes (case insensitive)
    // Make sure to normalize all codes to uppercase for consistent comparison
    const existingCodes = new Set<string>(existingCourseCodes.map(code => code.toUpperCase().trim()));

    courses.forEach(course => {
      // Skip entries with missing required fields
      if (!course.courseCode) {
        return;
      }

      // Ensure course code is properly normalized
      const normalizedCourseCode = course.courseCode.toUpperCase().trim();

      // Check for duplicates within the imported data
      if (seenCodes.has(normalizedCourseCode)) {
        duplicates.push(course.courseCode);
        return;
      }

      // Check for duplicates with existing courses in the app
      if (existingCodes.has(normalizedCourseCode)) {
        existingDuplicates.push(course.courseCode);
        return;
      }

      // Only trim courseName since other normalizations are done in parseExcelData
      const formattedCourse: CourseData = {
        ...course,
        courseName: course.courseName.trim(),
        courseCode: normalizedCourseCode // Ensure we store the normalized version
      };

      seenCodes.add(normalizedCourseCode);
      validCourses.push(formattedCourse);
    });

    return { validCourses, duplicates, existingDuplicates };
  };

  // Helper function to get value by mapped field
  const getValueByMappedField = (row: Record<string, unknown>, columnMapping: Record<string, string>, field: string) => {
    const mappedColumn = Object.keys(columnMapping).find(key => columnMapping[key] === field);
    return mappedColumn ? row[mappedColumn] : undefined;
  };

  // Helper function to generate a random color
  const generateRandomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  };

  // Apply manual mapping and parse data - simplified version
  const applyManualMapping = () => {
    // Parse the data with the manual mapping
    const parsedCourses = parseExcelData(rawData, mappedColumns);

    // Log course types for debugging
    console.log('Parsed courses with types:', parsedCourses.map(course => ({
      courseCode: course.courseCode,
      courseType: course.courseType
    })));

    // Process the data (validation, formatting, duplicate checking)
    const processedData = processCourseData(parsedCourses);
    setParsedData(processedData.validCourses);

    // Combine both types of duplicates for display
    const allDuplicates = [...processedData.duplicates];

    // Set appropriate error message based on duplicates found
    if (processedData.existingDuplicates.length > 0) {
      setImportError(`Found ${processedData.existingDuplicates.length} course codes that already exist in the system. These courses will be skipped during import.`);
      allDuplicates.push(...processedData.existingDuplicates);
    } else if (processedData.duplicates.length > 0) {
      setImportError(`Found ${processedData.duplicates.length} duplicate course codes within the imported data. Only the first occurrence of each code will be imported.`);
    } else {
      setImportError(null);
    }

    setDuplicates(allDuplicates);

    // Hide manual mapping
    setShowManualMapping(false);
  };

  // Handle import button click
  const handleImport = () => {
    onImport(parsedData);
    onClose();
  };

  // Get column mapping display name
  const getColumnMappingDisplayName = (key: string) => {
    switch (key) {
      case 'courseCode': return 'Course Code';
      case 'courseName': return 'Course Name';
      case 'loadHours': return 'Load Hours';
      case 'contactHours': return 'Contact Hours';
      case 'courseType': return 'Course Type';
      case 'maleSectionsCount': return 'Male Sections';
      case 'femaleSectionsCount': return 'Female Sections';
      default: return key;
    }
  };

  // Handle sheet selection and process the selected sheet
  const handleSheetSelection = () => {
    if (!workbookData || !selectedSheet) {
      setImportError('Please select a sheet to import');
      return;
    }

    try {
      const worksheet = workbookData.Sheets[selectedSheet];

      if (!worksheet) {
        throw new Error(`Sheet "${selectedSheet}" not found`);
      }

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        throw new Error(`No data found in sheet "${selectedSheet}"`);
      }

      // Store raw data and headers for potential manual mapping
      setRawData(jsonData as any);
      setExcelHeaders(Object.keys(jsonData[0] as object));

      // Detect columns and map them
      const detectedColumns = detectColumns(jsonData[0] as Record<string, unknown>);
      setMappedColumns(detectedColumns);

      // Check if all required fields are mapped
      const requiredFields = ['courseCode', 'courseName', 'loadHours', 'contactHours', 'courseType', 'maleSectionsCount', 'femaleSectionsCount'];
      const mappedFields = Object.values(detectedColumns);
      const missingFields = requiredFields.filter(field => !mappedFields.includes(field));

      // Hide sheet selection
      setShowSheetSelection(false);

      // If there are missing fields, show manual mapping
      if (missingFields.length > 0) {
        setShowManualMapping(true);
        setImportError(`Some required fields could not be automatically mapped: ${missingFields.map(f => getColumnMappingDisplayName(f)).join(', ')}. Please map them manually.`);
      } else {
        // Parse the Excel data
        const parsedData = parseExcelData(jsonData as any, detectedColumns);

        // Log course types for debugging
        console.log('Parsed courses with types:', parsedData.map(course => ({
          courseCode: course.courseCode,
          courseType: course.courseType
        })));

        // Process the data
        const processedData = processCourseData(parsedData);
        setParsedData(processedData.validCourses);

        // Combine both types of duplicates for display
        const allDuplicates = [...processedData.duplicates];

        // Add existing duplicates with a prefix to distinguish them
        if (processedData.existingDuplicates.length > 0) {
          setImportError(`Found ${processedData.existingDuplicates.length} course codes that already exist in the system. These courses will be skipped during import.`);
          allDuplicates.push(...processedData.existingDuplicates);
        }

        setDuplicates(allDuplicates);
      }
    } catch (error) {
      setImportError(`Error processing sheet "${selectedSheet}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Reset the modal state
  const handleReset = () => {
    setFile(null);
    setIsUploading(false);
    setUploadProgress(0);
    setParsedData([]);
    setDuplicates([]);
    setMappedColumns({});
    setImportError(null);
    setAvailableSheets([]);
    setSelectedSheet('');
    setShowSheetSelection(false);
    setWorkbookData(null);
    setRawData([]);
    setExcelHeaders([]);
    setShowManualMapping(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle column mapping change
  const handleColumnMappingChange = (excelColumn: string, ourField: string) => {
    setMappedColumns(prev => {
      const updated = { ...prev };

      // Remove any existing mapping for this Excel column
      Object.keys(updated).forEach(key => {
        if (key === excelColumn) {
          delete updated[key];
        }
      });

      // Add new mapping
      if (ourField) {
        updated[excelColumn] = ourField;
      }

      return updated;
    });
  };

  return (
    <AccessibleDialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            minHeight: '60vh'
          }
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          Import Courses from Excel
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Upload an Excel file with course data to import
        </Typography>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        {!file ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px dashed #ccc',
              borderRadius: 2,
              p: 5,
              minHeight: '200px',
              bgcolor: 'background.paper'
            }}
          >
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              ref={fileInputRef}
              style={{ display: 'none' }}
            />
            <CloudUploadIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Drag & Drop or Select File
            </Typography>
            <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
              Supported formats: .xlsx, .xls
            </Typography>
            <Button
              variant="contained"
              onClick={handleChooseFile}
              startIcon={<CloudUploadIcon />}
            >
              Choose File
            </Button>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {/* Left column - File info and status */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Paper elevation={1} sx={{ p: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  File Information
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Name:</strong> {file.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Size:</strong> {(file.size / 1024).toFixed(2)} KB
                  </Typography>
                </Box>

                {isUploading ? (
                  <Box sx={{ width: '100%', mt: 2 }}>
                    <LinearProgress variant="determinate" value={uploadProgress} />
                    <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                      Processing file... {uploadProgress}%
                    </Typography>
                  </Box>
                ) : importError ? (
                  <Alert severity="error" icon={<ErrorIcon fontSize="inherit" />} sx={{ mt: 2 }}>
                    {importError}
                  </Alert>
                ) : (
                  <Alert severity="success" icon={<CheckCircleIcon fontSize="inherit" />} sx={{ mt: 2 }}>
                    File processed successfully
                  </Alert>
                )}

                <Box sx={{ mt: 3 }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleReset}
                    fullWidth
                  >
                    Upload Different File
                  </Button>
                </Box>
              </Paper>
            </Grid>

            {/* Right column - Sheet selection, manual mapping, or data preview */}
            <Grid size={{ xs: 12, md: 8 }}>
              {showSheetSelection ? (
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Select Sheet to Import
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    This Excel file contains multiple sheets. Please select which sheet you want to import data from.
                  </Typography>

                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      <strong>Available Sheets:</strong>
                    </Typography>
                    <FormControl fullWidth>
                      <InputLabel id="sheet-select-label">Select Sheet</InputLabel>
                      <Select
                        labelId="sheet-select-label"
                        value={selectedSheet}
                        label="Select Sheet"
                        onChange={(e: SelectChangeEvent) => setSelectedSheet(e.target.value)}
                      >
                        {availableSheets.map((sheetName) => (
                          <MenuItem key={sheetName} value={sheetName}>
                            {sheetName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={handleReset}
                    >
                      Choose Different File
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSheetSelection}
                      disabled={!selectedSheet}
                    >
                      Continue with Selected Sheet
                    </Button>
                  </Box>
                </Paper>
              ) : showManualMapping ? (
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Manual Column Mapping
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Please map the Excel columns to the corresponding fields in the application.
                  </Typography>

                  <TableContainer component={Paper} variant="outlined" sx={{ mt: 2, mb: 3 }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow sx={{ bgcolor: 'background.paper' }}>
                          <TableCell><strong>Excel Column</strong></TableCell>
                          <TableCell><strong>Application Field</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {excelHeaders.map((header) => (
                          <TableRow key={header}>
                            <TableCell>{header}</TableCell>
                            <TableCell>
                              <FormControl fullWidth size="small">
                                <Select
                                  value={mappedColumns[header] || ''}
                                  onChange={(e: SelectChangeEvent) =>
                                    handleColumnMappingChange(header, e.target.value)
                                  }
                                  displayEmpty
                                >
                                  <MenuItem value=""><em>Not mapped</em></MenuItem>
                                  <MenuItem value="courseCode">Course Code</MenuItem>
                                  <MenuItem value="courseName">Course Name</MenuItem>
                                  <MenuItem value="loadHours">Load Hours</MenuItem>
                                  <MenuItem value="contactHours">Contact Hours</MenuItem>
                                  <MenuItem value="courseType">Course Type</MenuItem>
                                  <MenuItem value="maleSectionsCount">Male Sections</MenuItem>
                                  <MenuItem value="femaleSectionsCount">Female Sections</MenuItem>
                                </Select>
                              </FormControl>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={applyManualMapping}
                    >
                      Apply Mapping
                    </Button>
                  </Box>
                </Paper>
              ) : (
                <Paper elevation={1} sx={{ p: 2 }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Preview ({parsedData.length} courses)
                  </Typography>

                  {duplicates.length > 0 && (
                    <Alert
                      severity="warning"
                      icon={<InfoIcon fontSize="inherit" />}
                      sx={{ mb: 2 }}
                    >
                      Found {duplicates.length} duplicate course codes (either within the imported data or already existing in the system).
                      Only unique courses that don't already exist in the system will be imported.
                    </Alert>
                  )}

                  <TableContainer sx={{ maxHeight: 300 }}>
                    <Table stickyHeader size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>Code</strong></TableCell>
                          <TableCell><strong>Name</strong></TableCell>
                          <TableCell><strong>Type</strong></TableCell>
                          <TableCell><strong>Hours</strong></TableCell>
                          <TableCell><strong>Sections</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {parsedData.map((course) => (
                          <TableRow key={course.courseCode}>
                            <TableCell>{course.courseCode}</TableCell>
                            <TableCell>{course.courseName}</TableCell>
                            <TableCell>
                              <Chip
                                label={course.courseType}
                                size="small"
                                color={course.courseType === 'Theory' ? 'primary' : 'secondary'}
                                sx={{ fontWeight: 'medium' }}
                              />
                            </TableCell>
                            <TableCell>{course.contactHours}/{course.loadHours}</TableCell>
                            <TableCell>
                              M: {course.maleSectionsCount}, F: {course.femaleSectionsCount}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Column Mapping:</strong>
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                      {Object.entries(mappedColumns).map(([excelCol, ourField]) => (
                        <Chip
                          key={excelCol}
                          label={`${excelCol} → ${getColumnMappingDisplayName(ourField)}`}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                    <Button
                      variant="text"
                      color="primary"
                      size="small"
                      onClick={() => setShowManualMapping(true)}
                      sx={{ mt: 1 }}
                    >
                      Edit Mapping
                    </Button>
                  </Box>
                </Paper>
              )}
            </Grid>
          </Grid>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleImport}
          variant="contained"
          color="primary"
          disabled={parsedData.length === 0 || showManualMapping || showSheetSelection}
        >
          Import {parsedData.length} Courses
        </Button>
      </DialogActions>
    </AccessibleDialog>
  );
};

export default CourseImportModal;
