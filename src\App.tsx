import React, { useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import Header from './components/Header';
import TimetableCanvas from './components/TimetableCanvas';
import CoursesPanel from './components/CoursesPanel';
import LecturersPanel from './components/LecturersPanel';
import { AppProvider } from './context/AppContext';
import { DragDropProvider } from './context/DragDropContext';

import { IconButton, Tooltip } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import PersonIcon from '@mui/icons-material/Person';
import { ToastContainer } from 'react-toastify';
import { UIState, getUIStateWithDefaults } from './types/uiState';

/**
 * Main App component that serves as the root of the application
 * Manages theme state and renders the main layout components
 */
const App: React.FC = () => {
  // State for dark/light mode toggle - initialize with undefined to wait for store load
  const [darkMode, setDarkMode] = useState<boolean | undefined>(undefined);

  // State for panel visibility - initialize with undefined to wait for store load
  const [showCoursesPanel, setShowCoursesPanel] = useState<boolean | undefined>(undefined);
  const [showLecturersPanel, setShowLecturersPanel] = useState<boolean | undefined>(undefined);

  // Track if initial load is complete
  const [isInitialLoadComplete, setIsInitialLoadComplete] = useState(false);

  // Load UI state from store on component mount
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const rawUIState = await window.electronAPI.store.get('uiState');
        const uiState = getUIStateWithDefaults(rawUIState);

        if (uiState) {
          // Set dark mode
          const darkModeValue = uiState.darkMode !== undefined ? uiState.darkMode : false;
          setDarkMode(darkModeValue);

          // Apply dark mode class to HTML element for Tailwind
          if (darkModeValue) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }

          // Set panel visibility with fallback to defaults (force true as default)
          const coursesPanelValue = uiState.showCoursesPanel !== undefined ? uiState.showCoursesPanel : true;
          const lecturersPanelValue = uiState.showLecturersPanel !== undefined ? uiState.showLecturersPanel : true;

          // Force panels to be visible by default - override stored false values
          const finalCoursesPanelValue = coursesPanelValue === false ? true : coursesPanelValue;
          const finalLecturersPanelValue = lecturersPanelValue === false ? true : lecturersPanelValue;

          setShowCoursesPanel(finalCoursesPanelValue);
          setShowLecturersPanel(finalLecturersPanelValue);
        } else {
          // No UI state found, use defaults
          setDarkMode(false);
          setShowCoursesPanel(true);
          setShowLecturersPanel(true);
        }

        setIsInitialLoadComplete(true);
      } catch (error) {
        console.error('Error loading UI state:', error);
        // Fallback to defaults on error
        setDarkMode(false);
        setShowCoursesPanel(true);
        setShowLecturersPanel(true);
        setIsInitialLoadComplete(true);
      }
    };

    loadUIState();
  }, []);

  // Save UI state to store when it changes (only after initial load is complete)
  useEffect(() => {
    // Don't save during initial load to avoid overwriting loaded values
    if (!isInitialLoadComplete || darkMode === undefined || showCoursesPanel === undefined || showLecturersPanel === undefined) {
      return;
    }

    const saveUIState = async () => {
      try {
        // Get the current UI state
        const rawCurrentUIState = await window.electronAPI.store.get('uiState');
        const currentUIState = getUIStateWithDefaults(rawCurrentUIState);
        // Create a new UI state object with the updated values
        const newUIState: UIState = {
          ...currentUIState,
          darkMode,
          showCoursesPanel,
          showLecturersPanel
        };
        // Set the new UI state
        await window.electronAPI.store.set('uiState', newUIState);
      } catch (error) {
        console.error('Error saving UI state:', error);
      }
    };

    saveUIState();
  }, [darkMode, showCoursesPanel, showLecturersPanel, isInitialLoadComplete]);

  // Create theme based on dark mode state (use false as default while loading)
  const theme = createTheme({
    palette: {
      mode: darkMode === true ? 'dark' : 'light',
      error: {
        main: '#ef4444',
      },
    },
  });

  // Toggle dark/light mode
  const toggleDarkMode = () => {
    if (darkMode === undefined) return; // Don't toggle while loading

    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Apply dark mode class to HTML element for Tailwind
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // Toggle panels visibility
  const toggleCoursesPanel = () => {
    if (showCoursesPanel === undefined) return; // Don't toggle while loading
    setShowCoursesPanel(!showCoursesPanel);
  };

  const toggleLecturersPanel = () => {
    if (showLecturersPanel === undefined) return; // Don't toggle while loading
    setShowLecturersPanel(!showLecturersPanel);
  };

  // Don't render until initial load is complete
  if (!isInitialLoadComplete || darkMode === undefined || showCoursesPanel === undefined || showLecturersPanel === undefined) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }

  return (
    <AppProvider>
      <DragDropProvider>
        <ThemeProvider theme={theme}>
          <CssBaseline />
        <div className="flex flex-col h-screen overflow-hidden bg-gray-50 dark:bg-gray-900">
          {/* Header component */}
          <Header darkMode={darkMode} toggleDarkMode={toggleDarkMode} />

          {/* Main content area with panels and timetable canvas */}
          <div className="flex flex-1 overflow-hidden">
            {/* Left Column - Courses Panel */}
            {showCoursesPanel ? (
              <div className="w-[210px] border-r border-gray-200 dark:border-gray-700 overflow-y-auto relative">
                <CoursesPanel />
                <div className="absolute top-2 right-0 transform translate-x-1/5 z-[100]">
                  <Tooltip title="Hide courses panel">
                    <IconButton
                      size="small"
                      onClick={toggleCoursesPanel}
                      className="bg-blue-500 dark:bg-blue-700 shadow-md border border-blue-300 dark:border-blue-600 text-white !important"
                    >
                      <ChevronLeftIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </div>
              </div>
            ) : (
              <div className="border-r border-gray-200 dark:border-gray-700">
                <Tooltip title="Show courses panel">
                  <IconButton
                    size="small"
                    onClick={toggleCoursesPanel}
                    className="m-2 bg-blue-500 dark:bg-blue-700 shadow-md border-2 border-blue-300 dark:border-blue-400 text-blue-100 hover:text-blue-300"
                  >
                    <ChevronRightIcon fontSize="small" className="text-blue-600 dark:text-blue-400" />
                    <MenuBookIcon fontSize="small" className="text-blue-600 dark:text-blue-400" />
                  </IconButton>
                </Tooltip>
              </div>
            )}

            {/* Central Timetable Canvas */}
            <div className="flex-1 overflow-auto">
              <TimetableCanvas />
            </div>

            {/* Right Column - Lecturers Panel */}
            {showLecturersPanel ? (
              <div className="w-[210px] border-l border-gray-200 dark:border-gray-700 overflow-y-auto relative">
                <LecturersPanel />
                <div className="absolute top-2 left-0 transform -translate-x-1/5 z-[100]">
                  <Tooltip title="Hide lecturers panel">
                    <IconButton
                      size="small"
                      onClick={toggleLecturersPanel}
                      className="m-2 bg-blue-500 dark:bg-blue-700 shadow-md border border-blue-300 dark:border-blue-600 text-white !important"
                    >
                      <ChevronRightIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </div>
              </div>
            ) : (
              <div className="border-l border-gray-200 dark:border-gray-700">
                <Tooltip title="Show lecturers panel">
                  <IconButton
                    size="small"
                    onClick={toggleLecturersPanel}
                    className="m-2 bg-blue-500 dark:bg-blue-700 shadow-md border-2 border-blue-300 dark:border-blue-400 text-blue-100 hover:text-blue-300"
                  >
                    <ChevronLeftIcon fontSize="small" className="text-blue-600 dark:text-blue-400" />
                    <PersonIcon fontSize="small" className="text-blue-600 dark:text-blue-400" />
                  </IconButton>
                </Tooltip>
              </div>
            )}
          </div>
        </div>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={true}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
        </ThemeProvider>
      </DragDropProvider>
    </AppProvider>
  );
};

export default App;
