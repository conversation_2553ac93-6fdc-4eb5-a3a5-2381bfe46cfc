import React, { useState } from 'react';
import { useDragDrop } from '../../context/DragDropContext';
import { Chip, Tooltip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ManIcon from '@mui/icons-material/Man';
import WomanIcon from '@mui/icons-material/Woman';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import ErrorIcon from '@mui/icons-material/Error';
import { getArabicTextClass, getArabicFontFamily } from '../../utils/arabicUtils';

interface PgSessionCardProps {
  day: string;
  sessionIds: string[]; // Multiple session IDs from the main canvas that compose this PG session
  sectionId?: string;
  courseCode?: string;
  courseName?: string;
  courseColor?: string;
  sectionNumber?: number;
  gender?: 'M' | 'F';
  scheduledHours?: number;
  totalHours?: number;
  lecturerOccurrences?: Array<{
    id: string;
    lecturerId: string;
    firstName: string;
    lastName?: string;
  }>;
  onDelete?: () => void;
  onRemoveLecturer?: (lecturerId: string) => void;
  isHighlighted?: boolean;
  isEmpty?: boolean; // Flag to indicate if this is an empty droppable area
}

const PgSessionCard: React.FC<PgSessionCardProps> = ({
  day,
  sessionIds,
  courseCode,
  courseName,
  courseColor = '#e5e7eb',
  sectionNumber,
  gender,
  scheduledHours,
  totalHours,
  lecturerOccurrences = [],
  onDelete,
  onRemoveLecturer,
  isHighlighted = false,
  isEmpty = false
}) => {
  const { dragItem, canDropSectionAt, canDropLecturerAt, createSessionFromSection, assignLecturerToSession } = useDragDrop();
  const [isDragOver, setIsDragOver] = useState(false);

  // Handle drag over
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!dragItem) return;

    let canDrop = false;
    if (dragItem.type === 'SECTION') {
      // Check if this is a postgraduate section that can be dropped
      canDrop = canDropSectionAt(dragItem.id, day, 10); // Use period 10 (5 PM) as reference point
    } else if (dragItem.type === 'LECTURER' && sessionIds.length > 0) {
      canDrop = canDropLecturerAt(dragItem.id, sessionIds[0]); // Check against first session ID
    }

    if (canDrop) {
      setIsDragOver(true);
      e.dataTransfer.dropEffect = 'move';
    } else {
      e.dataTransfer.dropEffect = 'none';
    }
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'));
      if (data.type === 'SECTION') {
        // For postgraduate courses, we create a session at period 10 (5 PM)
        createSessionFromSection(data.id, day, 10);
      } else if (data.type === 'LECTURER' && sessionIds.length > 0) {
        // Assign lecturer to all session IDs that make up this PG session
        sessionIds.forEach(sessionId => {
          assignLecturerToSession(data.id, sessionId);
        });
      }
    } catch (error) {
      console.error('Error parsing drop data:', error);
    }
  };

  // Get status icon for section based on scheduled hours
  const getSectionStatusIcon = (scheduledHours?: number, totalHours?: number) => {
    if (!scheduledHours || !totalHours) return null;
    if (scheduledHours > totalHours) return <ErrorIcon fontSize="small" className="text-red-500 dark:text-red-400" />;
    if (scheduledHours === totalHours) return <CheckCircleIcon fontSize="small" className="text-green-500 dark:text-green-400" />;
    return <HourglassEmptyIcon fontSize="small" className="text-orange-500 dark:text-orange-400" />;
  };

  const isMale = gender === 'M';
  const genderIconClass = isMale ? 'text-blue-600 dark:text-blue-400' : 'text-pink-600 dark:text-pink-400';
  const genderBgClass = isMale
    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    : 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';

  // If there's no session (empty area), render an empty droppable area
  if (isEmpty) {
    return (
      <div
        className={`h-full w-full rounded ${isDragOver && dragItem?.type === 'SECTION' ? 'bg-blue-50 dark:bg-blue-900/20' : ''} transition-colors duration-200`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      />
    );
  }

  // Check if session has assigned lecturers
  const hasAssignedLecturers = lecturerOccurrences.length > 0;

  // Determine background color based on lecturer assignment status
  const getBackgroundColor = () => {
    if (hasAssignedLecturers) {
      // Green background for sessions with lecturers
      return '#10b981'; // green-500
    } else {
      // Orange background for sessions without lecturers
      return '#f97316'; // orange-500
    }
  };

  // If there is a session, render the session card
  return (
    <div
      className={`flex flex-col p-1 rounded-md shadow-sm border z-10 relative
        ${isDragOver && dragItem?.type === 'LECTURER' ? 'ring-2 ring-blue-500 dark:ring-blue-400' :
          hasAssignedLecturers ? 'border-2 border-green-500 dark:border-green-400' : 'border-gray-200 dark:border-gray-700'}
        ${isHighlighted ? 'ring-4 ring-yellow-400 dark:ring-yellow-500 shadow-lg shadow-yellow-300 dark:shadow-yellow-700 animate-pulse' : ''}
        transition-all group`}
      style={{ backgroundColor: getBackgroundColor() + '20' }}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Tooltip
            title={
              <span style={{
                fontFamily: getArabicFontFamily(courseName || '') || undefined
              }}>
                {courseName || ''}
              </span>
            }
            arrow
          >
            <span
              className="px-1 py-0.5 rounded-full text-xs font-medium text-white"
              style={{
                backgroundColor: courseColor
              }}
            >
              {courseCode}
            </span>
          </Tooltip>

          {/* Status icon */}
          {scheduledHours !== undefined && totalHours !== undefined && (
            <span className="ml-0.5">
              {getSectionStatusIcon(scheduledHours, totalHours)}
            </span>
          )}
        </div>

        <div className="flex items-center">
          <span className={`${genderIconClass} mr-0.5`}>
            {isMale ? <ManIcon fontSize="small" /> : <WomanIcon fontSize="small" />}
          </span>
          <span
            className={`px-1 py-0.5 rounded-full text-xs font-medium ${genderBgClass}`}
          >
            {sectionNumber}
          </span>
          {onDelete && (
            <button
              className="ml-0.5 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={onDelete}
              aria-label="Delete session"
            >
              <CloseIcon style={{ fontSize: '12px' }} />
            </button>
          )}
        </div>
      </div>

      {/* Lecturer chips */}
      {lecturerOccurrences.length > 0 && (
        <div className="flex flex-wrap gap-0.5 mt-0.5">
          {lecturerOccurrences.map(occurrence => {
            // Always use full name if lastName is available
            const displayName = occurrence.lastName
              ? `${occurrence.firstName} ${occurrence.lastName}`
              : occurrence.firstName;

            return (
              <Chip
                key={occurrence.id}
                label={displayName}
                size="small"
                onDelete={() => onRemoveLecturer?.(occurrence.lecturerId)}
                deleteIcon={<CloseIcon fontSize="small" />}
                className={`bg-gray-100 dark:bg-gray-700 text-xs ${getArabicTextClass(displayName)}`}
                style={{
                  height: '16px',
                  fontSize: '0.65rem',
                  fontFamily: getArabicFontFamily(displayName) || undefined
                }}
                title={displayName}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export default PgSessionCard;