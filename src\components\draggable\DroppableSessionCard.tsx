import React, { useState } from 'react';
import { useDragDrop } from '../../context/DragDropContext';
import { Session } from '../../types/models';
import { Chip, Tooltip } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ManIcon from '@mui/icons-material/Man';
import WomanIcon from '@mui/icons-material/Woman';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import ErrorIcon from '@mui/icons-material/Error';
import { getArabicTextClass, getArabicFontFamily } from '../../utils/arabicUtils';


interface DroppableSessionCardProps {
  day: string;
  period: number;
  sessionId?: string;
  sectionId?: string;
  courseCode?: string;
  courseName?: string; // Added courseName property
  courseColor?: string;
  sectionNumber?: number;
  gender?: 'M' | 'F';
  scheduledHours?: number;
  totalHours?: number;
  lecturerOccurrences?: Array<{
    id: string;
    lecturerId: string;
    firstName: string;
    lastName?: string;
  }>;
  onDelete?: () => void;
  onRemoveLecturer?: (lecturerId: string) => void;
  isAutoGenerated?: boolean; // Flag to indicate if the session was auto-generated
  isHighlighted?: boolean; // Flag to indicate if the session should be highlighted
}

const DroppableSessionCard: React.FC<DroppableSessionCardProps> = ({
  day,
  period,
  sessionId,
  courseCode,
  courseName,
  courseColor = '#e5e7eb',
  sectionNumber,
  gender,
  scheduledHours,
  totalHours,
  lecturerOccurrences = [],
  onDelete,
  onRemoveLecturer,
  isAutoGenerated = false,
  isHighlighted = false
}) => {
  const { dragItem, canDropSectionAt, canDropLecturerAt, canDropSessionAt, createSessionFromSection, assignLecturerToSession, moveSession, setDragItem } = useDragDrop();
  const [isDragOver, setIsDragOver] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  // Handle drag start for session cards
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>) => {
    if (!sessionId) return; // Only session cards can be dragged

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: 'SESSION',
      id: sessionId
    }));

    // Set drag image (optional)
    const dragImage = document.createElement('div');
    dragImage.className = 'p-2 rounded-md shadow-md bg-white dark:bg-gray-800';
    dragImage.style.width = '120px';
    dragImage.style.height = '40px';
    dragImage.textContent = courseCode || 'Session';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 60, 20);
    setTimeout(() => document.body.removeChild(dragImage), 0);

    // Update drag context
    setDragItem({
      type: 'SESSION',
      id: sessionId,
      data: { id: sessionId, sectionId: '', day, startPeriod: period, endPeriod: period } as Session
    });

    setIsDragging(true);
  };

  // Handle drag end
  const handleDragEnd = () => {
    setIsDragging(false);
    setDragItem(null);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!dragItem) return;

    let canDrop = false;
    if (dragItem.type === 'SECTION') {
      canDrop = canDropSectionAt(dragItem.id, day, period);
    } else if (dragItem.type === 'LECTURER' && sessionId) {
      canDrop = canDropLecturerAt(dragItem.id, sessionId);
    } else if (dragItem.type === 'SESSION') {
      canDrop = canDropSessionAt(dragItem.id, day, period);
    }

    if (canDrop) {
      setIsDragOver(true);
      e.dataTransfer.dropEffect = 'move';
    } else {
      e.dataTransfer.dropEffect = 'none';
    }
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
    try {
      const data = JSON.parse(e.dataTransfer.getData('application/json'));
      if (data.type === 'SECTION') {
        createSessionFromSection(data.id, day, period);
      } else if (data.type === 'LECTURER' && sessionId) {
        assignLecturerToSession(data.id, sessionId);
      } else if (data.type === 'SESSION') {
        moveSession(data.id, day, period);
      }
    } catch (error) {
      console.error('Error parsing drop data:', error);
    }
  };

  // Get status icon for section based on scheduled hours
  const getSectionStatusIcon = (scheduledHours?: number, totalHours?: number) => {
    if (!scheduledHours || !totalHours) return null;
    if (scheduledHours > totalHours) return <ErrorIcon fontSize="small" className="text-red-500 dark:text-red-400" />;
    if (scheduledHours === totalHours) return <CheckCircleIcon fontSize="small" className="text-green-500 dark:text-green-400" />;
    return <HourglassEmptyIcon fontSize="small" className="text-orange-500 dark:text-orange-400" />;
  };

  const isMale = gender === 'M';
  const genderIconClass = isMale ? 'text-blue-600 dark:text-blue-400' : 'text-pink-600 dark:text-pink-400';
  const genderBgClass = isMale
    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    : 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200';

  // If there's no session, render an empty droppable area
  if (!sessionId) {
    return (
      <div
        className={`h-full w-full rounded ${isDragOver && dragItem?.type === 'SECTION' ? 'bg-blue-50 dark:bg-blue-900/20' : ''} transition-colors duration-200`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      />
    );
  }

  // Check if session has assigned lecturers
  const hasAssignedLecturers = lecturerOccurrences.length > 0;

  // Determine border styling based on state priority
  const getBorderClasses = () => {
    if (isDragOver && dragItem?.type === 'LECTURER') {
      return 'ring-2 ring-blue-500 dark:ring-blue-400';
    }

    if (isAutoGenerated && hasAssignedLecturers) {
      // Auto-generated with lecturers: solid green border
      return 'border-2 border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20';
    }

    if (isAutoGenerated) {
      // Auto-generated without lecturers: dashed green border
      return 'border-2 border-dashed border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20';
    }

    if (hasAssignedLecturers) {
      // Manual with lecturers: solid green border
      return 'border-2 border-green-500 dark:border-green-400';
    }

    // Default: gray border
    return 'border-gray-200 dark:border-gray-700';
  };

  // Determine background color based on lecturer assignment status
  const getBackgroundColor = () => {
    if (hasAssignedLecturers) {
      // Green background for sessions with lecturers
      return '#10b981'; // green-500
    } else {
      // Orange background for sessions without lecturers
      return '#f97316'; // orange-500
    }
  };

  // If there is a session, render the session card
  return (
    <div
      className={`flex flex-col p-0.5 rounded-md shadow-sm border z-10 relative
        ${getBorderClasses()}
        ${isHighlighted ? 'ring-4 ring-yellow-400 dark:ring-yellow-500 shadow-lg shadow-yellow-300 dark:shadow-yellow-700 animate-pulse' : ''}
        transition-all group ${isDragging ? 'opacity-50' : ''} cursor-grab active:cursor-grabbing`}
      style={{ backgroundColor: getBackgroundColor() + '20' }}
      draggable={!!sessionId}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      title={isAutoGenerated ? 'Auto-generated session (indicated by dotted border)' : ''}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Tooltip
            title={
              <span style={{
                fontFamily: getArabicFontFamily(courseName || '') || undefined
              }}>
                {courseName || ''}
              </span>
            }
            arrow
          >
            <span
              className="px-1 py-0.5 rounded-full text-xs font-medium text-white"
              style={{
                backgroundColor: courseColor
              }}
            >
              {courseCode}
            </span>
          </Tooltip>
          {/* Status icon */}
          {scheduledHours !== undefined && totalHours !== undefined && (
            <span className="ml-0.5">
              {getSectionStatusIcon(scheduledHours, totalHours)}
            </span>
          )}

          {/* Auto-generated indicator removed - dotted border is sufficient */}

        </div>
        <div className="flex items-center">
          <span className={`${genderIconClass} mr-0.5`}>
            {isMale ? <ManIcon fontSize="small" /> : <WomanIcon fontSize="small" />}
          </span>
          <span
            className={`px-1 py-0.5 rounded-full text-xs font-medium ${genderBgClass}`}
          >
            {sectionNumber}
          </span>
          {onDelete && (
            <button
              className="ml-0.5 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={onDelete}
              aria-label="Delete session"
            >
              <CloseIcon style={{ fontSize: '12px' }} />
            </button>
          )}
        </div>
      </div>
      {lecturerOccurrences.length > 0 && (
        <div className="flex flex-wrap gap-0.5 mt-0.5">
          {lecturerOccurrences.map(occurrence => {
            // Always use full name if lastName is available
            const displayName = occurrence.lastName
              ? `${occurrence.firstName} ${occurrence.lastName}`
              : occurrence.firstName;

            return (
              <Chip
                key={occurrence.id}
                label={displayName}
                size="small"
                onDelete={onRemoveLecturer ? () => onRemoveLecturer(occurrence.lecturerId) : undefined}
                deleteIcon={onRemoveLecturer ? <CloseIcon fontSize="small" /> : undefined}
                className={`bg-gray-100 dark:bg-gray-700 text-xs ${getArabicTextClass(displayName)}`}
                style={{
                  height: '16px',
                  fontSize: '0.65rem',
                  fontFamily: getArabicFontFamily(displayName) || undefined
                }}
                title={displayName}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};

export default DroppableSessionCard;
