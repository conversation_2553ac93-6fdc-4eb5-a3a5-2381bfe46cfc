import React, { useMemo, useState, useRef } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Card,
  CardContent,

  Chip,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import SearchIcon from '@mui/icons-material/Search';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { Lecturer } from '../../../types/models';

// Props for the LecturerLoadsTab component
interface LecturerLoadsTabProps {
  lecturers: Lecturer[];
  calculateFallTeachingLoad: (lecturerId: string) => number;
  calculateSpringTeachingLoad: (lecturerId: string) => number;
  calculateSummerTeachingLoad: (lecturerId: string) => number;
  calculateYearlyLoad: (lecturer: Lecturer) => number;
}

const LecturerLoadsTab: React.FC<LecturerLoadsTabProps> = ({
  lecturers,
  calculateFallTeachingLoad,
  calculateSpringTeachingLoad,
  calculateSummerTeachingLoad,
  calculateYearlyLoad
}) => {
  // State for search term
  const [searchTerm, setSearchTerm] = useState('');

  // Create ref for the table container
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const totalLecturers = lecturers.length;
    let overloadedCount = 0;
    let underloadedCount = 0;
    let optimalCount = 0;
    let totalOverloadHours = 0;
    let totalUnderloadHours = 0;

    lecturers.forEach(lecturer => {
      const yearlyLoad = calculateYearlyLoad(lecturer);
      const overload = yearlyLoad - lecturer.maxYearLoad;

      if (overload > 0) {
        overloadedCount++;
        totalOverloadHours += overload;
      } else if (overload < 0) {
        underloadedCount++;
        totalUnderloadHours += Math.abs(overload);
      } else {
        optimalCount++;
      }
    });

    return {
      totalLecturers,
      overloadedCount,
      underloadedCount,
      optimalCount,
      totalOverloadHours,
      totalUnderloadHours,
      overloadedPercentage: totalLecturers > 0 ? (overloadedCount / totalLecturers) * 100 : 0,
      underloadedPercentage: totalLecturers > 0 ? (underloadedCount / totalLecturers) * 100 : 0,
      optimalPercentage: totalLecturers > 0 ? (optimalCount / totalLecturers) * 100 : 0
    };
  }, [lecturers, calculateYearlyLoad]);

  // Filter lecturers based on search term
  const filteredLecturers = useMemo(() => {
    return lecturers.filter(lecturer => {
      const fullName = `${lecturer.title} ${lecturer.firstName} ${lecturer.lastName}`.toLowerCase();
      return fullName.includes(searchTerm.toLowerCase());
    });
  }, [lecturers, searchTerm]);

  // Export table as PDF
  const exportToPdf = () => {
    console.log('LecturerLoadsTab exportToPdf called');
    if (!tableContainerRef.current) {
      console.log('tableContainerRef is null');
      return;
    }

    // Create a new jsPDF instance
    import('jspdf').then(({ jsPDF }) => {
      import('jspdf-autotable').then((autoTableModule) => {
        console.log('jsPDF and jspdf-autotable loaded');
        const doc = new jsPDF();

        // Add title
        doc.setFontSize(16);
        doc.text('Lecturer Loads', 14, 20);

        // Prepare table data
        const tableData = filteredLecturers.map(lecturer => {
          const fallTeaching = calculateFallTeachingLoad(lecturer.id);
          const springTeaching = calculateSpringTeachingLoad(lecturer.id);
          const summerTeaching = calculateSummerTeachingLoad(lecturer.id);
          const yearlyLoad = calculateYearlyLoad(lecturer);
          const overload = yearlyLoad - lecturer.maxYearLoad;

          // Format the status value as text instead of using Chip component
          let statusText = 'OK';
          if (overload > 0) {
            statusText = `+${overload.toFixed(1)}`;
          } else if (overload < 0) {
            statusText = `${overload.toFixed(1)}`;
          }

          console.log(`Lecturer: ${lecturer.lastName}, Overload: ${overload}, StatusText: ${statusText}`);

          return [
            `${lecturer.title} ${lecturer.firstName} ${lecturer.lastName}`,
            fallTeaching.toFixed(1),
            lecturer.supervisionHoursFall.toFixed(1),
            springTeaching.toFixed(1),
            lecturer.supervisionHoursSpring.toFixed(1),
            summerTeaching.toFixed(1),
            yearlyLoad.toFixed(1),
            lecturer.maxYearLoad.toFixed(1),
            statusText // Use text instead of Chip component
          ];
        });

        // Define table headers
        const headers = [
          'Lecturer',
          'Fall Teaching',
          'Fall Supervision',
          'Spring Teaching',
          'Spring Supervision',
          'Summer',
          'Total Load',
          'Max',
          'Status'
        ];

        // Add table to PDF using autoTable
        const autoTable = autoTableModule.default;
        console.log('Table data:', tableData);
        console.log('Headers:', headers);

        try {
          autoTable(doc, {
            head: [headers],
            body: tableData,
            startY: 30,
            theme: 'grid',
            styles: { fontSize: 8, cellPadding: 2 },
            headStyles: { fillColor: [66, 66, 66] },
            // Adjust column widths - make first column wider, status column narrower
            columnStyles: {
              0: { cellWidth: 40 }, // Lecturer name
              8: { cellWidth: 15, halign: 'center' } // Status column
            }
          });
          console.log('Table added to PDF successfully');
        } catch (error) {
          console.error('Error adding table to PDF:', error);
        }

        // Save PDF
        try {
          doc.save('lecturer_loads.pdf');
          console.log('PDF saved successfully');
        } catch (error) {
          console.error('Error saving PDF:', error);
        }
      });
    });
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Summary Cards */}
      <Grid container spacing={1}>
        <Grid size={{ xs: 6, sm: 3 }}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Total Lecturers
              </Typography>
              <Typography variant="h4" color="primary" sx={{ mb: 0 }}>
                {summaryStats.totalLecturers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 6, sm: 3 }}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Overloaded
              </Typography>
              <Typography variant="h5" color="error" sx={{ mb: 0 }}>
                {summaryStats.overloadedCount}
                <Typography variant="caption" sx={{ ml: 0.5 }}>
                  ({summaryStats.overloadedPercentage.toFixed(1)}%)
                </Typography>
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block">
                Total: +{summaryStats.totalOverloadHours.toFixed(1)} hours
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 6, sm: 3 }}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Underloaded
              </Typography>
              <Typography variant="h5" color="warning.main" sx={{ mb: 0 }}>
                {summaryStats.underloadedCount}
                <Typography variant="caption" sx={{ ml: 0.5 }}>
                  ({summaryStats.underloadedPercentage.toFixed(1)}%)
                </Typography>
              </Typography>
              <Typography variant="caption" color="text.secondary" display="block">
                Total: -{summaryStats.totalUnderloadHours.toFixed(1)} hours
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 6, sm: 3 }}>
          <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Typography variant="subtitle1" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                Optimal Load
              </Typography>
              <Typography variant="h5" color="success.main" sx={{ mb: 0 }}>
                {summaryStats.optimalCount}
                <Typography variant="caption" sx={{ ml: 0.5 }}>
                  ({summaryStats.optimalPercentage.toFixed(1)}%)
                </Typography>
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filter */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <TextField
          variant="outlined"
          size="small"
          placeholder="Search lecturers..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ flexGrow: 1, mr: 1 }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }
          }}
        />
        <IconButton size="small" title="Export as PDF" onClick={exportToPdf}>
          <FileDownloadIcon fontSize="small" />
        </IconButton>
      </Box>

      {/* Lecturers Table */}
      <Card variant="outlined">
        <CardContent sx={{ p: 1, '&:last-child': { pb: 1 } }}>
          <TableContainer ref={tableContainerRef} sx={{ flexGrow: 1, overflow: 'auto' }}>
            <Table stickyHeader size="small" sx={{ '& .MuiTableCell-root': { py: 0.75, px: 1 } }}>
              <TableHead>
                <TableRow>
                  <TableCell>Lecturer</TableCell>
                  <TableCell align="center">Fall Teaching</TableCell>
                  <TableCell align="center">Fall Supervision</TableCell>
                  <TableCell align="center">Spring Teaching</TableCell>
                  <TableCell align="center">Spring Supervision</TableCell>
                  <TableCell align="center">Summer</TableCell>
                  <TableCell align="center">Total Load</TableCell>
                  <TableCell align="center">Max</TableCell>
                  <TableCell align="center">Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredLecturers.map((lecturer) => {
                  const fallTeaching = calculateFallTeachingLoad(lecturer.id);
                  const springTeaching = calculateSpringTeachingLoad(lecturer.id);
                  const summerTeaching = calculateSummerTeachingLoad(lecturer.id);
                  const yearlyLoad = calculateYearlyLoad(lecturer);
                  const overload = yearlyLoad - lecturer.maxYearLoad;

                  return (
                    <TableRow key={lecturer.id} hover>
                      <TableCell>
                        <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                          {lecturer.title} {lecturer.firstName} {lecturer.lastName}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="caption">
                          {fallTeaching.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="caption">
                          {lecturer.supervisionHoursFall.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="caption">
                          {springTeaching.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="caption">
                          {lecturer.supervisionHoursSpring.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="caption">
                          {summerTeaching.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                          {yearlyLoad.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <Typography variant="caption">
                          {lecturer.maxYearLoad.toFixed(1)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        {overload > 0 ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            <Chip
                              label={`+${overload.toFixed(1)}`}
                              color="error"
                              size="small"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                            {/* Add a hidden text element that will be captured by html2canvas */}
                            <Typography
                              variant="caption"
                              sx={{
                                position: 'absolute',
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '0.7rem',
                                pointerEvents: 'none'
                              }}
                            >
                              +{overload.toFixed(1)}
                            </Typography>
                          </Box>
                        ) : overload < 0 ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            <Chip
                              label={`${overload.toFixed(1)}`}
                              color="warning"
                              size="small"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                            {/* Add a hidden text element that will be captured by html2canvas */}
                            <Typography
                              variant="caption"
                              sx={{
                                position: 'absolute',
                                color: 'black',
                                fontWeight: 'bold',
                                fontSize: '0.7rem',
                                pointerEvents: 'none'
                              }}
                            >
                              {overload.toFixed(1)}
                            </Typography>
                          </Box>
                        ) : (
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            <Chip
                              label="OK"
                              color="success"
                              size="small"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                            {/* Add a hidden text element that will be captured by html2canvas */}
                            <Typography
                              variant="caption"
                              sx={{
                                position: 'absolute',
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: '0.7rem',
                                pointerEvents: 'none'
                              }}
                            >
                              OK
                            </Typography>
                          </Box>
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LecturerLoadsTab;
