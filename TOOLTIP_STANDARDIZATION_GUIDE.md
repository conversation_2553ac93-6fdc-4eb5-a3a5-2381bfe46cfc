# QU Scheduler - Tooltip Standardization Guide

## Overview
This document outlines the standardized tooltip implementation across the QU Scheduler application, specifically focusing on the header component and establishing guidelines for consistent tooltip usage throughout the application.

## ✅ Completed Header Tooltip Standardization

### 1. **Comprehensive Coverage**
All interactive elements in the header now have tooltips:

#### Left Section:
- **Department Name Input**: "Enter your department name for timetable identification and export file naming"

#### Center Section:
- **Academic Year Input**: "Set the academic year for your timetable (e.g., 2024/2025)"
- **Semester Dropdown**: "Select the semester for your timetable scheduling"
- **About Icon**: "About QU Scheduler - View application information and credits"

#### Right Section:
- **Auto Button**: "Auto-Schedule All Sections - Automatically schedule all theory sections using rule-based algorithms and constraints"
- **Export Button**: "Export & Import - Export timetable data to JSON files or import existing timetable configurations"
- **Delete Button**: "Delete All Sessions - Remove all scheduled sessions from the current semester and reset the timetable"
- **Stats Button**: "Statistics & Analytics - View detailed statistics about course scheduling, lecturer workloads, and timetable utilization"
- **Zoom Out Icon**: "Zoom Out - Decrease the application zoom level for a wider view"
- **Zoom In Icon**: "Zoom In - Increase the application zoom level for better readability (double-click to reset zoom)"
- **Dark/Light Mode Toggle**: Dynamic tooltip based on current mode
- **Settings Gear Icon**: "Scheduling Rules & Configuration - Configure auto-scheduling rules, constraints, and system preferences"

### 2. **Standardized Format**

#### Tooltip Structure:
```tsx
<Tooltip 
  title="[Action/Element Name] - [Detailed description of purpose and expected outcome]"
  placement="bottom"
  arrow
>
  {/* Interactive element */}
</Tooltip>
```

#### Key Formatting Rules:
- **Consistent placement**: All header tooltips use `placement="bottom"`
- **Arrow indicators**: All tooltips include `arrow` prop for better visual connection
- **Two-part structure**: Brief action name followed by detailed explanation
- **Descriptive content**: Explains both what the element does and what the user can expect

### 3. **Content Guidelines**

#### For Icon-Only Elements:
- Start with the action name (e.g., "Zoom Out", "About QU Scheduler")
- Follow with a dash and detailed explanation
- Keep concise but informative

#### For Text Buttons:
- Start with the button text or expanded version
- Provide comprehensive context about the action
- Explain the expected outcome or what will happen

#### For Input Fields:
- Explain the purpose of the field
- Provide context about how the data will be used
- Include examples where helpful

### 4. **Technical Implementation**

#### Material-UI Tooltip Component:
```tsx
import { Tooltip } from '@mui/material';
```

#### Consistent Props:
- `title`: The tooltip content (string)
- `placement`: Position relative to element (standardized to "bottom" for header)
- `arrow`: Boolean to show arrow pointer (always true for header)

#### Special Cases:
- **Disabled buttons**: Wrap in `<span>` to ensure tooltip shows even when button is disabled
- **Dynamic content**: Use conditional logic for context-sensitive tooltips (e.g., dark/light mode)

## 🎯 Benefits Achieved

### 1. **Improved User Experience**
- Every interactive element provides clear guidance
- Users understand the purpose and outcome of each action
- Reduced learning curve for new users

### 2. **Accessibility Enhancement**
- Screen readers can access tooltip content
- Visual indicators help users with different abilities
- Consistent interaction patterns

### 3. **Professional Appearance**
- Uniform styling and behavior across all tooltips
- Consistent visual language
- Enhanced application polish

### 4. **Maintainability**
- Clear guidelines for future tooltip implementations
- Consistent code patterns
- Easy to update and modify

## 📋 Guidelines for Future Development

### When Adding New Interactive Elements:

1. **Always include tooltips** for any clickable element
2. **Follow the established format** (action name - detailed description)
3. **Use consistent placement** (bottom for header, appropriate for other areas)
4. **Include arrow prop** for better visual connection
5. **Test with different content lengths** to ensure proper display

### Content Writing Guidelines:

1. **Be descriptive but concise**
2. **Explain the outcome**, not just the action
3. **Use consistent terminology** across the application
4. **Consider the user's context** and information needs
5. **Avoid technical jargon** unless necessary

### Technical Considerations:

1. **Import Material-UI Tooltip** consistently
2. **Handle disabled states** properly with span wrappers
3. **Test tooltip positioning** to avoid viewport clipping
4. **Ensure responsive behavior** on different screen sizes
5. **Maintain accessibility standards**

## 🔄 Next Steps

To extend this standardization across the entire application:

1. **Audit other components** for missing tooltips
2. **Apply similar patterns** to panels, modals, and other interactive areas
3. **Create reusable tooltip components** for common patterns
4. **Document component-specific guidelines** as needed
5. **Regular review and updates** to maintain consistency

## 📝 Notes

- This standardization focuses on the header component as the primary navigation area
- The implementation uses Material-UI's Tooltip component for consistency with the existing design system
- All tooltips are designed to be informative without being overwhelming
- The format can be adapted for different component contexts while maintaining core principles
