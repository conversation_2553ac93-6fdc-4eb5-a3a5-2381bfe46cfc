
; Alternative process management functions using Windows commands

Function FindProcess
    Exch $0 ; Process name
    Push $1
    Push $2
    
    ; Use tasklist to check if process is running
    nsExec::ExecToStack 'tasklist /FI "IMAGENAME eq $0" /NH'
    Pop $1 ; Return code
    Pop $2 ; Output
    
    ; Check if process was found
    ${StrStr} $2 $2 "$0"
    ${If} $2 != ""
        StrCpy $0 "0" ; Process found
    ${Else}
        StrCpy $0 "1" ; Process not found
    ${EndIf}
    
    Pop $2
    Pop $1
    Exch $0
FunctionEnd

Function CloseProcess
    Exch $0 ; Process name
    Push $1
    
    ; Use taskkill to close process gracefully
    nsExec::ExecToStack 'taskkill /IM "$0" /T'
    Pop $1 ; Return code
    
    ${If} $1 == 0
        StrCpy $0 "0" ; Success
    ${Else}
        StrCpy $0 "1" ; Failed
    ${EndIf}
    
    Pop $1
    Exch $0
FunctionEnd

Function KillProcess
    Exch $0 ; Process name
    Push $1
    
    ; Use taskkill to force close process
    nsExec::ExecToStack 'taskkill /IM "$0" /F /T'
    Pop $1 ; Return code
    
    ${If} $1 == 0
        StrCpy $0 "0" ; Success
    ${Else}
        StrCpy $0 "1" ; Failed
    ${EndIf}
    
    Pop $1
    Exch $0
FunctionEnd
