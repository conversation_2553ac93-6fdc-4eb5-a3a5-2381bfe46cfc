#!/usr/bin/env node

/**
 * QU Scheduler Proper ICO Creation Script
 * 
 * This script creates a proper Windows ICO file from PNG sources
 * using the to-ico package for Windows executable embedding.
 */

const fs = require('fs');
const path = require('path');
const toIco = require('to-ico');

console.log('🎨 QU Scheduler Proper ICO Creation');
console.log('===================================');

async function createProperIco() {
    try {
        console.log('\n🔍 Reading PNG source files...');
        
        // Define the PNG files in order of preference (largest to smallest)
        const pngFiles = [
            'assets/icons/icon-256x256.png',
            'assets/icons/icon-128x128.png',
            'assets/icons/icon-64x64.png',
            'assets/icons/icon-48x48.png',
            'assets/icons/icon-32x32.png',
            'assets/icons/icon-16x16.png'
        ];
        
        const pngBuffers = [];
        
        for (const pngFile of pngFiles) {
            if (fs.existsSync(pngFile)) {
                const buffer = fs.readFileSync(pngFile);
                pngBuffers.push(buffer);
                console.log(`✅ Loaded: ${path.basename(pngFile)} (${(buffer.length / 1024).toFixed(1)} KB)`);
            } else {
                console.log(`⚠️  Missing: ${path.basename(pngFile)}`);
            }
        }
        
        if (pngBuffers.length === 0) {
            throw new Error('No PNG files found');
        }
        
        console.log(`\n🎨 Converting ${pngBuffers.length} PNG files to ICO...`);
        
        // Convert to ICO
        const icoBuffer = await toIco(pngBuffers);
        
        // Backup original ICO if it exists
        const originalIcoPath = 'assets/icons/icon.ico';
        if (fs.existsSync(originalIcoPath)) {
            fs.copyFileSync(originalIcoPath, 'assets/icons/icon-backup.ico');
            console.log('✅ Original ICO backed up');
        }
        
        // Write new ICO file
        fs.writeFileSync(originalIcoPath, icoBuffer);
        
        const stats = fs.statSync(originalIcoPath);
        console.log(`✅ New ICO file created: ${(stats.size / 1024).toFixed(1)} KB`);
        
        console.log('\n🔍 Verifying ICO file...');
        
        // Basic verification - check file header
        const header = fs.readFileSync(originalIcoPath, { start: 0, end: 6 });
        const reserved = header.readUInt16LE(0);
        const type = header.readUInt16LE(2);
        const count = header.readUInt16LE(4);
        
        console.log(`📊 ICO Header: Reserved=${reserved}, Type=${type}, Count=${count}`);
        
        if (reserved === 0 && type === 1 && count > 0) {
            console.log('✅ ICO file header is valid');
        } else {
            console.log('⚠️  ICO file header may have issues');
        }
        
        console.log('\n🎉 ICO CREATION COMPLETE!');
        console.log('=========================');
        console.log('\n📋 Next Steps:');
        console.log('   1. Test icon embedding: npm run embed:icon-manual');
        console.log('   2. Verify embedding: npm run verify:icon');
        console.log('   3. Build installer if successful');
        
        console.log('\n✅ The ICO file should now be compatible with rcedit and Windows executable embedding!');
        
    } catch (error) {
        console.error('\n❌ ICO creation failed:', error.message);
        console.log('\n🔧 Troubleshooting:');
        console.log('   1. Ensure PNG files exist in assets/icons/');
        console.log('   2. Check that PNG files are valid');
        console.log('   3. Try running: npm run standardize:icons');
        process.exit(1);
    }
}

// Run the ICO creation
createProperIco();
