# NSIS Plugins for QU Scheduler Installer

This directory contains the plugins and include files needed for the enhanced QU Scheduler installer.

## Files:

### Include Files:
- `nsProcess.nsh` - Include file for nsProcess plugin
- `ProcessManagement.nsh` - Alternative process management macros
- `AlternativeProcessFunctions.nsh` - Windows command-based process functions

## Usage:

The installer script will automatically use these files when building the enhanced installer.

## Alternative Implementation:

If the nsProcess plugin is not available, the installer will fall back to using Windows built-in commands (tasklist/taskkill) for process management.

## Notes:

- The nsProcess plugin provides more reliable process management
- The alternative implementation works on all Windows systems without additional plugins
- Both implementations provide the same functionality to the installer
