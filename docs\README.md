# Electron Timetable Application

## Overview
This is an Electron-based timetable management application for academic scheduling. The application provides features for creating, managing, and optimizing course timetables.

## Features
- Create and manage courses, sections, and lecturers
- Drag-and-drop timetable creation interface
- Rule-based automatic scheduling system with CPS (Complementary Pattern System) refinement
- Individual section auto-scheduling with intelligent pattern matching
- Comprehensive rule system for academic scheduling constraints
- Timetable visualization and export
- Session management across different days and time periods
- Performance-optimized scheduling algorithms with caching and profiling

## Development
```
# Install dependencies
npm install

# Start the development server
npm run dev

# Build the application
npm run make
```

## License
MIT