# QU Scheduler Enhanced Installer Instructions

## Overview

This directory contains the enhanced NSIS installer for QU Scheduler, featuring advanced process detection, previous installation management, and post-installation launch options.

## Enhanced Features

### 🔍 **Process Detection and Management**
- Automatically detects running QU Scheduler processes
- User-friendly dialog to close application before installation
- Graceful and force-close options with retry mechanism

### 🔄 **Previous Installation Detection**
- Scans registry and file system for existing installations
- Offers automatic uninstallation of previous versions
- Clean removal with registry and file cleanup

### 🚀 **Post-Installation Launch**
- Optional "Launch QU Scheduler now" checkbox (pre-checked)
- Launches application with normal user privileges

## Prerequisites

### 1. Install NSIS (Nullsoft Scriptable Install System)
- Download from: https://nsis.sourceforge.io/
- Install version 3.08 or later
- Ensure `makensis.exe` is in your PATH
- Verify: `makensis /VERSION`

### 2. Node.js Build Environment
- Node.js 14 or later required
- Used for build scripts and automation

## Building the Enhanced Installer

### Method 1: Using Build Script (Recommended)
```bash
# 1. Package the Electron application
npm run package

# 2. Build the enhanced installer
node installer/build-nsis.js
```

### Method 2: Using npm Script
```bash
# Combined package and installer build
npm run make:nsis
```

### Build Output
- **File:** `installer/QU-Scheduler-Setup.exe`
- **Size:** ~116.65 MB
- **Features:** Full enhanced installer with conflict resolution

## Installation Experience

### Scenario 1: Clean Installation
1. ✅ System requirements check
2. ✅ License agreement
3. ✅ Component selection
4. ✅ Installation location
5. ✅ File installation
6. ✅ Optional launch checkbox
7. ✅ Complete

### Scenario 2: Application Running
1. ✅ System requirements check
2. ⚠️ **Process Detection:** "QU Scheduler is running..."
3. 👤 **User Choice:** Close application or abort
4. 🔄 **Automatic Close:** Graceful then force close if needed
5. ✅ Continue with standard installation

### Scenario 3: Previous Version Exists
1. ✅ System requirements check
2. ⚠️ **Previous Installation:** "Previous version detected..."
3. 👤 **User Choice:** Uninstall previous or install alongside
4. 🗑️ **Automatic Uninstall:** Silent removal of old version
5. ✅ Continue with new installation

## Installer Features

### Enhanced Features
- ✅ **Process Detection:** Automatic detection and closure of running applications
- ✅ **Previous Installation Handling:** Clean removal of old versions
- ✅ **Post-Install Launch:** Optional immediate application startup
- ✅ **Conflict Resolution:** Automatic handling of common installation issues

### Standard Features
- ✅ Professional Windows installer interface
- ✅ Custom installation location selection
- ✅ Desktop shortcut creation (optional)
- ✅ Start Menu shortcuts
- ✅ Quick Launch shortcut (optional)
- ✅ Complete uninstaller with process management
- ✅ License agreement display
- ✅ System requirements verification (Windows 7+ 64-bit)

## User Instructions

### For End Users
1. Download `QU-Scheduler-Setup.exe`
2. Right-click and select "Run as administrator"
3. **If prompted about running application:** Click "Yes" to close automatically
4. **If prompted about previous version:** Click "Yes" to uninstall (recommended)
5. Follow installation wizard
6. Choose installation location (default recommended)
7. Select desired shortcuts
8. **On finish page:** Keep "Launch QU Scheduler now" checked for immediate access
9. Click "Finish" to complete

### For Administrators
- **Silent Installation:** Use `/S` flag for automated deployment
- **Custom Location:** Use `/D=C:\Custom\Path` for specific installation directory
- **Process Management:** Installer handles running applications automatically
- **Clean Upgrades:** Previous versions are cleanly removed when detected

## Testing Recommendations

### Test Scenarios
1. **Clean System:** Fresh Windows, no previous installation
2. **Running Application:** Install while QU Scheduler is active
3. **Previous Version:** Install over existing installation
4. **Multiple Processes:** Multiple QU Scheduler instances running
5. **Limited Privileges:** Test with restricted user accounts
6. **Uninstallation:** Complete removal verification

### Expected Results
- ✅ All scenarios complete successfully
- ✅ No manual intervention for common conflicts
- ✅ Clear dialogs with appropriate options
- ✅ Complete cleanup on uninstallation

## Troubleshooting

### Enhanced Installer Issues

1. **"Application is running" dialog**
   - **Normal behavior:** Installer detected running QU Scheduler
   - **Action:** Click "Yes" to close application automatically
   - **Result:** Installation continues after process termination

2. **"Previous version detected" dialog**
   - **Normal behavior:** Installer found existing installation
   - **Recommended:** Click "Yes" to uninstall previous version
   - **Alternative:** Click "No" to install alongside (not recommended)

### Common Issues

1. **"Administrator rights required"**
   - **Solution:** Right-click installer and select "Run as administrator"

2. **"NSIS Error" during installation**
   - **Cause:** Corrupted download or insufficient disk space
   - **Solution:** Re-download installer, check available disk space

3. **Antivirus blocking installation**
   - **Cause:** False positive detection
   - **Solution:** Temporarily disable antivirus or add exception

4. **Process termination fails**
   - **Cause:** Application may be unresponsive
   - **Solution:** Manually close QU Scheduler from Task Manager, retry installer

## Distribution

### File Information
- **Primary File:** `installer/QU-Scheduler-Setup.exe`
- **Size:** 116.65 MB
- **Checksum:** Consider providing MD5/SHA256 for verification
- **Compatibility:** Windows 7+ (64-bit)

### Distribution Methods
- **Direct Download:** Email, USB drive, network share
- **Enterprise Deployment:** Group Policy, SCCM, automated deployment
- **University Portal:** Internal software distribution system

## Support

### Technical Support
- **Contact:** <EMAIL>
- **Institution:** Qatar University
- **Department:** Computer Science & Engineering

### Documentation
- **Enhanced Features:** See `ENHANCED_INSTALLER_FEATURES.md`
- **Technical Details:** Process management and registry handling
- **User Guide:** Complete installation scenarios and troubleshooting

## Version Information

- **Application:** QU Scheduler v1.0.0
- **Installer Type:** Enhanced NSIS-based Windows installer
- **Features:** Process detection, previous installation handling, post-install launch
- **Target Audience:** Qatar University department heads
- **Distribution:** Internal university use

The enhanced installer provides a professional, conflict-free installation experience suitable for enterprise deployment.