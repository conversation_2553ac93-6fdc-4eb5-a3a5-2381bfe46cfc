import { useEffect, useRef } from 'react';

/**
 * Custom hook for managing focus in modal dialogs to prevent ARIA accessibility violations
 * This hook ensures proper focus management when modals are opened/closed
 */
export const useFocusManagement = (isOpen: boolean) => {
  const previousActiveElement = useRef<HTMLElement | null>(null);
  const modalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element before opening modal
      previousActiveElement.current = document.activeElement as HTMLElement;

      // Remove focus from any element that might be hidden by aria-hidden
      if (document.activeElement && document.activeElement !== document.body) {
        (document.activeElement as HTMLElement).blur();
      }

      // Focus the modal container after a short delay to ensure it's rendered
      const timer = setTimeout(() => {
        if (modalRef.current) {
          modalRef.current.focus();
        }
      }, 100);

      return () => clearTimeout(timer);
    } else {
      // Restore focus to the previously focused element when modal closes
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
        previousActiveElement.current = null;
      }
    }
  }, [isOpen]);

  return modalRef;
};

/**
 * Hook to manage inert attribute for background content when modals are open
 * This prevents focus from reaching hidden content
 */
export const useInertBackground = (isOpen: boolean) => {
  useEffect(() => {
    const rootElement = document.getElementById('root');
    const bodyElement = document.body;

    if (isOpen) {
      // First, ensure no elements in the root have focus
      const activeElement = document.activeElement as HTMLElement;
      if (activeElement && rootElement && rootElement.contains(activeElement)) {
        // If the active element is inside root, blur it first
        activeElement.blur();
        // Also ensure document.body gets focus temporarily
        document.body.focus();
      }

      // Use a small delay to ensure focus has been properly removed
      const timer = setTimeout(() => {
        // Add inert attribute to prevent focus on background content
        if (rootElement) {
          rootElement.setAttribute('inert', '');
        }
        // Fallback: add class to body for CSS-based focus prevention
        bodyElement.classList.add('modal-open');
      }, 10);

      return () => clearTimeout(timer);
    } else {
      // Remove inert attribute when modal closes
      if (rootElement) {
        rootElement.removeAttribute('inert');
      }
      bodyElement.classList.remove('modal-open');
    }

    // Cleanup function
    return () => {
      if (rootElement) {
        rootElement.removeAttribute('inert');
      }
      bodyElement.classList.remove('modal-open');
    };
  }, [isOpen]);
};
